(()=>{var e={};e.id=1931,e.ids=[1931],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},35434:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d}),t(90908),t(30829),t(35866);var r=t(23191),s=t(88716),n=t(37922),i=t.n(n),l=t(95231),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(a,c);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,90908)),"D:\\Users\\Downloads\\kodeXGuard\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\Users\\Downloads\\kodeXGuard\\app\\page.tsx"],x="/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},60546:(e,a,t)=>{Promise.resolve().then(t.bind(t,67382))},87443:(e,a,t)=>{Promise.resolve().then(t.bind(t,48467))},27807:(e,a,t)=>{Promise.resolve().then(t.t.bind(t,12994,23)),Promise.resolve().then(t.t.bind(t,96114,23)),Promise.resolve().then(t.t.bind(t,9727,23)),Promise.resolve().then(t.t.bind(t,79671,23)),Promise.resolve().then(t.t.bind(t,41868,23)),Promise.resolve().then(t.t.bind(t,84759,23))},67382:(e,a,t)=>{"use strict";t.d(a,{AuthProvider:()=>i,a:()=>l});var r=t(10326),s=t(17577);let n=(0,s.createContext)(void 0);function i({children:e}){let[a,t]=(0,s.useState)(null),[i,l]=(0,s.useState)(null),[c,d]=(0,s.useState)(!0),o=async(e,a)=>{try{d(!0);let r=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:a})}),s=await r.json();if(s.success&&s.token)return l(s.token),t(s.user),!0;return!1}catch(e){return console.error("Login error:",e),!1}finally{d(!1)}},x=async()=>{try{let e=await fetch("/api/auth/refresh",{method:"POST",headers:{Authorization:`Bearer ${i}`}}),a=await e.json();a.success&&a.token&&l(a.token)}catch(e){console.error("Token refresh error:",e),m()}},m=()=>{t(null),l(null)};return r.jsx(n.Provider,{value:{user:a,token:i,login:o,logout:m,isLoading:c,isAuthenticated:!!a&&!!i,refreshToken:x},children:e})}function l(){let e=(0,s.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},48467:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>C});var r=t(10326),s=t(17577),n=t(90434),i=t(16361),l=t(76557);let c=(0,l.Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]),d=(0,l.Z)("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),o=(0,l.Z)("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]),x=(0,l.Z)("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]);var m=t(58038),h=t(5932),u=t(42887),p=t(77636),y=t(7027);let g=(0,l.Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),b=(0,l.Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);var f=t(54014);function j(){let e=new Date().getFullYear();return(0,r.jsxs)("footer",{className:"bg-gray-900 border-t border-gray-800",children:[r.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[r.jsx("div",{className:"w-8 h-8 bg-cyber-green rounded-lg flex items-center justify-center",children:r.jsx(m.Z,{className:"h-5 w-5 text-black"})}),r.jsx("span",{className:"font-bold text-white text-xl font-cyber",children:"KodeXGuard"})]}),r.jsx("p",{className:"text-gray-400 mb-6 max-w-sm",children:"Platform cybersecurity terdepan di Indonesia untuk OSINT, vulnerability scanning, dan analisis keamanan yang komprehensif."}),(0,r.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 text-gray-400",children:[r.jsx(h.Z,{className:"h-4 w-4 text-cyber-green"}),r.jsx("span",{className:"text-sm",children:"<EMAIL>"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3 text-gray-400",children:[r.jsx(u.Z,{className:"h-4 w-4 text-cyber-green"}),r.jsx("span",{className:"text-sm",children:"+62 21 1234 5678"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3 text-gray-400",children:[r.jsx(p.Z,{className:"h-4 w-4 text-cyber-green"}),r.jsx("span",{className:"text-sm",children:"Jakarta, Indonesia"})]})]}),r.jsx("div",{className:"flex space-x-4",children:[{name:"GitHub",icon:c,href:"https://github.com/kodexguard"},{name:"Twitter",icon:d,href:"https://twitter.com/kodexguard"},{name:"LinkedIn",icon:o,href:"https://linkedin.com/company/kodexguard"},{name:"Instagram",icon:x,href:"https://instagram.com/kodexguard"}].map(e=>{let a=e.icon;return r.jsx("a",{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center text-gray-400 hover:text-cyber-green hover:bg-gray-700 transition-all duration-200","aria-label":e.name,children:r.jsx(a,{className:"h-5 w-5"})},e.name)})})]}),[{title:"Platform",links:[{name:"Dashboard",href:"/dashboard"},{name:"OSINT Tools",href:"/osint"},{name:"Vulnerability Scanner",href:"/scanner"},{name:"File Analyzer",href:"/file-analyzer"},{name:"CVE Intelligence",href:"/cve"},{name:"API Playground",href:"/playground"}]},{title:"Resources",links:[{name:"Documentation",href:"/docs"},{name:"API Reference",href:"/docs/api"},{name:"Tutorials",href:"/docs/tutorials"},{name:"Blog",href:"/blog"},{name:"Community",href:"/community"},{name:"Status Page",href:"/status",external:!0}]},{title:"Company",links:[{name:"About Us",href:"/about"},{name:"Careers",href:"/careers"},{name:"Press Kit",href:"/press"},{name:"Partners",href:"/partners"},{name:"Contact",href:"/contact"},{name:"Support",href:"/support"}]},{title:"Legal",links:[{name:"Privacy Policy",href:"/privacy"},{name:"Terms of Service",href:"/terms"},{name:"Cookie Policy",href:"/cookies"},{name:"Security",href:"/security"},{name:"Compliance",href:"/compliance"},{name:"Bug Bounty",href:"/bug-bounty"}]}].map(e=>(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-white font-semibold mb-4",children:e.title}),r.jsx("ul",{className:"space-y-3",children:e.links.map(e=>r.jsx("li",{children:e.external?(0,r.jsxs)("a",{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-cyber-green transition-colors duration-200 text-sm flex items-center space-x-1",children:[r.jsx("span",{children:e.name}),r.jsx(y.Z,{className:"h-3 w-3"})]}):r.jsx(n.default,{href:e.href,className:"text-gray-400 hover:text-cyber-green transition-colors duration-200 text-sm",children:e.name})},e.name))})]},e.title))]})}),r.jsx("div",{className:"border-t border-gray-800",children:r.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between",children:[(0,r.jsxs)("div",{className:"mb-4 lg:mb-0",children:[r.jsx("h3",{className:"text-white font-semibold mb-2",children:"Stay Updated"}),r.jsx("p",{className:"text-gray-400 text-sm",children:"Dapatkan update terbaru tentang threat intelligence dan cybersecurity."})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3",children:[r.jsx("input",{type:"email",placeholder:"Enter your email",className:"px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyber-green focus:border-transparent"}),(0,r.jsxs)("button",{className:"cyber-btn-primary flex items-center justify-center space-x-2",children:[r.jsx("span",{children:"Subscribe"}),r.jsx(g,{className:"h-4 w-4"})]})]})]})})}),r.jsx("div",{className:"border-t border-gray-800",children:r.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-6 mb-4 md:mb-0",children:[(0,r.jsxs)("p",{className:"text-gray-400 text-sm",children:["\xa9 ",e," KodeXGuard. All rights reserved."]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-gray-400 text-sm",children:[r.jsx("span",{children:"Made with"}),r.jsx(b,{className:"h-4 w-4 text-red-500"}),r.jsx("span",{children:"in Indonesia"}),r.jsx(f.Z,{className:"h-4 w-4 text-cyber-green ml-2"})]})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-400",children:[r.jsx(n.default,{href:"/privacy",className:"hover:text-cyber-green transition-colors",children:"Privacy"}),r.jsx(n.default,{href:"/terms",className:"hover:text-cyber-green transition-colors",children:"Terms"}),r.jsx(n.default,{href:"/cookies",className:"hover:text-cyber-green transition-colors",children:"Cookies"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-400",children:[r.jsx("span",{children:"\uD83C\uDDEE\uD83C\uDDE9"}),r.jsx("span",{children:"Indonesia"})]})]})]})})})]})}var v=t(88307),N=t(6530),k=t(94244),w=t(50732),P=t(92498),S=t(39183),A=t(33734);function C(){let[e,a]=(0,s.useState)(0),[t,l]=(0,s.useState)({users:0,scans:0,vulnerabilities:0,cveDatabase:0}),c=[{icon:v.Z,title:"OSINT Investigator",description:"Investigasi mendalam dengan pencarian NIK, NPWP, nomor HP, email, domain, dan tracking lokasi real-time",color:"text-cyber-green"},{icon:m.Z,title:"Vulnerability Scanner",description:"Deteksi SQLi, XSS, LFI, RCE, Path Traversal dengan scoring CVSS dan mapping CVE otomatis",color:"text-cyber-blue"},{icon:N.Z,title:"File Analyzer",description:"Analisis webshell, malware, ransomware, dan deteksi secret/token dalam berbagai format file",color:"text-cyber-purple"},{icon:k.Z,title:"CVE Intelligence",description:"Database CVE terupdate harian dengan Google dorking preset dan payload generator",color:"text-cyber-red"},{icon:w.Z,title:"Bot Integration",description:"WhatsApp & Telegram bot untuk eksekusi scan, monitoring, dan notifikasi real-time",color:"text-nusantara-gold"},{icon:P.Z,title:"Developer Playground",description:"API testing environment dengan Swagger docs dan request builder interaktif",color:"text-nusantara-emerald"}];return(0,r.jsxs)("div",{className:"min-h-screen",children:[r.jsx(i.default,{isLandingPage:!0}),r.jsx("section",{className:"pt-32 pb-20 px-4",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto text-center",children:[(0,r.jsxs)("h1",{className:"text-5xl md:text-7xl font-bold font-cyber mb-6",children:[r.jsx("span",{className:"cyber-text",children:"Kode"}),r.jsx("span",{className:"nusantara-gold",children:"X"}),r.jsx("span",{className:"text-white",children:"Guard"})]}),r.jsx("p",{className:"text-xl md:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto",children:"Platform Cybersecurity & Bug Hunting mandiri dengan fitur OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bot Automation"}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12",children:[(0,r.jsxs)(n.default,{href:"/register",className:"cyber-btn-primary text-lg px-8 py-4",children:["Mulai Gratis ",r.jsx(S.Z,{className:"ml-2 h-5 w-5"})]}),r.jsx(n.default,{href:"/demo",className:"cyber-btn text-lg px-8 py-4",children:"Lihat Demo"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto",children:[(0,r.jsxs)("div",{className:"cyber-card p-6",children:[r.jsx("div",{className:"text-3xl font-bold cyber-text",children:t.users.toLocaleString()}),r.jsx("div",{className:"text-gray-400",children:"Active Users"})]}),(0,r.jsxs)("div",{className:"cyber-card p-6",children:[r.jsx("div",{className:"text-3xl font-bold text-cyber-blue",children:t.scans.toLocaleString()}),r.jsx("div",{className:"text-gray-400",children:"Scans Completed"})]}),(0,r.jsxs)("div",{className:"cyber-card p-6",children:[r.jsx("div",{className:"text-3xl font-bold text-cyber-red",children:t.vulnerabilities.toLocaleString()}),r.jsx("div",{className:"text-gray-400",children:"Vulnerabilities Found"})]}),(0,r.jsxs)("div",{className:"cyber-card p-6",children:[r.jsx("div",{className:"text-3xl font-bold nusantara-gold",children:t.cveDatabase.toLocaleString()}),r.jsx("div",{className:"text-gray-400",children:"CVE Database"})]})]})]})}),r.jsx("section",{id:"features",className:"py-20 px-4",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsxs)("h2",{className:"text-4xl font-bold font-cyber mb-4",children:["Fitur ",r.jsx("span",{className:"cyber-text",children:"Unggulan"})]}),r.jsx("p",{className:"text-xl text-gray-300 max-w-3xl mx-auto",children:"Solusi lengkap untuk kebutuhan cybersecurity dan bug hunting dengan teknologi terdepan"})]}),r.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:c.map((t,s)=>{let n=t.icon;return(0,r.jsxs)("div",{className:`cyber-card p-8 transition-all duration-300 cursor-pointer ${e===s?"border-cyber-green shadow-lg shadow-cyber-green/20":""}`,onClick:()=>a(s),children:[r.jsx(n,{className:`h-12 w-12 ${t.color} mb-4`}),r.jsx("h3",{className:"text-xl font-bold mb-3",children:t.title}),r.jsx("p",{className:"text-gray-400",children:t.description})]},s)})})]})}),r.jsx("section",{id:"pricing",className:"py-20 px-4 bg-cyber-darker/50",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsxs)("h2",{className:"text-4xl font-bold font-cyber mb-4",children:["Pilih ",r.jsx("span",{className:"nusantara-gold",children:"Plan"})," Anda"]}),r.jsx("p",{className:"text-xl text-gray-300",children:"Dari gratis hingga enterprise, kami punya solusi untuk semua kebutuhan"})]}),r.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:[{name:"Gratis",price:"Rp 0",period:"/bulan",features:["5 Scan/hari","Basic OSINT","File Analyzer (2MB)","Community Support"],color:"border-gray-600",popular:!1},{name:"Pelajar",price:"Rp 25.000",period:"/bulan",features:["50 Scan/hari","Full OSINT","File Analyzer (10MB)","CVE Database","Email Support"],color:"border-cyber-blue",popular:!1},{name:"Bug Hunter",price:"Rp 150.000",period:"/bulan",features:["Unlimited Scan","Advanced OSINT","File Analyzer (100MB)","Bot Integration","Priority Support","API Access"],color:"border-cyber-green",popular:!0},{name:"Cybersecurity",price:"Rp 500.000",period:"/bulan",features:["Enterprise Features","Custom Integration","Dedicated Support","White-label API","Advanced Analytics"],color:"border-nusantara-gold",popular:!1}].map((e,a)=>(0,r.jsxs)("div",{className:`cyber-card p-8 relative ${e.color} ${e.popular?"ring-2 ring-cyber-green":""}`,children:[e.popular&&r.jsx("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:r.jsx("span",{className:"bg-cyber-green text-black px-4 py-1 rounded-full text-sm font-bold",children:"POPULER"})}),(0,r.jsxs)("div",{className:"text-center mb-6",children:[r.jsx("h3",{className:"text-xl font-bold mb-2",children:e.name}),(0,r.jsxs)("div",{className:"text-3xl font-bold cyber-text",children:[e.price,r.jsx("span",{className:"text-sm text-gray-400",children:e.period})]})]}),r.jsx("ul",{className:"space-y-3 mb-8",children:e.features.map((e,a)=>(0,r.jsxs)("li",{className:"flex items-center",children:[r.jsx(A.Z,{className:"h-4 w-4 text-cyber-green mr-2"}),r.jsx("span",{className:"text-gray-300",children:e})]},a))}),r.jsx("button",{className:`w-full py-3 rounded-lg font-semibold transition-all duration-300 ${e.popular?"bg-cyber-green text-black hover:bg-transparent hover:text-cyber-green border border-cyber-green":"cyber-btn"}`,children:"Pilih Plan"})]},a))})]})}),r.jsx("footer",{className:"py-12 px-4 border-t border-gray-800",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsxs)("div",{className:"grid md:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[r.jsx(m.Z,{className:"h-6 w-6 text-cyber-green"}),r.jsx("span",{className:"text-lg font-bold font-cyber",children:"KodeXGuard"})]}),r.jsx("p",{className:"text-gray-400",children:"Platform cybersecurity terdepan untuk profesional keamanan dan bug hunter Indonesia."})]}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-semibold mb-4",children:"Produk"}),(0,r.jsxs)("ul",{className:"space-y-2 text-gray-400",children:[r.jsx("li",{children:r.jsx(n.default,{href:"/osint",className:"hover:text-cyber-green",children:"OSINT Tools"})}),r.jsx("li",{children:r.jsx(n.default,{href:"/scanner",className:"hover:text-cyber-green",children:"Vulnerability Scanner"})}),r.jsx("li",{children:r.jsx(n.default,{href:"/file-analyzer",className:"hover:text-cyber-green",children:"File Analyzer"})}),r.jsx("li",{children:r.jsx(n.default,{href:"/cve",className:"hover:text-cyber-green",children:"CVE Database"})})]})]}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-semibold mb-4",children:"Sumber Daya"}),(0,r.jsxs)("ul",{className:"space-y-2 text-gray-400",children:[r.jsx("li",{children:r.jsx(n.default,{href:"/docs",className:"hover:text-cyber-green",children:"Dokumentasi"})}),r.jsx("li",{children:r.jsx(n.default,{href:"/api",className:"hover:text-cyber-green",children:"API Reference"})}),r.jsx("li",{children:r.jsx(n.default,{href:"/blog",className:"hover:text-cyber-green",children:"Blog"})}),r.jsx("li",{children:r.jsx(n.default,{href:"/community",className:"hover:text-cyber-green",children:"Komunitas"})})]})]}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-semibold mb-4",children:"Perusahaan"}),(0,r.jsxs)("ul",{className:"space-y-2 text-gray-400",children:[r.jsx("li",{children:r.jsx(n.default,{href:"/about",className:"hover:text-cyber-green",children:"Tentang Kami"})}),r.jsx("li",{children:r.jsx(n.default,{href:"/contact",className:"hover:text-cyber-green",children:"Kontak"})}),r.jsx("li",{children:r.jsx(n.default,{href:"/privacy",className:"hover:text-cyber-green",children:"Kebijakan Privasi"})}),r.jsx("li",{children:r.jsx(n.default,{href:"/terms",className:"hover:text-cyber-green",children:"Syarat & Ketentuan"})})]})]})]}),r.jsx("div",{className:"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400",children:r.jsx("p",{children:"\xa9 2024 KodeXGuard. All rights reserved. Made with ❤️ for Indonesian Cybersecurity Community."})})]})}),r.jsx(j,{})]})}},76557:(e,a,t)=>{"use strict";t.d(a,{Z:()=>i});var r=t(17577),s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),i=(e,a)=>{let t=(0,r.forwardRef)(({color:t="currentColor",size:i=24,strokeWidth:l=2,absoluteStrokeWidth:c,className:d="",children:o,...x},m)=>(0,r.createElement)("svg",{ref:m,...s,width:i,height:i,stroke:t,strokeWidth:c?24*Number(l)/Number(i):l,className:["lucide",`lucide-${n(e)}`,d].join(" "),...x},[...a.map(([e,a])=>(0,r.createElement)(e,a)),...Array.isArray(o)?o:[o]]));return t.displayName=`${e}`,t}},50732:(e,a,t)=>{"use strict";t.d(a,{Z:()=>r});let r=(0,t(76557).Z)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},94244:(e,a,t)=>{"use strict";t.d(a,{Z:()=>r});let r=(0,t(76557).Z)("Bug",[["path",{d:"m8 2 1.88 1.88",key:"fmnt4t"}],["path",{d:"M14.12 3.88 16 2",key:"qol33r"}],["path",{d:"M9 7.13v-1a3.003 3.003 0 1 1 6 0v1",key:"d7y7pr"}],["path",{d:"M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6",key:"xs1cw7"}],["path",{d:"M12 20v-9",key:"1qisl0"}],["path",{d:"M6.53 9C4.6 8.8 3 7.1 3 5",key:"32zzws"}],["path",{d:"M6 13H2",key:"82j7cp"}],["path",{d:"M3 21c0-2.1 1.7-3.9 3.8-4",key:"4p0ekp"}],["path",{d:"M20.97 5c0 2.1-1.6 3.8-3.5 4",key:"18gb23"}],["path",{d:"M22 13h-4",key:"1jl80f"}],["path",{d:"M17.2 17c2.1.1 3.8 1.9 3.8 4",key:"k3fwyw"}]])},39183:(e,a,t)=>{"use strict";t.d(a,{Z:()=>r});let r=(0,t(76557).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},92498:(e,a,t)=>{"use strict";t.d(a,{Z:()=>r});let r=(0,t(76557).Z)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},7027:(e,a,t)=>{"use strict";t.d(a,{Z:()=>r});let r=(0,t(76557).Z)("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]])},6530:(e,a,t)=>{"use strict";t.d(a,{Z:()=>r});let r=(0,t(76557).Z)("FileSearch",[["path",{d:"M4 22h14a2 2 0 0 0 2-2V7.5L14.5 2H6a2 2 0 0 0-2 2v3",key:"am10z3"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["path",{d:"M5 17a3 3 0 1 0 0-6 3 3 0 0 0 0 6z",key:"ychnub"}],["path",{d:"m9 18-1.5-1.5",key:"1j6qii"}]])},5932:(e,a,t)=>{"use strict";t.d(a,{Z:()=>r});let r=(0,t(76557).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},77636:(e,a,t)=>{"use strict";t.d(a,{Z:()=>r});let r=(0,t(76557).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},42887:(e,a,t)=>{"use strict";t.d(a,{Z:()=>r});let r=(0,t(76557).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},33734:(e,a,t)=>{"use strict";t.d(a,{Z:()=>r});let r=(0,t(76557).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},30829:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>d,metadata:()=>c});var r=t(19510),s=t(77366),n=t.n(s);t(7633);var i=t(68570);let l=(0,i.createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\contexts\AuthContext.tsx#AuthProvider`);(0,i.createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\contexts\AuthContext.tsx#useAuth`);let c={title:"KodeXGuard - Platform Cybersecurity & Bug Hunting",description:"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting dengan dukungan Bot WhatsApp/Telegram",keywords:"cybersecurity, bug hunting, OSINT, vulnerability scanner, CVE, penetration testing, security tools",authors:[{name:"KodeXGuard Team"}],creator:"KodeXGuard",publisher:"KodeXGuard",robots:"index, follow",openGraph:{type:"website",locale:"id_ID",url:"https://kodexguard.com",title:"KodeXGuard - Platform Cybersecurity & Bug Hunting",description:"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting",siteName:"KodeXGuard"},twitter:{card:"summary_large_image",title:"KodeXGuard - Platform Cybersecurity & Bug Hunting",description:"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting"},viewport:"width=device-width, initial-scale=1",themeColor:"#00ff41"};function d({children:e}){return(0,r.jsxs)("html",{lang:"id",className:"dark",children:[(0,r.jsxs)("head",{children:[r.jsx("link",{rel:"icon",href:"/favicon.ico"}),r.jsx("link",{rel:"apple-touch-icon",href:"/apple-touch-icon.png"}),r.jsx("link",{rel:"manifest",href:"/manifest.json"})]}),(0,r.jsxs)("body",{className:`${n().className} min-h-screen bg-gradient-cyber text-white antialiased`,children:[r.jsx("div",{className:"matrix-bg",children:r.jsx("div",{className:"absolute inset-0 bg-gradient-to-b from-transparent via-cyber-dark/20 to-cyber-dark"})}),r.jsx("div",{className:"relative z-10",children:r.jsx(l,{children:e})}),r.jsx("script",{dangerouslySetInnerHTML:{__html:`
              // Matrix Rain Effect
              function createMatrixRain() {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.style.position = 'fixed';
                canvas.style.top = '0';
                canvas.style.left = '0';
                canvas.style.width = '100%';
                canvas.style.height = '100%';
                canvas.style.pointerEvents = 'none';
                canvas.style.zIndex = '-1';
                canvas.style.opacity = '0.1';
                document.body.appendChild(canvas);
                
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
                
                const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';
                const charArray = chars.split('');
                const fontSize = 14;
                const columns = canvas.width / fontSize;
                const drops = [];
                
                for (let i = 0; i < columns; i++) {
                  drops[i] = 1;
                }
                
                function draw() {
                  ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
                  ctx.fillRect(0, 0, canvas.width, canvas.height);
                  
                  ctx.fillStyle = '#00ff41';
                  ctx.font = fontSize + 'px monospace';
                  
                  for (let i = 0; i < drops.length; i++) {
                    const text = charArray[Math.floor(Math.random() * charArray.length)];
                    ctx.fillText(text, i * fontSize, drops[i] * fontSize);
                    
                    if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
                      drops[i] = 0;
                    }
                    drops[i]++;
                  }
                }
                
                setInterval(draw, 50);
                
                window.addEventListener('resize', () => {
                  canvas.width = window.innerWidth;
                  canvas.height = window.innerHeight;
                });
              }
              
              // Initialize matrix effect after page load
              if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', createMatrixRain);
              } else {
                createMatrixRain();
              }
            `}})]})]})}},90908:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\page.tsx#default`)},7633:()=>{}};var a=require("../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),r=a.X(0,[9276,82,434,6361],()=>t(35434));module.exports=r})();