"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry";
exports.ids = ["vendor-chunks/@opentelemetry"];
exports.modules = {

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/api/context.js":
/*!******************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/api/context.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContextAPI: () => (/* binding */ ContextAPI)\n/* harmony export */ });\n/* harmony import */ var _context_NoopContextManager__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../context/NoopContextManager */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/context/NoopContextManager.js\");\n/* harmony import */ var _internal_global_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../internal/global-utils */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/internal/global-utils.js\");\n/* harmony import */ var _diag__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./diag */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/api/diag.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __read = (undefined && undefined.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\n\n\n\nvar API_NAME = 'context';\nvar NOOP_CONTEXT_MANAGER = new _context_NoopContextManager__WEBPACK_IMPORTED_MODULE_0__.NoopContextManager();\n/**\n * Singleton object which represents the entry point to the OpenTelemetry Context API\n */\nvar ContextAPI = /** @class */ (function () {\n    /** Empty private constructor prevents end users from constructing a new instance of the API */\n    function ContextAPI() {\n    }\n    /** Get the singleton instance of the Context API */\n    ContextAPI.getInstance = function () {\n        if (!this._instance) {\n            this._instance = new ContextAPI();\n        }\n        return this._instance;\n    };\n    /**\n     * Set the current context manager.\n     *\n     * @returns true if the context manager was successfully registered, else false\n     */\n    ContextAPI.prototype.setGlobalContextManager = function (contextManager) {\n        return (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_1__.registerGlobal)(API_NAME, contextManager, _diag__WEBPACK_IMPORTED_MODULE_2__.DiagAPI.instance());\n    };\n    /**\n     * Get the currently active context\n     */\n    ContextAPI.prototype.active = function () {\n        return this._getContextManager().active();\n    };\n    /**\n     * Execute a function with an active context\n     *\n     * @param context context to be active during function execution\n     * @param fn function to execute in a context\n     * @param thisArg optional receiver to be used for calling fn\n     * @param args optional arguments forwarded to fn\n     */\n    ContextAPI.prototype.with = function (context, fn, thisArg) {\n        var _a;\n        var args = [];\n        for (var _i = 3; _i < arguments.length; _i++) {\n            args[_i - 3] = arguments[_i];\n        }\n        return (_a = this._getContextManager()).with.apply(_a, __spreadArray([context, fn, thisArg], __read(args), false));\n    };\n    /**\n     * Bind a context to a target function or event emitter\n     *\n     * @param context context to bind to the event emitter or function. Defaults to the currently active context\n     * @param target function or event emitter to bind\n     */\n    ContextAPI.prototype.bind = function (context, target) {\n        return this._getContextManager().bind(context, target);\n    };\n    ContextAPI.prototype._getContextManager = function () {\n        return (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_1__.getGlobal)(API_NAME) || NOOP_CONTEXT_MANAGER;\n    };\n    /** Disable and remove the global context manager */\n    ContextAPI.prototype.disable = function () {\n        this._getContextManager().disable();\n        (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_1__.unregisterGlobal)(API_NAME, _diag__WEBPACK_IMPORTED_MODULE_2__.DiagAPI.instance());\n    };\n    return ContextAPI;\n}());\n\n//# sourceMappingURL=context.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/api/context.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/api/diag.js":
/*!***************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/api/diag.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DiagAPI: () => (/* binding */ DiagAPI)\n/* harmony export */ });\n/* harmony import */ var _diag_ComponentLogger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../diag/ComponentLogger */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/diag/ComponentLogger.js\");\n/* harmony import */ var _diag_internal_logLevelLogger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../diag/internal/logLevelLogger */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/diag/internal/logLevelLogger.js\");\n/* harmony import */ var _diag_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../diag/types */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/diag/types.js\");\n/* harmony import */ var _internal_global_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../internal/global-utils */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/internal/global-utils.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __read = (undefined && undefined.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\n\n\n\n\nvar API_NAME = 'diag';\n/**\n * Singleton object which represents the entry point to the OpenTelemetry internal\n * diagnostic API\n */\nvar DiagAPI = /** @class */ (function () {\n    /**\n     * Private internal constructor\n     * @private\n     */\n    function DiagAPI() {\n        function _logProxy(funcName) {\n            return function () {\n                var args = [];\n                for (var _i = 0; _i < arguments.length; _i++) {\n                    args[_i] = arguments[_i];\n                }\n                var logger = (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_0__.getGlobal)('diag');\n                // shortcut if logger not set\n                if (!logger)\n                    return;\n                return logger[funcName].apply(logger, __spreadArray([], __read(args), false));\n            };\n        }\n        // Using self local variable for minification purposes as 'this' cannot be minified\n        var self = this;\n        // DiagAPI specific functions\n        var setLogger = function (logger, optionsOrLogLevel) {\n            var _a, _b, _c;\n            if (optionsOrLogLevel === void 0) { optionsOrLogLevel = { logLevel: _diag_types__WEBPACK_IMPORTED_MODULE_1__.DiagLogLevel.INFO }; }\n            if (logger === self) {\n                // There isn't much we can do here.\n                // Logging to the console might break the user application.\n                // Try to log to self. If a logger was previously registered it will receive the log.\n                var err = new Error('Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation');\n                self.error((_a = err.stack) !== null && _a !== void 0 ? _a : err.message);\n                return false;\n            }\n            if (typeof optionsOrLogLevel === 'number') {\n                optionsOrLogLevel = {\n                    logLevel: optionsOrLogLevel,\n                };\n            }\n            var oldLogger = (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_0__.getGlobal)('diag');\n            var newLogger = (0,_diag_internal_logLevelLogger__WEBPACK_IMPORTED_MODULE_2__.createLogLevelDiagLogger)((_b = optionsOrLogLevel.logLevel) !== null && _b !== void 0 ? _b : _diag_types__WEBPACK_IMPORTED_MODULE_1__.DiagLogLevel.INFO, logger);\n            // There already is an logger registered. We'll let it know before overwriting it.\n            if (oldLogger && !optionsOrLogLevel.suppressOverrideMessage) {\n                var stack = (_c = new Error().stack) !== null && _c !== void 0 ? _c : '<failed to generate stacktrace>';\n                oldLogger.warn(\"Current logger will be overwritten from \" + stack);\n                newLogger.warn(\"Current logger will overwrite one already registered from \" + stack);\n            }\n            return (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_0__.registerGlobal)('diag', newLogger, self, true);\n        };\n        self.setLogger = setLogger;\n        self.disable = function () {\n            (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_0__.unregisterGlobal)(API_NAME, self);\n        };\n        self.createComponentLogger = function (options) {\n            return new _diag_ComponentLogger__WEBPACK_IMPORTED_MODULE_3__.DiagComponentLogger(options);\n        };\n        self.verbose = _logProxy('verbose');\n        self.debug = _logProxy('debug');\n        self.info = _logProxy('info');\n        self.warn = _logProxy('warn');\n        self.error = _logProxy('error');\n    }\n    /** Get the singleton instance of the DiagAPI API */\n    DiagAPI.instance = function () {\n        if (!this._instance) {\n            this._instance = new DiagAPI();\n        }\n        return this._instance;\n    };\n    return DiagAPI;\n}());\n\n//# sourceMappingURL=diag.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/api/diag.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/api/metrics.js":
/*!******************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/api/metrics.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MetricsAPI: () => (/* binding */ MetricsAPI)\n/* harmony export */ });\n/* harmony import */ var _metrics_NoopMeterProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../metrics/NoopMeterProvider */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/metrics/NoopMeterProvider.js\");\n/* harmony import */ var _internal_global_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../internal/global-utils */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/internal/global-utils.js\");\n/* harmony import */ var _diag__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./diag */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/api/diag.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\n\nvar API_NAME = 'metrics';\n/**\n * Singleton object which represents the entry point to the OpenTelemetry Metrics API\n */\nvar MetricsAPI = /** @class */ (function () {\n    /** Empty private constructor prevents end users from constructing a new instance of the API */\n    function MetricsAPI() {\n    }\n    /** Get the singleton instance of the Metrics API */\n    MetricsAPI.getInstance = function () {\n        if (!this._instance) {\n            this._instance = new MetricsAPI();\n        }\n        return this._instance;\n    };\n    /**\n     * Set the current global meter provider.\n     * Returns true if the meter provider was successfully registered, else false.\n     */\n    MetricsAPI.prototype.setGlobalMeterProvider = function (provider) {\n        return (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_0__.registerGlobal)(API_NAME, provider, _diag__WEBPACK_IMPORTED_MODULE_1__.DiagAPI.instance());\n    };\n    /**\n     * Returns the global meter provider.\n     */\n    MetricsAPI.prototype.getMeterProvider = function () {\n        return (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_0__.getGlobal)(API_NAME) || _metrics_NoopMeterProvider__WEBPACK_IMPORTED_MODULE_2__.NOOP_METER_PROVIDER;\n    };\n    /**\n     * Returns a meter from the global meter provider.\n     */\n    MetricsAPI.prototype.getMeter = function (name, version, options) {\n        return this.getMeterProvider().getMeter(name, version, options);\n    };\n    /** Remove the global meter provider */\n    MetricsAPI.prototype.disable = function () {\n        (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_0__.unregisterGlobal)(API_NAME, _diag__WEBPACK_IMPORTED_MODULE_1__.DiagAPI.instance());\n    };\n    return MetricsAPI;\n}());\n\n//# sourceMappingURL=metrics.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/api/metrics.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/api/propagation.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/api/propagation.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PropagationAPI: () => (/* binding */ PropagationAPI)\n/* harmony export */ });\n/* harmony import */ var _internal_global_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../internal/global-utils */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/internal/global-utils.js\");\n/* harmony import */ var _propagation_NoopTextMapPropagator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../propagation/NoopTextMapPropagator */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/propagation/NoopTextMapPropagator.js\");\n/* harmony import */ var _propagation_TextMapPropagator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../propagation/TextMapPropagator */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/propagation/TextMapPropagator.js\");\n/* harmony import */ var _baggage_context_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../baggage/context-helpers */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/baggage/context-helpers.js\");\n/* harmony import */ var _baggage_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../baggage/utils */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/baggage/utils.js\");\n/* harmony import */ var _diag__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./diag */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/api/diag.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\n\n\n\n\nvar API_NAME = 'propagation';\nvar NOOP_TEXT_MAP_PROPAGATOR = new _propagation_NoopTextMapPropagator__WEBPACK_IMPORTED_MODULE_0__.NoopTextMapPropagator();\n/**\n * Singleton object which represents the entry point to the OpenTelemetry Propagation API\n */\nvar PropagationAPI = /** @class */ (function () {\n    /** Empty private constructor prevents end users from constructing a new instance of the API */\n    function PropagationAPI() {\n        this.createBaggage = _baggage_utils__WEBPACK_IMPORTED_MODULE_1__.createBaggage;\n        this.getBaggage = _baggage_context_helpers__WEBPACK_IMPORTED_MODULE_2__.getBaggage;\n        this.getActiveBaggage = _baggage_context_helpers__WEBPACK_IMPORTED_MODULE_2__.getActiveBaggage;\n        this.setBaggage = _baggage_context_helpers__WEBPACK_IMPORTED_MODULE_2__.setBaggage;\n        this.deleteBaggage = _baggage_context_helpers__WEBPACK_IMPORTED_MODULE_2__.deleteBaggage;\n    }\n    /** Get the singleton instance of the Propagator API */\n    PropagationAPI.getInstance = function () {\n        if (!this._instance) {\n            this._instance = new PropagationAPI();\n        }\n        return this._instance;\n    };\n    /**\n     * Set the current propagator.\n     *\n     * @returns true if the propagator was successfully registered, else false\n     */\n    PropagationAPI.prototype.setGlobalPropagator = function (propagator) {\n        return (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_3__.registerGlobal)(API_NAME, propagator, _diag__WEBPACK_IMPORTED_MODULE_4__.DiagAPI.instance());\n    };\n    /**\n     * Inject context into a carrier to be propagated inter-process\n     *\n     * @param context Context carrying tracing data to inject\n     * @param carrier carrier to inject context into\n     * @param setter Function used to set values on the carrier\n     */\n    PropagationAPI.prototype.inject = function (context, carrier, setter) {\n        if (setter === void 0) { setter = _propagation_TextMapPropagator__WEBPACK_IMPORTED_MODULE_5__.defaultTextMapSetter; }\n        return this._getGlobalPropagator().inject(context, carrier, setter);\n    };\n    /**\n     * Extract context from a carrier\n     *\n     * @param context Context which the newly created context will inherit from\n     * @param carrier Carrier to extract context from\n     * @param getter Function used to extract keys from a carrier\n     */\n    PropagationAPI.prototype.extract = function (context, carrier, getter) {\n        if (getter === void 0) { getter = _propagation_TextMapPropagator__WEBPACK_IMPORTED_MODULE_5__.defaultTextMapGetter; }\n        return this._getGlobalPropagator().extract(context, carrier, getter);\n    };\n    /**\n     * Return a list of all fields which may be used by the propagator.\n     */\n    PropagationAPI.prototype.fields = function () {\n        return this._getGlobalPropagator().fields();\n    };\n    /** Remove the global propagator */\n    PropagationAPI.prototype.disable = function () {\n        (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_3__.unregisterGlobal)(API_NAME, _diag__WEBPACK_IMPORTED_MODULE_4__.DiagAPI.instance());\n    };\n    PropagationAPI.prototype._getGlobalPropagator = function () {\n        return (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_3__.getGlobal)(API_NAME) || NOOP_TEXT_MAP_PROPAGATOR;\n    };\n    return PropagationAPI;\n}());\n\n//# sourceMappingURL=propagation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/api/propagation.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/api/trace.js":
/*!****************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/api/trace.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TraceAPI: () => (/* binding */ TraceAPI)\n/* harmony export */ });\n/* harmony import */ var _internal_global_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../internal/global-utils */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/internal/global-utils.js\");\n/* harmony import */ var _trace_ProxyTracerProvider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../trace/ProxyTracerProvider */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/ProxyTracerProvider.js\");\n/* harmony import */ var _trace_spancontext_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../trace/spancontext-utils */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/spancontext-utils.js\");\n/* harmony import */ var _trace_context_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../trace/context-utils */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/context-utils.js\");\n/* harmony import */ var _diag__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./diag */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/api/diag.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\n\n\n\nvar API_NAME = 'trace';\n/**\n * Singleton object which represents the entry point to the OpenTelemetry Tracing API\n */\nvar TraceAPI = /** @class */ (function () {\n    /** Empty private constructor prevents end users from constructing a new instance of the API */\n    function TraceAPI() {\n        this._proxyTracerProvider = new _trace_ProxyTracerProvider__WEBPACK_IMPORTED_MODULE_0__.ProxyTracerProvider();\n        this.wrapSpanContext = _trace_spancontext_utils__WEBPACK_IMPORTED_MODULE_1__.wrapSpanContext;\n        this.isSpanContextValid = _trace_spancontext_utils__WEBPACK_IMPORTED_MODULE_1__.isSpanContextValid;\n        this.deleteSpan = _trace_context_utils__WEBPACK_IMPORTED_MODULE_2__.deleteSpan;\n        this.getSpan = _trace_context_utils__WEBPACK_IMPORTED_MODULE_2__.getSpan;\n        this.getActiveSpan = _trace_context_utils__WEBPACK_IMPORTED_MODULE_2__.getActiveSpan;\n        this.getSpanContext = _trace_context_utils__WEBPACK_IMPORTED_MODULE_2__.getSpanContext;\n        this.setSpan = _trace_context_utils__WEBPACK_IMPORTED_MODULE_2__.setSpan;\n        this.setSpanContext = _trace_context_utils__WEBPACK_IMPORTED_MODULE_2__.setSpanContext;\n    }\n    /** Get the singleton instance of the Trace API */\n    TraceAPI.getInstance = function () {\n        if (!this._instance) {\n            this._instance = new TraceAPI();\n        }\n        return this._instance;\n    };\n    /**\n     * Set the current global tracer.\n     *\n     * @returns true if the tracer provider was successfully registered, else false\n     */\n    TraceAPI.prototype.setGlobalTracerProvider = function (provider) {\n        var success = (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_3__.registerGlobal)(API_NAME, this._proxyTracerProvider, _diag__WEBPACK_IMPORTED_MODULE_4__.DiagAPI.instance());\n        if (success) {\n            this._proxyTracerProvider.setDelegate(provider);\n        }\n        return success;\n    };\n    /**\n     * Returns the global tracer provider.\n     */\n    TraceAPI.prototype.getTracerProvider = function () {\n        return (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_3__.getGlobal)(API_NAME) || this._proxyTracerProvider;\n    };\n    /**\n     * Returns a tracer from the global tracer provider.\n     */\n    TraceAPI.prototype.getTracer = function (name, version) {\n        return this.getTracerProvider().getTracer(name, version);\n    };\n    /** Remove the global tracer provider */\n    TraceAPI.prototype.disable = function () {\n        (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_3__.unregisterGlobal)(API_NAME, _diag__WEBPACK_IMPORTED_MODULE_4__.DiagAPI.instance());\n        this._proxyTracerProvider = new _trace_ProxyTracerProvider__WEBPACK_IMPORTED_MODULE_0__.ProxyTracerProvider();\n    };\n    return TraceAPI;\n}());\n\n//# sourceMappingURL=trace.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/api/trace.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/baggage/context-helpers.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/baggage/context-helpers.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deleteBaggage: () => (/* binding */ deleteBaggage),\n/* harmony export */   getActiveBaggage: () => (/* binding */ getActiveBaggage),\n/* harmony export */   getBaggage: () => (/* binding */ getBaggage),\n/* harmony export */   setBaggage: () => (/* binding */ setBaggage)\n/* harmony export */ });\n/* harmony import */ var _api_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../api/context */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/api/context.js\");\n/* harmony import */ var _context_context__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../context/context */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/context/context.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\n/**\n * Baggage key\n */\nvar BAGGAGE_KEY = (0,_context_context__WEBPACK_IMPORTED_MODULE_0__.createContextKey)('OpenTelemetry Baggage Key');\n/**\n * Retrieve the current baggage from the given context\n *\n * @param {Context} Context that manage all context values\n * @returns {Baggage} Extracted baggage from the context\n */\nfunction getBaggage(context) {\n    return context.getValue(BAGGAGE_KEY) || undefined;\n}\n/**\n * Retrieve the current baggage from the active/current context\n *\n * @returns {Baggage} Extracted baggage from the context\n */\nfunction getActiveBaggage() {\n    return getBaggage(_api_context__WEBPACK_IMPORTED_MODULE_1__.ContextAPI.getInstance().active());\n}\n/**\n * Store a baggage in the given context\n *\n * @param {Context} Context that manage all context values\n * @param {Baggage} baggage that will be set in the actual context\n */\nfunction setBaggage(context, baggage) {\n    return context.setValue(BAGGAGE_KEY, baggage);\n}\n/**\n * Delete the baggage stored in the given context\n *\n * @param {Context} Context that manage all context values\n */\nfunction deleteBaggage(context) {\n    return context.deleteValue(BAGGAGE_KEY);\n}\n//# sourceMappingURL=context-helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/baggage/context-helpers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/baggage/internal/baggage-impl.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/baggage/internal/baggage-impl.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaggageImpl: () => (/* binding */ BaggageImpl)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __read = (undefined && undefined.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __values = (undefined && undefined.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar BaggageImpl = /** @class */ (function () {\n    function BaggageImpl(entries) {\n        this._entries = entries ? new Map(entries) : new Map();\n    }\n    BaggageImpl.prototype.getEntry = function (key) {\n        var entry = this._entries.get(key);\n        if (!entry) {\n            return undefined;\n        }\n        return Object.assign({}, entry);\n    };\n    BaggageImpl.prototype.getAllEntries = function () {\n        return Array.from(this._entries.entries()).map(function (_a) {\n            var _b = __read(_a, 2), k = _b[0], v = _b[1];\n            return [k, v];\n        });\n    };\n    BaggageImpl.prototype.setEntry = function (key, entry) {\n        var newBaggage = new BaggageImpl(this._entries);\n        newBaggage._entries.set(key, entry);\n        return newBaggage;\n    };\n    BaggageImpl.prototype.removeEntry = function (key) {\n        var newBaggage = new BaggageImpl(this._entries);\n        newBaggage._entries.delete(key);\n        return newBaggage;\n    };\n    BaggageImpl.prototype.removeEntries = function () {\n        var e_1, _a;\n        var keys = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            keys[_i] = arguments[_i];\n        }\n        var newBaggage = new BaggageImpl(this._entries);\n        try {\n            for (var keys_1 = __values(keys), keys_1_1 = keys_1.next(); !keys_1_1.done; keys_1_1 = keys_1.next()) {\n                var key = keys_1_1.value;\n                newBaggage._entries.delete(key);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (keys_1_1 && !keys_1_1.done && (_a = keys_1.return)) _a.call(keys_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return newBaggage;\n    };\n    BaggageImpl.prototype.clear = function () {\n        return new BaggageImpl();\n    };\n    return BaggageImpl;\n}());\n\n//# sourceMappingURL=baggage-impl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/baggage/internal/baggage-impl.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/baggage/internal/symbol.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/baggage/internal/symbol.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   baggageEntryMetadataSymbol: () => (/* binding */ baggageEntryMetadataSymbol)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Symbol used to make BaggageEntryMetadata an opaque type\n */\nvar baggageEntryMetadataSymbol = Symbol('BaggageEntryMetadata');\n//# sourceMappingURL=symbol.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvYXBpL2J1aWxkL2VzbS9iYWdnYWdlL2ludGVybmFsL3N5bWJvbC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCIsInNvdXJjZXMiOlsid2VicGFjazovL2tvZGV4Z3VhcmQvLi9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvYXBpL2J1aWxkL2VzbS9iYWdnYWdlL2ludGVybmFsL3N5bWJvbC5qcz9iZjNhIl0sInNvdXJjZXNDb250ZW50IjpbIi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG4vKipcbiAqIFN5bWJvbCB1c2VkIHRvIG1ha2UgQmFnZ2FnZUVudHJ5TWV0YWRhdGEgYW4gb3BhcXVlIHR5cGVcbiAqL1xuZXhwb3J0IHZhciBiYWdnYWdlRW50cnlNZXRhZGF0YVN5bWJvbCA9IFN5bWJvbCgnQmFnZ2FnZUVudHJ5TWV0YWRhdGEnKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXN5bWJvbC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/baggage/internal/symbol.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/baggage/utils.js":
/*!********************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/baggage/utils.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   baggageEntryMetadataFromString: () => (/* binding */ baggageEntryMetadataFromString),\n/* harmony export */   createBaggage: () => (/* binding */ createBaggage)\n/* harmony export */ });\n/* harmony import */ var _api_diag__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../api/diag */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/api/diag.js\");\n/* harmony import */ var _internal_baggage_impl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./internal/baggage-impl */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/baggage/internal/baggage-impl.js\");\n/* harmony import */ var _internal_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./internal/symbol */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/baggage/internal/symbol.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\n\nvar diag = _api_diag__WEBPACK_IMPORTED_MODULE_0__.DiagAPI.instance();\n/**\n * Create a new Baggage with optional entries\n *\n * @param entries An array of baggage entries the new baggage should contain\n */\nfunction createBaggage(entries) {\n    if (entries === void 0) { entries = {}; }\n    return new _internal_baggage_impl__WEBPACK_IMPORTED_MODULE_1__.BaggageImpl(new Map(Object.entries(entries)));\n}\n/**\n * Create a serializable BaggageEntryMetadata object from a string.\n *\n * @param str string metadata. Format is currently not defined by the spec and has no special meaning.\n *\n */\nfunction baggageEntryMetadataFromString(str) {\n    if (typeof str !== 'string') {\n        diag.error(\"Cannot create baggage metadata from unknown type: \" + typeof str);\n        str = '';\n    }\n    return {\n        __TYPE__: _internal_symbol__WEBPACK_IMPORTED_MODULE_2__.baggageEntryMetadataSymbol,\n        toString: function () {\n            return str;\n        },\n    };\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/baggage/utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/context-api.js":
/*!******************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/context-api.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   context: () => (/* binding */ context)\n/* harmony export */ });\n/* harmony import */ var _api_context__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api/context */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/api/context.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Split module-level variable definition into separate files to allow\n// tree-shaking on each api instance.\n\n/** Entrypoint for context API */\nvar context = _api_context__WEBPACK_IMPORTED_MODULE_0__.ContextAPI.getInstance();\n//# sourceMappingURL=context-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvYXBpL2J1aWxkL2VzbS9jb250ZXh0LWFwaS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDMkM7QUFDM0M7QUFDTyxjQUFjLG9EQUFVO0FBQy9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va29kZXhndWFyZC8uL25vZGVfbW9kdWxlcy9Ab3BlbnRlbGVtZXRyeS9hcGkvYnVpbGQvZXNtL2NvbnRleHQtYXBpLmpzPzAyYjEiXSwic291cmNlc0NvbnRlbnQiOlsiLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbi8vIFNwbGl0IG1vZHVsZS1sZXZlbCB2YXJpYWJsZSBkZWZpbml0aW9uIGludG8gc2VwYXJhdGUgZmlsZXMgdG8gYWxsb3dcbi8vIHRyZWUtc2hha2luZyBvbiBlYWNoIGFwaSBpbnN0YW5jZS5cbmltcG9ydCB7IENvbnRleHRBUEkgfSBmcm9tICcuL2FwaS9jb250ZXh0Jztcbi8qKiBFbnRyeXBvaW50IGZvciBjb250ZXh0IEFQSSAqL1xuZXhwb3J0IHZhciBjb250ZXh0ID0gQ29udGV4dEFQSS5nZXRJbnN0YW5jZSgpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29udGV4dC1hcGkuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/context-api.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/context/NoopContextManager.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/context/NoopContextManager.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NoopContextManager: () => (/* binding */ NoopContextManager)\n/* harmony export */ });\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./context */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/context/context.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __read = (undefined && undefined.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\n\nvar NoopContextManager = /** @class */ (function () {\n    function NoopContextManager() {\n    }\n    NoopContextManager.prototype.active = function () {\n        return _context__WEBPACK_IMPORTED_MODULE_0__.ROOT_CONTEXT;\n    };\n    NoopContextManager.prototype.with = function (_context, fn, thisArg) {\n        var args = [];\n        for (var _i = 3; _i < arguments.length; _i++) {\n            args[_i - 3] = arguments[_i];\n        }\n        return fn.call.apply(fn, __spreadArray([thisArg], __read(args), false));\n    };\n    NoopContextManager.prototype.bind = function (_context, target) {\n        return target;\n    };\n    NoopContextManager.prototype.enable = function () {\n        return this;\n    };\n    NoopContextManager.prototype.disable = function () {\n        return this;\n    };\n    return NoopContextManager;\n}());\n\n//# sourceMappingURL=NoopContextManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/context/NoopContextManager.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/context/context.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/context/context.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ROOT_CONTEXT: () => (/* binding */ ROOT_CONTEXT),\n/* harmony export */   createContextKey: () => (/* binding */ createContextKey)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/** Get a key to uniquely identify a context value */\nfunction createContextKey(description) {\n    // The specification states that for the same input, multiple calls should\n    // return different keys. Due to the nature of the JS dependency management\n    // system, this creates problems where multiple versions of some package\n    // could hold different keys for the same property.\n    //\n    // Therefore, we use Symbol.for which returns the same key for the same input.\n    return Symbol.for(description);\n}\nvar BaseContext = /** @class */ (function () {\n    /**\n     * Construct a new context which inherits values from an optional parent context.\n     *\n     * @param parentContext a context from which to inherit values\n     */\n    function BaseContext(parentContext) {\n        // for minification\n        var self = this;\n        self._currentContext = parentContext ? new Map(parentContext) : new Map();\n        self.getValue = function (key) { return self._currentContext.get(key); };\n        self.setValue = function (key, value) {\n            var context = new BaseContext(self._currentContext);\n            context._currentContext.set(key, value);\n            return context;\n        };\n        self.deleteValue = function (key) {\n            var context = new BaseContext(self._currentContext);\n            context._currentContext.delete(key);\n            return context;\n        };\n    }\n    return BaseContext;\n}());\n/** The root context is used as the default parent context when there is no active context */\nvar ROOT_CONTEXT = new BaseContext();\n//# sourceMappingURL=context.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvYXBpL2J1aWxkL2VzbS9jb250ZXh0L2NvbnRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUNBQXlDO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNPO0FBQ1AiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb2RleGd1YXJkLy4vbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2FwaS9idWlsZC9lc20vY29udGV4dC9jb250ZXh0LmpzPzhmZmUiXSwic291cmNlc0NvbnRlbnQiOlsiLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbi8qKiBHZXQgYSBrZXkgdG8gdW5pcXVlbHkgaWRlbnRpZnkgYSBjb250ZXh0IHZhbHVlICovXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlQ29udGV4dEtleShkZXNjcmlwdGlvbikge1xuICAgIC8vIFRoZSBzcGVjaWZpY2F0aW9uIHN0YXRlcyB0aGF0IGZvciB0aGUgc2FtZSBpbnB1dCwgbXVsdGlwbGUgY2FsbHMgc2hvdWxkXG4gICAgLy8gcmV0dXJuIGRpZmZlcmVudCBrZXlzLiBEdWUgdG8gdGhlIG5hdHVyZSBvZiB0aGUgSlMgZGVwZW5kZW5jeSBtYW5hZ2VtZW50XG4gICAgLy8gc3lzdGVtLCB0aGlzIGNyZWF0ZXMgcHJvYmxlbXMgd2hlcmUgbXVsdGlwbGUgdmVyc2lvbnMgb2Ygc29tZSBwYWNrYWdlXG4gICAgLy8gY291bGQgaG9sZCBkaWZmZXJlbnQga2V5cyBmb3IgdGhlIHNhbWUgcHJvcGVydHkuXG4gICAgLy9cbiAgICAvLyBUaGVyZWZvcmUsIHdlIHVzZSBTeW1ib2wuZm9yIHdoaWNoIHJldHVybnMgdGhlIHNhbWUga2V5IGZvciB0aGUgc2FtZSBpbnB1dC5cbiAgICByZXR1cm4gU3ltYm9sLmZvcihkZXNjcmlwdGlvbik7XG59XG52YXIgQmFzZUNvbnRleHQgPSAvKiogQGNsYXNzICovIChmdW5jdGlvbiAoKSB7XG4gICAgLyoqXG4gICAgICogQ29uc3RydWN0IGEgbmV3IGNvbnRleHQgd2hpY2ggaW5oZXJpdHMgdmFsdWVzIGZyb20gYW4gb3B0aW9uYWwgcGFyZW50IGNvbnRleHQuXG4gICAgICpcbiAgICAgKiBAcGFyYW0gcGFyZW50Q29udGV4dCBhIGNvbnRleHQgZnJvbSB3aGljaCB0byBpbmhlcml0IHZhbHVlc1xuICAgICAqL1xuICAgIGZ1bmN0aW9uIEJhc2VDb250ZXh0KHBhcmVudENvbnRleHQpIHtcbiAgICAgICAgLy8gZm9yIG1pbmlmaWNhdGlvblxuICAgICAgICB2YXIgc2VsZiA9IHRoaXM7XG4gICAgICAgIHNlbGYuX2N1cnJlbnRDb250ZXh0ID0gcGFyZW50Q29udGV4dCA/IG5ldyBNYXAocGFyZW50Q29udGV4dCkgOiBuZXcgTWFwKCk7XG4gICAgICAgIHNlbGYuZ2V0VmFsdWUgPSBmdW5jdGlvbiAoa2V5KSB7IHJldHVybiBzZWxmLl9jdXJyZW50Q29udGV4dC5nZXQoa2V5KTsgfTtcbiAgICAgICAgc2VsZi5zZXRWYWx1ZSA9IGZ1bmN0aW9uIChrZXksIHZhbHVlKSB7XG4gICAgICAgICAgICB2YXIgY29udGV4dCA9IG5ldyBCYXNlQ29udGV4dChzZWxmLl9jdXJyZW50Q29udGV4dCk7XG4gICAgICAgICAgICBjb250ZXh0Ll9jdXJyZW50Q29udGV4dC5zZXQoa2V5LCB2YWx1ZSk7XG4gICAgICAgICAgICByZXR1cm4gY29udGV4dDtcbiAgICAgICAgfTtcbiAgICAgICAgc2VsZi5kZWxldGVWYWx1ZSA9IGZ1bmN0aW9uIChrZXkpIHtcbiAgICAgICAgICAgIHZhciBjb250ZXh0ID0gbmV3IEJhc2VDb250ZXh0KHNlbGYuX2N1cnJlbnRDb250ZXh0KTtcbiAgICAgICAgICAgIGNvbnRleHQuX2N1cnJlbnRDb250ZXh0LmRlbGV0ZShrZXkpO1xuICAgICAgICAgICAgcmV0dXJuIGNvbnRleHQ7XG4gICAgICAgIH07XG4gICAgfVxuICAgIHJldHVybiBCYXNlQ29udGV4dDtcbn0oKSk7XG4vKiogVGhlIHJvb3QgY29udGV4dCBpcyB1c2VkIGFzIHRoZSBkZWZhdWx0IHBhcmVudCBjb250ZXh0IHdoZW4gdGhlcmUgaXMgbm8gYWN0aXZlIGNvbnRleHQgKi9cbmV4cG9ydCB2YXIgUk9PVF9DT05URVhUID0gbmV3IEJhc2VDb250ZXh0KCk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb250ZXh0LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/context/context.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/diag-api.js":
/*!***************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/diag-api.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   diag: () => (/* binding */ diag)\n/* harmony export */ });\n/* harmony import */ var _api_diag__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api/diag */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/api/diag.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Split module-level variable definition into separate files to allow\n// tree-shaking on each api instance.\n\n/**\n * Entrypoint for Diag API.\n * Defines Diagnostic handler used for internal diagnostic logging operations.\n * The default provides a Noop DiagLogger implementation which may be changed via the\n * diag.setLogger(logger: DiagLogger) function.\n */\nvar diag = _api_diag__WEBPACK_IMPORTED_MODULE_0__.DiagAPI.instance();\n//# sourceMappingURL=diag-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvYXBpL2J1aWxkL2VzbS9kaWFnLWFwaS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDcUM7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ08sV0FBVyw4Q0FBTztBQUN6QiIsInNvdXJjZXMiOlsid2VicGFjazovL2tvZGV4Z3VhcmQvLi9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvYXBpL2J1aWxkL2VzbS9kaWFnLWFwaS5qcz9mY2U5Il0sInNvdXJjZXNDb250ZW50IjpbIi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG4vLyBTcGxpdCBtb2R1bGUtbGV2ZWwgdmFyaWFibGUgZGVmaW5pdGlvbiBpbnRvIHNlcGFyYXRlIGZpbGVzIHRvIGFsbG93XG4vLyB0cmVlLXNoYWtpbmcgb24gZWFjaCBhcGkgaW5zdGFuY2UuXG5pbXBvcnQgeyBEaWFnQVBJIH0gZnJvbSAnLi9hcGkvZGlhZyc7XG4vKipcbiAqIEVudHJ5cG9pbnQgZm9yIERpYWcgQVBJLlxuICogRGVmaW5lcyBEaWFnbm9zdGljIGhhbmRsZXIgdXNlZCBmb3IgaW50ZXJuYWwgZGlhZ25vc3RpYyBsb2dnaW5nIG9wZXJhdGlvbnMuXG4gKiBUaGUgZGVmYXVsdCBwcm92aWRlcyBhIE5vb3AgRGlhZ0xvZ2dlciBpbXBsZW1lbnRhdGlvbiB3aGljaCBtYXkgYmUgY2hhbmdlZCB2aWEgdGhlXG4gKiBkaWFnLnNldExvZ2dlcihsb2dnZXI6IERpYWdMb2dnZXIpIGZ1bmN0aW9uLlxuICovXG5leHBvcnQgdmFyIGRpYWcgPSBEaWFnQVBJLmluc3RhbmNlKCk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1kaWFnLWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/diag-api.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/diag/ComponentLogger.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/diag/ComponentLogger.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DiagComponentLogger: () => (/* binding */ DiagComponentLogger)\n/* harmony export */ });\n/* harmony import */ var _internal_global_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../internal/global-utils */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/internal/global-utils.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __read = (undefined && undefined.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\n\n/**\n * Component Logger which is meant to be used as part of any component which\n * will add automatically additional namespace in front of the log message.\n * It will then forward all message to global diag logger\n * @example\n * const cLogger = diag.createComponentLogger({ namespace: '@opentelemetry/instrumentation-http' });\n * cLogger.debug('test');\n * // @opentelemetry/instrumentation-http test\n */\nvar DiagComponentLogger = /** @class */ (function () {\n    function DiagComponentLogger(props) {\n        this._namespace = props.namespace || 'DiagComponentLogger';\n    }\n    DiagComponentLogger.prototype.debug = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return logProxy('debug', this._namespace, args);\n    };\n    DiagComponentLogger.prototype.error = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return logProxy('error', this._namespace, args);\n    };\n    DiagComponentLogger.prototype.info = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return logProxy('info', this._namespace, args);\n    };\n    DiagComponentLogger.prototype.warn = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return logProxy('warn', this._namespace, args);\n    };\n    DiagComponentLogger.prototype.verbose = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return logProxy('verbose', this._namespace, args);\n    };\n    return DiagComponentLogger;\n}());\n\nfunction logProxy(funcName, namespace, args) {\n    var logger = (0,_internal_global_utils__WEBPACK_IMPORTED_MODULE_0__.getGlobal)('diag');\n    // shortcut if logger not set\n    if (!logger) {\n        return;\n    }\n    args.unshift(namespace);\n    return logger[funcName].apply(logger, __spreadArray([], __read(args), false));\n}\n//# sourceMappingURL=ComponentLogger.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/diag/ComponentLogger.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/diag/consoleLogger.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/diag/consoleLogger.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DiagConsoleLogger: () => (/* binding */ DiagConsoleLogger)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar consoleMap = [\n    { n: 'error', c: 'error' },\n    { n: 'warn', c: 'warn' },\n    { n: 'info', c: 'info' },\n    { n: 'debug', c: 'debug' },\n    { n: 'verbose', c: 'trace' },\n];\n/**\n * A simple Immutable Console based diagnostic logger which will output any messages to the Console.\n * If you want to limit the amount of logging to a specific level or lower use the\n * {@link createLogLevelDiagLogger}\n */\nvar DiagConsoleLogger = /** @class */ (function () {\n    function DiagConsoleLogger() {\n        function _consoleFunc(funcName) {\n            return function () {\n                var args = [];\n                for (var _i = 0; _i < arguments.length; _i++) {\n                    args[_i] = arguments[_i];\n                }\n                if (console) {\n                    // Some environments only expose the console when the F12 developer console is open\n                    // eslint-disable-next-line no-console\n                    var theFunc = console[funcName];\n                    if (typeof theFunc !== 'function') {\n                        // Not all environments support all functions\n                        // eslint-disable-next-line no-console\n                        theFunc = console.log;\n                    }\n                    // One last final check\n                    if (typeof theFunc === 'function') {\n                        return theFunc.apply(console, args);\n                    }\n                }\n            };\n        }\n        for (var i = 0; i < consoleMap.length; i++) {\n            this[consoleMap[i].n] = _consoleFunc(consoleMap[i].c);\n        }\n    }\n    return DiagConsoleLogger;\n}());\n\n//# sourceMappingURL=consoleLogger.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/diag/consoleLogger.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/diag/internal/logLevelLogger.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/diag/internal/logLevelLogger.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createLogLevelDiagLogger: () => (/* binding */ createLogLevelDiagLogger)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/diag/types.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nfunction createLogLevelDiagLogger(maxLevel, logger) {\n    if (maxLevel < _types__WEBPACK_IMPORTED_MODULE_0__.DiagLogLevel.NONE) {\n        maxLevel = _types__WEBPACK_IMPORTED_MODULE_0__.DiagLogLevel.NONE;\n    }\n    else if (maxLevel > _types__WEBPACK_IMPORTED_MODULE_0__.DiagLogLevel.ALL) {\n        maxLevel = _types__WEBPACK_IMPORTED_MODULE_0__.DiagLogLevel.ALL;\n    }\n    // In case the logger is null or undefined\n    logger = logger || {};\n    function _filterFunc(funcName, theLevel) {\n        var theFunc = logger[funcName];\n        if (typeof theFunc === 'function' && maxLevel >= theLevel) {\n            return theFunc.bind(logger);\n        }\n        return function () { };\n    }\n    return {\n        error: _filterFunc('error', _types__WEBPACK_IMPORTED_MODULE_0__.DiagLogLevel.ERROR),\n        warn: _filterFunc('warn', _types__WEBPACK_IMPORTED_MODULE_0__.DiagLogLevel.WARN),\n        info: _filterFunc('info', _types__WEBPACK_IMPORTED_MODULE_0__.DiagLogLevel.INFO),\n        debug: _filterFunc('debug', _types__WEBPACK_IMPORTED_MODULE_0__.DiagLogLevel.DEBUG),\n        verbose: _filterFunc('verbose', _types__WEBPACK_IMPORTED_MODULE_0__.DiagLogLevel.VERBOSE),\n    };\n}\n//# sourceMappingURL=logLevelLogger.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/diag/internal/logLevelLogger.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/diag/types.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/diag/types.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DiagLogLevel: () => (/* binding */ DiagLogLevel)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Defines the available internal logging levels for the diagnostic logger, the numeric values\n * of the levels are defined to match the original values from the initial LogLevel to avoid\n * compatibility/migration issues for any implementation that assume the numeric ordering.\n */\nvar DiagLogLevel;\n(function (DiagLogLevel) {\n    /** Diagnostic Logging level setting to disable all logging (except and forced logs) */\n    DiagLogLevel[DiagLogLevel[\"NONE\"] = 0] = \"NONE\";\n    /** Identifies an error scenario */\n    DiagLogLevel[DiagLogLevel[\"ERROR\"] = 30] = \"ERROR\";\n    /** Identifies a warning scenario */\n    DiagLogLevel[DiagLogLevel[\"WARN\"] = 50] = \"WARN\";\n    /** General informational log message */\n    DiagLogLevel[DiagLogLevel[\"INFO\"] = 60] = \"INFO\";\n    /** General debug log message */\n    DiagLogLevel[DiagLogLevel[\"DEBUG\"] = 70] = \"DEBUG\";\n    /**\n     * Detailed trace level logging should only be used for development, should only be set\n     * in a development environment.\n     */\n    DiagLogLevel[DiagLogLevel[\"VERBOSE\"] = 80] = \"VERBOSE\";\n    /** Used to set the logging level to include all logging */\n    DiagLogLevel[DiagLogLevel[\"ALL\"] = 9999] = \"ALL\";\n})(DiagLogLevel || (DiagLogLevel = {}));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/diag/types.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/index.js":
/*!************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DiagConsoleLogger: () => (/* reexport safe */ _diag_consoleLogger__WEBPACK_IMPORTED_MODULE_2__.DiagConsoleLogger),\n/* harmony export */   DiagLogLevel: () => (/* reexport safe */ _diag_types__WEBPACK_IMPORTED_MODULE_3__.DiagLogLevel),\n/* harmony export */   INVALID_SPANID: () => (/* reexport safe */ _trace_invalid_span_constants__WEBPACK_IMPORTED_MODULE_15__.INVALID_SPANID),\n/* harmony export */   INVALID_SPAN_CONTEXT: () => (/* reexport safe */ _trace_invalid_span_constants__WEBPACK_IMPORTED_MODULE_15__.INVALID_SPAN_CONTEXT),\n/* harmony export */   INVALID_TRACEID: () => (/* reexport safe */ _trace_invalid_span_constants__WEBPACK_IMPORTED_MODULE_15__.INVALID_TRACEID),\n/* harmony export */   ProxyTracer: () => (/* reexport safe */ _trace_ProxyTracer__WEBPACK_IMPORTED_MODULE_7__.ProxyTracer),\n/* harmony export */   ProxyTracerProvider: () => (/* reexport safe */ _trace_ProxyTracerProvider__WEBPACK_IMPORTED_MODULE_8__.ProxyTracerProvider),\n/* harmony export */   ROOT_CONTEXT: () => (/* reexport safe */ _context_context__WEBPACK_IMPORTED_MODULE_1__.ROOT_CONTEXT),\n/* harmony export */   SamplingDecision: () => (/* reexport safe */ _trace_SamplingResult__WEBPACK_IMPORTED_MODULE_9__.SamplingDecision),\n/* harmony export */   SpanKind: () => (/* reexport safe */ _trace_span_kind__WEBPACK_IMPORTED_MODULE_10__.SpanKind),\n/* harmony export */   SpanStatusCode: () => (/* reexport safe */ _trace_status__WEBPACK_IMPORTED_MODULE_11__.SpanStatusCode),\n/* harmony export */   TraceFlags: () => (/* reexport safe */ _trace_trace_flags__WEBPACK_IMPORTED_MODULE_12__.TraceFlags),\n/* harmony export */   ValueType: () => (/* reexport safe */ _metrics_Metric__WEBPACK_IMPORTED_MODULE_5__.ValueType),\n/* harmony export */   baggageEntryMetadataFromString: () => (/* reexport safe */ _baggage_utils__WEBPACK_IMPORTED_MODULE_0__.baggageEntryMetadataFromString),\n/* harmony export */   context: () => (/* reexport safe */ _context_api__WEBPACK_IMPORTED_MODULE_16__.context),\n/* harmony export */   createContextKey: () => (/* reexport safe */ _context_context__WEBPACK_IMPORTED_MODULE_1__.createContextKey),\n/* harmony export */   createNoopMeter: () => (/* reexport safe */ _metrics_NoopMeter__WEBPACK_IMPORTED_MODULE_4__.createNoopMeter),\n/* harmony export */   createTraceState: () => (/* reexport safe */ _trace_internal_utils__WEBPACK_IMPORTED_MODULE_13__.createTraceState),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   defaultTextMapGetter: () => (/* reexport safe */ _propagation_TextMapPropagator__WEBPACK_IMPORTED_MODULE_6__.defaultTextMapGetter),\n/* harmony export */   defaultTextMapSetter: () => (/* reexport safe */ _propagation_TextMapPropagator__WEBPACK_IMPORTED_MODULE_6__.defaultTextMapSetter),\n/* harmony export */   diag: () => (/* reexport safe */ _diag_api__WEBPACK_IMPORTED_MODULE_17__.diag),\n/* harmony export */   isSpanContextValid: () => (/* reexport safe */ _trace_spancontext_utils__WEBPACK_IMPORTED_MODULE_14__.isSpanContextValid),\n/* harmony export */   isValidSpanId: () => (/* reexport safe */ _trace_spancontext_utils__WEBPACK_IMPORTED_MODULE_14__.isValidSpanId),\n/* harmony export */   isValidTraceId: () => (/* reexport safe */ _trace_spancontext_utils__WEBPACK_IMPORTED_MODULE_14__.isValidTraceId),\n/* harmony export */   metrics: () => (/* reexport safe */ _metrics_api__WEBPACK_IMPORTED_MODULE_18__.metrics),\n/* harmony export */   propagation: () => (/* reexport safe */ _propagation_api__WEBPACK_IMPORTED_MODULE_19__.propagation),\n/* harmony export */   trace: () => (/* reexport safe */ _trace_api__WEBPACK_IMPORTED_MODULE_20__.trace)\n/* harmony export */ });\n/* harmony import */ var _baggage_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./baggage/utils */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/baggage/utils.js\");\n/* harmony import */ var _context_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./context/context */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/context/context.js\");\n/* harmony import */ var _diag_consoleLogger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./diag/consoleLogger */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/diag/consoleLogger.js\");\n/* harmony import */ var _diag_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./diag/types */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/diag/types.js\");\n/* harmony import */ var _metrics_NoopMeter__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./metrics/NoopMeter */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/metrics/NoopMeter.js\");\n/* harmony import */ var _metrics_Metric__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./metrics/Metric */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/metrics/Metric.js\");\n/* harmony import */ var _propagation_TextMapPropagator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./propagation/TextMapPropagator */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/propagation/TextMapPropagator.js\");\n/* harmony import */ var _trace_ProxyTracer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./trace/ProxyTracer */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/ProxyTracer.js\");\n/* harmony import */ var _trace_ProxyTracerProvider__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./trace/ProxyTracerProvider */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/ProxyTracerProvider.js\");\n/* harmony import */ var _trace_SamplingResult__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./trace/SamplingResult */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/SamplingResult.js\");\n/* harmony import */ var _trace_span_kind__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./trace/span_kind */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/span_kind.js\");\n/* harmony import */ var _trace_status__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./trace/status */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/status.js\");\n/* harmony import */ var _trace_trace_flags__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./trace/trace_flags */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/trace_flags.js\");\n/* harmony import */ var _trace_internal_utils__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./trace/internal/utils */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/internal/utils.js\");\n/* harmony import */ var _trace_spancontext_utils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./trace/spancontext-utils */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/spancontext-utils.js\");\n/* harmony import */ var _trace_invalid_span_constants__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./trace/invalid-span-constants */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/invalid-span-constants.js\");\n/* harmony import */ var _context_api__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./context-api */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/context-api.js\");\n/* harmony import */ var _diag_api__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./diag-api */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/diag-api.js\");\n/* harmony import */ var _metrics_api__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./metrics-api */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/metrics-api.js\");\n/* harmony import */ var _propagation_api__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./propagation-api */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/propagation-api.js\");\n/* harmony import */ var _trace_api__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./trace-api */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/trace-api.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// Context APIs\n\n// Diag APIs\n\n\n// Metrics APIs\n\n\n// Propagation APIs\n\n\n\n\n\n\n\n\n\n\n// Split module-level variable definition into separate files to allow\n// tree-shaking on each api instance.\n\n\n\n\n\n// Named export.\n\n// Default export.\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    context: _context_api__WEBPACK_IMPORTED_MODULE_16__.context,\n    diag: _diag_api__WEBPACK_IMPORTED_MODULE_17__.diag,\n    metrics: _metrics_api__WEBPACK_IMPORTED_MODULE_18__.metrics,\n    propagation: _propagation_api__WEBPACK_IMPORTED_MODULE_19__.propagation,\n    trace: _trace_api__WEBPACK_IMPORTED_MODULE_20__.trace,\n});\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/internal/global-utils.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/internal/global-utils.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getGlobal: () => (/* binding */ getGlobal),\n/* harmony export */   registerGlobal: () => (/* binding */ registerGlobal),\n/* harmony export */   unregisterGlobal: () => (/* binding */ unregisterGlobal)\n/* harmony export */ });\n/* harmony import */ var _platform__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../platform */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/platform/node/globalThis.js\");\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../version */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/version.js\");\n/* harmony import */ var _semver__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./semver */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/internal/semver.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\n\nvar major = _version__WEBPACK_IMPORTED_MODULE_0__.VERSION.split('.')[0];\nvar GLOBAL_OPENTELEMETRY_API_KEY = Symbol.for(\"opentelemetry.js.api.\" + major);\nvar _global = _platform__WEBPACK_IMPORTED_MODULE_1__._globalThis;\nfunction registerGlobal(type, instance, diag, allowOverride) {\n    var _a;\n    if (allowOverride === void 0) { allowOverride = false; }\n    var api = (_global[GLOBAL_OPENTELEMETRY_API_KEY] = (_a = _global[GLOBAL_OPENTELEMETRY_API_KEY]) !== null && _a !== void 0 ? _a : {\n        version: _version__WEBPACK_IMPORTED_MODULE_0__.VERSION,\n    });\n    if (!allowOverride && api[type]) {\n        // already registered an API of this type\n        var err = new Error(\"@opentelemetry/api: Attempted duplicate registration of API: \" + type);\n        diag.error(err.stack || err.message);\n        return false;\n    }\n    if (api.version !== _version__WEBPACK_IMPORTED_MODULE_0__.VERSION) {\n        // All registered APIs must be of the same version exactly\n        var err = new Error(\"@opentelemetry/api: Registration of version v\" + api.version + \" for \" + type + \" does not match previously registered API v\" + _version__WEBPACK_IMPORTED_MODULE_0__.VERSION);\n        diag.error(err.stack || err.message);\n        return false;\n    }\n    api[type] = instance;\n    diag.debug(\"@opentelemetry/api: Registered a global for \" + type + \" v\" + _version__WEBPACK_IMPORTED_MODULE_0__.VERSION + \".\");\n    return true;\n}\nfunction getGlobal(type) {\n    var _a, _b;\n    var globalVersion = (_a = _global[GLOBAL_OPENTELEMETRY_API_KEY]) === null || _a === void 0 ? void 0 : _a.version;\n    if (!globalVersion || !(0,_semver__WEBPACK_IMPORTED_MODULE_2__.isCompatible)(globalVersion)) {\n        return;\n    }\n    return (_b = _global[GLOBAL_OPENTELEMETRY_API_KEY]) === null || _b === void 0 ? void 0 : _b[type];\n}\nfunction unregisterGlobal(type, diag) {\n    diag.debug(\"@opentelemetry/api: Unregistering a global for \" + type + \" v\" + _version__WEBPACK_IMPORTED_MODULE_0__.VERSION + \".\");\n    var api = _global[GLOBAL_OPENTELEMETRY_API_KEY];\n    if (api) {\n        delete api[type];\n    }\n}\n//# sourceMappingURL=global-utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/internal/global-utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/internal/semver.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/internal/semver.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _makeCompatibilityCheck: () => (/* binding */ _makeCompatibilityCheck),\n/* harmony export */   isCompatible: () => (/* binding */ isCompatible)\n/* harmony export */ });\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../version */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/version.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nvar re = /^(\\d+)\\.(\\d+)\\.(\\d+)(-(.+))?$/;\n/**\n * Create a function to test an API version to see if it is compatible with the provided ownVersion.\n *\n * The returned function has the following semantics:\n * - Exact match is always compatible\n * - Major versions must match exactly\n *    - 1.x package cannot use global 2.x package\n *    - 2.x package cannot use global 1.x package\n * - The minor version of the API module requesting access to the global API must be less than or equal to the minor version of this API\n *    - 1.3 package may use 1.4 global because the later global contains all functions 1.3 expects\n *    - 1.4 package may NOT use 1.3 global because it may try to call functions which don't exist on 1.3\n * - If the major version is 0, the minor version is treated as the major and the patch is treated as the minor\n * - Patch and build tag differences are not considered at this time\n *\n * @param ownVersion version which should be checked against\n */\nfunction _makeCompatibilityCheck(ownVersion) {\n    var acceptedVersions = new Set([ownVersion]);\n    var rejectedVersions = new Set();\n    var myVersionMatch = ownVersion.match(re);\n    if (!myVersionMatch) {\n        // we cannot guarantee compatibility so we always return noop\n        return function () { return false; };\n    }\n    var ownVersionParsed = {\n        major: +myVersionMatch[1],\n        minor: +myVersionMatch[2],\n        patch: +myVersionMatch[3],\n        prerelease: myVersionMatch[4],\n    };\n    // if ownVersion has a prerelease tag, versions must match exactly\n    if (ownVersionParsed.prerelease != null) {\n        return function isExactmatch(globalVersion) {\n            return globalVersion === ownVersion;\n        };\n    }\n    function _reject(v) {\n        rejectedVersions.add(v);\n        return false;\n    }\n    function _accept(v) {\n        acceptedVersions.add(v);\n        return true;\n    }\n    return function isCompatible(globalVersion) {\n        if (acceptedVersions.has(globalVersion)) {\n            return true;\n        }\n        if (rejectedVersions.has(globalVersion)) {\n            return false;\n        }\n        var globalVersionMatch = globalVersion.match(re);\n        if (!globalVersionMatch) {\n            // cannot parse other version\n            // we cannot guarantee compatibility so we always noop\n            return _reject(globalVersion);\n        }\n        var globalVersionParsed = {\n            major: +globalVersionMatch[1],\n            minor: +globalVersionMatch[2],\n            patch: +globalVersionMatch[3],\n            prerelease: globalVersionMatch[4],\n        };\n        // if globalVersion has a prerelease tag, versions must match exactly\n        if (globalVersionParsed.prerelease != null) {\n            return _reject(globalVersion);\n        }\n        // major versions must match\n        if (ownVersionParsed.major !== globalVersionParsed.major) {\n            return _reject(globalVersion);\n        }\n        if (ownVersionParsed.major === 0) {\n            if (ownVersionParsed.minor === globalVersionParsed.minor &&\n                ownVersionParsed.patch <= globalVersionParsed.patch) {\n                return _accept(globalVersion);\n            }\n            return _reject(globalVersion);\n        }\n        if (ownVersionParsed.minor <= globalVersionParsed.minor) {\n            return _accept(globalVersion);\n        }\n        return _reject(globalVersion);\n    };\n}\n/**\n * Test an API version to see if it is compatible with this API.\n *\n * - Exact match is always compatible\n * - Major versions must match exactly\n *    - 1.x package cannot use global 2.x package\n *    - 2.x package cannot use global 1.x package\n * - The minor version of the API module requesting access to the global API must be less than or equal to the minor version of this API\n *    - 1.3 package may use 1.4 global because the later global contains all functions 1.3 expects\n *    - 1.4 package may NOT use 1.3 global because it may try to call functions which don't exist on 1.3\n * - If the major version is 0, the minor version is treated as the major and the patch is treated as the minor\n * - Patch and build tag differences are not considered at this time\n *\n * @param version version of the API requesting an instance of the global API\n */\nvar isCompatible = _makeCompatibilityCheck(_version__WEBPACK_IMPORTED_MODULE_0__.VERSION);\n//# sourceMappingURL=semver.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/internal/semver.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/metrics-api.js":
/*!******************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/metrics-api.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   metrics: () => (/* binding */ metrics)\n/* harmony export */ });\n/* harmony import */ var _api_metrics__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api/metrics */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/api/metrics.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Split module-level variable definition into separate files to allow\n// tree-shaking on each api instance.\n\n/** Entrypoint for metrics API */\nvar metrics = _api_metrics__WEBPACK_IMPORTED_MODULE_0__.MetricsAPI.getInstance();\n//# sourceMappingURL=metrics-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvYXBpL2J1aWxkL2VzbS9tZXRyaWNzLWFwaS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDMkM7QUFDM0M7QUFDTyxjQUFjLG9EQUFVO0FBQy9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va29kZXhndWFyZC8uL25vZGVfbW9kdWxlcy9Ab3BlbnRlbGVtZXRyeS9hcGkvYnVpbGQvZXNtL21ldHJpY3MtYXBpLmpzPzljMzIiXSwic291cmNlc0NvbnRlbnQiOlsiLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbi8vIFNwbGl0IG1vZHVsZS1sZXZlbCB2YXJpYWJsZSBkZWZpbml0aW9uIGludG8gc2VwYXJhdGUgZmlsZXMgdG8gYWxsb3dcbi8vIHRyZWUtc2hha2luZyBvbiBlYWNoIGFwaSBpbnN0YW5jZS5cbmltcG9ydCB7IE1ldHJpY3NBUEkgfSBmcm9tICcuL2FwaS9tZXRyaWNzJztcbi8qKiBFbnRyeXBvaW50IGZvciBtZXRyaWNzIEFQSSAqL1xuZXhwb3J0IHZhciBtZXRyaWNzID0gTWV0cmljc0FQSS5nZXRJbnN0YW5jZSgpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bWV0cmljcy1hcGkuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/metrics-api.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/metrics/Metric.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/metrics/Metric.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ValueType: () => (/* binding */ ValueType)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/** The Type of value. It describes how the data is reported. */\nvar ValueType;\n(function (ValueType) {\n    ValueType[ValueType[\"INT\"] = 0] = \"INT\";\n    ValueType[ValueType[\"DOUBLE\"] = 1] = \"DOUBLE\";\n})(ValueType || (ValueType = {}));\n//# sourceMappingURL=Metric.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvYXBpL2J1aWxkL2VzbS9tZXRyaWNzL01ldHJpYy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBLENBQUMsOEJBQThCO0FBQy9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va29kZXhndWFyZC8uL25vZGVfbW9kdWxlcy9Ab3BlbnRlbGVtZXRyeS9hcGkvYnVpbGQvZXNtL21ldHJpY3MvTWV0cmljLmpzP2Q1ZGIiXSwic291cmNlc0NvbnRlbnQiOlsiLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbi8qKiBUaGUgVHlwZSBvZiB2YWx1ZS4gSXQgZGVzY3JpYmVzIGhvdyB0aGUgZGF0YSBpcyByZXBvcnRlZC4gKi9cbmV4cG9ydCB2YXIgVmFsdWVUeXBlO1xuKGZ1bmN0aW9uIChWYWx1ZVR5cGUpIHtcbiAgICBWYWx1ZVR5cGVbVmFsdWVUeXBlW1wiSU5UXCJdID0gMF0gPSBcIklOVFwiO1xuICAgIFZhbHVlVHlwZVtWYWx1ZVR5cGVbXCJET1VCTEVcIl0gPSAxXSA9IFwiRE9VQkxFXCI7XG59KShWYWx1ZVR5cGUgfHwgKFZhbHVlVHlwZSA9IHt9KSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1NZXRyaWMuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/metrics/Metric.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/metrics/NoopMeter.js":
/*!************************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/metrics/NoopMeter.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NOOP_COUNTER_METRIC: () => (/* binding */ NOOP_COUNTER_METRIC),\n/* harmony export */   NOOP_GAUGE_METRIC: () => (/* binding */ NOOP_GAUGE_METRIC),\n/* harmony export */   NOOP_HISTOGRAM_METRIC: () => (/* binding */ NOOP_HISTOGRAM_METRIC),\n/* harmony export */   NOOP_METER: () => (/* binding */ NOOP_METER),\n/* harmony export */   NOOP_OBSERVABLE_COUNTER_METRIC: () => (/* binding */ NOOP_OBSERVABLE_COUNTER_METRIC),\n/* harmony export */   NOOP_OBSERVABLE_GAUGE_METRIC: () => (/* binding */ NOOP_OBSERVABLE_GAUGE_METRIC),\n/* harmony export */   NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC: () => (/* binding */ NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC),\n/* harmony export */   NOOP_UP_DOWN_COUNTER_METRIC: () => (/* binding */ NOOP_UP_DOWN_COUNTER_METRIC),\n/* harmony export */   NoopCounterMetric: () => (/* binding */ NoopCounterMetric),\n/* harmony export */   NoopGaugeMetric: () => (/* binding */ NoopGaugeMetric),\n/* harmony export */   NoopHistogramMetric: () => (/* binding */ NoopHistogramMetric),\n/* harmony export */   NoopMeter: () => (/* binding */ NoopMeter),\n/* harmony export */   NoopMetric: () => (/* binding */ NoopMetric),\n/* harmony export */   NoopObservableCounterMetric: () => (/* binding */ NoopObservableCounterMetric),\n/* harmony export */   NoopObservableGaugeMetric: () => (/* binding */ NoopObservableGaugeMetric),\n/* harmony export */   NoopObservableMetric: () => (/* binding */ NoopObservableMetric),\n/* harmony export */   NoopObservableUpDownCounterMetric: () => (/* binding */ NoopObservableUpDownCounterMetric),\n/* harmony export */   NoopUpDownCounterMetric: () => (/* binding */ NoopUpDownCounterMetric),\n/* harmony export */   createNoopMeter: () => (/* binding */ createNoopMeter)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n/**\n * NoopMeter is a noop implementation of the {@link Meter} interface. It reuses\n * constant NoopMetrics for all of its methods.\n */\nvar NoopMeter = /** @class */ (function () {\n    function NoopMeter() {\n    }\n    /**\n     * @see {@link Meter.createGauge}\n     */\n    NoopMeter.prototype.createGauge = function (_name, _options) {\n        return NOOP_GAUGE_METRIC;\n    };\n    /**\n     * @see {@link Meter.createHistogram}\n     */\n    NoopMeter.prototype.createHistogram = function (_name, _options) {\n        return NOOP_HISTOGRAM_METRIC;\n    };\n    /**\n     * @see {@link Meter.createCounter}\n     */\n    NoopMeter.prototype.createCounter = function (_name, _options) {\n        return NOOP_COUNTER_METRIC;\n    };\n    /**\n     * @see {@link Meter.createUpDownCounter}\n     */\n    NoopMeter.prototype.createUpDownCounter = function (_name, _options) {\n        return NOOP_UP_DOWN_COUNTER_METRIC;\n    };\n    /**\n     * @see {@link Meter.createObservableGauge}\n     */\n    NoopMeter.prototype.createObservableGauge = function (_name, _options) {\n        return NOOP_OBSERVABLE_GAUGE_METRIC;\n    };\n    /**\n     * @see {@link Meter.createObservableCounter}\n     */\n    NoopMeter.prototype.createObservableCounter = function (_name, _options) {\n        return NOOP_OBSERVABLE_COUNTER_METRIC;\n    };\n    /**\n     * @see {@link Meter.createObservableUpDownCounter}\n     */\n    NoopMeter.prototype.createObservableUpDownCounter = function (_name, _options) {\n        return NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC;\n    };\n    /**\n     * @see {@link Meter.addBatchObservableCallback}\n     */\n    NoopMeter.prototype.addBatchObservableCallback = function (_callback, _observables) { };\n    /**\n     * @see {@link Meter.removeBatchObservableCallback}\n     */\n    NoopMeter.prototype.removeBatchObservableCallback = function (_callback) { };\n    return NoopMeter;\n}());\n\nvar NoopMetric = /** @class */ (function () {\n    function NoopMetric() {\n    }\n    return NoopMetric;\n}());\n\nvar NoopCounterMetric = /** @class */ (function (_super) {\n    __extends(NoopCounterMetric, _super);\n    function NoopCounterMetric() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    NoopCounterMetric.prototype.add = function (_value, _attributes) { };\n    return NoopCounterMetric;\n}(NoopMetric));\n\nvar NoopUpDownCounterMetric = /** @class */ (function (_super) {\n    __extends(NoopUpDownCounterMetric, _super);\n    function NoopUpDownCounterMetric() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    NoopUpDownCounterMetric.prototype.add = function (_value, _attributes) { };\n    return NoopUpDownCounterMetric;\n}(NoopMetric));\n\nvar NoopGaugeMetric = /** @class */ (function (_super) {\n    __extends(NoopGaugeMetric, _super);\n    function NoopGaugeMetric() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    NoopGaugeMetric.prototype.record = function (_value, _attributes) { };\n    return NoopGaugeMetric;\n}(NoopMetric));\n\nvar NoopHistogramMetric = /** @class */ (function (_super) {\n    __extends(NoopHistogramMetric, _super);\n    function NoopHistogramMetric() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    NoopHistogramMetric.prototype.record = function (_value, _attributes) { };\n    return NoopHistogramMetric;\n}(NoopMetric));\n\nvar NoopObservableMetric = /** @class */ (function () {\n    function NoopObservableMetric() {\n    }\n    NoopObservableMetric.prototype.addCallback = function (_callback) { };\n    NoopObservableMetric.prototype.removeCallback = function (_callback) { };\n    return NoopObservableMetric;\n}());\n\nvar NoopObservableCounterMetric = /** @class */ (function (_super) {\n    __extends(NoopObservableCounterMetric, _super);\n    function NoopObservableCounterMetric() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    return NoopObservableCounterMetric;\n}(NoopObservableMetric));\n\nvar NoopObservableGaugeMetric = /** @class */ (function (_super) {\n    __extends(NoopObservableGaugeMetric, _super);\n    function NoopObservableGaugeMetric() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    return NoopObservableGaugeMetric;\n}(NoopObservableMetric));\n\nvar NoopObservableUpDownCounterMetric = /** @class */ (function (_super) {\n    __extends(NoopObservableUpDownCounterMetric, _super);\n    function NoopObservableUpDownCounterMetric() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    return NoopObservableUpDownCounterMetric;\n}(NoopObservableMetric));\n\nvar NOOP_METER = new NoopMeter();\n// Synchronous instruments\nvar NOOP_COUNTER_METRIC = new NoopCounterMetric();\nvar NOOP_GAUGE_METRIC = new NoopGaugeMetric();\nvar NOOP_HISTOGRAM_METRIC = new NoopHistogramMetric();\nvar NOOP_UP_DOWN_COUNTER_METRIC = new NoopUpDownCounterMetric();\n// Asynchronous instruments\nvar NOOP_OBSERVABLE_COUNTER_METRIC = new NoopObservableCounterMetric();\nvar NOOP_OBSERVABLE_GAUGE_METRIC = new NoopObservableGaugeMetric();\nvar NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC = new NoopObservableUpDownCounterMetric();\n/**\n * Create a no-op Meter\n */\nfunction createNoopMeter() {\n    return NOOP_METER;\n}\n//# sourceMappingURL=NoopMeter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/metrics/NoopMeter.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/metrics/NoopMeterProvider.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/metrics/NoopMeterProvider.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NOOP_METER_PROVIDER: () => (/* binding */ NOOP_METER_PROVIDER),\n/* harmony export */   NoopMeterProvider: () => (/* binding */ NoopMeterProvider)\n/* harmony export */ });\n/* harmony import */ var _NoopMeter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./NoopMeter */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/metrics/NoopMeter.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * An implementation of the {@link MeterProvider} which returns an impotent Meter\n * for all calls to `getMeter`\n */\nvar NoopMeterProvider = /** @class */ (function () {\n    function NoopMeterProvider() {\n    }\n    NoopMeterProvider.prototype.getMeter = function (_name, _version, _options) {\n        return _NoopMeter__WEBPACK_IMPORTED_MODULE_0__.NOOP_METER;\n    };\n    return NoopMeterProvider;\n}());\n\nvar NOOP_METER_PROVIDER = new NoopMeterProvider();\n//# sourceMappingURL=NoopMeterProvider.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/metrics/NoopMeterProvider.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/platform/node/globalThis.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/platform/node/globalThis.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _globalThis: () => (/* binding */ _globalThis)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nvar _globalThis = typeof globalThis === 'object' ? globalThis : global;\n//# sourceMappingURL=globalThis.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvYXBpL2J1aWxkL2VzbS9wbGF0Zm9ybS9ub2RlL2dsb2JhbFRoaXMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va29kZXhndWFyZC8uL25vZGVfbW9kdWxlcy9Ab3BlbnRlbGVtZXRyeS9hcGkvYnVpbGQvZXNtL3BsYXRmb3JtL25vZGUvZ2xvYmFsVGhpcy5qcz9kMzdlIl0sInNvdXJjZXNDb250ZW50IjpbIi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG4vKiogb25seSBnbG9iYWxzIHRoYXQgY29tbW9uIHRvIG5vZGUgYW5kIGJyb3dzZXJzIGFyZSBhbGxvd2VkICovXG4vLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm9kZS9uby11bnN1cHBvcnRlZC1mZWF0dXJlcy9lcy1idWlsdGluc1xuZXhwb3J0IHZhciBfZ2xvYmFsVGhpcyA9IHR5cGVvZiBnbG9iYWxUaGlzID09PSAnb2JqZWN0JyA/IGdsb2JhbFRoaXMgOiBnbG9iYWw7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1nbG9iYWxUaGlzLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/platform/node/globalThis.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/propagation-api.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/propagation-api.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   propagation: () => (/* binding */ propagation)\n/* harmony export */ });\n/* harmony import */ var _api_propagation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api/propagation */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/api/propagation.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Split module-level variable definition into separate files to allow\n// tree-shaking on each api instance.\n\n/** Entrypoint for propagation API */\nvar propagation = _api_propagation__WEBPACK_IMPORTED_MODULE_0__.PropagationAPI.getInstance();\n//# sourceMappingURL=propagation-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvYXBpL2J1aWxkL2VzbS9wcm9wYWdhdGlvbi1hcGkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ21EO0FBQ25EO0FBQ08sa0JBQWtCLDREQUFjO0FBQ3ZDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va29kZXhndWFyZC8uL25vZGVfbW9kdWxlcy9Ab3BlbnRlbGVtZXRyeS9hcGkvYnVpbGQvZXNtL3Byb3BhZ2F0aW9uLWFwaS5qcz85ODVhIl0sInNvdXJjZXNDb250ZW50IjpbIi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG4vLyBTcGxpdCBtb2R1bGUtbGV2ZWwgdmFyaWFibGUgZGVmaW5pdGlvbiBpbnRvIHNlcGFyYXRlIGZpbGVzIHRvIGFsbG93XG4vLyB0cmVlLXNoYWtpbmcgb24gZWFjaCBhcGkgaW5zdGFuY2UuXG5pbXBvcnQgeyBQcm9wYWdhdGlvbkFQSSB9IGZyb20gJy4vYXBpL3Byb3BhZ2F0aW9uJztcbi8qKiBFbnRyeXBvaW50IGZvciBwcm9wYWdhdGlvbiBBUEkgKi9cbmV4cG9ydCB2YXIgcHJvcGFnYXRpb24gPSBQcm9wYWdhdGlvbkFQSS5nZXRJbnN0YW5jZSgpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cHJvcGFnYXRpb24tYXBpLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/propagation-api.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/propagation/NoopTextMapPropagator.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/propagation/NoopTextMapPropagator.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NoopTextMapPropagator: () => (/* binding */ NoopTextMapPropagator)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * No-op implementations of {@link TextMapPropagator}.\n */\nvar NoopTextMapPropagator = /** @class */ (function () {\n    function NoopTextMapPropagator() {\n    }\n    /** Noop inject function does nothing */\n    NoopTextMapPropagator.prototype.inject = function (_context, _carrier) { };\n    /** Noop extract function does nothing and returns the input context */\n    NoopTextMapPropagator.prototype.extract = function (context, _carrier) {\n        return context;\n    };\n    NoopTextMapPropagator.prototype.fields = function () {\n        return [];\n    };\n    return NoopTextMapPropagator;\n}());\n\n//# sourceMappingURL=NoopTextMapPropagator.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/propagation/NoopTextMapPropagator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/propagation/TextMapPropagator.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/propagation/TextMapPropagator.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultTextMapGetter: () => (/* binding */ defaultTextMapGetter),\n/* harmony export */   defaultTextMapSetter: () => (/* binding */ defaultTextMapSetter)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar defaultTextMapGetter = {\n    get: function (carrier, key) {\n        if (carrier == null) {\n            return undefined;\n        }\n        return carrier[key];\n    },\n    keys: function (carrier) {\n        if (carrier == null) {\n            return [];\n        }\n        return Object.keys(carrier);\n    },\n};\nvar defaultTextMapSetter = {\n    set: function (carrier, key, value) {\n        if (carrier == null) {\n            return;\n        }\n        carrier[key] = value;\n    },\n};\n//# sourceMappingURL=TextMapPropagator.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/propagation/TextMapPropagator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/trace-api.js":
/*!****************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/trace-api.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   trace: () => (/* binding */ trace)\n/* harmony export */ });\n/* harmony import */ var _api_trace__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api/trace */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/api/trace.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Split module-level variable definition into separate files to allow\n// tree-shaking on each api instance.\n\n/** Entrypoint for trace API */\nvar trace = _api_trace__WEBPACK_IMPORTED_MODULE_0__.TraceAPI.getInstance();\n//# sourceMappingURL=trace-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvYXBpL2J1aWxkL2VzbS90cmFjZS1hcGkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ3VDO0FBQ3ZDO0FBQ08sWUFBWSxnREFBUTtBQUMzQiIsInNvdXJjZXMiOlsid2VicGFjazovL2tvZGV4Z3VhcmQvLi9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvYXBpL2J1aWxkL2VzbS90cmFjZS1hcGkuanM/ZDI2NiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuLy8gU3BsaXQgbW9kdWxlLWxldmVsIHZhcmlhYmxlIGRlZmluaXRpb24gaW50byBzZXBhcmF0ZSBmaWxlcyB0byBhbGxvd1xuLy8gdHJlZS1zaGFraW5nIG9uIGVhY2ggYXBpIGluc3RhbmNlLlxuaW1wb3J0IHsgVHJhY2VBUEkgfSBmcm9tICcuL2FwaS90cmFjZSc7XG4vKiogRW50cnlwb2ludCBmb3IgdHJhY2UgQVBJICovXG5leHBvcnQgdmFyIHRyYWNlID0gVHJhY2VBUEkuZ2V0SW5zdGFuY2UoKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXRyYWNlLWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/trace-api.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/NonRecordingSpan.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/trace/NonRecordingSpan.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NonRecordingSpan: () => (/* binding */ NonRecordingSpan)\n/* harmony export */ });\n/* harmony import */ var _invalid_span_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./invalid-span-constants */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/invalid-span-constants.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * The NonRecordingSpan is the default {@link Span} that is used when no Span\n * implementation is available. All operations are no-op including context\n * propagation.\n */\nvar NonRecordingSpan = /** @class */ (function () {\n    function NonRecordingSpan(_spanContext) {\n        if (_spanContext === void 0) { _spanContext = _invalid_span_constants__WEBPACK_IMPORTED_MODULE_0__.INVALID_SPAN_CONTEXT; }\n        this._spanContext = _spanContext;\n    }\n    // Returns a SpanContext.\n    NonRecordingSpan.prototype.spanContext = function () {\n        return this._spanContext;\n    };\n    // By default does nothing\n    NonRecordingSpan.prototype.setAttribute = function (_key, _value) {\n        return this;\n    };\n    // By default does nothing\n    NonRecordingSpan.prototype.setAttributes = function (_attributes) {\n        return this;\n    };\n    // By default does nothing\n    NonRecordingSpan.prototype.addEvent = function (_name, _attributes) {\n        return this;\n    };\n    NonRecordingSpan.prototype.addLink = function (_link) {\n        return this;\n    };\n    NonRecordingSpan.prototype.addLinks = function (_links) {\n        return this;\n    };\n    // By default does nothing\n    NonRecordingSpan.prototype.setStatus = function (_status) {\n        return this;\n    };\n    // By default does nothing\n    NonRecordingSpan.prototype.updateName = function (_name) {\n        return this;\n    };\n    // By default does nothing\n    NonRecordingSpan.prototype.end = function (_endTime) { };\n    // isRecording always returns false for NonRecordingSpan.\n    NonRecordingSpan.prototype.isRecording = function () {\n        return false;\n    };\n    // By default does nothing\n    NonRecordingSpan.prototype.recordException = function (_exception, _time) { };\n    return NonRecordingSpan;\n}());\n\n//# sourceMappingURL=NonRecordingSpan.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/NonRecordingSpan.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/NoopTracer.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/trace/NoopTracer.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NoopTracer: () => (/* binding */ NoopTracer)\n/* harmony export */ });\n/* harmony import */ var _api_context__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../api/context */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/api/context.js\");\n/* harmony import */ var _trace_context_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../trace/context-utils */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/context-utils.js\");\n/* harmony import */ var _NonRecordingSpan__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./NonRecordingSpan */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/NonRecordingSpan.js\");\n/* harmony import */ var _spancontext_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./spancontext-utils */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/spancontext-utils.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\n\n\nvar contextApi = _api_context__WEBPACK_IMPORTED_MODULE_0__.ContextAPI.getInstance();\n/**\n * No-op implementations of {@link Tracer}.\n */\nvar NoopTracer = /** @class */ (function () {\n    function NoopTracer() {\n    }\n    // startSpan starts a noop span.\n    NoopTracer.prototype.startSpan = function (name, options, context) {\n        if (context === void 0) { context = contextApi.active(); }\n        var root = Boolean(options === null || options === void 0 ? void 0 : options.root);\n        if (root) {\n            return new _NonRecordingSpan__WEBPACK_IMPORTED_MODULE_1__.NonRecordingSpan();\n        }\n        var parentFromContext = context && (0,_trace_context_utils__WEBPACK_IMPORTED_MODULE_2__.getSpanContext)(context);\n        if (isSpanContext(parentFromContext) &&\n            (0,_spancontext_utils__WEBPACK_IMPORTED_MODULE_3__.isSpanContextValid)(parentFromContext)) {\n            return new _NonRecordingSpan__WEBPACK_IMPORTED_MODULE_1__.NonRecordingSpan(parentFromContext);\n        }\n        else {\n            return new _NonRecordingSpan__WEBPACK_IMPORTED_MODULE_1__.NonRecordingSpan();\n        }\n    };\n    NoopTracer.prototype.startActiveSpan = function (name, arg2, arg3, arg4) {\n        var opts;\n        var ctx;\n        var fn;\n        if (arguments.length < 2) {\n            return;\n        }\n        else if (arguments.length === 2) {\n            fn = arg2;\n        }\n        else if (arguments.length === 3) {\n            opts = arg2;\n            fn = arg3;\n        }\n        else {\n            opts = arg2;\n            ctx = arg3;\n            fn = arg4;\n        }\n        var parentContext = ctx !== null && ctx !== void 0 ? ctx : contextApi.active();\n        var span = this.startSpan(name, opts, parentContext);\n        var contextWithSpanSet = (0,_trace_context_utils__WEBPACK_IMPORTED_MODULE_2__.setSpan)(parentContext, span);\n        return contextApi.with(contextWithSpanSet, fn, undefined, span);\n    };\n    return NoopTracer;\n}());\n\nfunction isSpanContext(spanContext) {\n    return (typeof spanContext === 'object' &&\n        typeof spanContext['spanId'] === 'string' &&\n        typeof spanContext['traceId'] === 'string' &&\n        typeof spanContext['traceFlags'] === 'number');\n}\n//# sourceMappingURL=NoopTracer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/NoopTracer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/NoopTracerProvider.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/trace/NoopTracerProvider.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NoopTracerProvider: () => (/* binding */ NoopTracerProvider)\n/* harmony export */ });\n/* harmony import */ var _NoopTracer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./NoopTracer */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/NoopTracer.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * An implementation of the {@link TracerProvider} which returns an impotent\n * Tracer for all calls to `getTracer`.\n *\n * All operations are no-op.\n */\nvar NoopTracerProvider = /** @class */ (function () {\n    function NoopTracerProvider() {\n    }\n    NoopTracerProvider.prototype.getTracer = function (_name, _version, _options) {\n        return new _NoopTracer__WEBPACK_IMPORTED_MODULE_0__.NoopTracer();\n    };\n    return NoopTracerProvider;\n}());\n\n//# sourceMappingURL=NoopTracerProvider.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/NoopTracerProvider.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/ProxyTracer.js":
/*!************************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/trace/ProxyTracer.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProxyTracer: () => (/* binding */ ProxyTracer)\n/* harmony export */ });\n/* harmony import */ var _NoopTracer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./NoopTracer */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/NoopTracer.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nvar NOOP_TRACER = new _NoopTracer__WEBPACK_IMPORTED_MODULE_0__.NoopTracer();\n/**\n * Proxy tracer provided by the proxy tracer provider\n */\nvar ProxyTracer = /** @class */ (function () {\n    function ProxyTracer(_provider, name, version, options) {\n        this._provider = _provider;\n        this.name = name;\n        this.version = version;\n        this.options = options;\n    }\n    ProxyTracer.prototype.startSpan = function (name, options, context) {\n        return this._getTracer().startSpan(name, options, context);\n    };\n    ProxyTracer.prototype.startActiveSpan = function (_name, _options, _context, _fn) {\n        var tracer = this._getTracer();\n        return Reflect.apply(tracer.startActiveSpan, tracer, arguments);\n    };\n    /**\n     * Try to get a tracer from the proxy tracer provider.\n     * If the proxy tracer provider has no delegate, return a noop tracer.\n     */\n    ProxyTracer.prototype._getTracer = function () {\n        if (this._delegate) {\n            return this._delegate;\n        }\n        var tracer = this._provider.getDelegateTracer(this.name, this.version, this.options);\n        if (!tracer) {\n            return NOOP_TRACER;\n        }\n        this._delegate = tracer;\n        return this._delegate;\n    };\n    return ProxyTracer;\n}());\n\n//# sourceMappingURL=ProxyTracer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/ProxyTracer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/ProxyTracerProvider.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/trace/ProxyTracerProvider.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProxyTracerProvider: () => (/* binding */ ProxyTracerProvider)\n/* harmony export */ });\n/* harmony import */ var _ProxyTracer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ProxyTracer */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/ProxyTracer.js\");\n/* harmony import */ var _NoopTracerProvider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./NoopTracerProvider */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/NoopTracerProvider.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\nvar NOOP_TRACER_PROVIDER = new _NoopTracerProvider__WEBPACK_IMPORTED_MODULE_0__.NoopTracerProvider();\n/**\n * Tracer provider which provides {@link ProxyTracer}s.\n *\n * Before a delegate is set, tracers provided are NoOp.\n *   When a delegate is set, traces are provided from the delegate.\n *   When a delegate is set after tracers have already been provided,\n *   all tracers already provided will use the provided delegate implementation.\n */\nvar ProxyTracerProvider = /** @class */ (function () {\n    function ProxyTracerProvider() {\n    }\n    /**\n     * Get a {@link ProxyTracer}\n     */\n    ProxyTracerProvider.prototype.getTracer = function (name, version, options) {\n        var _a;\n        return ((_a = this.getDelegateTracer(name, version, options)) !== null && _a !== void 0 ? _a : new _ProxyTracer__WEBPACK_IMPORTED_MODULE_1__.ProxyTracer(this, name, version, options));\n    };\n    ProxyTracerProvider.prototype.getDelegate = function () {\n        var _a;\n        return (_a = this._delegate) !== null && _a !== void 0 ? _a : NOOP_TRACER_PROVIDER;\n    };\n    /**\n     * Set the delegate tracer provider\n     */\n    ProxyTracerProvider.prototype.setDelegate = function (delegate) {\n        this._delegate = delegate;\n    };\n    ProxyTracerProvider.prototype.getDelegateTracer = function (name, version, options) {\n        var _a;\n        return (_a = this._delegate) === null || _a === void 0 ? void 0 : _a.getTracer(name, version, options);\n    };\n    return ProxyTracerProvider;\n}());\n\n//# sourceMappingURL=ProxyTracerProvider.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/ProxyTracerProvider.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/SamplingResult.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/trace/SamplingResult.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SamplingDecision: () => (/* binding */ SamplingDecision)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @deprecated use the one declared in @opentelemetry/sdk-trace-base instead.\n * A sampling decision that determines how a {@link Span} will be recorded\n * and collected.\n */\nvar SamplingDecision;\n(function (SamplingDecision) {\n    /**\n     * `Span.isRecording() === false`, span will not be recorded and all events\n     * and attributes will be dropped.\n     */\n    SamplingDecision[SamplingDecision[\"NOT_RECORD\"] = 0] = \"NOT_RECORD\";\n    /**\n     * `Span.isRecording() === true`, but `Sampled` flag in {@link TraceFlags}\n     * MUST NOT be set.\n     */\n    SamplingDecision[SamplingDecision[\"RECORD\"] = 1] = \"RECORD\";\n    /**\n     * `Span.isRecording() === true` AND `Sampled` flag in {@link TraceFlags}\n     * MUST be set.\n     */\n    SamplingDecision[SamplingDecision[\"RECORD_AND_SAMPLED\"] = 2] = \"RECORD_AND_SAMPLED\";\n})(SamplingDecision || (SamplingDecision = {}));\n//# sourceMappingURL=SamplingResult.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/SamplingResult.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/context-utils.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/trace/context-utils.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deleteSpan: () => (/* binding */ deleteSpan),\n/* harmony export */   getActiveSpan: () => (/* binding */ getActiveSpan),\n/* harmony export */   getSpan: () => (/* binding */ getSpan),\n/* harmony export */   getSpanContext: () => (/* binding */ getSpanContext),\n/* harmony export */   setSpan: () => (/* binding */ setSpan),\n/* harmony export */   setSpanContext: () => (/* binding */ setSpanContext)\n/* harmony export */ });\n/* harmony import */ var _context_context__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../context/context */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/context/context.js\");\n/* harmony import */ var _NonRecordingSpan__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./NonRecordingSpan */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/NonRecordingSpan.js\");\n/* harmony import */ var _api_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../api/context */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/api/context.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\n\n/**\n * span key\n */\nvar SPAN_KEY = (0,_context_context__WEBPACK_IMPORTED_MODULE_0__.createContextKey)('OpenTelemetry Context Key SPAN');\n/**\n * Return the span if one exists\n *\n * @param context context to get span from\n */\nfunction getSpan(context) {\n    return context.getValue(SPAN_KEY) || undefined;\n}\n/**\n * Gets the span from the current context, if one exists.\n */\nfunction getActiveSpan() {\n    return getSpan(_api_context__WEBPACK_IMPORTED_MODULE_1__.ContextAPI.getInstance().active());\n}\n/**\n * Set the span on a context\n *\n * @param context context to use as parent\n * @param span span to set active\n */\nfunction setSpan(context, span) {\n    return context.setValue(SPAN_KEY, span);\n}\n/**\n * Remove current span stored in the context\n *\n * @param context context to delete span from\n */\nfunction deleteSpan(context) {\n    return context.deleteValue(SPAN_KEY);\n}\n/**\n * Wrap span context in a NoopSpan and set as span in a new\n * context\n *\n * @param context context to set active span on\n * @param spanContext span context to be wrapped\n */\nfunction setSpanContext(context, spanContext) {\n    return setSpan(context, new _NonRecordingSpan__WEBPACK_IMPORTED_MODULE_2__.NonRecordingSpan(spanContext));\n}\n/**\n * Get the span context of the span if it exists.\n *\n * @param context context to get values from\n */\nfunction getSpanContext(context) {\n    var _a;\n    return (_a = getSpan(context)) === null || _a === void 0 ? void 0 : _a.spanContext();\n}\n//# sourceMappingURL=context-utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/context-utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/internal/tracestate-impl.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/trace/internal/tracestate-impl.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TraceStateImpl: () => (/* binding */ TraceStateImpl)\n/* harmony export */ });\n/* harmony import */ var _tracestate_validators__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./tracestate-validators */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/internal/tracestate-validators.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nvar MAX_TRACE_STATE_ITEMS = 32;\nvar MAX_TRACE_STATE_LEN = 512;\nvar LIST_MEMBERS_SEPARATOR = ',';\nvar LIST_MEMBER_KEY_VALUE_SPLITTER = '=';\n/**\n * TraceState must be a class and not a simple object type because of the spec\n * requirement (https://www.w3.org/TR/trace-context/#tracestate-field).\n *\n * Here is the list of allowed mutations:\n * - New key-value pair should be added into the beginning of the list\n * - The value of any key can be updated. Modified keys MUST be moved to the\n * beginning of the list.\n */\nvar TraceStateImpl = /** @class */ (function () {\n    function TraceStateImpl(rawTraceState) {\n        this._internalState = new Map();\n        if (rawTraceState)\n            this._parse(rawTraceState);\n    }\n    TraceStateImpl.prototype.set = function (key, value) {\n        // TODO: Benchmark the different approaches(map vs list) and\n        // use the faster one.\n        var traceState = this._clone();\n        if (traceState._internalState.has(key)) {\n            traceState._internalState.delete(key);\n        }\n        traceState._internalState.set(key, value);\n        return traceState;\n    };\n    TraceStateImpl.prototype.unset = function (key) {\n        var traceState = this._clone();\n        traceState._internalState.delete(key);\n        return traceState;\n    };\n    TraceStateImpl.prototype.get = function (key) {\n        return this._internalState.get(key);\n    };\n    TraceStateImpl.prototype.serialize = function () {\n        var _this = this;\n        return this._keys()\n            .reduce(function (agg, key) {\n            agg.push(key + LIST_MEMBER_KEY_VALUE_SPLITTER + _this.get(key));\n            return agg;\n        }, [])\n            .join(LIST_MEMBERS_SEPARATOR);\n    };\n    TraceStateImpl.prototype._parse = function (rawTraceState) {\n        if (rawTraceState.length > MAX_TRACE_STATE_LEN)\n            return;\n        this._internalState = rawTraceState\n            .split(LIST_MEMBERS_SEPARATOR)\n            .reverse() // Store in reverse so new keys (.set(...)) will be placed at the beginning\n            .reduce(function (agg, part) {\n            var listMember = part.trim(); // Optional Whitespace (OWS) handling\n            var i = listMember.indexOf(LIST_MEMBER_KEY_VALUE_SPLITTER);\n            if (i !== -1) {\n                var key = listMember.slice(0, i);\n                var value = listMember.slice(i + 1, part.length);\n                if ((0,_tracestate_validators__WEBPACK_IMPORTED_MODULE_0__.validateKey)(key) && (0,_tracestate_validators__WEBPACK_IMPORTED_MODULE_0__.validateValue)(value)) {\n                    agg.set(key, value);\n                }\n                else {\n                    // TODO: Consider to add warning log\n                }\n            }\n            return agg;\n        }, new Map());\n        // Because of the reverse() requirement, trunc must be done after map is created\n        if (this._internalState.size > MAX_TRACE_STATE_ITEMS) {\n            this._internalState = new Map(Array.from(this._internalState.entries())\n                .reverse() // Use reverse same as original tracestate parse chain\n                .slice(0, MAX_TRACE_STATE_ITEMS));\n        }\n    };\n    TraceStateImpl.prototype._keys = function () {\n        return Array.from(this._internalState.keys()).reverse();\n    };\n    TraceStateImpl.prototype._clone = function () {\n        var traceState = new TraceStateImpl();\n        traceState._internalState = new Map(this._internalState);\n        return traceState;\n    };\n    return TraceStateImpl;\n}());\n\n//# sourceMappingURL=tracestate-impl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/internal/tracestate-impl.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/internal/tracestate-validators.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/trace/internal/tracestate-validators.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   validateKey: () => (/* binding */ validateKey),\n/* harmony export */   validateValue: () => (/* binding */ validateValue)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar VALID_KEY_CHAR_RANGE = '[_0-9a-z-*/]';\nvar VALID_KEY = \"[a-z]\" + VALID_KEY_CHAR_RANGE + \"{0,255}\";\nvar VALID_VENDOR_KEY = \"[a-z0-9]\" + VALID_KEY_CHAR_RANGE + \"{0,240}@[a-z]\" + VALID_KEY_CHAR_RANGE + \"{0,13}\";\nvar VALID_KEY_REGEX = new RegExp(\"^(?:\" + VALID_KEY + \"|\" + VALID_VENDOR_KEY + \")$\");\nvar VALID_VALUE_BASE_REGEX = /^[ -~]{0,255}[!-~]$/;\nvar INVALID_VALUE_COMMA_EQUAL_REGEX = /,|=/;\n/**\n * Key is opaque string up to 256 characters printable. It MUST begin with a\n * lowercase letter, and can only contain lowercase letters a-z, digits 0-9,\n * underscores _, dashes -, asterisks *, and forward slashes /.\n * For multi-tenant vendor scenarios, an at sign (@) can be used to prefix the\n * vendor name. Vendors SHOULD set the tenant ID at the beginning of the key.\n * see https://www.w3.org/TR/trace-context/#key\n */\nfunction validateKey(key) {\n    return VALID_KEY_REGEX.test(key);\n}\n/**\n * Value is opaque string up to 256 characters printable ASCII RFC0020\n * characters (i.e., the range 0x20 to 0x7E) except comma , and =.\n */\nfunction validateValue(value) {\n    return (VALID_VALUE_BASE_REGEX.test(value) &&\n        !INVALID_VALUE_COMMA_EQUAL_REGEX.test(value));\n}\n//# sourceMappingURL=tracestate-validators.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/internal/tracestate-validators.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/internal/utils.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/trace/internal/utils.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTraceState: () => (/* binding */ createTraceState)\n/* harmony export */ });\n/* harmony import */ var _tracestate_impl__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./tracestate-impl */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/internal/tracestate-impl.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nfunction createTraceState(rawTraceState) {\n    return new _tracestate_impl__WEBPACK_IMPORTED_MODULE_0__.TraceStateImpl(rawTraceState);\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvYXBpL2J1aWxkL2VzbS90cmFjZS9pbnRlcm5hbC91dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNtRDtBQUM1QztBQUNQLGVBQWUsNERBQWM7QUFDN0I7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2tvZGV4Z3VhcmQvLi9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvYXBpL2J1aWxkL2VzbS90cmFjZS9pbnRlcm5hbC91dGlscy5qcz8wNWJkIl0sInNvdXJjZXNDb250ZW50IjpbIi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5pbXBvcnQgeyBUcmFjZVN0YXRlSW1wbCB9IGZyb20gJy4vdHJhY2VzdGF0ZS1pbXBsJztcbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVUcmFjZVN0YXRlKHJhd1RyYWNlU3RhdGUpIHtcbiAgICByZXR1cm4gbmV3IFRyYWNlU3RhdGVJbXBsKHJhd1RyYWNlU3RhdGUpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXRpbHMuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/internal/utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/invalid-span-constants.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/trace/invalid-span-constants.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   INVALID_SPANID: () => (/* binding */ INVALID_SPANID),\n/* harmony export */   INVALID_SPAN_CONTEXT: () => (/* binding */ INVALID_SPAN_CONTEXT),\n/* harmony export */   INVALID_TRACEID: () => (/* binding */ INVALID_TRACEID)\n/* harmony export */ });\n/* harmony import */ var _trace_flags__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./trace_flags */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/trace_flags.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nvar INVALID_SPANID = '0000000000000000';\nvar INVALID_TRACEID = '00000000000000000000000000000000';\nvar INVALID_SPAN_CONTEXT = {\n    traceId: INVALID_TRACEID,\n    spanId: INVALID_SPANID,\n    traceFlags: _trace_flags__WEBPACK_IMPORTED_MODULE_0__.TraceFlags.NONE,\n};\n//# sourceMappingURL=invalid-span-constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvYXBpL2J1aWxkL2VzbS90cmFjZS9pbnZhbGlkLXNwYW4tY29uc3RhbnRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDMkM7QUFDcEM7QUFDQTtBQUNBO0FBQ1A7QUFDQTtBQUNBLGdCQUFnQixvREFBVTtBQUMxQjtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va29kZXhndWFyZC8uL25vZGVfbW9kdWxlcy9Ab3BlbnRlbGVtZXRyeS9hcGkvYnVpbGQvZXNtL3RyYWNlL2ludmFsaWQtc3Bhbi1jb25zdGFudHMuanM/MmYzMSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuaW1wb3J0IHsgVHJhY2VGbGFncyB9IGZyb20gJy4vdHJhY2VfZmxhZ3MnO1xuZXhwb3J0IHZhciBJTlZBTElEX1NQQU5JRCA9ICcwMDAwMDAwMDAwMDAwMDAwJztcbmV4cG9ydCB2YXIgSU5WQUxJRF9UUkFDRUlEID0gJzAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwJztcbmV4cG9ydCB2YXIgSU5WQUxJRF9TUEFOX0NPTlRFWFQgPSB7XG4gICAgdHJhY2VJZDogSU5WQUxJRF9UUkFDRUlELFxuICAgIHNwYW5JZDogSU5WQUxJRF9TUEFOSUQsXG4gICAgdHJhY2VGbGFnczogVHJhY2VGbGFncy5OT05FLFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWludmFsaWQtc3Bhbi1jb25zdGFudHMuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/invalid-span-constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/span_kind.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/trace/span_kind.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpanKind: () => (/* binding */ SpanKind)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar SpanKind;\n(function (SpanKind) {\n    /** Default value. Indicates that the span is used internally. */\n    SpanKind[SpanKind[\"INTERNAL\"] = 0] = \"INTERNAL\";\n    /**\n     * Indicates that the span covers server-side handling of an RPC or other\n     * remote request.\n     */\n    SpanKind[SpanKind[\"SERVER\"] = 1] = \"SERVER\";\n    /**\n     * Indicates that the span covers the client-side wrapper around an RPC or\n     * other remote request.\n     */\n    SpanKind[SpanKind[\"CLIENT\"] = 2] = \"CLIENT\";\n    /**\n     * Indicates that the span describes producer sending a message to a\n     * broker. Unlike client and server, there is no direct critical path latency\n     * relationship between producer and consumer spans.\n     */\n    SpanKind[SpanKind[\"PRODUCER\"] = 3] = \"PRODUCER\";\n    /**\n     * Indicates that the span describes consumer receiving a message from a\n     * broker. Unlike client and server, there is no direct critical path latency\n     * relationship between producer and consumer spans.\n     */\n    SpanKind[SpanKind[\"CONSUMER\"] = 4] = \"CONSUMER\";\n})(SpanKind || (SpanKind = {}));\n//# sourceMappingURL=span_kind.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/span_kind.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/spancontext-utils.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/trace/spancontext-utils.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isSpanContextValid: () => (/* binding */ isSpanContextValid),\n/* harmony export */   isValidSpanId: () => (/* binding */ isValidSpanId),\n/* harmony export */   isValidTraceId: () => (/* binding */ isValidTraceId),\n/* harmony export */   wrapSpanContext: () => (/* binding */ wrapSpanContext)\n/* harmony export */ });\n/* harmony import */ var _invalid_span_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./invalid-span-constants */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/invalid-span-constants.js\");\n/* harmony import */ var _NonRecordingSpan__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./NonRecordingSpan */ \"(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/NonRecordingSpan.js\");\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\nvar VALID_TRACEID_REGEX = /^([0-9a-f]{32})$/i;\nvar VALID_SPANID_REGEX = /^[0-9a-f]{16}$/i;\nfunction isValidTraceId(traceId) {\n    return VALID_TRACEID_REGEX.test(traceId) && traceId !== _invalid_span_constants__WEBPACK_IMPORTED_MODULE_0__.INVALID_TRACEID;\n}\nfunction isValidSpanId(spanId) {\n    return VALID_SPANID_REGEX.test(spanId) && spanId !== _invalid_span_constants__WEBPACK_IMPORTED_MODULE_0__.INVALID_SPANID;\n}\n/**\n * Returns true if this {@link SpanContext} is valid.\n * @return true if this {@link SpanContext} is valid.\n */\nfunction isSpanContextValid(spanContext) {\n    return (isValidTraceId(spanContext.traceId) && isValidSpanId(spanContext.spanId));\n}\n/**\n * Wrap the given {@link SpanContext} in a new non-recording {@link Span}\n *\n * @param spanContext span context to be wrapped\n * @returns a new non-recording {@link Span} with the provided context\n */\nfunction wrapSpanContext(spanContext) {\n    return new _NonRecordingSpan__WEBPACK_IMPORTED_MODULE_1__.NonRecordingSpan(spanContext);\n}\n//# sourceMappingURL=spancontext-utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/spancontext-utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/status.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/trace/status.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpanStatusCode: () => (/* binding */ SpanStatusCode)\n/* harmony export */ });\n/**\n * An enumeration of status codes.\n */\nvar SpanStatusCode;\n(function (SpanStatusCode) {\n    /**\n     * The default status.\n     */\n    SpanStatusCode[SpanStatusCode[\"UNSET\"] = 0] = \"UNSET\";\n    /**\n     * The operation has been validated by an Application developer or\n     * Operator to have completed successfully.\n     */\n    SpanStatusCode[SpanStatusCode[\"OK\"] = 1] = \"OK\";\n    /**\n     * The operation contains an error.\n     */\n    SpanStatusCode[SpanStatusCode[\"ERROR\"] = 2] = \"ERROR\";\n})(SpanStatusCode || (SpanStatusCode = {}));\n//# sourceMappingURL=status.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvYXBpL2J1aWxkL2VzbS90cmFjZS9zdGF0dXMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsd0NBQXdDO0FBQ3pDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va29kZXhndWFyZC8uL25vZGVfbW9kdWxlcy9Ab3BlbnRlbGVtZXRyeS9hcGkvYnVpbGQvZXNtL3RyYWNlL3N0YXR1cy5qcz9iMjdiIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQW4gZW51bWVyYXRpb24gb2Ygc3RhdHVzIGNvZGVzLlxuICovXG5leHBvcnQgdmFyIFNwYW5TdGF0dXNDb2RlO1xuKGZ1bmN0aW9uIChTcGFuU3RhdHVzQ29kZSkge1xuICAgIC8qKlxuICAgICAqIFRoZSBkZWZhdWx0IHN0YXR1cy5cbiAgICAgKi9cbiAgICBTcGFuU3RhdHVzQ29kZVtTcGFuU3RhdHVzQ29kZVtcIlVOU0VUXCJdID0gMF0gPSBcIlVOU0VUXCI7XG4gICAgLyoqXG4gICAgICogVGhlIG9wZXJhdGlvbiBoYXMgYmVlbiB2YWxpZGF0ZWQgYnkgYW4gQXBwbGljYXRpb24gZGV2ZWxvcGVyIG9yXG4gICAgICogT3BlcmF0b3IgdG8gaGF2ZSBjb21wbGV0ZWQgc3VjY2Vzc2Z1bGx5LlxuICAgICAqL1xuICAgIFNwYW5TdGF0dXNDb2RlW1NwYW5TdGF0dXNDb2RlW1wiT0tcIl0gPSAxXSA9IFwiT0tcIjtcbiAgICAvKipcbiAgICAgKiBUaGUgb3BlcmF0aW9uIGNvbnRhaW5zIGFuIGVycm9yLlxuICAgICAqL1xuICAgIFNwYW5TdGF0dXNDb2RlW1NwYW5TdGF0dXNDb2RlW1wiRVJST1JcIl0gPSAyXSA9IFwiRVJST1JcIjtcbn0pKFNwYW5TdGF0dXNDb2RlIHx8IChTcGFuU3RhdHVzQ29kZSA9IHt9KSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zdGF0dXMuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/status.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/trace_flags.js":
/*!************************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/trace/trace_flags.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TraceFlags: () => (/* binding */ TraceFlags)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar TraceFlags;\n(function (TraceFlags) {\n    /** Represents no flag set. */\n    TraceFlags[TraceFlags[\"NONE\"] = 0] = \"NONE\";\n    /** Bit to represent whether trace is sampled in trace flags. */\n    TraceFlags[TraceFlags[\"SAMPLED\"] = 1] = \"SAMPLED\";\n})(TraceFlags || (TraceFlags = {}));\n//# sourceMappingURL=trace_flags.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvYXBpL2J1aWxkL2VzbS90cmFjZS90cmFjZV9mbGFncy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxnQ0FBZ0M7QUFDakMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb2RleGd1YXJkLy4vbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2FwaS9idWlsZC9lc20vdHJhY2UvdHJhY2VfZmxhZ3MuanM/ZTc0OCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuZXhwb3J0IHZhciBUcmFjZUZsYWdzO1xuKGZ1bmN0aW9uIChUcmFjZUZsYWdzKSB7XG4gICAgLyoqIFJlcHJlc2VudHMgbm8gZmxhZyBzZXQuICovXG4gICAgVHJhY2VGbGFnc1tUcmFjZUZsYWdzW1wiTk9ORVwiXSA9IDBdID0gXCJOT05FXCI7XG4gICAgLyoqIEJpdCB0byByZXByZXNlbnQgd2hldGhlciB0cmFjZSBpcyBzYW1wbGVkIGluIHRyYWNlIGZsYWdzLiAqL1xuICAgIFRyYWNlRmxhZ3NbVHJhY2VGbGFnc1tcIlNBTVBMRURcIl0gPSAxXSA9IFwiU0FNUExFRFwiO1xufSkoVHJhY2VGbGFncyB8fCAoVHJhY2VGbGFncyA9IHt9KSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10cmFjZV9mbGFncy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/trace/trace_flags.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/api/build/esm/version.js":
/*!**************************************************************!*\
  !*** ./node_modules/@opentelemetry/api/build/esm/version.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VERSION: () => (/* binding */ VERSION)\n/* harmony export */ });\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// this is autogenerated file, see scripts/version-update.js\nvar VERSION = '1.9.0';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvYXBpL2J1aWxkL2VzbS92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb2RleGd1YXJkLy4vbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2FwaS9idWlsZC9lc20vdmVyc2lvbi5qcz82NGIzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG4vLyB0aGlzIGlzIGF1dG9nZW5lcmF0ZWQgZmlsZSwgc2VlIHNjcmlwdHMvdmVyc2lvbi11cGRhdGUuanNcbmV4cG9ydCB2YXIgVkVSU0lPTiA9ICcxLjkuMCc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD12ZXJzaW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@opentelemetry/api/build/esm/version.js\n");

/***/ })

};
;