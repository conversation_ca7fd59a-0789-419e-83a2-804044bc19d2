"use strict";(()=>{var e={};e.id=742,e.ids=[742],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},80847:(e,t,a)=>{a.r(t),a.d(t,{originalPathname:()=>g,patchFetch:()=>h,requestAsyncStorage:()=>l,routeModule:()=>d,serverHooks:()=>m,staticGenerationAsyncStorage:()=>p});var s={};a.r(s),a.d(s,{GET:()=>c,POST:()=>u});var n=a(49303),i=a(88716),r=a(60670),o=a(87070);async function c(e){try{let t=e.headers.get("authorization"),a=t?.replace("Bearer ","");if(!a)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!a.startsWith("kxg_"))return o.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let s=[{id:"osint_template_001",name:"Person Investigation",description:"Comprehensive person lookup using multiple identity sources",category:"person",searchTypes:["email","phone","name","nik","npwp"],sources:["Dukcapil Database","Kemkes Database","Social Media","Email Breach DB","Phone Number DB"],settings:{deepSearch:!0,includeSocialMedia:!0,includeBreaches:!0,maxResults:50,confidenceThreshold:70},isDefault:!0,createdAt:new Date(Date.now()-2592e6).toISOString(),updatedAt:new Date(Date.now()-432e6).toISOString(),usageCount:234},{id:"osint_template_002",name:"Business Intelligence",description:"Corporate and business entity investigation template",category:"business",searchTypes:["domain","email","name"],sources:["Domain WHOIS","Social Media","GitHub","Email Breach DB"],settings:{deepSearch:!0,includeSocialMedia:!1,includeBreaches:!0,maxResults:100,confidenceThreshold:60},isDefault:!0,createdAt:new Date(Date.now()-3888e6).toISOString(),updatedAt:new Date(Date.now()-2592e5).toISOString(),usageCount:156},{id:"osint_template_003",name:"Domain Analysis",description:"Complete domain and infrastructure investigation",category:"domain",searchTypes:["domain","email"],sources:["Domain WHOIS","GitHub","Social Media"],settings:{deepSearch:!0,includeSocialMedia:!1,includeBreaches:!1,maxResults:25,confidenceThreshold:80},isDefault:!0,createdAt:new Date(Date.now()-1728e6).toISOString(),updatedAt:new Date(Date.now()-1728e5).toISOString(),usageCount:89},{id:"osint_template_004",name:"Device Tracking",description:"Mobile device and IMEI investigation template",category:"device",searchTypes:["imei","phone"],sources:["Phone Number DB","Location Services"],settings:{deepSearch:!1,includeSocialMedia:!1,includeBreaches:!1,maxResults:10,confidenceThreshold:90},isDefault:!0,createdAt:new Date(Date.now()-1296e6).toISOString(),updatedAt:new Date(Date.now()-864e5).toISOString(),usageCount:67},{id:"osint_template_005",name:"Quick Lookup",description:"Fast basic search for quick information gathering",category:"person",searchTypes:["email","phone"],sources:["Social Media","Email Breach DB"],settings:{deepSearch:!1,includeSocialMedia:!0,includeBreaches:!0,maxResults:15,confidenceThreshold:50},isDefault:!0,createdAt:new Date(Date.now()-5184e6).toISOString(),updatedAt:new Date(Date.now()-6048e5).toISOString(),usageCount:345},{id:"osint_template_006",name:"Social Media Deep Dive",description:"Comprehensive social media investigation template",category:"custom",searchTypes:["email","phone","name"],sources:["Social Media","GitHub"],settings:{deepSearch:!0,includeSocialMedia:!0,includeBreaches:!1,maxResults:75,confidenceThreshold:40},isDefault:!1,createdAt:new Date(Date.now()-864e6).toISOString(),updatedAt:new Date(Date.now()-864e5).toISOString(),usageCount:123}];return s.sort((e,t)=>t.usageCount-e.usageCount),o.NextResponse.json({success:!0,data:s,meta:{total:s.length,default:s.filter(e=>e.isDefault).length,custom:s.filter(e=>!e.isDefault).length,categories:{person:s.filter(e=>"person"===e.category).length,business:s.filter(e=>"business"===e.category).length,domain:s.filter(e=>"domain"===e.category).length,device:s.filter(e=>"device"===e.category).length,custom:s.filter(e=>"custom"===e.category).length}},timestamp:new Date().toISOString()})}catch(e){return console.error("OSINT templates API error:",e),o.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}async function u(e){try{let t=e.headers.get("authorization"),a=t?.replace("Bearer ","");if(!a)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!a.startsWith("kxg_"))return o.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let{name:s,description:n,category:i,searchTypes:r,sources:c,settings:u}=await e.json();if(!s||!n||!i||!r||!c)return o.NextResponse.json({success:!1,error:"Name, description, category, searchTypes, and sources are required"},{status:400});let d={id:`osint_template_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,name:s,description:n,category:i,searchTypes:r,sources:c,settings:u||{deepSearch:!1,includeSocialMedia:!0,includeBreaches:!0,maxResults:25,confidenceThreshold:60},isDefault:!1,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),usageCount:0};return o.NextResponse.json({success:!0,data:d,message:"OSINT template created successfully",timestamp:new Date().toISOString()})}catch(e){return console.error("Create OSINT template error:",e),o.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/osint/templates/route",pathname:"/api/osint/templates",filename:"route",bundlePath:"app/api/osint/templates/route"},resolvedPagePath:"D:\\Users\\Downloads\\kodeXGuard\\app\\api\\osint\\templates\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:l,staticGenerationAsyncStorage:p,serverHooks:m}=d,g="/api/osint/templates/route";function h(){return(0,r.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:p})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[216,592],()=>a(80847));module.exports=s})();