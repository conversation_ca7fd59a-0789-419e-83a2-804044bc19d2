(()=>{var e={};e.id=11,e.ids=[11],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},57226:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>u,tree:()=>d}),a(7203),a(30829),a(35866);var r=a(23191),t=a(88716),l=a(37922),n=a.n(l),i=a(95231),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);a.d(s,c);let d=["",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,7203)),"D:\\Users\\Downloads\\kodeXGuard\\app\\register\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,35866,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\Users\\Downloads\\kodeXGuard\\app\\register\\page.tsx"],m="/register/page",x={require:a,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},43495:(e,s,a)=>{Promise.resolve().then(a.bind(a,91472))},91472:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>h});var r=a(10326),t=a(17577),l=a(90434),n=a(35047),i=a(58038),c=a(79635),d=a(5932),o=a(9015),m=a(91216),x=a(12714),u=a(32933),p=a(2262);function h(){let e=(0,n.useRouter)(),[s,a]=(0,t.useState)({username:"",email:"",fullName:"",password:"",confirmPassword:"",plan:"free",agreeToTerms:!1}),[h,g]=(0,t.useState)(!1),[j,b]=(0,t.useState)(!1),[y,f]=(0,t.useState)(!1),[N,v]=(0,t.useState)(""),[w,P]=(0,t.useState)(""),k=(e=>{let s=e.length>=8,a=/[A-Z]/.test(e),r=/[a-z]/.test(e),t=/\d/.test(e),l=/[!@#$%^&*(),.?":{}|<>]/.test(e);return{minLength:s,hasUpper:a,hasLower:r,hasNumber:t,hasSpecial:l,isValid:s&&a&&r&&t&&l}})(s.password),S=async a=>{if(a.preventDefault(),f(!0),v(""),P(""),!k.isValid){v("Password does not meet requirements"),f(!1);return}if(s.password!==s.confirmPassword){v("Passwords do not match"),f(!1);return}if(!s.agreeToTerms){v("You must agree to the terms and conditions"),f(!1);return}try{let a=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:s.username,email:s.email,fullName:s.fullName,password:s.password,plan:s.plan})});if(a.ok)P("Account created successfully! Please check your email for verification."),setTimeout(()=>{e.push("/login")},3e3);else{let e=await a.json();v(e.message||"Registration failed")}}catch(e){v("Network error. Please try again.")}finally{f(!1)}},T=e=>{let{name:s,value:r,type:t}=e.target,l=e.target.checked;a(e=>({...e,[s]:"checkbox"===t?l:r}))};return(0,r.jsxs)("div",{className:"min-h-screen flex items-center justify-center px-4 py-12",children:[r.jsx("div",{className:"absolute inset-0 bg-gradient-cyber opacity-50"}),r.jsx("div",{className:"absolute inset-0 nusantara-pattern opacity-5"}),(0,r.jsxs)("div",{className:"relative z-10 w-full max-w-2xl",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsxs)(l.default,{href:"/",className:"inline-flex items-center space-x-2",children:[r.jsx(i.Z,{className:"h-12 w-12 text-cyber-green animate-pulse"}),(0,r.jsxs)("span",{className:"text-3xl font-bold font-cyber",children:[r.jsx("span",{className:"cyber-text",children:"Kode"}),r.jsx("span",{className:"nusantara-gold",children:"X"}),r.jsx("span",{className:"text-white",children:"Guard"})]})]}),r.jsx("p",{className:"mt-2 text-gray-400",children:"Bergabung dengan komunitas cybersecurity Indonesia"})]}),r.jsx(p.Zb,{border:"green",glow:!0,children:(0,r.jsxs)("div",{className:"p-8",children:[N&&r.jsx("div",{className:"mb-6",children:r.jsx(p.XO,{type:"error",title:"Registration Failed",message:N,onClose:()=>v("")})}),w&&r.jsx("div",{className:"mb-6",children:r.jsx(p.XO,{type:"success",title:"Registration Successful",message:w})}),(0,r.jsxs)("form",{onSubmit:S,className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-300 mb-2",children:"Username"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:r.jsx(c.Z,{className:"h-5 w-5 text-gray-400"})}),r.jsx("input",{id:"username",name:"username",type:"text",required:!0,value:s.username,onChange:T,className:"cyber-input pl-10 w-full",placeholder:"johndoe"})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"fullName",className:"block text-sm font-medium text-gray-300 mb-2",children:"Full Name"}),r.jsx("input",{id:"fullName",name:"fullName",type:"text",required:!0,value:s.fullName,onChange:T,className:"cyber-input w-full",placeholder:"John Doe"})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-300 mb-2",children:"Email Address"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:r.jsx(d.Z,{className:"h-5 w-5 text-gray-400"})}),r.jsx("input",{id:"email",name:"email",type:"email",required:!0,value:s.email,onChange:T,className:"cyber-input pl-10 w-full",placeholder:"<EMAIL>"})]})]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-300 mb-2",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:r.jsx(o.Z,{className:"h-5 w-5 text-gray-400"})}),r.jsx("input",{id:"password",name:"password",type:h?"text":"password",required:!0,value:s.password,onChange:T,className:"cyber-input pl-10 pr-10 w-full",placeholder:"••••••••"}),r.jsx("button",{type:"button",onClick:()=>g(!h),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-cyber-green",children:h?r.jsx(m.Z,{className:"h-5 w-5"}):r.jsx(x.Z,{className:"h-5 w-5"})})]}),s.password&&(0,r.jsxs)("div",{className:"mt-2 space-y-1",children:[(0,r.jsxs)("div",{className:`text-xs flex items-center space-x-2 ${k.minLength?"text-green-400":"text-gray-400"}`,children:[r.jsx(u.Z,{className:"h-3 w-3"}),r.jsx("span",{children:"At least 8 characters"})]}),(0,r.jsxs)("div",{className:`text-xs flex items-center space-x-2 ${k.hasUpper?"text-green-400":"text-gray-400"}`,children:[r.jsx(u.Z,{className:"h-3 w-3"}),r.jsx("span",{children:"One uppercase letter"})]}),(0,r.jsxs)("div",{className:`text-xs flex items-center space-x-2 ${k.hasLower?"text-green-400":"text-gray-400"}`,children:[r.jsx(u.Z,{className:"h-3 w-3"}),r.jsx("span",{children:"One lowercase letter"})]}),(0,r.jsxs)("div",{className:`text-xs flex items-center space-x-2 ${k.hasNumber?"text-green-400":"text-gray-400"}`,children:[r.jsx(u.Z,{className:"h-3 w-3"}),r.jsx("span",{children:"One number"})]}),(0,r.jsxs)("div",{className:`text-xs flex items-center space-x-2 ${k.hasSpecial?"text-green-400":"text-gray-400"}`,children:[r.jsx(u.Z,{className:"h-3 w-3"}),r.jsx("span",{children:"One special character"})]})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-300 mb-2",children:"Confirm Password"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:r.jsx(o.Z,{className:"h-5 w-5 text-gray-400"})}),r.jsx("input",{id:"confirmPassword",name:"confirmPassword",type:j?"text":"password",required:!0,value:s.confirmPassword,onChange:T,className:"cyber-input pl-10 pr-10 w-full",placeholder:"••••••••"}),r.jsx("button",{type:"button",onClick:()=>b(!j),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-cyber-green",children:j?r.jsx(m.Z,{className:"h-5 w-5"}):r.jsx(x.Z,{className:"h-5 w-5"})})]}),s.confirmPassword&&s.password!==s.confirmPassword&&r.jsx("p",{className:"mt-1 text-xs text-red-400",children:"Passwords do not match"})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Choose Your Plan"}),r.jsx("div",{className:"grid md:grid-cols-3 gap-4",children:[{id:"free",name:"Gratis",price:"Rp 0",features:["5 Scan/hari","Basic OSINT","Community Support"]},{id:"student",name:"Pelajar",price:"Rp 25.000",features:["50 Scan/hari","Full OSINT","Email Support"]},{id:"bug_hunter",name:"Bug Hunter",price:"Rp 150.000",features:["Unlimited Scan","Bot Integration","Priority Support"]}].map(e=>(0,r.jsxs)("label",{className:`cursor-pointer border rounded-lg p-4 transition-all duration-200 ${s.plan===e.id?"border-cyber-green bg-cyber-green/10":"border-gray-600 hover:border-gray-500"}`,children:[r.jsx("input",{type:"radio",name:"plan",value:e.id,checked:s.plan===e.id,onChange:T,className:"sr-only"}),(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("h4",{className:"font-semibold text-white",children:e.name}),r.jsx("p",{className:"text-cyber-green font-bold",children:e.price}),r.jsx("ul",{className:"mt-2 text-xs text-gray-400 space-y-1",children:e.features.map((e,s)=>(0,r.jsxs)("li",{children:["• ",e]},s))})]})]},e.id))})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[r.jsx("input",{id:"agreeToTerms",name:"agreeToTerms",type:"checkbox",checked:s.agreeToTerms,onChange:T,className:"mt-1 h-4 w-4 text-cyber-green focus:ring-cyber-green border-gray-600 rounded bg-gray-800"}),(0,r.jsxs)("label",{htmlFor:"agreeToTerms",className:"text-sm text-gray-300",children:["I agree to the"," ",r.jsx(l.default,{href:"/terms",className:"text-cyber-green hover:underline",children:"Terms of Service"})," ","and"," ",r.jsx(l.default,{href:"/privacy",className:"text-cyber-green hover:underline",children:"Privacy Policy"})]})]}),r.jsx("button",{type:"submit",disabled:y||!s.agreeToTerms||!k.isValid,className:"w-full cyber-btn-primary py-3 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed",children:y?(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[r.jsx("div",{className:"w-5 h-5 border-2 border-black border-t-transparent rounded-full animate-spin"}),r.jsx("span",{children:"Creating Account..."})]}):"Create Account"})]}),r.jsx("div",{className:"mt-6 text-center",children:(0,r.jsxs)("p",{className:"text-gray-400",children:["Already have an account?"," ",r.jsx(l.default,{href:"/login",className:"text-cyber-green hover:text-cyber-blue transition-colors font-semibold",children:"Sign in here"})]})})]})})]})]})}},7203:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>r});let r=(0,a(68570).createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\register\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),r=s.X(0,[216,592],()=>a(57226));module.exports=r})();