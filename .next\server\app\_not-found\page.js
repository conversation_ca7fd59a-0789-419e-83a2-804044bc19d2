"use strict";(()=>{var e={};e.id=409,e.ids=[409],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},69853:(e,n,t)=>{t.r(n),t.d(n,{GlobalError:()=>s.a,__next_app__:()=>c,originalPathname:()=>u,pages:()=>p,routeModule:()=>x,tree:()=>l}),t(7352),t(35866),t(30829);var r=t(23191),o=t(88716),a=t(37922),s=t.n(a),i=t(95231),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(n,d);let l=["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],p=[],u="/_not-found/page",c={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})}};var n=require("../../webpack-runtime.js");n.C(e);var t=e=>n(n.s=e),r=n.X(0,[216,592],()=>t(69853));module.exports=r})();