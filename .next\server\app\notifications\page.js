(()=>{var e={};e.id=5193,e.ids=[5193],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},54717:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>h,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c}),r(90834),r(30829),r(35866);var a=r(23191),n=r(88716),s=r(37922),i=r.n(s),o=r(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c=["",{children:["notifications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,90834)),"D:\\Users\\Downloads\\kodeXGuard\\app\\notifications\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\Users\\Downloads\\kodeXGuard\\app\\notifications\\page.tsx"],u="/notifications/page",h={require:r,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/notifications/page",pathname:"/notifications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},60546:(e,t,r)=>{Promise.resolve().then(r.bind(r,67382))},28931:(e,t,r)=>{Promise.resolve().then(r.bind(r,68636))},27807:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,12994,23)),Promise.resolve().then(r.t.bind(r,96114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,79671,23)),Promise.resolve().then(r.t.bind(r,41868,23)),Promise.resolve().then(r.t.bind(r,84759,23))},67382:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>i,a:()=>o});var a=r(10326),n=r(17577);let s=(0,n.createContext)(void 0);function i({children:e}){let[t,r]=(0,n.useState)(null),[i,o]=(0,n.useState)(null),[l,c]=(0,n.useState)(!0),d=async(e,t)=>{try{c(!0);let a=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})}),n=await a.json();if(n.success&&n.token)return o(n.token),r(n.user),!0;return!1}catch(e){return console.error("Login error:",e),!1}finally{c(!1)}},u=async()=>{try{let e=await fetch("/api/auth/refresh",{method:"POST",headers:{Authorization:`Bearer ${i}`}}),t=await e.json();t.success&&t.token&&o(t.token)}catch(e){console.error("Token refresh error:",e),h()}},h=()=>{r(null),o(null)};return a.jsx(s.Provider,{value:{user:t,token:i,login:d,logout:h,isLoading:l,isAuthenticated:!!t&&!!i,refreshToken:u},children:e})}function o(){let e=(0,n.useContext)(s);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},68636:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var a=r(10326),n=r(17577),s=r(54659),i=r(37202),o=r(91470),l=r(18019),c=r(6507),d=r(98091),u=r(12714),h=r(48998),x=r(90434);function m(){let[e,t]=(0,n.useState)([]),[r,m]=(0,n.useState)("all"),[g,p]=(0,n.useState)("all"),[y,f]=(0,n.useState)(!0),[v,b]=(0,n.useState)([]),w=async e=>{try{let r=localStorage.getItem("auth-token");if(!r)return;await fetch(`/api/notifications/${e}/read`,{method:"POST",headers:{Authorization:`Bearer ${r}`}}),t(t=>t.map(t=>t.id===e?{...t,read:!0}:t))}catch(r){console.error("Failed to mark notification as read:",r),t(t=>t.map(t=>t.id===e?{...t,read:!0}:t))}},j=async()=>{try{let e=localStorage.getItem("auth-token");if(!e)return;await fetch("/api/notifications/mark-all-read",{method:"POST",headers:{Authorization:`Bearer ${e}`}}),t(e=>e.map(e=>({...e,read:!0})))}catch(e){console.error("Failed to mark all notifications as read:",e),t(e=>e.map(e=>({...e,read:!0})))}},k=async e=>{try{if(!localStorage.getItem("auth-token"))return;t(t=>t.filter(t=>t.id!==e))}catch(e){console.error("Failed to delete notification:",e)}},N=async()=>{try{if(!localStorage.getItem("auth-token"))return;t(e=>e.filter(e=>!v.includes(e.id))),b([])}catch(e){console.error("Failed to delete selected notifications:",e)}},S=e=>{switch(e){case"success":return s.Z;case"warning":return i.Z;case"error":return o.Z;default:return l.Z}},P=e=>{switch(e){case"success":return"text-green-500";case"warning":return"text-yellow-500";case"error":return"text-red-500";default:return"text-blue-500"}},C=e=>{switch(e){case"success":return"bg-green-500/10 border-green-500/20";case"warning":return"bg-yellow-500/10 border-yellow-500/20";case"error":return"bg-red-500/10 border-red-500/20";default:return"bg-blue-500/10 border-blue-500/20"}},A=e.filter(e=>("read"!==r||!!e.read)&&("unread"!==r||!e.read)&&("all"===g||e.type===g)),Z=e.filter(e=>!e.read).length,M=e=>{let t=new Date(e),r=(new Date().getTime()-t.getTime())/36e5;return r<1?`${Math.floor(60*r)} minutes ago`:r<24?`${Math.floor(r)} hours ago`:t.toLocaleDateString("id-ID",{day:"numeric",month:"short",year:"numeric"})};return a.jsx("div",{className:"min-h-screen bg-black text-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[a.jsx("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[a.jsx("div",{className:"w-12 h-12 bg-cyber-green rounded-lg flex items-center justify-center",children:a.jsx(c.Z,{className:"h-6 w-6 text-black"})}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold font-cyber",children:"Notifications"}),a.jsx("p",{className:"text-gray-400",children:Z>0?`${Z} unread notifications`:"All notifications read"})]})]})}),a.jsx("div",{className:"mb-6 bg-gray-900 rounded-lg border border-gray-800 p-4",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex flex-wrap gap-3",children:[(0,a.jsxs)("select",{value:r,onChange:e=>m(e.target.value),className:"bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-cyber-green",children:[a.jsx("option",{value:"all",children:"All Notifications"}),a.jsx("option",{value:"unread",children:"Unread Only"}),a.jsx("option",{value:"read",children:"Read Only"})]}),(0,a.jsxs)("select",{value:g,onChange:e=>p(e.target.value),className:"bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-cyber-green",children:[a.jsx("option",{value:"all",children:"All Types"}),a.jsx("option",{value:"success",children:"Success"}),a.jsx("option",{value:"warning",children:"Warning"}),a.jsx("option",{value:"error",children:"Error"}),a.jsx("option",{value:"info",children:"Info"})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[v.length>0&&(0,a.jsxs)("button",{onClick:N,className:"flex items-center gap-2 px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm",children:[a.jsx(d.Z,{className:"h-4 w-4"}),"Delete Selected (",v.length,")"]}),Z>0&&(0,a.jsxs)("button",{onClick:j,className:"flex items-center gap-2 px-3 py-2 bg-cyber-green text-black rounded-lg hover:bg-cyber-green/90 transition-colors text-sm",children:[a.jsx(u.Z,{className:"h-4 w-4"}),"Mark All Read"]})]})]})}),a.jsx("div",{className:"space-y-4",children:y?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("div",{className:"w-8 h-8 border-2 border-cyber-green border-t-transparent rounded-full animate-spin mx-auto mb-4"}),a.jsx("p",{className:"text-gray-400",children:"Loading notifications..."})]}):0===A.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx(c.Z,{className:"h-12 w-12 text-gray-600 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-400",children:"No notifications found"})]}):A.map(e=>{let t=S(e.type);return a.jsx("div",{className:`bg-gray-900 border rounded-lg p-4 transition-all hover:bg-gray-800/50 ${e.read?"border-gray-800":`${C(e.type)} border-l-4`}`,children:(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[a.jsx("input",{type:"checkbox",checked:v.includes(e.id),onChange:t=>{t.target.checked?b(t=>[...t,e.id]):b(t=>t.filter(t=>t!==e.id))},className:"mt-1 w-4 h-4 text-cyber-green bg-gray-800 border-gray-600 rounded focus:ring-cyber-green"}),a.jsx(t,{className:`flex-shrink-0 h-5 w-5 mt-0.5 ${P(e.type)}`}),a.jsx("div",{className:"flex-1 min-w-0",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("h3",{className:`text-sm font-medium ${e.read?"text-gray-300":"text-white"}`,children:e.title}),a.jsx("p",{className:"text-sm text-gray-400 mt-1",children:e.message}),(0,a.jsxs)("div",{className:"flex items-center gap-4 mt-3",children:[(0,a.jsxs)("span",{className:"text-xs text-gray-500 flex items-center gap-1",children:[a.jsx(h.Z,{className:"h-3 w-3"}),M(e.timestamp)]}),e.action&&(0,a.jsxs)(x.default,{href:e.action.url,className:"text-xs text-cyber-green hover:text-cyber-green/80 transition-colors",onClick:()=>!e.read&&w(e.id),children:[e.action.label," →"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 ml-4",children:[!e.read&&a.jsx("button",{onClick:()=>w(e.id),className:"text-gray-400 hover:text-cyber-green transition-colors",title:"Mark as read",children:a.jsx(u.Z,{className:"h-4 w-4"})}),a.jsx("button",{onClick:()=>k(e.id),className:"text-gray-400 hover:text-red-400 transition-colors",title:"Delete notification",children:a.jsx(d.Z,{className:"h-4 w-4"})})]})]})})]})},e.id)})})]})})}},76557:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var a=r(17577),n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),i=(e,t)=>{let r=(0,a.forwardRef)(({color:r="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:l,className:c="",children:d,...u},h)=>(0,a.createElement)("svg",{ref:h,...n,width:i,height:i,stroke:r,strokeWidth:l?24*Number(o)/Number(i):o,className:["lucide",`lucide-${s(e)}`,c].join(" "),...u},[...t.map(([e,t])=>(0,a.createElement)(e,t)),...Array.isArray(d)?d:[d]]));return r.displayName=`${e}`,r}},37202:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(76557).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},6507:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(76557).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},54659:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(76557).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},48998:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(76557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},12714:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(76557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},18019:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(76557).Z)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},98091:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(76557).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},91470:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(76557).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},30829:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>l});var a=r(19510),n=r(77366),s=r.n(n);r(7633);var i=r(68570);let o=(0,i.createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\contexts\AuthContext.tsx#AuthProvider`);(0,i.createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\contexts\AuthContext.tsx#useAuth`);let l={title:"KodeXGuard - Platform Cybersecurity & Bug Hunting",description:"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting dengan dukungan Bot WhatsApp/Telegram",keywords:"cybersecurity, bug hunting, OSINT, vulnerability scanner, CVE, penetration testing, security tools",authors:[{name:"KodeXGuard Team"}],creator:"KodeXGuard",publisher:"KodeXGuard",robots:"index, follow",openGraph:{type:"website",locale:"id_ID",url:"https://kodexguard.com",title:"KodeXGuard - Platform Cybersecurity & Bug Hunting",description:"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting",siteName:"KodeXGuard"},twitter:{card:"summary_large_image",title:"KodeXGuard - Platform Cybersecurity & Bug Hunting",description:"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting"},viewport:"width=device-width, initial-scale=1",themeColor:"#00ff41"};function c({children:e}){return(0,a.jsxs)("html",{lang:"id",className:"dark",children:[(0,a.jsxs)("head",{children:[a.jsx("link",{rel:"icon",href:"/favicon.ico"}),a.jsx("link",{rel:"apple-touch-icon",href:"/apple-touch-icon.png"}),a.jsx("link",{rel:"manifest",href:"/manifest.json"})]}),(0,a.jsxs)("body",{className:`${s().className} min-h-screen bg-gradient-cyber text-white antialiased`,children:[a.jsx("div",{className:"matrix-bg",children:a.jsx("div",{className:"absolute inset-0 bg-gradient-to-b from-transparent via-cyber-dark/20 to-cyber-dark"})}),a.jsx("div",{className:"relative z-10",children:a.jsx(o,{children:e})}),a.jsx("script",{dangerouslySetInnerHTML:{__html:`
              // Matrix Rain Effect
              function createMatrixRain() {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.style.position = 'fixed';
                canvas.style.top = '0';
                canvas.style.left = '0';
                canvas.style.width = '100%';
                canvas.style.height = '100%';
                canvas.style.pointerEvents = 'none';
                canvas.style.zIndex = '-1';
                canvas.style.opacity = '0.1';
                document.body.appendChild(canvas);
                
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
                
                const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';
                const charArray = chars.split('');
                const fontSize = 14;
                const columns = canvas.width / fontSize;
                const drops = [];
                
                for (let i = 0; i < columns; i++) {
                  drops[i] = 1;
                }
                
                function draw() {
                  ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
                  ctx.fillRect(0, 0, canvas.width, canvas.height);
                  
                  ctx.fillStyle = '#00ff41';
                  ctx.font = fontSize + 'px monospace';
                  
                  for (let i = 0; i < drops.length; i++) {
                    const text = charArray[Math.floor(Math.random() * charArray.length)];
                    ctx.fillText(text, i * fontSize, drops[i] * fontSize);
                    
                    if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
                      drops[i] = 0;
                    }
                    drops[i]++;
                  }
                }
                
                setInterval(draw, 50);
                
                window.addEventListener('resize', () => {
                  canvas.width = window.innerWidth;
                  canvas.height = window.innerHeight;
                });
              }
              
              // Initialize matrix effect after page load
              if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', createMatrixRain);
              } else {
                createMatrixRain();
              }
            `}})]})]})}},90834:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(68570).createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\notifications\page.tsx#default`)},7633:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[9276,82,434],()=>r(54717));module.exports=a})();