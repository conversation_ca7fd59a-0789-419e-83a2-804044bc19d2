"use strict";exports.id=633,exports.ids=[633],exports.modules={8633:(e,t,r)=>{r.r(t),r.d(t,{default:()=>I});var a=r(10326),s=r(17577),n=r(90434),i=r(35047),l=r(24319),c=r(88307),o=r(58038),d=r(6530),x=r(88319),m=r(53468),h=r(67048),u=r(50732),g=r(92498),b=r(58907),f=r(79635),p=r(28916),y=r(6343),j=r(26092),N=r(3634),v=r(33734),w=r(90748),k=r(94019),Z=r(39183),$=r(11890),S=r(88378),C=r(6507),z=r(71810);function I({user:e}){let[t,r]=(0,s.useState)(!1),[I,O]=(0,s.useState)(!1),[P,T]=(0,s.useState)(!1),[A,D]=(0,s.useState)([]),[F,B]=(0,s.useState)(0),H=(0,i.usePathname)(),L=(0,i.useRouter)(),E=async e=>{try{let t=localStorage.getItem("auth-token");if(!t)return;await fetch(`/api/notifications/${e}/read`,{method:"POST",headers:{Authorization:`Bearer ${t}`}}),D(t=>t.map(t=>t.id===e?{...t,read:!0}:t))}catch(t){console.error("Failed to mark notification as read:",t),D(t=>t.map(t=>t.id===e?{...t,read:!0}:t))}},G=async()=>{try{let e=localStorage.getItem("auth-token");if(!e)return;await fetch("/api/notifications/mark-all-read",{method:"POST",headers:{Authorization:`Bearer ${e}`}}),D(e=>e.map(e=>({...e,read:!0})))}catch(e){console.error("Failed to mark all notifications as read:",e),D(e=>e.map(e=>({...e,read:!0})))}},K=[{id:"dashboard",name:"Dashboard",href:"/dashboard",icon:l.Z},{id:"osint",name:"OSINT Investigator",href:"/osint",icon:c.Z,badge:"HOT"},{id:"scanner",name:"Vulnerability Scanner",href:"/scanner",icon:o.Z,premium:!0},{id:"file-analyzer",name:"File Analyzer",href:"/file-analyzer",icon:d.Z},{id:"cve",name:"CVE Intelligence",href:"/cve",icon:x.Z,badge:"NEW"},{id:"dorking",name:"Google Dorking",href:"/dorking",icon:m.Z},{id:"tools",name:"Security Tools",href:"/tools",icon:h.Z},{id:"bot",name:"Bot Center",href:"/bot",icon:u.Z,premium:!0,adminOnly:!0},{id:"playground",name:"API Playground",href:"/playground",icon:g.Z,premium:!0},{id:"leaderboard",name:"Leaderboard",href:"/leaderboard",icon:b.Z},{id:"profile",name:"Profile & API Keys",href:"/profile",icon:f.Z},{id:"plan",name:"Plans & Billing",href:"/plan",icon:p.Z},{id:"docs",name:"Documentation",href:"/docs",icon:y.Z}].filter(t=>(!t.adminOnly||!!["super_admin","admin"].includes(e.role))&&(!t.premium||"gratis"!==e.plan)),U=e=>{switch(e){case"cybersecurity":return j.Z;case"bughunter":return N.Z;case"hobby":return v.Z;default:return f.Z}};return(0,a.jsxs)(a.Fragment,{children:[I&&a.jsx("div",{className:"fixed inset-0 bg-black/50 z-40 lg:hidden",onClick:()=>O(!1)}),a.jsx("button",{onClick:()=>O(!0),className:"fixed top-4 left-4 z-50 lg:hidden bg-gray-800 text-white p-2 rounded-lg border border-gray-700 hover:bg-gray-700 transition-colors",children:a.jsx(w.Z,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{className:`
        fixed lg:sticky top-0 left-0 h-screen bg-gray-900/95 backdrop-blur-sm border-r border-gray-800 z-50 lg:z-auto
        transition-all duration-300 ease-in-out flex-shrink-0
        ${t?"w-16":"w-64"}
        ${I?"translate-x-0":"-translate-x-full lg:translate-x-0"}
        lg:h-[calc(100vh-4rem)]
      `,children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-800",children:[!t&&(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("div",{className:"w-8 h-8 bg-cyber-green rounded-lg flex items-center justify-center",children:a.jsx(o.Z,{className:"h-5 w-5 text-black"})}),a.jsx("span",{className:"font-bold text-white font-cyber",children:"KodeXGuard"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("button",{onClick:()=>O(!1),className:"lg:hidden text-gray-400 hover:text-white transition-colors",children:a.jsx(k.Z,{className:"h-5 w-5"})}),a.jsx("button",{onClick:()=>r(!t),className:"hidden lg:block text-gray-400 hover:text-white transition-colors",children:t?a.jsx(Z.Z,{className:"h-5 w-5"}):a.jsx($.Z,{className:"h-5 w-5"})})]})]}),a.jsx("div",{className:"p-4 border-b border-gray-800",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[e.avatar?a.jsx("img",{src:e.avatar,alt:e.username,className:"w-10 h-10 rounded-full"}):a.jsx("div",{className:"w-10 h-10 rounded-full bg-cyber-green/20 flex items-center justify-center",children:a.jsx("span",{className:"text-cyber-green font-bold",children:e.username.charAt(0).toUpperCase()})}),!t&&(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[a.jsx("div",{className:"font-medium text-white truncate",children:e.username}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(()=>{let t=U(e.plan);return a.jsx(t,{className:"h-3 w-3 text-nusantara-gold"})})(),a.jsx("span",{className:`text-xs px-2 py-1 rounded-full font-semibold ${(e=>{switch(e){case"cybersecurity":return"text-red-400 bg-red-900/20";case"bughunter":return"text-purple-400 bg-purple-900/20";case"hobby":return"text-green-400 bg-green-900/20";case"pelajar":return"text-blue-400 bg-blue-900/20";default:return"text-gray-400 bg-gray-900/20"}})(e.plan)}`,children:e.plan.toUpperCase()})]})]})]})}),a.jsx("nav",{className:"flex-1 overflow-y-auto py-4",children:a.jsx("div",{className:"space-y-1 px-2",children:K.map(e=>{let r=e.icon,s=H===e.href;return(0,a.jsxs)(n.default,{href:e.href,className:`
                    group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200
                    ${s?"bg-cyber-green text-black":"text-gray-300 hover:text-white hover:bg-gray-800/50"}
                    ${t?"justify-center":"justify-start"}
                  `,children:[a.jsx(r,{className:`h-5 w-5 flex-shrink-0 ${t?"":"mr-3"}`}),!t&&(0,a.jsxs)(a.Fragment,{children:[a.jsx("span",{className:"flex-1",children:e.name}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[e.badge&&a.jsx("span",{className:`
                            px-2 py-1 text-xs font-bold rounded-full
                            ${"HOT"===e.badge?"bg-red-500 text-white":"bg-blue-500 text-white"}
                          `,children:e.badge}),e.premium&&a.jsx(j.Z,{className:"h-3 w-3 text-nusantara-gold"}),e.adminOnly&&a.jsx(S.Z,{className:"h-3 w-3 text-orange-400"})]})]}),t&&(0,a.jsxs)("div",{className:"absolute left-16 bg-gray-800 text-white px-2 py-1 rounded-lg text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50",children:[e.name,e.badge&&a.jsx("span",{className:`ml-2 px-1 py-0.5 text-xs rounded ${"HOT"===e.badge?"bg-red-500":"bg-blue-500"}`,children:e.badge})]})]},e.id)})})}),a.jsx("div",{className:"border-t border-gray-800 p-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{onClick:()=>T(!P),className:`
                  w-full flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors
                  ${t?"justify-center":"justify-start"}
                  ${P?"bg-gray-800/50 text-white":""}
                `,children:[(0,a.jsxs)("div",{className:"relative",children:[a.jsx(C.Z,{className:`h-5 w-5 ${t?"":"mr-3"}`}),F>0&&a.jsx("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs w-4 h-4 rounded-full flex items-center justify-center",children:F>9?"9+":F})]}),!t&&a.jsx("span",{children:"Notifications"}),!t&&F>0&&a.jsx("span",{className:"ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full",children:F})]}),P&&!t&&(0,a.jsxs)("div",{className:"absolute bottom-full left-0 right-0 mb-2 bg-gray-800 border border-gray-700 rounded-lg shadow-xl max-h-96 overflow-y-auto z-50",children:[(0,a.jsxs)("div",{className:"p-3 border-b border-gray-700 flex items-center justify-between",children:[a.jsx("h3",{className:"text-sm font-semibold text-white",children:"Notifications"}),F>0&&a.jsx("button",{onClick:G,className:"text-xs text-cyber-green hover:text-cyber-green/80 transition-colors",children:"Mark all read"})]}),a.jsx("div",{className:"max-h-80 overflow-y-auto",children:0===A.length?a.jsx("div",{className:"p-4 text-center text-gray-400 text-sm",children:"No notifications"}):A.map(e=>a.jsx("div",{className:`p-3 border-b border-gray-700 last:border-b-0 hover:bg-gray-700/50 transition-colors ${e.read?"":"bg-gray-700/20"}`,onClick:()=>!e.read&&E(e.id),children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx("div",{className:`flex-shrink-0 w-2 h-2 rounded-full mt-2 ${"success"===e.type?"bg-green-500":"warning"===e.type?"bg-yellow-500":"error"===e.type?"bg-red-500":"bg-blue-500"}`}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("h4",{className:`text-sm font-medium ${e.read?"text-gray-300":"text-white"}`,children:e.title}),a.jsx("span",{className:"text-xs text-gray-400",children:new Date(e.timestamp).toLocaleTimeString("id-ID",{hour:"2-digit",minute:"2-digit"})})]}),a.jsx("p",{className:"text-xs text-gray-400 mt-1",children:e.message}),e.action&&(0,a.jsxs)(n.default,{href:e.action.url,className:"inline-block mt-2 text-xs text-cyber-green hover:text-cyber-green/80 transition-colors",onClick:()=>T(!1),children:[e.action.label," →"]})]})]})},e.id))})]})]}),(0,a.jsxs)(n.default,{href:"/settings",className:`
                w-full flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors
                ${t?"justify-center":"justify-start"}
                ${"/settings"===H?"bg-gray-800/50 text-white":""}
              `,children:[a.jsx(S.Z,{className:`h-5 w-5 ${t?"":"mr-3"}`}),!t&&a.jsx("span",{children:"Settings"})]}),(0,a.jsxs)("button",{onClick:()=>{localStorage.removeItem("auth-token"),L.push("/login")},className:`
                w-full flex items-center px-3 py-2 text-sm font-medium text-red-400 hover:text-red-300 hover:bg-red-900/20 rounded-lg transition-colors
                ${t?"justify-center":"justify-start"}
              `,children:[a.jsx(z.Z,{className:`h-5 w-5 ${t?"":"mr-3"}`}),!t&&a.jsx("span",{children:"Logout"})]})]})})]})]})}}};