import mysql from 'mysql2/promise'
import { createClient } from 'redis'
import { Client } from '@elastic/elasticsearch'

// MySQL Database Connection
export class Database {
  private static instance: Database
  private pool: mysql.Pool

  private constructor() {
    this.pool = mysql.createPool({
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'rootkan',
      database: process.env.DB_NAME || 'db_kodexguard',
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0,
      charset: 'utf8mb4',
      timezone: '+00:00'
    })
  }

  public static getInstance(): Database {
    if (!Database.instance) {
      Database.instance = new Database()
    }
    return Database.instance
  }

  public async query(sql: string, params?: any[]): Promise<any> {
    try {
      const [rows] = await this.pool.execute(sql, params)
      return rows
    } catch (error) {
      console.error('Database query error:', error)
      throw error
    }
  }

  public async transaction<T>(callback: (connection: mysql.PoolConnection) => Promise<T>): Promise<T> {
    const connection = await this.pool.getConnection()
    try {
      await connection.beginTransaction()
      const result = await callback(connection)
      await connection.commit()
      return result
    } catch (error) {
      await connection.rollback()
      throw error
    } finally {
      connection.release()
    }
  }

  public async close(): Promise<void> {
    await this.pool.end()
  }
}

// Redis Cache Connection
export class Cache {
  private static instance: Cache
  private client: any

  private constructor() {
    this.client = createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379',
      password: process.env.REDIS_PASSWORD || undefined,
      socket: {
        reconnectStrategy: (retries) => Math.min(retries * 50, 500)
      }
    })

    this.client.on('error', (err: any) => {
      console.error('Redis Client Error:', err)
    })

    this.client.on('connect', () => {
      console.log('Redis Client Connected')
    })
  }

  public static getInstance(): Cache {
    if (!Cache.instance) {
      Cache.instance = new Cache()
    }
    return Cache.instance
  }

  public async connect(): Promise<void> {
    if (!this.client.isOpen) {
      await this.client.connect()
    }
  }

  public async get(key: string): Promise<string | null> {
    await this.connect()
    return await this.client.get(key)
  }

  public async set(key: string, value: string, ttl?: number): Promise<void> {
    await this.connect()
    if (ttl) {
      await this.client.setEx(key, ttl, value)
    } else {
      await this.client.set(key, value)
    }
  }

  public async del(key: string): Promise<void> {
    await this.connect()
    await this.client.del(key)
  }

  public async exists(key: string): Promise<boolean> {
    await this.connect()
    return (await this.client.exists(key)) === 1
  }

  public async incr(key: string): Promise<number> {
    await this.connect()
    return await this.client.incr(key)
  }

  public async expire(key: string, ttl: number): Promise<void> {
    await this.connect()
    await this.client.expire(key, ttl)
  }

  public async close(): Promise<void> {
    if (this.client.isOpen) {
      await this.client.quit()
    }
  }
}

// Elasticsearch Search Engine
export class SearchEngine {
  private static instance: SearchEngine
  private client: Client

  private constructor() {
    this.client = new Client({
      node: process.env.ELASTICSEARCH_URL || 'http://localhost:9200',
      auth: process.env.ELASTICSEARCH_USERNAME && process.env.ELASTICSEARCH_PASSWORD ? {
        username: process.env.ELASTICSEARCH_USERNAME,
        password: process.env.ELASTICSEARCH_PASSWORD
      } : undefined,
      requestTimeout: 30000,
      pingTimeout: 3000,
      sniffOnStart: false
    })
  }

  public static getInstance(): SearchEngine {
    if (!SearchEngine.instance) {
      SearchEngine.instance = new SearchEngine()
    }
    return SearchEngine.instance
  }

  public async index(index: string, id: string, document: any): Promise<void> {
    try {
      await this.client.index({
        index,
        id,
        body: document
      })
    } catch (error) {
      console.error('Elasticsearch index error:', error)
      throw error
    }
  }

  public async search(index: string, query: any): Promise<any> {
    try {
      const response = await this.client.search({
        index,
        body: query
      })
      return response
    } catch (error) {
      console.error('Elasticsearch search error:', error)
      throw error
    }
  }

  public async delete(index: string, id: string): Promise<void> {
    try {
      await this.client.delete({
        index,
        id
      })
    } catch (error) {
      console.error('Elasticsearch delete error:', error)
      throw error
    }
  }

  public async createIndex(index: string, mapping: any): Promise<void> {
    try {
      const exists = await this.client.indices.exists({ index })
      if (!exists) {
        await this.client.indices.create({
          index,
          body: {
            mappings: mapping
          }
        })
      }
    } catch (error) {
      console.error('Elasticsearch create index error:', error)
      throw error
    }
  }

  public async close(): Promise<void> {
    await this.client.close()
  }
}

// Export instances
export const db = Database.getInstance()
export const cache = Cache.getInstance()
export const search = SearchEngine.getInstance()
