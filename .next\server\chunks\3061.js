"use strict";exports.id=3061,exports.ids=[3061],exports.modules={83061:(e,t,s)=>{s.d(t,{Z:()=>m});var a=s(10326),r=s(17577),i=s(43353),n=s.n(i);class o{constructor(){this.memoryCache=new Map,this.diskCache=null,this.stats={hits:0,misses:0,sets:0,deletes:0,size:0},this.TTL={STATIC:864e5,API:3e5,USER_DATA:9e5,SEARCH:6e5,DASHBOARD:12e4,REALTIME:3e4},this.initDiskCache(),this.startCleanupInterval()}async initDiskCache(){}isExpired(e){return Date.now()-e.timestamp>e.ttl}generateKey(e,t){return`${e}:${t}`}async get(e,t,s){let a=this.memoryCache.get(e);if(a&&!this.isExpired(a))return this.stats.hits++,a.data;if(this.diskCache)try{let t=await this.diskCache.get("cache",e);if(t&&!this.isExpired(t))return this.memoryCache.set(e,t),this.stats.hits++,t.data}catch(e){console.warn("Disk cache read error:",e)}if(t)try{let a=await t();return null!=a&&await this.set(e,a,s||this.TTL.API),this.stats.misses++,a}catch(e){return console.error("Cache fallback error:",e),this.stats.misses++,null}return this.stats.misses++,null}async set(e,t,s=this.TTL.API){let a={data:t,timestamp:Date.now(),ttl:s,version:"1.0"};if(this.memoryCache.set(e,a),this.stats.sets++,this.stats.size=this.memoryCache.size,this.diskCache&&s>this.TTL.REALTIME)try{await this.diskCache.put("cache",{key:e,...a})}catch(e){console.warn("Disk cache write error:",e)}this.memoryCache.size>1e3&&this.evictOldest()}async delete(e){if(this.memoryCache.delete(e),this.stats.deletes++,this.stats.size=this.memoryCache.size,this.diskCache)try{await this.diskCache.delete("cache",e)}catch(e){console.warn("Disk cache delete error:",e)}}async clear(){if(this.memoryCache.clear(),this.stats={hits:0,misses:0,sets:0,deletes:0,size:0},this.diskCache)try{await this.diskCache.clear("cache")}catch(e){console.warn("Disk cache clear error:",e)}}evictOldest(){let e=Array.from(this.memoryCache.entries());e.sort((e,t)=>e[1].timestamp-t[1].timestamp);let t=Math.floor(.1*e.length);for(let s=0;s<t;s++)this.memoryCache.delete(e[s][0])}startCleanupInterval(){}cleanup(){for(let[e,t]of(Date.now(),this.memoryCache.entries()))this.isExpired(t)&&this.memoryCache.delete(e);this.stats.size=this.memoryCache.size}async cacheUserData(e,t){await this.set(`user:${e}`,t,this.TTL.USER_DATA)}async getUserData(e){return await this.get(`user:${e}`)}async cacheSearchResults(e,t){let s=`search:${btoa(e)}`;await this.set(s,t,this.TTL.SEARCH)}async getSearchResults(e){let t=`search:${btoa(e)}`;return await this.get(t)}async cacheDashboardData(e,t){await this.set(`dashboard:${e}`,t,this.TTL.DASHBOARD)}async getDashboardData(e){return await this.get(`dashboard:${e}`)}async cacheApiResponse(e,t,s){let a=`api:${e}:${JSON.stringify(t)}`;await this.set(a,s,this.TTL.API)}async getApiResponse(e,t){let s=`api:${e}:${JSON.stringify(t)}`;return await this.get(s)}getStats(){return{...this.stats}}getHitRate(){let e=this.stats.hits+this.stats.misses;return e>0?this.stats.hits/e*100:0}async preloadCriticalData(e){let t=["/api/user/profile","/api/dashboard/stats","/api/notifications","/api/user/plan"].map(async t=>{try{let s=await fetch(t);if(s.ok){let a=await s.json();await this.cacheApiResponse(t,{userId:e},a)}}catch(e){console.warn(`Preload failed for ${t}:`,e)}});await Promise.allSettled(t)}}let l=new o;class c{constructor(){this.preloadQueue=new Map,this.preloadedRoutes=new Set,this.isPreloading=!1,this.observer=null,this.ROUTE_STRATEGIES=[{route:"/dashboard",dependencies:["/osint","/scanner","/profile"],dataEndpoints:["/api/dashboard/stats","/api/user/profile","/api/notifications"],assets:[],priority:1},{route:"/osint",dependencies:["/file-analyzer","/cve"],dataEndpoints:["/api/osint/recent","/api/osint/templates"],assets:[],priority:2},{route:"/scanner",dependencies:["/cve","/tools"],dataEndpoints:["/api/scanner/history","/api/scanner/templates"],assets:[],priority:2},{route:"/profile",dependencies:["/settings","/plan"],dataEndpoints:["/api/user/profile","/api/user/api-keys","/api/user/usage"],assets:[],priority:3},{route:"/settings",dependencies:[],dataEndpoints:["/api/user/settings","/api/user/preferences"],assets:[],priority:4}],this.initIntersectionObserver(),this.initRoutePreloading()}initIntersectionObserver(){}initRoutePreloading(){}async preloadCriticalRoutes(){let e=window.location.pathname,t=this.ROUTE_STRATEGIES.find(t=>t.route===e);if(t){for(let e of t.dependencies)await this.preloadRoute(e,"high");for(let e of t.dataEndpoints)await this.preloadData(e)}"/dashboard"!==e&&await this.preloadRoute("/dashboard","medium")}setupHoverPreloading(){}setupBehaviorBasedPreloading(){}predictNextRoutes(e,t){return({"/dashboard":["/osint","/scanner","/profile"],"/osint":["/file-analyzer","/cve","/dashboard"],"/scanner":["/cve","/tools","/dashboard"],"/profile":["/settings","/plan","/dashboard"],"/":["/dashboard","/login","/register"]})[e]||[]}getUserBehaviorPattern(){return{}}async preloadRoute(e,t="medium"){if(!this.preloadedRoutes.has(e))try{let t=this.ROUTE_STRATEGIES.find(t=>t.route===e);if(t){let e=t.dataEndpoints.map(e=>this.preloadData(e));await Promise.allSettled(e)}this.preloadedRoutes.add(e),console.log(`✅ Preloaded route: ${e}`)}catch(t){console.warn(`❌ Failed to preload route ${e}:`,t)}}async preloadData(e){try{if(await l.get(e))return;let t=await fetch(e,{method:"GET",headers:{"Content-Type":"application/json"}});if(t.ok){let s=await t.json();await l.set(e,s),console.log(`📦 Preloaded data: ${e}`)}}catch(t){console.warn(`❌ Failed to preload data ${e}:`,t)}}async preloadCriticalAssets(){let e=["/images/logo.svg","/images/cyber-bg.jpg"].map(e=>new Promise(t=>{let s=document.createElement("link");s.rel="preload",s.href=e,s.as=(e.endsWith(".svg"),"image"),s.onload=()=>t(),s.onerror=()=>t(),document.head.appendChild(s)}));await Promise.allSettled(e)}observeLinks(){this.observer&&document.querySelectorAll('a[href^="/"]').forEach(e=>{this.observer.observe(e)})}async preloadUserJourney(e,t){for(let[s,a]of this.getUserJourneyRoutes(e,t).entries())setTimeout(()=>{this.preloadRoute(a,s<3?"high":"low")},500*s)}getUserJourneyRoutes(e,t){let s=["/dashboard","/profile","/settings"];return"cybersecurity"===t||"bughunter"===t?[...s,"/osint","/scanner","/file-analyzer","/cve","/playground","/bot"]:[...s,"/osint","/file-analyzer","/cve","/tools"]}getStats(){return{preloadedRoutes:Array.from(this.preloadedRoutes),queueSize:this.preloadQueue.size,isPreloading:this.isPreloading,cacheStats:l.getStats()}}clearPreloaded(){this.preloadedRoutes.clear(),this.preloadQueue.clear()}}new c;class d{constructor(){this.metrics={},this.budget={pageLoadTime:2e3,firstContentfulPaint:1e3,largestContentfulPaint:2500,firstInputDelay:100,cumulativeLayoutShift:.1,timeToInteractive:3e3},this.observers=new Map,this.routeStartTime=0,this.initPerformanceObservers(),this.trackPageLoad(),this.trackRouteChanges()}initPerformanceObservers(){}initWebVitalsObserver(){if(!("PerformanceObserver"in window))return;let e=new PerformanceObserver(e=>{let t=e.getEntries(),s=t[t.length-1];this.metrics.firstContentfulPaint=s.startTime,this.checkBudget("firstContentfulPaint",s.startTime)});e.observe({entryTypes:["paint"]}),this.observers.set("fcp",e);let t=new PerformanceObserver(e=>{let t=e.getEntries(),s=t[t.length-1];this.metrics.largestContentfulPaint=s.startTime,this.checkBudget("largestContentfulPaint",s.startTime)});t.observe({entryTypes:["largest-contentful-paint"]}),this.observers.set("lcp",t);let s=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{this.metrics.firstInputDelay=e.processingStart-e.startTime,this.checkBudget("firstInputDelay",this.metrics.firstInputDelay)})});s.observe({entryTypes:["first-input"]}),this.observers.set("fid",s);let a=0,r=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{e.hadRecentInput||(a+=e.value)}),this.metrics.cumulativeLayoutShift=a,this.checkBudget("cumulativeLayoutShift",a)});r.observe({entryTypes:["layout-shift"]}),this.observers.set("cls",r)}initNavigationObserver(){if(!("PerformanceObserver"in window))return;let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{this.metrics.pageLoadTime=e.loadEventEnd-e.fetchStart,this.metrics.timeToInteractive=e.domInteractive-e.fetchStart,this.checkBudget("pageLoadTime",this.metrics.pageLoadTime),this.checkBudget("timeToInteractive",this.metrics.timeToInteractive)})});e.observe({entryTypes:["navigation"]}),this.observers.set("navigation",e)}initResourceObserver(){if(!("PerformanceObserver"in window))return;let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{e.duration>1e3&&console.warn(`Slow resource detected: ${e.name} (${e.duration}ms)`),e.name.includes("/api/")&&(this.metrics.apiResponseTime=e.duration)})});e.observe({entryTypes:["resource"]}),this.observers.set("resource",e)}initLongTaskObserver(){if(!("PerformanceObserver"in window))return;let e=0,t=new PerformanceObserver(t=>{t.getEntries().forEach(t=>{t.duration>50&&(e+=t.duration-50)}),this.metrics.totalBlockingTime=e});t.observe({entryTypes:["longtask"]}),this.observers.set("longtask",t)}trackPageLoad(){}trackRouteChanges(){}markRouteChangeComplete(){this.routeStartTime>0&&(this.metrics.routeChangeTime=performance.now()-this.routeStartTime,this.routeStartTime=0,this.metrics.routeChangeTime>500&&console.warn(`Slow route change: ${this.metrics.routeChangeTime}ms`))}checkBudget(e,t){let s=this.budget[e];t>s&&(console.warn(`Performance budget exceeded for ${e}: ${t}ms > ${s}ms`),this.suggestOptimizations(e,t,s))}suggestOptimizations(e,t,s){console.group(`🚀 Performance Optimization Suggestions for ${e}`),console.log(`Current: ${t}ms, Budget: ${s}ms`),(({pageLoadTime:["Enable compression (gzip/brotli)","Optimize images and use WebP format","Implement code splitting","Use CDN for static assets"],firstContentfulPaint:["Optimize critical CSS","Preload key resources","Reduce server response time","Eliminate render-blocking resources"],largestContentfulPaint:["Optimize images above the fold","Preload LCP element","Reduce server response time","Remove unused CSS"],firstInputDelay:["Reduce JavaScript execution time","Break up long tasks","Use web workers for heavy computations","Optimize third-party scripts"],cumulativeLayoutShift:["Set size attributes on images and videos","Reserve space for ads and embeds","Avoid inserting content above existing content","Use CSS aspect-ratio for responsive images"]})[e]||[]).forEach(e=>console.log(`• ${e}`)),console.groupEnd()}calculatePerformanceScore(){let e=100;return Object.entries({firstContentfulPaint:.15,largestContentfulPaint:.25,firstInputDelay:.25,cumulativeLayoutShift:.25,totalBlockingTime:.1}).forEach(([t,s])=>{let a=this.metrics[t],r=this.budget[t];if(a&&r){let t=a/r;t>1&&(e-=(t-1)*s*100)}}),Math.max(0,Math.min(100,e))}reportMetrics(){let e=this.calculatePerformanceScore();console.group("\uD83D\uDCCA Performance Metrics Report"),console.log("Overall Score:",`${e.toFixed(1)}/100`),console.log("Metrics:",this.metrics),console.log("Cache Hit Rate:",this.getCacheHitRate()),console.groupEnd(),this.sendToAnalytics({...this.metrics,performanceScore:e,timestamp:Date.now(),userAgent:navigator.userAgent,url:window.location.href})}getCacheHitRate(){return 0}sendToAnalytics(e){}getMetrics(){return{...this.metrics}}getPerformanceScore(){return this.calculatePerformanceScore()}updateBudget(e){this.budget={...this.budget,...e}}cleanup(){this.observers.forEach(e=>e.disconnect()),this.observers.clear()}}new d;let h=n()(()=>Promise.all([s.e(434),s.e(2675)]).then(s.bind(s,22675)),{loadableGenerated:{modules:["components\\DashboardLayout.tsx -> ./Sidebar"]},loading:()=>a.jsx("div",{className:"w-64 bg-gray-900 animate-pulse"}),ssr:!0}),u=n()(()=>Promise.all([s.e(434),s.e(6361)]).then(s.bind(s,16361)),{loadableGenerated:{modules:["components\\DashboardLayout.tsx -> ./Navbar"]},loading:()=>a.jsx("div",{className:"h-16 bg-gray-900 animate-pulse"}),ssr:!0});function m({children:e,user:t,title:s,showSearch:i=!0}){return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-cyber",children:[a.jsx(r.Suspense,{fallback:a.jsx("div",{className:"h-16 bg-gray-900 animate-pulse"}),children:a.jsx(u,{user:t,title:s,showSearch:i,isLandingPage:!1})}),(0,a.jsxs)("div",{className:"flex pt-16",children:[a.jsx(r.Suspense,{fallback:a.jsx("div",{className:"w-64 bg-gray-900 animate-pulse"}),children:a.jsx(h,{user:t})}),a.jsx("main",{className:"flex-1 min-w-0 lg:ml-0",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:a.jsx("div",{className:"w-full",children:a.jsx(r.Suspense,{fallback:(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("div",{className:"h-8 bg-gray-800 rounded animate-pulse"}),a.jsx("div",{className:"h-64 bg-gray-800 rounded animate-pulse"})]}),children:e})})})})]})]})}},43353:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}});let a=s(91174);s(10326),s(17577);let r=a._(s(77028));function i(e,t){var s;let a={loading:e=>{let{error:t,isLoading:s,pastDelay:a}=e;return null}};"function"==typeof e&&(a.loader=e);let i={...a,...t};return(0,r.default)({...i,modules:null==(s=i.loadableGenerated)?void 0:s.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},933:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return r}});let a=s(94129);function r(e){let{reason:t,children:s}=e;throw new a.BailoutToCSRError(t)}},77028:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return c}});let a=s(10326),r=s(17577),i=s(933),n=s(46618);function o(e){return{default:e&&"default"in e?e.default:e}}let l={loader:()=>Promise.resolve(o(()=>null)),loading:null,ssr:!0},c=function(e){let t={...l,...e},s=(0,r.lazy)(()=>t.loader().then(o)),c=t.loading;function d(e){let o=c?(0,a.jsx)(c,{isLoading:!0,pastDelay:!0,error:null}):null,l=t.ssr?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.PreloadCss,{moduleIds:t.modules}),(0,a.jsx)(s,{...e})]}):(0,a.jsx)(i.BailoutToCSR,{reason:"next/dynamic",children:(0,a.jsx)(s,{...e})});return(0,a.jsx)(r.Suspense,{fallback:o,children:l})}return d.displayName="LoadableComponent",d}},46618:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadCss",{enumerable:!0,get:function(){return i}});let a=s(10326),r=s(54580);function i(e){let{moduleIds:t}=e,s=(0,r.getExpectedRequestStore)("next/dynamic css"),i=[];if(s.reactLoadableManifest&&t){let e=s.reactLoadableManifest;for(let s of t){if(!e[s])continue;let t=e[s].files.filter(e=>e.endsWith(".css"));i.push(...t)}}return 0===i.length?null:(0,a.jsx)(a.Fragment,{children:i.map(e=>(0,a.jsx)("link",{precedence:"dynamic",rel:"stylesheet",href:s.assetPrefix+"/_next/"+encodeURI(e),as:"style"},e))})}}};