(()=>{var e={};e.id=4178,e.ids=[4178],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},20820:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>r.a,__next_app__:()=>m,originalPathname:()=>o,pages:()=>x,routeModule:()=>h,tree:()=>d}),t(44591),t(30829),t(35866);var a=t(23191),i=t(88716),l=t(37922),r=t.n(l),n=t(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,44591)),"D:\\Users\\Downloads\\kodeXGuard\\app\\profile\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],x=["D:\\Users\\Downloads\\kodeXGuard\\app\\profile\\page.tsx"],o="/profile/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},44493:(e,s,t)=>{Promise.resolve().then(t.bind(t,83094))},83094:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var a=t(10326),i=t(17577),l=t(83061),r=t(2262),n=t(79635),c=t(58038),d=t(66697),x=t(3634),o=t(76828),m=t(70003);let h=(0,t(76557).Z)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);var p=t(54659),u=t(31215),y=t(83855),g=t(98091),b=t(91216),j=t(12714),f=t(43810),v=t(54014);function N(){let[e]=(0,i.useState)({username:"admin",avatar:"",role:"super_admin",plan:"cybersecurity"}),[s,t]=(0,i.useState)("profile"),[N,k]=(0,i.useState)({id:"1",username:"admin",email:"<EMAIL>",fullName:"Super Administrator",avatar:"",bio:"Platform creator and cybersecurity enthusiast. Building tools to make the internet safer.",role:"super_admin",plan:"cybersecurity",planExpiry:"2024-12-31",isActive:!0,emailVerified:!0,createdAt:"2023-01-01",lastLogin:"2024-01-15T10:30:00Z",stats:{totalScans:1247,vulnerabilitiesFound:89,filesAnalyzed:456,apiCalls:28560,score:9870,rank:4}}),[w,A]=(0,i.useState)([{id:"1",name:"Production API",keyPrefix:"kxg_prod_",permissions:["scan:read","scan:write","osint:read","file:analyze"],isActive:!0,lastUsed:"2 hours ago",usageCount:15420,rateLimit:1e4,createdAt:"2023-06-15",expiresAt:"2024-06-15"},{id:"2",name:"Development API",keyPrefix:"kxg_dev_",permissions:["scan:read","osint:read"],isActive:!0,lastUsed:"1 day ago",usageCount:2847,rateLimit:1e3,createdAt:"2023-08-20"},{id:"3",name:"Bot Integration",keyPrefix:"kxg_bot_",permissions:["scan:write","user:read"],isActive:!1,lastUsed:"1 week ago",usageCount:567,rateLimit:5e3,createdAt:"2023-10-10",expiresAt:"2024-10-10"}]),[Z,P]=(0,i.useState)(!1),[C,S]=(0,i.useState)(!1),[L,I]=(0,i.useState)(""),[_,M]=(0,i.useState)([]),[D,U]=(0,i.useState)(null),V=e=>{A(s=>s.map(s=>s.id===e?{...s,isActive:!s.isActive}:s))},E=e=>{A(s=>s.filter(s=>s.id!==e))},z=e=>{navigator.clipboard.writeText(e+"xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx")},K=e=>e.includes("admin")?"bg-red-900/50 text-red-400":e.includes("write")?"bg-orange-900/50 text-orange-400":"bg-blue-900/50 text-blue-400";return a.jsx(l.Z,{user:e,title:"Profile & API Keys",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[a.jsx(n.Z,{className:"h-8 w-8 text-cyber-green"}),(0,a.jsxs)("h1",{className:"text-3xl font-bold font-cyber text-white",children:["Profile & ",a.jsx("span",{className:"cyber-text",children:"API Keys"})]})]}),a.jsx("p",{className:"text-gray-400 max-w-3xl",children:"Kelola profil pengguna, statistik aktivitas, dan API keys untuk integrasi dengan aplikasi eksternal. Monitor penggunaan API dan atur permissions sesuai kebutuhan."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[a.jsx(r.Rm,{title:"Total Scans",value:N.stats.totalScans,icon:c.Z,color:"green",trend:{value:12,isPositive:!0}}),a.jsx(r.Rm,{title:"Vulnerabilities Found",value:N.stats.vulnerabilitiesFound,icon:d.Z,color:"red"}),a.jsx(r.Rm,{title:"API Calls",value:N.stats.apiCalls,icon:x.Z,color:"purple",trend:{value:23,isPositive:!0}}),a.jsx(r.Rm,{title:"Hunter Rank",value:`#${N.stats.rank}`,icon:n.Z,color:"gold"})]}),a.jsx("div",{className:"mb-8",children:a.jsx("div",{className:"flex space-x-1 bg-gray-800/50 p-1 rounded-lg",children:[{id:"profile",name:"Profile Settings",icon:n.Z},{id:"apikeys",name:"API Keys",icon:o.Z},{id:"activity",name:"Activity Log",icon:d.Z}].map(e=>{let i=e.icon;return(0,a.jsxs)("button",{onClick:()=>t(e.id),className:`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${s===e.id?"bg-cyber-green text-black":"text-gray-300 hover:text-white hover:bg-gray-700/50"}`,children:[a.jsx(i,{className:"h-4 w-4"}),a.jsx("span",{children:e.name})]},e.id)})})}),"profile"===s&&(0,a.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[a.jsx("div",{className:"lg:col-span-2",children:a.jsx(r.Zb,{border:"green",glow:!0,children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[a.jsx("h2",{className:"text-xl font-bold text-white",children:"Profile Information"}),(0,a.jsxs)("button",{onClick:()=>P(!Z),className:"cyber-btn text-sm",children:[a.jsx(m.Z,{className:"h-4 w-4 mr-2"}),Z?"Cancel":"Edit Profile"]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,a.jsxs)("div",{className:"relative",children:[N.avatar?a.jsx("img",{src:N.avatar,alt:N.username,className:"h-20 w-20 rounded-full"}):a.jsx("div",{className:"h-20 w-20 rounded-full bg-cyber-green/20 flex items-center justify-center",children:a.jsx("span",{className:"text-2xl font-bold text-cyber-green",children:N.username.charAt(0).toUpperCase()})}),Z&&a.jsx("button",{className:"absolute bottom-0 right-0 bg-cyber-green text-black rounded-full p-2 hover:bg-cyber-blue transition-colors",children:a.jsx(h,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-xl font-bold text-white",children:N.fullName}),(0,a.jsxs)("p",{className:"text-gray-400",children:["@",N.username]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[a.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-semibold ${"super_admin"===N.role?"bg-red-900/50 text-red-400":"admin"===N.role?"bg-orange-900/50 text-orange-400":"bg-blue-900/50 text-blue-400"}`,children:N.role.replace("_"," ").toUpperCase()}),a.jsx("span",{className:"px-2 py-1 bg-nusantara-gold/20 text-nusantara-gold rounded-full text-xs font-semibold",children:N.plan.toUpperCase()})]})]})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Full Name"}),a.jsx("input",{type:"text",value:N.fullName,onChange:e=>k(s=>({...s,fullName:e.target.value})),disabled:!Z,className:"cyber-input w-full disabled:opacity-50"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Username"}),a.jsx("input",{type:"text",value:N.username,onChange:e=>k(s=>({...s,username:e.target.value})),disabled:!Z,className:"cyber-input w-full disabled:opacity-50"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Email Address"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("input",{type:"email",value:N.email,onChange:e=>k(s=>({...s,email:e.target.value})),disabled:!Z,className:"cyber-input w-full disabled:opacity-50"}),N.emailVerified&&a.jsx(p.Z,{className:"absolute right-3 top-3 h-5 w-5 text-green-400"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Plan Expiry"}),a.jsx("input",{type:"text",value:N.planExpiry?new Date(N.planExpiry).toLocaleDateString():"Never",disabled:!0,className:"cyber-input w-full disabled:opacity-50"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Bio"}),a.jsx("textarea",{value:N.bio||"",onChange:e=>k(s=>({...s,bio:e.target.value})),disabled:!Z,className:"cyber-input w-full h-24 disabled:opacity-50",placeholder:"Tell us about yourself..."})]}),Z&&(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsxs)("button",{onClick:()=>{P(!1)},className:"cyber-btn-primary",children:[a.jsx(u.Z,{className:"h-4 w-4 mr-2"}),"Save Changes"]}),a.jsx("button",{onClick:()=>P(!1),className:"cyber-btn",children:"Cancel"})]})]})]})})}),(0,a.jsxs)("div",{children:[a.jsx(r.Zb,{children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Account Information"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Member Since"}),a.jsx("span",{className:"text-white",children:new Date(N.createdAt).toLocaleDateString()})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Last Login"}),a.jsx("span",{className:"text-white",children:new Date(N.lastLogin).toLocaleString()})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Account Status"}),a.jsx("span",{className:`font-semibold ${N.isActive?"text-green-400":"text-red-400"}`,children:N.isActive?"Active":"Inactive"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Email Verified"}),a.jsx("span",{className:`font-semibold ${N.emailVerified?"text-green-400":"text-red-400"}`,children:N.emailVerified?"Verified":"Unverified"})]})]})]})}),a.jsx(r.Zb,{className:"mt-6",border:"gold",children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Hunter Statistics"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Hunter Score"}),a.jsx("span",{className:"text-cyber-green font-bold",children:N.stats.score.toLocaleString()})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Global Rank"}),(0,a.jsxs)("span",{className:"text-nusantara-gold font-bold",children:["#",N.stats.rank]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Files Analyzed"}),a.jsx("span",{className:"text-white",children:N.stats.filesAnalyzed.toLocaleString()})]})]})]})})]})]}),"apikeys"===s&&(0,a.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[a.jsx("div",{className:"lg:col-span-2",children:a.jsx(r.Zb,{border:"green",glow:!0,children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[a.jsx("h2",{className:"text-xl font-bold text-white",children:"API Keys"}),(0,a.jsxs)("button",{onClick:()=>S(!0),className:"cyber-btn-primary",children:[a.jsx(y.Z,{className:"h-4 w-4 mr-2"}),"Create New Key"]})]}),a.jsx("div",{className:"space-y-4",children:w.map(e=>(0,a.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-4 border border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("h3",{className:"font-bold text-white",children:e.name}),a.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-semibold ${e.isActive?"bg-green-900/50 text-green-400":"bg-red-900/50 text-red-400"}`,children:e.isActive?"Active":"Inactive"})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-400 mt-1",children:["Created: ",new Date(e.createdAt).toLocaleDateString(),e.expiresAt&&(0,a.jsxs)("span",{children:[" • Expires: ",new Date(e.expiresAt).toLocaleDateString()]})]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx("button",{onClick:()=>V(e.id),className:`text-sm px-3 py-1 rounded transition-colors ${e.isActive?"bg-red-600 hover:bg-red-700 text-white":"bg-green-600 hover:bg-green-700 text-white"}`,children:e.isActive?"Disable":"Enable"}),a.jsx("button",{onClick:()=>E(e.id),className:"text-red-400 hover:text-red-300 transition-colors",children:a.jsx(g.Z,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"API Key"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("div",{className:"flex-1 bg-gray-900/50 rounded p-3 font-mono text-sm",children:D===e.id?e.keyPrefix+"xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx":e.keyPrefix+"••••••••••••••••••••••••••••••••"}),a.jsx("button",{onClick:()=>U(D===e.id?null:e.id),className:"cyber-btn text-sm",children:D===e.id?a.jsx(b.Z,{className:"h-4 w-4"}):a.jsx(j.Z,{className:"h-4 w-4"})}),a.jsx("button",{onClick:()=>z(e.keyPrefix),className:"cyber-btn text-sm",children:a.jsx(f.Z,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Permissions"}),a.jsx("div",{className:"flex flex-wrap gap-2",children:e.permissions.map((e,s)=>a.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-semibold ${K(e)}`,children:e},s))})]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-gray-400",children:"Usage Count:"}),a.jsx("span",{className:"text-white ml-2 font-semibold",children:e.usageCount.toLocaleString()})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-gray-400",children:"Rate Limit:"}),(0,a.jsxs)("span",{className:"text-white ml-2 font-semibold",children:[e.rateLimit.toLocaleString(),"/day"]})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-gray-400",children:"Last Used:"}),a.jsx("span",{className:"text-white ml-2 font-semibold",children:e.lastUsed||"Never"})]})]})]})]},e.id))})]})})}),(0,a.jsxs)("div",{children:[a.jsx(r.Zb,{children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"API Usage"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Total API Keys"}),a.jsx("span",{className:"text-white font-semibold",children:w.length})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Active Keys"}),a.jsx("span",{className:"text-green-400 font-semibold",children:w.filter(e=>e.isActive).length})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Total Calls"}),a.jsx("span",{className:"text-white font-semibold",children:w.reduce((e,s)=>e+s.usageCount,0).toLocaleString()})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Daily Limit"}),a.jsx("span",{className:"text-cyber-green font-semibold",children:"100,000"})]})]})]})}),a.jsx(r.Zb,{className:"mt-6",children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"API Documentation"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[a.jsx("p",{className:"text-gray-300",children:"Use your API keys to integrate KodeXGuard with your applications."}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("p",{className:"text-gray-400",children:["• Base URL: ",a.jsx("code",{className:"text-cyber-green",children:"https://api.kodexguard.com/v1"})]}),a.jsx("p",{className:"text-gray-400",children:"• Authentication: Bearer token"}),a.jsx("p",{className:"text-gray-400",children:"• Rate limiting: Per API key"}),a.jsx("p",{className:"text-gray-400",children:"• Response format: JSON"})]}),a.jsx("button",{className:"cyber-btn text-sm w-full mt-4",children:"View Full Documentation"})]})]})})]})]}),"activity"===s&&a.jsx(r.Zb,{children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h2",{className:"text-xl font-bold text-white mb-6",children:"Activity Log"}),a.jsx("div",{className:"space-y-4",children:[{action:"Vulnerability scan completed",target:"https://example.com",time:"2 hours ago",type:"scan"},{action:"API key created",target:"Production API",time:"1 day ago",type:"api"},{action:"Profile updated",target:"Bio information",time:"2 days ago",type:"profile"},{action:"OSINT search performed",target:"<EMAIL>",time:"3 days ago",type:"osint"},{action:"File analyzed",target:"suspicious.php",time:"1 week ago",type:"file"}].map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-gray-800/50 rounded-lg",children:[(0,a.jsxs)("div",{className:`p-2 rounded-full ${"scan"===e.type?"bg-blue-900/50":"api"===e.type?"bg-green-900/50":"profile"===e.type?"bg-purple-900/50":"osint"===e.type?"bg-yellow-900/50":"bg-red-900/50"}`,children:["scan"===e.type&&a.jsx(c.Z,{className:"h-4 w-4 text-blue-400"}),"api"===e.type&&a.jsx(o.Z,{className:"h-4 w-4 text-green-400"}),"profile"===e.type&&a.jsx(n.Z,{className:"h-4 w-4 text-purple-400"}),"osint"===e.type&&a.jsx(v.Z,{className:"h-4 w-4 text-yellow-400"}),"file"===e.type&&a.jsx(c.Z,{className:"h-4 w-4 text-red-400"})]}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("div",{className:"font-medium text-white",children:e.action}),a.jsx("div",{className:"text-sm text-gray-400",children:e.target})]}),a.jsx("div",{className:"text-sm text-gray-400",children:e.time})]},s))})]})}),C&&a.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:a.jsx(r.Zb,{className:"w-full max-w-md mx-4",border:"green",children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Create New API Key"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Key Name"}),a.jsx("input",{type:"text",value:L,onChange:e=>I(e.target.value),placeholder:"My API Key",className:"cyber-input w-full"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Permissions"}),a.jsx("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:[{id:"scan:read",name:"Read Scan Results",description:"View vulnerability scan results"},{id:"scan:write",name:"Create Scans",description:"Start new vulnerability scans"},{id:"osint:read",name:"OSINT Search",description:"Perform OSINT investigations"},{id:"file:analyze",name:"File Analysis",description:"Analyze files for threats"},{id:"cve:read",name:"CVE Database",description:"Access CVE intelligence data"},{id:"user:read",name:"User Info",description:"Read user profile information"},{id:"admin:read",name:"Admin Access",description:"Administrative operations (Admin only)"}].map(e=>(0,a.jsxs)("label",{className:"flex items-start space-x-3 cursor-pointer",children:[a.jsx("input",{type:"checkbox",checked:_.includes(e.id),onChange:s=>{s.target.checked?M(s=>[...s,e.id]):M(s=>s.filter(s=>s!==e.id))},className:"mt-1 rounded bg-gray-800 border-gray-600"}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm font-medium text-white",children:e.name}),a.jsx("div",{className:"text-xs text-gray-400",children:e.description})]})]},e.id))})]})]}),(0,a.jsxs)("div",{className:"flex space-x-3 mt-6",children:[a.jsx("button",{onClick:()=>{if(!L.trim()||0===_.length)return;let s={id:Date.now().toString(),name:L,keyPrefix:"kxg_"+Math.random().toString(36).substr(2,8)+"_",permissions:_,isActive:!0,usageCount:0,rateLimit:"cybersecurity"===e.plan?1e5:"bug_hunter"===e.plan?1e4:1e3,createdAt:new Date().toISOString()};A(e=>[...e,s]),I(""),M([]),S(!1)},disabled:!L.trim()||0===_.length,className:"cyber-btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:"Create Key"}),a.jsx("button",{onClick:()=>S(!1),className:"cyber-btn",children:"Cancel"})]})]})})})]})})}},66697:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},54659:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},43810:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},91216:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},12714:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},54014:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},76828:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Key",[["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["path",{d:"m15.5 7.5 3 3L22 7l-3-3",key:"1rn1fs"}]])},70003:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},83855:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},31215:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},58038:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},98091:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},79635:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},3634:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},44591:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\profile\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[9276,82,7608,3061],()=>t(20820));module.exports=a})();