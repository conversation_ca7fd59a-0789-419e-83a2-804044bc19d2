"use strict";(()=>{var e={};e.id=78,e.ids=[78],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},96119:e=>{e.exports=require("perf_hooks")},35816:e=>{e.exports=require("process")},76162:e=>{e.exports=require("stream")},74026:e=>{e.exports=require("string_decoder")},95346:e=>{e.exports=require("timers")},82452:e=>{e.exports=require("tls")},74175:e=>{e.exports=require("tty")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},98061:e=>{e.exports=require("node:assert")},92761:e=>{e.exports=require("node:async_hooks")},72254:e=>{e.exports=require("node:buffer")},40027:e=>{e.exports=require("node:console")},6005:e=>{e.exports=require("node:crypto")},65714:e=>{e.exports=require("node:diagnostics_channel")},30604:e=>{e.exports=require("node:dns")},15673:e=>{e.exports=require("node:events")},88849:e=>{e.exports=require("node:http")},42725:e=>{e.exports=require("node:http2")},22286:e=>{e.exports=require("node:https")},87503:e=>{e.exports=require("node:net")},70612:e=>{e.exports=require("node:os")},38846:e=>{e.exports=require("node:perf_hooks")},97742:e=>{e.exports=require("node:process")},39630:e=>{e.exports=require("node:querystring")},55467:e=>{e.exports=require("node:sqlite")},84492:e=>{e.exports=require("node:stream")},99397:e=>{e.exports=require("node:timers/promises")},31764:e=>{e.exports=require("node:tls")},41041:e=>{e.exports=require("node:url")},47261:e=>{e.exports=require("node:util")},93746:e=>{e.exports=require("node:util/types")},24086:e=>{e.exports=require("node:worker_threads")},65628:e=>{e.exports=require("node:zlib")},93630:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>q,patchFetch:()=>w,requestAsyncStorage:()=>S,routeModule:()=>m,serverHooks:()=>g,staticGenerationAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{GET:()=>h,POST:()=>y});var a=r(49303),i=r(88716),n=r(60670),o=r(87070),c=r(29712),u=r(24544);class l{constructor(){this.scanTests=new Map,this.activeScan=new Map,this.initializeScanTests()}initializeScanTests(){this.scanTests.set("sqli",[{name:"Basic SQL Injection",type:"sqli",payloads:["' OR '1'='1","' OR 1=1--","' UNION SELECT NULL--","'; DROP TABLE users--","' AND (SELECT COUNT(*) FROM information_schema.tables)>0--"],patterns:[/mysql_fetch_array/i,/ORA-\d{5}/i,/Microsoft.*ODBC.*SQL Server/i,/PostgreSQL.*ERROR/i,/Warning.*mysql_/i,/valid MySQL result/i,/MySqlClient\./i],severity:"critical",description:"SQL Injection vulnerability allows attackers to manipulate database queries",recommendation:"Use parameterized queries and input validation"}]),this.scanTests.set("xss",[{name:"Cross-Site Scripting",type:"xss",payloads:['<script>alert("XSS")</script>','<img src=x onerror=alert("XSS")>','<svg onload=alert("XSS")>','javascript:alert("XSS")','"><script>alert("XSS")</script>'],patterns:[/<script[^>]*>.*?<\/script>/i,/javascript:/i,/on\w+\s*=/i],severity:"high",description:"Cross-Site Scripting allows execution of malicious scripts",recommendation:"Implement proper input sanitization and output encoding"}]),this.scanTests.set("lfi",[{name:"Local File Inclusion",type:"lfi",payloads:["../../../etc/passwd","..\\..\\..\\windows\\system32\\drivers\\etc\\hosts","/etc/passwd","C:\\windows\\system32\\drivers\\etc\\hosts","....//....//....//etc/passwd"],patterns:[/root:.*:0:0:/,/\[drivers\]/i,/# Copyright \(c\) 1993-2009 Microsoft Corp/i],severity:"high",description:"Local File Inclusion allows reading sensitive files",recommendation:"Validate and sanitize file path inputs"}]),this.scanTests.set("rce",[{name:"Remote Code Execution",type:"rce",payloads:["; ls -la","| whoami","`id`","$(whoami)","; cat /etc/passwd","& dir","| type C:\\windows\\system32\\drivers\\etc\\hosts"],patterns:[/uid=\d+\(.*?\)/,/gid=\d+\(.*?\)/,/root:.*:0:0:/,/Volume.*Serial Number/i,/Directory of/i],severity:"critical",description:"Remote Code Execution allows arbitrary command execution",recommendation:"Avoid system calls with user input, use whitelisting"}]),this.scanTests.set("path_traversal",[{name:"Path Traversal",type:"path_traversal",payloads:["../","..\\","..../","....\\","%2e%2e%2f","%2e%2e%5c","..%2f","..%5c"],patterns:[/root:.*:0:0:/,/\[boot loader\]/i,/\[operating systems\]/i],severity:"medium",description:"Path traversal allows access to files outside intended directory",recommendation:"Implement proper path validation and canonicalization"}])}async startScan(e){let t=this.generateScanId();try{return await u.db.query(`INSERT INTO scan_history (user_id, scan_type, target, status, created_at)
         VALUES (?, 'vulnerability', ?, 'pending', NOW())`,[e.userId,e.target]),this.executeScan(t,e).catch(e=>{console.error(`Scan ${t} failed:`,e),this.updateScanStatus(t,"failed")}),{scanId:t,status:"started"}}catch(e){throw console.error("Failed to start scan:",e),Error("Failed to start scan")}}async executeScan(e,t){let r=Date.now();try{await this.updateScanStatus(e,"running");let s=[],a=await this.discoverEndpoints(t.target,t.maxDepth||3);for(let e of t.scanTypes)for(let r of this.scanTests.get(e)||[])for(let e of a){let a=await this.testEndpoint(e,r,t.timeout||3e4);s.push(...a)}let i=this.calculateSummary(s),n=Date.now()-r,o={scanId:e,target:t.target,status:"completed",vulnerabilities:s,summary:i,scanDuration:n,startTime:new Date(r),endTime:new Date};await this.saveScanResults(o,t.userId),await this.updateScanStatus(e,"completed")}catch(t){console.error(`Scan execution failed for ${e}:`,t),await this.updateScanStatus(e,"failed")}}async discoverEndpoints(e,t){let r=new Set,s=new URL(e);r.add(e);try{let e=`${s.origin}/robots.txt`,t=await c.Z.get(e,{timeout:1e4});if(200===t.status){let e=t.data.match(/Disallow:\s*(.+)/gi);e&&e.forEach(e=>{let t=e.replace(/Disallow:\s*/i,"").trim();t&&"/"!==t&&r.add(`${s.origin}${t}`)})}}catch(e){}return["/admin","/login","/api","/search","/contact","/upload","/download","/user","/profile"].forEach(e=>{r.add(`${s.origin}${e}`)}),Array.from(r).slice(0,20)}async testEndpoint(e,t,r){let s=[];try{for(let a of t.payloads){let i=`${e}?test=${encodeURIComponent(a)}`,n=await this.testRequest("GET",i,null,t,r);n&&s.push(n);let o=await this.testRequest("POST",e,{test:a},t,r);o&&s.push(o)}}catch(e){}return s}async testRequest(e,t,r,s,a){try{let i={method:e,url:t,timeout:a,validateStatus:()=>!0,maxRedirects:5};r&&"POST"===e&&(i.data=r,i.headers={"Content-Type":"application/x-www-form-urlencoded"});let n=(await (0,c.Z)(i)).data;for(let e of s.patterns)if(e.test(n))return{id:this.generateVulnId(),type:s.type,severity:s.severity,url:t,parameter:r?Object.keys(r)[0]:"query parameter",payload:r?Object.values(r)[0]:new URL(t).searchParams.get("test")||"",evidence:n.substring(0,500),recommendation:s.recommendation,description:s.description};return null}catch(e){return null}}calculateSummary(e){let t={total:e.length,critical:0,high:0,medium:0,low:0,info:0};return e.forEach(e=>{t[e.severity]++}),t}async saveScanResults(e,t){try{await u.db.query(`UPDATE scan_history 
         SET results = ?, status = ?, vulnerabilities_found = ?, scan_duration = ?, completed_at = NOW()
         WHERE user_id = ? AND target = ? AND status = 'running'`,[JSON.stringify(e),e.status,e.vulnerabilities.length,e.scanDuration,t,e.target])}catch(e){console.error("Failed to save scan results:",e)}}async updateScanStatus(e,t){try{console.log(`Scan ${e} status updated to: ${t}`)}catch(e){console.error("Failed to update scan status:",e)}}async getScanResults(e,t){try{let e=await u.db.queryOne("SELECT * FROM scan_history WHERE user_id = ? ORDER BY created_at DESC LIMIT 1",[t]);if(!e||!e.results)return null;return JSON.parse(e.results)}catch(e){return console.error("Failed to get scan results:",e),null}}async getScanHistory(e,t=20){try{return await u.db.query(`SELECT * FROM scan_history 
         WHERE user_id = ? AND scan_type = 'vulnerability'
         ORDER BY created_at DESC 
         LIMIT ?`,[e,t])}catch(e){return console.error("Failed to get scan history:",e),[]}}generateScanId(){return"scan_"+Date.now()+"_"+Math.random().toString(36).substr(2,9)}generateVulnId(){return"vuln_"+Date.now()+"_"+Math.random().toString(36).substr(2,9)}}let d=new l;var p=r(90455);async function y(e){try{let t=e.headers.get("authorization"),r=t?.replace("Bearer ","");if(!r)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});let s=await p.e8.getUserByToken(r);if(!s)return o.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let{target:a,scanTypes:i=[],maxDepth:n=3,timeout:c=30,followRedirects:l=!0}=await e.json();if(!a)return o.NextResponse.json({success:!1,error:"Target URL is required",code:"MISSING_TARGET"},{status:400});if(!i||0===i.length)return o.NextResponse.json({success:!1,error:"At least one scan type is required",code:"MISSING_SCAN_TYPES"},{status:400});try{new URL(a)}catch{return o.NextResponse.json({success:!1,error:"Invalid URL format",code:"INVALID_URL"},{status:400})}let y=["sqli","xss","lfi","rfi","rce","csrf","ssrf","xxe","idor","path_traversal"],h=i.filter(e=>!y.includes(e));if(h.length>0)return o.NextResponse.json({success:!1,error:`Invalid scan types: ${h.join(", ")}`,code:"INVALID_SCAN_TYPES"},{status:400});let m=u.vo.getInstance(),S=new Date().toISOString().split("T")[0],x=await m.query(`SELECT COUNT(*) as count FROM scan_history
       WHERE user_id = ? AND scan_type = 'vulnerability' AND DATE(created_at) = ?`,[s.id,S]),g=await m.query("SELECT scan_quota_daily FROM plans WHERE id = ?",[s.planId]),q=g[0]?.scan_quota_daily||5,w=x[0]?.count||0;if(w>=q)return o.NextResponse.json({success:!1,error:`Daily scan quota exceeded. Used: ${w}/${q}`,code:"QUOTA_EXCEEDED"},{status:429});let f={target:a,scanTypes:i,maxDepth:n,timeout:1e3*c,followRedirects:l,userId:s.id},v=await d.startScan(f);return o.NextResponse.json({success:!0,data:{scanId:v.scanId,status:v.status,target:a,scanTypes:i,quotaUsed:w+1,quotaLimit:q,maxDepth:n,timeout:c},message:"Vulnerability scan started successfully",timestamp:new Date().toISOString()})}catch(e){return console.error("Vulnerability scan error:",e),o.NextResponse.json({success:!1,error:"Internal server error",code:"INTERNAL_ERROR"},{status:500})}}async function h(e){try{let t=e.headers.get("authorization"),r=t?.replace("Bearer ","");if(!r)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});let s=await p.e8.getUserByToken(r);if(!s)return o.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let{searchParams:a}=new URL(e.url),i=parseInt(a.get("page")||"1"),n=parseInt(a.get("limit")||"20"),c=await d.getScanHistory(s.id,n);return o.NextResponse.json({success:!0,data:c,pagination:{page:i,limit:n,total:c.length,totalPages:Math.ceil(c.length/n)},timestamp:new Date().toISOString()})}catch(e){return console.error("Scan history error:",e),o.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let m=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/scan/vulnerability/route",pathname:"/api/scan/vulnerability",filename:"route",bundlePath:"app/api/scan/vulnerability/route"},resolvedPagePath:"D:\\Users\\Downloads\\kodeXGuard\\app\\api\\scan\\vulnerability\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:S,staticGenerationAsyncStorage:x,serverHooks:g}=m,q="/api/scan/vulnerability/route";function w(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:x})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[216,592],()=>r(93630));module.exports=s})();