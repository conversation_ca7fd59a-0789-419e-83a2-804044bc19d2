"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/user/preferences/route";
exports.ids = ["app/api/user/preferences/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fpreferences%2Froute&page=%2Fapi%2Fuser%2Fpreferences%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fpreferences%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fpreferences%2Froute&page=%2Fapi%2Fuser%2Fpreferences%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fpreferences%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Users_Downloads_kodeXGuard_app_api_user_preferences_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/user/preferences/route.ts */ \"(rsc)/./app/api/user/preferences/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/user/preferences/route\",\n        pathname: \"/api/user/preferences\",\n        filename: \"route\",\n        bundlePath: \"app/api/user/preferences/route\"\n    },\n    resolvedPagePath: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\api\\\\user\\\\preferences\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Users_Downloads_kodeXGuard_app_api_user_preferences_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/user/preferences/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fpreferences%2Froute&page=%2Fapi%2Fuser%2Fpreferences%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fpreferences%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/user/preferences/route.ts":
/*!*******************************************!*\
  !*** ./app/api/user/preferences/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function GET(request) {\n    try {\n        // Get auth token\n        const authHeader = request.headers.get(\"authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        // Simple token validation for testing\n        if (!token.startsWith(\"kxg_\")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid token\"\n            }, {\n                status: 401\n            });\n        }\n        // Mock user preferences data\n        const userPreferences = {\n            general: {\n                language: \"id\",\n                timezone: \"Asia/Jakarta\",\n                theme: \"dark\",\n                dateFormat: \"DD/MM/YYYY\",\n                timeFormat: \"24h\",\n                currency: \"IDR\",\n                autoSave: true,\n                compactMode: false\n            },\n            dashboard: {\n                defaultView: \"overview\",\n                showWelcomeMessage: true,\n                refreshInterval: 30,\n                chartsType: \"line\",\n                showQuickActions: true,\n                recentItemsCount: 10\n            },\n            notifications: {\n                desktop: true,\n                sound: true,\n                vibration: false,\n                emailDigest: \"daily\",\n                pushFrequency: \"immediate\"\n            },\n            scanning: {\n                defaultScanType: [\n                    \"sqli\",\n                    \"xss\",\n                    \"csrf\"\n                ],\n                autoStartScans: false,\n                saveReports: true,\n                reportFormat: \"pdf\",\n                maxConcurrentScans: 3,\n                defaultTimeout: 300\n            },\n            osint: {\n                defaultSearchTypes: [\n                    \"email\",\n                    \"phone\",\n                    \"name\"\n                ],\n                autoSaveSearches: true,\n                confidenceThreshold: 70,\n                maxResults: 50,\n                includeBreaches: true,\n                includeSocialMedia: true\n            },\n            privacy: {\n                shareUsageData: false,\n                allowCookies: true,\n                trackingOptOut: false,\n                dataRetentionDays: 365\n            },\n            accessibility: {\n                highContrast: false,\n                largeText: false,\n                reducedMotion: false,\n                screenReader: false,\n                keyboardNavigation: true\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: userPreferences,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"User preferences API error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PUT(request) {\n    try {\n        // Get auth token\n        const authHeader = request.headers.get(\"authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        // Simple token validation for testing\n        if (!token.startsWith(\"kxg_\")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid token\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { general, dashboard, notifications, scanning, osint, privacy, accessibility } = body;\n        // Validate input\n        if (!general && !dashboard && !notifications && !scanning && !osint && !privacy && !accessibility) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"At least one preference category is required\"\n            }, {\n                status: 400\n            });\n        }\n        // In a real app, update the user preferences in database\n        // For demo, just return success with updated data\n        const updatedPreferences = {\n            general: general || {\n                language: \"id\",\n                timezone: \"Asia/Jakarta\",\n                theme: \"dark\",\n                dateFormat: \"DD/MM/YYYY\",\n                timeFormat: \"24h\",\n                currency: \"IDR\",\n                autoSave: true,\n                compactMode: false\n            },\n            dashboard: dashboard || {\n                defaultView: \"overview\",\n                showWelcomeMessage: true,\n                refreshInterval: 30,\n                chartsType: \"line\",\n                showQuickActions: true,\n                recentItemsCount: 10\n            },\n            notifications: notifications || {\n                desktop: true,\n                sound: true,\n                vibration: false,\n                emailDigest: \"daily\",\n                pushFrequency: \"immediate\"\n            },\n            scanning: scanning || {\n                defaultScanType: [\n                    \"sqli\",\n                    \"xss\",\n                    \"csrf\"\n                ],\n                autoStartScans: false,\n                saveReports: true,\n                reportFormat: \"pdf\",\n                maxConcurrentScans: 3,\n                defaultTimeout: 300\n            },\n            osint: osint || {\n                defaultSearchTypes: [\n                    \"email\",\n                    \"phone\",\n                    \"name\"\n                ],\n                autoSaveSearches: true,\n                confidenceThreshold: 70,\n                maxResults: 50,\n                includeBreaches: true,\n                includeSocialMedia: true\n            },\n            privacy: privacy || {\n                shareUsageData: false,\n                allowCookies: true,\n                trackingOptOut: false,\n                dataRetentionDays: 365\n            },\n            accessibility: accessibility || {\n                highContrast: false,\n                largeText: false,\n                reducedMotion: false,\n                screenReader: false,\n                keyboardNavigation: true\n            },\n            updatedAt: new Date().toISOString()\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: updatedPreferences,\n            message: \"Preferences updated successfully\",\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"Update preferences error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/user/preferences/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fpreferences%2Froute&page=%2Fapi%2Fuser%2Fpreferences%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fpreferences%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();