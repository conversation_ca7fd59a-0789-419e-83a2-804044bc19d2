import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    const analysisType = formData.get('analysisType') as string || 'general'

    if (!file) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'File is required',
          code: 'MISSING_FILE'
        },
        { status: 400 }
      )
    }

    // Validate file size (max 100MB for demo)
    const maxSize = 100 * 1024 * 1024 // 100MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'File size exceeds maximum limit (100MB)',
          code: 'FILE_TOO_LARGE'
        },
        { status: 400 }
      )
    }

    // Validate analysis type
    const validTypes = ['malware', 'webshell', 'secret', 'general']
    if (!validTypes.includes(analysisType)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid analysis type',
          code: 'INVALID_ANALYSIS_TYPE'
        },
        { status: 400 }
      )
    }

    // Generate analysis ID
    const analysisId = `analysis_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Read file content for analysis
    const fileBuffer = await file.arrayBuffer()
    const fileContent = new TextDecoder().decode(fileBuffer)

    // Simulate analysis delay
    await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000))

    // Perform mock analysis
    const analysisResults = performMockAnalysis(file, fileContent, analysisType)

    return NextResponse.json({
      success: true,
      data: {
        analysisId,
        filename: file.name,
        fileSize: file.size,
        mimeType: file.type,
        hash: generateMockHash(),
        threatLevel: analysisResults.threatLevel,
        results: analysisResults.results
      },
      message: 'File analysis completed successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('File analysis error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    )
  }
}

function performMockAnalysis(file: File, content: string, analysisType: string) {
  const filename = file.name.toLowerCase()
  const extension = filename.split('.').pop() || ''
  
  let threatLevel: 'safe' | 'suspicious' | 'malicious' = 'safe'
  const results: any = {}

  // Malware detection
  if (analysisType === 'malware' || analysisType === 'general') {
    const malwareIndicators = [
      'CreateProcess', 'WriteProcessMemory', 'VirtualAlloc', 'GetProcAddress',
      'LoadLibrary', 'RegCreateKey', 'RegSetValue', 'CreateFile'
    ]
    
    const foundIndicators = malwareIndicators.filter(indicator => 
      content.toLowerCase().includes(indicator.toLowerCase())
    )

    results.malwareDetection = {
      detected: foundIndicators.length > 2 || ['exe', 'dll', 'scr'].includes(extension),
      indicators: foundIndicators,
      confidence: foundIndicators.length > 0 ? Math.min(foundIndicators.length * 20 + 40, 95) : 10
    }

    if (results.malwareDetection.detected) {
      threatLevel = 'malicious'
    }
  }

  // Webshell detection
  if (analysisType === 'webshell' || analysisType === 'general') {
    const webshellIndicators = [
      'eval(', 'exec(', 'system(', 'shell_exec', 'passthru', 'base64_decode',
      '$_GET', '$_POST', '$_REQUEST', 'file_get_contents', 'fwrite', 'fopen'
    ]

    const foundWebshellIndicators = webshellIndicators.filter(indicator => 
      content.toLowerCase().includes(indicator.toLowerCase())
    )

    const isPhpFile = ['php', 'php3', 'php4', 'php5', 'phtml'].includes(extension)
    const hasObfuscation = content.includes('base64_decode') || content.includes('str_rot13')

    results.webshellDetection = {
      detected: (foundWebshellIndicators.length > 3 && isPhpFile) || hasObfuscation,
      type: isPhpFile ? 'PHP Webshell' : 'Generic Webshell',
      obfuscated: hasObfuscation,
      indicators: foundWebshellIndicators,
      confidence: foundWebshellIndicators.length > 0 ? Math.min(foundWebshellIndicators.length * 15 + 30, 90) : 5
    }

    if (results.webshellDetection.detected && threatLevel === 'safe') {
      threatLevel = 'suspicious'
    }
  }

  // Secret detection
  if (analysisType === 'secret' || analysisType === 'general') {
    const secrets = detectSecrets(content)
    
    results.secretDetection = {
      detected: secrets.length > 0,
      secrets: secrets,
      totalFound: secrets.length
    }

    if (secrets.length > 0 && threatLevel === 'safe') {
      threatLevel = 'suspicious'
    }
  }

  // General file analysis
  if (analysisType === 'general') {
    results.fileAnalysis = {
      fileType: getFileType(extension),
      encoding: 'UTF-8',
      lineCount: content.split('\n').length,
      characterCount: content.length,
      suspiciousPatterns: findSuspiciousPatterns(content),
      entropy: calculateEntropy(content)
    }
  }

  return { threatLevel, results }
}

function detectSecrets(content: string) {
  const secrets = []
  
  // API Keys
  const apiKeyPatterns = [
    /sk-[a-zA-Z0-9]{48}/g, // OpenAI API keys
    /AIza[0-9A-Za-z\\-_]{35}/g, // Google API keys
    /AKIA[0-9A-Z]{16}/g, // AWS Access Keys
    /ghp_[a-zA-Z0-9]{36}/g, // GitHub Personal Access Tokens
  ]

  apiKeyPatterns.forEach(pattern => {
    const matches = content.match(pattern)
    if (matches) {
      matches.forEach(match => {
        secrets.push({
          type: 'API Key',
          value: match.substring(0, 10) + '...',
          confidence: 90,
          line: findLineNumber(content, match)
        })
      })
    }
  })

  // Passwords
  const passwordPatterns = [
    /password\s*[:=]\s*["']([^"']{6,})["']/gi,
    /pwd\s*[:=]\s*["']([^"']{6,})["']/gi,
    /pass\s*[:=]\s*["']([^"']{6,})["']/gi
  ]

  passwordPatterns.forEach(pattern => {
    const matches = [...content.matchAll(pattern)]
    matches.forEach(match => {
      secrets.push({
        type: 'Password',
        value: '***hidden***',
        confidence: 75,
        line: findLineNumber(content, match[0])
      })
    })
  })

  // Database URLs
  const dbUrlPattern = /(mysql|postgresql|mongodb):\/\/[^\s"']+/gi
  const dbMatches = [...content.matchAll(dbUrlPattern)]
  dbMatches.forEach(match => {
    secrets.push({
      type: 'Database URL',
      value: match[0].substring(0, 20) + '...',
      confidence: 85,
      line: findLineNumber(content, match[0])
    })
  })

  return secrets
}

function findLineNumber(content: string, searchString: string): number {
  const lines = content.substring(0, content.indexOf(searchString)).split('\n')
  return lines.length
}

function getFileType(extension: string): string {
  const types: { [key: string]: string } = {
    'php': 'PHP Script',
    'js': 'JavaScript',
    'py': 'Python Script',
    'txt': 'Text File',
    'html': 'HTML Document',
    'css': 'CSS Stylesheet',
    'sql': 'SQL Script',
    'json': 'JSON Data',
    'xml': 'XML Document',
    'exe': 'Executable',
    'dll': 'Dynamic Library',
    'zip': 'Archive',
    'pdf': 'PDF Document'
  }
  return types[extension] || 'Unknown'
}

function findSuspiciousPatterns(content: string): string[] {
  const patterns = [
    'eval(',
    'exec(',
    'system(',
    'shell_exec',
    'base64_decode',
    'file_get_contents',
    'curl_exec',
    'fsockopen',
    'socket_create'
  ]

  return patterns.filter(pattern => 
    content.toLowerCase().includes(pattern.toLowerCase())
  )
}

function calculateEntropy(content: string): number {
  const freq: { [key: string]: number } = {}
  
  for (const char of content) {
    freq[char] = (freq[char] || 0) + 1
  }

  let entropy = 0
  const length = content.length

  for (const count of Object.values(freq)) {
    const probability = count / length
    entropy -= probability * Math.log2(probability)
  }

  return Math.round(entropy * 100) / 100
}

function generateMockHash(): string {
  return 'sha256:' + Array.from({ length: 64 }, () => 
    Math.floor(Math.random() * 16).toString(16)
  ).join('')
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
