import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('query') || ''
    const severity = searchParams.get('severity') || ''
    const year = searchParams.get('year') || ''
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Validate limit
    if (limit > 100) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Limit cannot exceed 100',
          code: 'LIMIT_EXCEEDED'
        },
        { status: 400 }
      )
    }

    // Simulate search delay
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000))

    // Generate mock CVE data
    const mockCVEs = generateMockCVEs(query, severity, year, limit, offset)
    const totalCVEs = 189234 // Mock total count

    return NextResponse.json({
      success: true,
      data: {
        query,
        filters: {
          severity,
          year
        },
        total: totalCVEs,
        limit,
        offset,
        cves: mockCVEs
      },
      message: 'CVE search completed successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('CVE search error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    )
  }
}

function generateMockCVEs(query: string, severity: string, year: string, limit: number, offset: number) {
  const cves = []
  const severities = severity ? [severity] : ['critical', 'high', 'medium', 'low']
  const years = year ? [year] : ['2024', '2023', '2022', '2021']
  
  for (let i = 0; i < limit; i++) {
    const cveYear = years[Math.floor(Math.random() * years.length)]
    const cveSeverity = severities[Math.floor(Math.random() * severities.length)]
    const cveNumber = (offset + i + 1).toString().padStart(4, '0')
    
    const cve = {
      cveId: `CVE-${cveYear}-${cveNumber}`,
      description: generateCVEDescription(query, cveSeverity),
      severity: cveSeverity,
      cvssScore: generateCVSSScore(cveSeverity),
      cvssVector: generateCVSSVector(),
      publishedDate: generateRandomDate(cveYear),
      modifiedDate: generateRandomDate(cveYear, true),
      affectedProducts: generateAffectedProducts(),
      references: generateReferences(cveYear, cveNumber),
      exploits: generateExploits(cveSeverity),
      trending: Math.random() > 0.8 // 20% chance of being trending
    }
    
    cves.push(cve)
  }
  
  return cves
}

function generateCVEDescription(query: string, severity: string) {
  const vulnerabilityTypes = [
    'SQL injection', 'Cross-site scripting (XSS)', 'Remote code execution',
    'Buffer overflow', 'Authentication bypass', 'Path traversal',
    'Cross-site request forgery (CSRF)', 'Server-side request forgery (SSRF)',
    'XML external entity (XXE)', 'Insecure direct object reference'
  ]
  
  const products = [
    'WordPress Plugin', 'Apache HTTP Server', 'Microsoft Windows',
    'Linux Kernel', 'PHP Framework', 'Node.js Package',
    'Java Library', 'Python Package', 'Web Application',
    'Mobile Application', 'Database System', 'Network Device'
  ]
  
  const vulnType = vulnerabilityTypes[Math.floor(Math.random() * vulnerabilityTypes.length)]
  const product = products[Math.floor(Math.random() * products.length)]
  
  let description = `${vulnType} vulnerability in ${product}`
  
  if (query && query.length > 0) {
    description += ` related to ${query}`
  }
  
  switch (severity) {
    case 'critical':
      description += ' allows remote attackers to execute arbitrary code with system privileges'
      break
    case 'high':
      description += ' allows remote attackers to gain unauthorized access'
      break
    case 'medium':
      description += ' allows attackers to bypass security restrictions'
      break
    case 'low':
      description += ' may allow information disclosure'
      break
  }
  
  return description + '.'
}

function generateCVSSScore(severity: string): number {
  const ranges = {
    critical: [9.0, 10.0],
    high: [7.0, 8.9],
    medium: [4.0, 6.9],
    low: [0.1, 3.9]
  }
  
  const range = ranges[severity as keyof typeof ranges] || [0.1, 10.0]
  const score = Math.random() * (range[1] - range[0]) + range[0]
  return Math.round(score * 10) / 10
}

function generateCVSSVector(): string {
  const vectors = [
    'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H',
    'CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H',
    'CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:H/I:H/A:H',
    'CVSS:3.1/AV:L/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H',
    'CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N'
  ]
  
  return vectors[Math.floor(Math.random() * vectors.length)]
}

function generateRandomDate(year: string, isModified = false): string {
  const startDate = new Date(`${year}-01-01`)
  const endDate = isModified ? new Date() : new Date(`${year}-12-31`)
  
  const randomTime = startDate.getTime() + Math.random() * (endDate.getTime() - startDate.getTime())
  return new Date(randomTime).toISOString().split('T')[0]
}

function generateAffectedProducts(): string[] {
  const products = [
    'WebApp Framework 1.0-2.5',
    'CMS Platform 3.x',
    'E-commerce Plugin 2.1-2.3',
    'API Gateway 1.x-2.x',
    'File Manager Pro 3.0',
    'Security Scanner 4.2',
    'Database Connector 1.5',
    'Authentication Service 2.0'
  ]
  
  const count = Math.floor(Math.random() * 3) + 1
  const selected = []
  
  for (let i = 0; i < count; i++) {
    const product = products[Math.floor(Math.random() * products.length)]
    if (!selected.includes(product)) {
      selected.push(product)
    }
  }
  
  return selected
}

function generateReferences(year: string, cveNumber: string): string[] {
  return [
    `https://nvd.nist.gov/vuln/detail/CVE-${year}-${cveNumber}`,
    `https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-${year}-${cveNumber}`,
    `https://security-tracker.debian.org/tracker/CVE-${year}-${cveNumber}`,
    `https://access.redhat.com/security/cve/CVE-${year}-${cveNumber}`
  ]
}

function generateExploits(severity: string) {
  if (severity === 'low' || Math.random() > 0.4) {
    return []
  }
  
  const exploits = []
  const exploitCount = severity === 'critical' ? Math.floor(Math.random() * 3) + 1 : Math.floor(Math.random() * 2) + 1
  
  for (let i = 0; i < exploitCount; i++) {
    exploits.push({
      title: `${severity.charAt(0).toUpperCase() + severity.slice(1)} Exploit ${i + 1}`,
      url: `https://exploit-db.com/exploits/${Math.floor(Math.random() * 50000) + 10000}`,
      type: Math.random() > 0.5 ? 'Public Exploit' : 'Proof of Concept'
    })
  }
  
  return exploits
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
