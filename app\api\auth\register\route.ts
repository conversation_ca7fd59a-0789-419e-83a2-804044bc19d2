import { NextRequest, NextResponse } from 'next/server'
import { AuthService } from '@/lib/auth'
import { Database } from '@/lib/database'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { username, email, password, fullName } = body

    // Validate input
    if (!username || !email || !password) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Username, email and password are required',
          code: 'MISSING_FIELDS'
        },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid email format',
          code: 'INVALID_EMAIL'
        },
        { status: 400 }
      )
    }

    // Validate password strength
    if (password.length < 6) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Password must be at least 6 characters long',
          code: 'WEAK_PASSWORD'
        },
        { status: 400 }
      )
    }

    // Use real authentication service
    const result = await AuthService.register({
      username,
      email,
      password,
      fullName
    })

    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          error: result.error || 'Registration failed',
          code: result.error?.includes('exists') ? 'EMAIL_EXISTS' : 'REGISTRATION_FAILED'
        },
        { status: 409 }
      )
    }

    // Get user plan information
    const db = Database.getInstance()
    const planInfo = await db.query(
      'SELECT name, slug FROM plans WHERE id = ?',
      [result.user?.planId]
    )

    const userData = {
      id: result.user?.id,
      username: result.user?.username,
      email: result.user?.email,
      fullName: result.user?.fullName,
      role: result.user?.role,
      plan: planInfo[0]?.slug || 'gratis',
      planName: planInfo[0]?.name || 'Gratis',
      isActive: result.user?.isActive,
      emailVerified: result.user?.emailVerified,
      createdAt: result.user?.createdAt,
      lastLogin: null
    }

    const response = NextResponse.json({
      success: true,
      data: {
        token: result.token,
        user: userData,
        expiresIn: '7d'
      },
      message: 'Registration successful',
      timestamp: new Date().toISOString()
    })

    // Set HTTP-only cookie for token
    response.cookies.set('auth-token', result.token!, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 // 24 hours
    })

    return response

  } catch (error) {
    console.error('Registration error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
