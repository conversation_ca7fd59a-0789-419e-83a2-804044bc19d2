'use client'

import { useState, useEffect } from 'react'
import DashboardLayout from '@/components/DashboardLayout'
import { Card, StatCard, AlertCard } from '@/components/Card'
import { 
  Search, 
  User, 
  Phone, 
  Mail, 
  Globe, 
  MapPin, 
  CreditCard,
  Shield,
  Eye,
  Database,
  Clock,
  Download,
  Filter,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react'

interface OSINTResult {
  id: string
  type: string
  source: string
  data: any
  confidence: number
  timestamp: string
  status: 'found' | 'not_found' | 'error'
}

export default function OSINTPage() {
  const [user] = useState({
    username: 'admin',
    avatar: '',
    role: 'super_admin',
    plan: 'cybersecurity'
  })

  const [searchType, setSearchType] = useState('email')
  const [searchQuery, setSearchQuery] = useState('')
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<OSINTResult[]>([])
  const [stats, setStats] = useState({
    totalSearches: 1247,
    successfulFinds: 892,
    dataSourcesActive: 15,
    lastUpdate: '2 minutes ago'
  })

  const searchTypes = [
    { id: 'email', name: 'Email Address', icon: Mail, placeholder: '<EMAIL>' },
    { id: 'phone', name: 'Phone Number', icon: Phone, placeholder: '+62812345678' },
    { id: 'nik', name: 'NIK', icon: CreditCard, placeholder: '1234567890123456' },
    { id: 'npwp', name: 'NPWP', icon: CreditCard, placeholder: '12.345.678.9-012.345' },
    { id: 'name', name: 'Full Name', icon: User, placeholder: 'John Doe' },
    { id: 'domain', name: 'Domain', icon: Globe, placeholder: 'example.com' },
    { id: 'imei', name: 'IMEI', icon: Phone, placeholder: '123456789012345' },
    { id: 'address', name: 'Address', icon: MapPin, placeholder: 'Jakarta, Indonesia' }
  ]

  const dataSources = [
    { name: 'Dukcapil Database', status: 'active', lastUpdate: '1 hour ago' },
    { name: 'Kemkes Database', status: 'active', lastUpdate: '30 minutes ago' },
    { name: 'GitHub Leaked Data', status: 'active', lastUpdate: '5 minutes ago' },
    { name: 'Social Media APIs', status: 'active', lastUpdate: '2 minutes ago' },
    { name: 'Domain WHOIS', status: 'active', lastUpdate: '1 minute ago' },
    { name: 'Phone Number DB', status: 'maintenance', lastUpdate: '2 hours ago' },
    { name: 'Email Breach DB', status: 'active', lastUpdate: '10 minutes ago' },
    { name: 'Location Services', status: 'active', lastUpdate: '3 minutes ago' }
  ]

  const handleSearch = async () => {
    if (!searchQuery.trim()) return

    setLoading(true)
    setResults([])

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Mock results based on search type
      const mockResults: OSINTResult[] = [
        {
          id: '1',
          type: searchType,
          source: 'Dukcapil Database',
          data: {
            name: 'John Doe',
            email: searchQuery,
            phone: '+62812345678',
            address: 'Jakarta, Indonesia',
            verified: true
          },
          confidence: 95,
          timestamp: new Date().toISOString(),
          status: 'found'
        },
        {
          id: '2',
          type: searchType,
          source: 'Social Media',
          data: {
            platform: 'LinkedIn',
            profile: 'https://linkedin.com/in/johndoe',
            company: 'Tech Corp',
            location: 'Jakarta'
          },
          confidence: 87,
          timestamp: new Date().toISOString(),
          status: 'found'
        },
        {
          id: '3',
          type: searchType,
          source: 'GitHub',
          data: {
            username: 'johndoe',
            repositories: 15,
            followers: 42,
            email_exposed: true
          },
          confidence: 78,
          timestamp: new Date().toISOString(),
          status: 'found'
        }
      ]

      setResults(mockResults)
    } catch (error) {
      console.error('Search failed:', error)
    } finally {
      setLoading(false)
    }
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return 'text-green-400'
    if (confidence >= 70) return 'text-yellow-400'
    return 'text-red-400'
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'found': return CheckCircle
      case 'not_found': return XCircle
      case 'error': return AlertTriangle
      default: return Clock
    }
  }

  const exportResults = () => {
    const dataStr = JSON.stringify(results, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `osint-results-${Date.now()}.json`
    link.click()
  }

  return (
    <DashboardLayout user={user} title="OSINT Investigator">
      <div>
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <Search className="h-8 w-8 text-cyber-green" />
              <h1 className="text-3xl font-bold font-cyber text-white">
                OSINT <span className="cyber-text">Investigator</span>
              </h1>
            </div>
            <p className="text-gray-400 max-w-3xl">
              Investigasi mendalam dengan pencarian NIK, NPWP, nomor HP, email, domain, dan tracking lokasi real-time. 
              Akses database leaked Dukcapil, Kemkes, GitHub dan sumber data lainnya.
            </p>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <StatCard
              title="Total Searches"
              value={stats.totalSearches}
              icon={Search}
              color="green"
              trend={{ value: 12, isPositive: true }}
            />
            <StatCard
              title="Successful Finds"
              value={stats.successfulFinds}
              icon={CheckCircle}
              color="blue"
              trend={{ value: 8, isPositive: true }}
            />
            <StatCard
              title="Data Sources"
              value={stats.dataSourcesActive}
              icon={Database}
              color="purple"
            />
            <StatCard
              title="Last Update"
              value={stats.lastUpdate}
              icon={Clock}
              color="gold"
            />
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Search Panel */}
            <div className="lg:col-span-2">
              <Card border="green" glow>
                <div className="p-6">
                  <h2 className="text-xl font-bold text-white mb-6">Search Configuration</h2>
                  
                  {/* Search Type Selection */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-300 mb-3">
                      Search Type
                    </label>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                      {searchTypes.map((type) => {
                        const Icon = type.icon
                        return (
                          <button
                            key={type.id}
                            onClick={() => setSearchType(type.id)}
                            className={`p-3 rounded-lg border transition-all duration-200 ${
                              searchType === type.id
                                ? 'border-cyber-green bg-cyber-green/10 text-cyber-green'
                                : 'border-gray-600 hover:border-gray-500 text-gray-300'
                            }`}
                          >
                            <Icon className="h-5 w-5 mx-auto mb-2" />
                            <div className="text-xs font-medium">{type.name}</div>
                          </button>
                        )
                      })}
                    </div>
                  </div>

                  {/* Search Input */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Search Query
                    </label>
                    <div className="flex space-x-3">
                      <input
                        type="text"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        placeholder={searchTypes.find(t => t.id === searchType)?.placeholder}
                        className="cyber-input flex-1"
                        onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                      />
                      <button
                        onClick={handleSearch}
                        disabled={loading || !searchQuery.trim()}
                        className="cyber-btn-primary px-6 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {loading ? (
                          <RefreshCw className="h-5 w-5 animate-spin" />
                        ) : (
                          <Search className="h-5 w-5" />
                        )}
                      </button>
                    </div>
                  </div>

                  {/* Advanced Options */}
                  <div className="border-t border-gray-700 pt-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <label className="flex items-center space-x-2">
                          <input type="checkbox" className="rounded bg-gray-800 border-gray-600" />
                          <span className="text-sm text-gray-300">Deep Search</span>
                        </label>
                        <label className="flex items-center space-x-2">
                          <input type="checkbox" className="rounded bg-gray-800 border-gray-600" />
                          <span className="text-sm text-gray-300">Include Social Media</span>
                        </label>
                      </div>
                      <button className="text-sm text-cyber-green hover:text-cyber-blue transition-colors">
                        <Filter className="h-4 w-4 inline mr-1" />
                        More Filters
                      </button>
                    </div>
                  </div>
                </div>
              </Card>

              {/* Results */}
              {results.length > 0 && (
                <Card className="mt-6">
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-6">
                      <h3 className="text-lg font-bold text-white">
                        Search Results ({results.length})
                      </h3>
                      <button
                        onClick={exportResults}
                        className="cyber-btn text-sm"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Export
                      </button>
                    </div>

                    <div className="space-y-4">
                      {results.map((result) => {
                        const StatusIcon = getStatusIcon(result.status)
                        return (
                          <div
                            key={result.id}
                            className="bg-gray-800/50 rounded-lg p-4 border border-gray-700"
                          >
                            <div className="flex items-start justify-between mb-3">
                              <div className="flex items-center space-x-3">
                                <StatusIcon className={`h-5 w-5 ${
                                  result.status === 'found' ? 'text-green-400' :
                                  result.status === 'not_found' ? 'text-red-400' :
                                  'text-yellow-400'
                                }`} />
                                <div>
                                  <h4 className="font-semibold text-white">{result.source}</h4>
                                  <p className="text-sm text-gray-400">{result.type}</p>
                                </div>
                              </div>
                              <div className="text-right">
                                <div className={`text-sm font-semibold ${getConfidenceColor(result.confidence)}`}>
                                  {result.confidence}% confidence
                                </div>
                                <div className="text-xs text-gray-500">
                                  {new Date(result.timestamp).toLocaleString()}
                                </div>
                              </div>
                            </div>

                            <div className="bg-gray-900/50 rounded p-3">
                              <pre className="text-sm text-gray-300 whitespace-pre-wrap">
                                {JSON.stringify(result.data, null, 2)}
                              </pre>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                </Card>
              )}

              {loading && (
                <Card className="mt-6">
                  <div className="p-8 text-center">
                    <RefreshCw className="h-8 w-8 text-cyber-green animate-spin mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-white mb-2">Searching...</h3>
                    <p className="text-gray-400">Scanning multiple data sources for information</p>
                  </div>
                </Card>
              )}
            </div>

            {/* Data Sources Status */}
            <div>
              <Card>
                <div className="p-6">
                  <h3 className="text-lg font-bold text-white mb-4">Data Sources</h3>
                  <div className="space-y-3">
                    {dataSources.map((source, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                        <div>
                          <div className="font-medium text-white text-sm">{source.name}</div>
                          <div className="text-xs text-gray-400">{source.lastUpdate}</div>
                        </div>
                        <div className={`px-2 py-1 rounded-full text-xs font-semibold ${
                          source.status === 'active' 
                            ? 'bg-green-900/50 text-green-400' 
                            : 'bg-yellow-900/50 text-yellow-400'
                        }`}>
                          {source.status}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </Card>

              {/* Usage Info */}
              <Card className="mt-6" border="gold">
                <div className="p-6">
                  <h3 className="text-lg font-bold text-white mb-4">Plan Usage</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Daily Searches</span>
                      <span className="text-white">Unlimited</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Deep Search</span>
                      <span className="text-cyber-green">✓ Enabled</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Export Results</span>
                      <span className="text-cyber-green">✓ Enabled</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">API Access</span>
                      <span className="text-cyber-green">✓ Enabled</span>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </div>
      </div>
    </DashboardLayout>
  )
}


