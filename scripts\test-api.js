const axios = require('axios')

const BASE_URL = 'http://localhost:3000'

async function testAPI() {
  console.log('🧪 Testing KodeXGuard API...\n')

  try {
    // Test 1: Login
    console.log('1. Testing Login...')
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    })

    if (loginResponse.data.success) {
      console.log('✅ Login successful')
      console.log(`   Token: ${loginResponse.data.data.token.substring(0, 20)}...`)
      console.log(`   User: ${loginResponse.data.data.user.username} (${loginResponse.data.data.user.role})`)
      
      const token = loginResponse.data.data.token

      // Test 2: OSINT Search
      console.log('\n2. Testing OSINT Search...')
      const osintResponse = await axios.post(`${BASE_URL}/api/osint/search`, {
        query: '<EMAIL>',
        type: 'email',
        deepSearch: false,
        sources: []
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (osintResponse.data.success) {
        console.log('✅ OSINT search successful')
        console.log(`   Results: ${osintResponse.data.data.totalResults}`)
        console.log(`   Confidence: ${osintResponse.data.data.confidenceScore}%`)
        console.log(`   Sources: ${osintResponse.data.data.sources.join(', ')}`)
      } else {
        console.log('❌ OSINT search failed:', osintResponse.data.error)
      }

      // Test 3: Vulnerability Scan
      console.log('\n3. Testing Vulnerability Scan...')
      const scanResponse = await axios.post(`${BASE_URL}/api/scan/vulnerability`, {
        target: 'https://example.com',
        scanTypes: ['sqli', 'xss'],
        maxDepth: 2,
        timeout: 30
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (scanResponse.data.success) {
        console.log('✅ Vulnerability scan started')
        console.log(`   Scan ID: ${scanResponse.data.data.scanId}`)
        console.log(`   Target: ${scanResponse.data.data.target}`)
        console.log(`   Types: ${scanResponse.data.data.scanTypes.join(', ')}`)
      } else {
        console.log('❌ Vulnerability scan failed:', scanResponse.data.error)
      }

      // Test 4: File Analysis (without actual file)
      console.log('\n4. Testing File Analysis endpoint...')
      try {
        const fileResponse = await axios.get(`${BASE_URL}/api/file/analyze`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })

        if (fileResponse.data.success) {
          console.log('✅ File analysis history retrieved')
          console.log(`   History items: ${fileResponse.data.data.length}`)
        } else {
          console.log('❌ File analysis failed:', fileResponse.data.error)
        }
      } catch (error) {
        console.log('⚠️  File analysis endpoint test skipped (expected for GET without history)')
      }

    } else {
      console.log('❌ Login failed:', loginResponse.data.error)
    }

  } catch (error) {
    console.error('❌ API test failed:', error.response?.data || error.message)
  }

  console.log('\n🏁 API testing completed!')
}

// Run tests
testAPI().catch(console.error)
