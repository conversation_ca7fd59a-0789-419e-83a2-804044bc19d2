import { NextRequest, NextResponse } from 'next/server'

interface ApiKey {
  id: string
  name: string
  keyPrefix: string
  fullKey?: string // only shown when creating
  isActive: boolean
  permissions: string[]
  lastUsed?: string
  usageCount: number
  rateLimit: number
  allowedIPs: string[]
  createdAt: string
  expiresAt?: string
}

export async function GET(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Simple token validation for testing
    if (!token.startsWith('kxg_')) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    // Mock API keys data
    const apiKeys: ApiKey[] = [
      {
        id: 'key_001',
        name: 'Production API',
        keyPrefix: 'kxg_prod_',
        isActive: true,
        permissions: ['scan:read', 'scan:write', 'osint:read', 'osint:write', 'notifications:read'],
        lastUsed: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
        usageCount: 15420,
        rateLimit: 1000,
        allowedIPs: ['*************', '*********'],
        createdAt: new Date(Date.now() - 86400000 * 180).toISOString(), // 6 months ago
        expiresAt: new Date(Date.now() + 86400000 * 185).toISOString() // 6 months from now
      },
      {
        id: 'key_002',
        name: 'Development API',
        keyPrefix: 'kxg_dev_',
        isActive: true,
        permissions: ['scan:read', 'osint:read', 'notifications:read'],
        lastUsed: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
        usageCount: 2340,
        rateLimit: 100,
        allowedIPs: ['127.0.0.1', '***********/24'],
        createdAt: new Date(Date.now() - 86400000 * 30).toISOString(), // 1 month ago
      },
      {
        id: 'key_003',
        name: 'Mobile App API',
        keyPrefix: 'kxg_mobile_',
        isActive: false,
        permissions: ['scan:read', 'osint:read'],
        lastUsed: new Date(Date.now() - 86400000 * 7).toISOString(), // 1 week ago
        usageCount: 890,
        rateLimit: 500,
        allowedIPs: [],
        createdAt: new Date(Date.now() - 86400000 * 60).toISOString(), // 2 months ago
        expiresAt: new Date(Date.now() + 86400000 * 305).toISOString() // 10 months from now
      },
      {
        id: 'key_004',
        name: 'Webhook Integration',
        keyPrefix: 'kxg_webhook_',
        isActive: true,
        permissions: ['notifications:write', 'scan:read'],
        lastUsed: new Date(Date.now() - 1800000).toISOString(), // 30 minutes ago
        usageCount: 5670,
        rateLimit: 200,
        allowedIPs: ['webhook.example.com'],
        createdAt: new Date(Date.now() - 86400000 * 45).toISOString(), // 1.5 months ago
      }
    ]

    return NextResponse.json({
      success: true,
      data: apiKeys,
      meta: {
        total: apiKeys.length,
        active: apiKeys.filter(key => key.isActive).length,
        inactive: apiKeys.filter(key => !key.isActive).length,
        totalUsage: apiKeys.reduce((sum, key) => sum + key.usageCount, 0)
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('API keys API error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Simple token validation for testing
    if (!token.startsWith('kxg_')) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    const body = await request.json()
    const { name, permissions, rateLimit, allowedIPs, expiresAt } = body

    // Validate input
    if (!name || !permissions || !Array.isArray(permissions)) {
      return NextResponse.json({
        success: false,
        error: 'Name and permissions array are required'
      }, { status: 400 })
    }

    // Generate new API key
    const keyId = `key_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const keyPrefix = `kxg_${name.toLowerCase().replace(/\s+/g, '_')}_`
    const fullKey = `${keyPrefix}${Math.random().toString(36).substr(2, 32)}`

    const newApiKey: ApiKey = {
      id: keyId,
      name,
      keyPrefix,
      fullKey, // Only shown when creating
      isActive: true,
      permissions,
      usageCount: 0,
      rateLimit: rateLimit || 1000,
      allowedIPs: allowedIPs || [],
      createdAt: new Date().toISOString(),
      expiresAt
    }

    // In a real app, save to database
    // For demo, just return the created key
    
    return NextResponse.json({
      success: true,
      data: newApiKey,
      message: 'API key created successfully. Please save the full key as it will not be shown again.',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Create API key error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Simple token validation for testing
    if (!token.startsWith('kxg_')) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    const body = await request.json()
    const { keyId } = body

    if (!keyId) {
      return NextResponse.json({
        success: false,
        error: 'Key ID is required'
      }, { status: 400 })
    }

    // In a real app, delete the API key from database
    // For demo, just return success
    
    return NextResponse.json({
      success: true,
      message: `API key ${keyId} deleted successfully`,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Delete API key error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}
