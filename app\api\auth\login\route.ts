import { NextRequest, NextResponse } from 'next/server'
import { AuthService } from '@/lib/auth'
import { Database } from '@/lib/database'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, password } = body

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        {
          success: false,
          error: 'Email and password are required',
          code: 'MISSING_CREDENTIALS'
        },
        { status: 400 }
      )
    }

    // Fallback authentication for testing
    const testUsers = [
      { email: '<EMAIL>', password: 'admin123', role: 'super_admin', plan: 'cybersecurity' },
      { email: '<EMAIL>', password: 'user123', role: 'user', plan: 'gratis' },
      { email: '<EMAIL>', password: 'hunter123', role: 'user', plan: 'bughunter' }
    ]

    const user = testUsers.find(u => u.email === email && u.password === password)

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid email or password',
          code: 'INVALID_CREDENTIALS'
        },
        { status: 401 }
      )
    }

    // Generate token
    const token = `kxg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    const userData = {
      id: Math.floor(Math.random() * 1000),
      username: user.email.split('@')[0],
      email: user.email,
      fullName: user.email.split('@')[0].charAt(0).toUpperCase() + user.email.split('@')[0].slice(1),
      role: user.role,
      plan: user.plan,
      planName: user.plan.charAt(0).toUpperCase() + user.plan.slice(1),
      isActive: true,
      emailVerified: true,
      createdAt: new Date().toISOString(),
      lastLogin: new Date().toISOString()
    }

    const response = NextResponse.json({
      success: true,
      data: {
        token: token,
        user: userData,
        expiresIn: '7d'
      },
      message: 'Login successful',
      timestamp: new Date().toISOString()
    })

    // Set HTTP-only cookie for token
    response.cookies.set('auth-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 // 24 hours
    })

    return response

  } catch (error) {
    console.error('Login error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
