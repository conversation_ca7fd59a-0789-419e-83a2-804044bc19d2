"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/osint/recent/route";
exports.ids = ["app/api/osint/recent/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fosint%2Frecent%2Froute&page=%2Fapi%2Fosint%2Frecent%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fosint%2Frecent%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fosint%2Frecent%2Froute&page=%2Fapi%2Fosint%2Frecent%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fosint%2Frecent%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Users_Downloads_kodeXGuard_app_api_osint_recent_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/osint/recent/route.ts */ \"(rsc)/./app/api/osint/recent/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/osint/recent/route\",\n        pathname: \"/api/osint/recent\",\n        filename: \"route\",\n        bundlePath: \"app/api/osint/recent/route\"\n    },\n    resolvedPagePath: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\api\\\\osint\\\\recent\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Users_Downloads_kodeXGuard_app_api_osint_recent_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/osint/recent/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fosint%2Frecent%2Froute&page=%2Fapi%2Fosint%2Frecent%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fosint%2Frecent%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/osint/recent/route.ts":
/*!***************************************!*\
  !*** ./app/api/osint/recent/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function GET(request) {\n    try {\n        // Get auth token\n        const authHeader = request.headers.get(\"authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        // Simple token validation for testing\n        if (!token.startsWith(\"kxg_\")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid token\"\n            }, {\n                status: 401\n            });\n        }\n        // Mock recent searches data\n        const recentSearches = [\n            {\n                id: \"search_001\",\n                query: \"<EMAIL>\",\n                type: \"email\",\n                results: 5,\n                confidence: 92,\n                timestamp: new Date(Date.now() - 1800000).toISOString(),\n                status: \"completed\",\n                sources: [\n                    \"Dukcapil Database\",\n                    \"Social Media\",\n                    \"GitHub\",\n                    \"Email Breach DB\"\n                ]\n            },\n            {\n                id: \"search_002\",\n                query: \"+628123456789\",\n                type: \"phone\",\n                results: 3,\n                confidence: 87,\n                timestamp: new Date(Date.now() - 3600000).toISOString(),\n                status: \"completed\",\n                sources: [\n                    \"Phone Number DB\",\n                    \"Social Media\",\n                    \"Location Services\"\n                ]\n            },\n            {\n                id: \"search_003\",\n                query: \"1234567890123456\",\n                type: \"nik\",\n                results: 2,\n                confidence: 95,\n                timestamp: new Date(Date.now() - 5400000).toISOString(),\n                status: \"completed\",\n                sources: [\n                    \"Dukcapil Database\",\n                    \"Kemkes Database\"\n                ]\n            },\n            {\n                id: \"search_004\",\n                query: \"example.com\",\n                type: \"domain\",\n                results: 8,\n                confidence: 78,\n                timestamp: new Date(Date.now() - 7200000).toISOString(),\n                status: \"completed\",\n                sources: [\n                    \"Domain WHOIS\",\n                    \"GitHub\",\n                    \"Social Media\",\n                    \"Email Breach DB\"\n                ]\n            },\n            {\n                id: \"search_005\",\n                query: \"Jane Smith\",\n                type: \"name\",\n                results: 12,\n                confidence: 65,\n                timestamp: new Date(Date.now() - 10800000).toISOString(),\n                status: \"partial\",\n                sources: [\n                    \"Social Media\",\n                    \"GitHub\",\n                    \"Location Services\"\n                ]\n            },\n            {\n                id: \"search_006\",\n                query: \"12.345.678.9-012.345\",\n                type: \"npwp\",\n                results: 1,\n                confidence: 98,\n                timestamp: new Date(Date.now() - 14400000).toISOString(),\n                status: \"completed\",\n                sources: [\n                    \"Dukcapil Database\"\n                ]\n            },\n            {\n                id: \"search_007\",\n                query: \"123456789012345\",\n                type: \"imei\",\n                results: 0,\n                confidence: 0,\n                timestamp: new Date(Date.now() - 18000000).toISOString(),\n                status: \"failed\",\n                sources: []\n            },\n            {\n                id: \"search_008\",\n                query: \"Jakarta Selatan\",\n                type: \"address\",\n                results: 25,\n                confidence: 45,\n                timestamp: new Date(Date.now() - 21600000).toISOString(),\n                status: \"partial\",\n                sources: [\n                    \"Location Services\",\n                    \"Social Media\",\n                    \"Dukcapil Database\"\n                ]\n            },\n            {\n                id: \"search_009\",\n                query: \"<EMAIL>\",\n                type: \"email\",\n                results: 7,\n                confidence: 89,\n                timestamp: new Date(Date.now() - 25200000).toISOString(),\n                status: \"completed\",\n                sources: [\n                    \"Email Breach DB\",\n                    \"GitHub\",\n                    \"Social Media\"\n                ]\n            },\n            {\n                id: \"search_010\",\n                query: \"+6281987654321\",\n                type: \"phone\",\n                results: 4,\n                confidence: 82,\n                timestamp: new Date(Date.now() - 28800000).toISOString(),\n                status: \"completed\",\n                sources: [\n                    \"Phone Number DB\",\n                    \"Social Media\",\n                    \"Location Services\",\n                    \"Dukcapil Database\"\n                ]\n            }\n        ];\n        // Sort by timestamp (newest first)\n        recentSearches.sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());\n        // Get query parameters for filtering\n        const url = new URL(request.url);\n        const limit = parseInt(url.searchParams.get(\"limit\") || \"10\");\n        const type = url.searchParams.get(\"type\");\n        const status = url.searchParams.get(\"status\");\n        let filteredSearches = recentSearches;\n        // Apply filters\n        if (type) {\n            filteredSearches = filteredSearches.filter((search)=>search.type === type);\n        }\n        if (status) {\n            filteredSearches = filteredSearches.filter((search)=>search.status === status);\n        }\n        // Apply limit\n        filteredSearches = filteredSearches.slice(0, limit);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: filteredSearches,\n            meta: {\n                total: recentSearches.length,\n                filtered: filteredSearches.length,\n                completed: recentSearches.filter((s)=>s.status === \"completed\").length,\n                failed: recentSearches.filter((s)=>s.status === \"failed\").length,\n                partial: recentSearches.filter((s)=>s.status === \"partial\").length,\n                averageConfidence: Math.round(recentSearches.filter((s)=>s.status === \"completed\").reduce((sum, s)=>sum + s.confidence, 0) / recentSearches.filter((s)=>s.status === \"completed\").length),\n                searchTypes: {\n                    email: recentSearches.filter((s)=>s.type === \"email\").length,\n                    phone: recentSearches.filter((s)=>s.type === \"phone\").length,\n                    nik: recentSearches.filter((s)=>s.type === \"nik\").length,\n                    npwp: recentSearches.filter((s)=>s.type === \"npwp\").length,\n                    name: recentSearches.filter((s)=>s.type === \"name\").length,\n                    domain: recentSearches.filter((s)=>s.type === \"domain\").length,\n                    imei: recentSearches.filter((s)=>s.type === \"imei\").length,\n                    address: recentSearches.filter((s)=>s.type === \"address\").length\n                }\n            },\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"OSINT recent searches API error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request) {\n    try {\n        // Get auth token\n        const authHeader = request.headers.get(\"authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        // Simple token validation for testing\n        if (!token.startsWith(\"kxg_\")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid token\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { searchId } = body;\n        if (!searchId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Search ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        // In a real app, delete the search from database\n        // For demo, just return success\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: `Search ${searchId} deleted successfully`,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"Delete search error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./app/api/osint/recent/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fosint%2Frecent%2Froute&page=%2Fapi%2Fosint%2Frecent%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fosint%2Frecent%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();