(()=>{var e={};e.id=2626,e.ids=[2626],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10929:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>u,tree:()=>c}),t(94687),t(30829),t(35866);var r=t(23191),a=t(88716),n=t(37922),l=t.n(n),i=t(95231),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(s,d);let c=["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,94687)),"D:\\Users\\Downloads\\kodeXGuard\\app\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\Users\\Downloads\\kodeXGuard\\app\\login\\page.tsx"],m="/login/page",x={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},67552:(e,s,t)=>{Promise.resolve().then(t.bind(t,1442))},1442:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var r=t(10326),a=t(17577),n=t(90434),l=t(35047),i=t(58038),d=t(5932),c=t(9015),o=t(91216),m=t(12714),x=t(2262),u=t(67382);function p(){let e=(0,l.useRouter)(),{login:s,isAuthenticated:t,isLoading:p}=(0,u.a)(),[h,g]=(0,a.useState)({email:"",password:"",rememberMe:!1}),[y,b]=(0,a.useState)(!1),[j,f]=(0,a.useState)(!1),[v,N]=(0,a.useState)(""),w=async t=>{t.preventDefault(),f(!0),N("");try{await s(h.email,h.password)?e.push("/dashboard"):N("Invalid email or password")}catch(e){N("Network error. Please try again.")}finally{f(!1)}},k=e=>{let{name:s,value:t,type:r,checked:a}=e.target;g(e=>({...e,[s]:"checkbox"===r?a:t}))};return(0,r.jsxs)("div",{className:"min-h-screen flex items-center justify-center px-4 py-12",children:[r.jsx("div",{className:"absolute inset-0 bg-gradient-cyber opacity-50"}),r.jsx("div",{className:"absolute inset-0 nusantara-pattern opacity-5"}),(0,r.jsxs)("div",{className:"relative z-10 w-full max-w-md",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsxs)(n.default,{href:"/",className:"inline-flex items-center space-x-2",children:[r.jsx(i.Z,{className:"h-12 w-12 text-cyber-green animate-pulse"}),(0,r.jsxs)("span",{className:"text-3xl font-bold font-cyber",children:[r.jsx("span",{className:"cyber-text",children:"Kode"}),r.jsx("span",{className:"nusantara-gold",children:"X"}),r.jsx("span",{className:"text-white",children:"Guard"})]})]}),r.jsx("p",{className:"mt-2 text-gray-400",children:"Masuk ke akun Anda"})]}),r.jsx(x.Zb,{border:"green",glow:!0,children:(0,r.jsxs)("div",{className:"p-8",children:[v&&r.jsx("div",{className:"mb-6",children:r.jsx(x.XO,{type:"error",title:"Login Gagal",message:v,onClose:()=>N("")})}),(0,r.jsxs)("form",{onSubmit:w,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-300 mb-2",children:"Email Address"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:r.jsx(d.Z,{className:"h-5 w-5 text-gray-400"})}),r.jsx("input",{id:"email",name:"email",type:"email",required:!0,value:h.email,onChange:k,className:"cyber-input pl-10 w-full",placeholder:"<EMAIL>"})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-300 mb-2",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:r.jsx(c.Z,{className:"h-5 w-5 text-gray-400"})}),r.jsx("input",{id:"password",name:"password",type:y?"text":"password",required:!0,value:h.password,onChange:k,className:"cyber-input pl-10 pr-10 w-full",placeholder:"••••••••"}),r.jsx("button",{type:"button",onClick:()=>b(!y),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-cyber-green",children:y?r.jsx(o.Z,{className:"h-5 w-5"}):r.jsx(m.Z,{className:"h-5 w-5"})})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("input",{id:"rememberMe",name:"rememberMe",type:"checkbox",checked:h.rememberMe,onChange:k,className:"h-4 w-4 text-cyber-green focus:ring-cyber-green border-gray-600 rounded bg-gray-800"}),r.jsx("label",{htmlFor:"rememberMe",className:"ml-2 block text-sm text-gray-300",children:"Remember me"})]}),r.jsx(n.default,{href:"/forgot-password",className:"text-sm text-cyber-green hover:text-cyber-blue transition-colors",children:"Forgot password?"})]}),r.jsx("button",{type:"submit",disabled:j,className:"w-full cyber-btn-primary py-3 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed",children:j?(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[r.jsx("div",{className:"w-5 h-5 border-2 border-black border-t-transparent rounded-full animate-spin"}),r.jsx("span",{children:"Signing in..."})]}):"Sign In"})]}),(0,r.jsxs)("div",{className:"mt-6 p-4 bg-gray-800/50 rounded-lg border border-gray-700",children:[r.jsx("h4",{className:"text-sm font-semibold text-cyber-green mb-2",children:"Demo Credentials:"}),(0,r.jsxs)("div",{className:"text-xs text-gray-400 space-y-1",children:[(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"Admin:"})," <EMAIL> / admin123"]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"User:"})," <EMAIL> / admin123"]})]})]}),r.jsx("div",{className:"mt-6 text-center",children:(0,r.jsxs)("p",{className:"text-gray-400",children:["Don't have an account?"," ",r.jsx(n.default,{href:"/register",className:"text-cyber-green hover:text-cyber-blue transition-colors font-semibold",children:"Sign up here"})]})})]})}),r.jsx("div",{className:"mt-8 text-center",children:(0,r.jsxs)("p",{className:"text-gray-500 text-sm",children:["By signing in, you agree to our"," ",r.jsx(n.default,{href:"/terms",className:"text-cyber-green hover:underline",children:"Terms of Service"})," ","and"," ",r.jsx(n.default,{href:"/privacy",className:"text-cyber-green hover:underline",children:"Privacy Policy"})]})})]})]})}},91216:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},12714:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},9015:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},5932:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},58038:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},35047:(e,s,t)=>{"use strict";var r=t(77389);t.o(r,"usePathname")&&t.d(s,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}})},94687:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\login\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[9276,82,434,7608],()=>t(10929));module.exports=r})();