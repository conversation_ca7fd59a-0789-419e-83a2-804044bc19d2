'use client'

import { useState } from 'react'
import DashboardLayout from '@/components/DashboardLayout'
import { Card } from '@/components/Card'
import {
  Shield,
  Users,
  Activity,
  Database,
  Server,
  UserCheck,
  Crown
} from 'lucide-react'

function AdminPage() {
  const user = {
    username: 'admin',
    avatar: '',
    role: 'super_admin',
    plan: 'cybersecurity'
  }

  const [activeTab, setActiveTab] = useState('overview')

  const tabs = [
    { id: 'overview', name: 'Overview', icon: Activity },
    { id: 'users', name: 'User Management', icon: Users },
    { id: 'system', name: 'System Health', icon: Server },
    { id: 'logs', name: 'System Logs', icon: Database }
  ]

  return (
    <DashboardLayout user={user} title="Admin Panel">
      <div>
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <Shield className="h-8 w-8 text-cyber-green" />
            <h1 className="text-3xl font-bold font-cyber text-white">
              <span className="cyber-text">Admin Panel</span>
            </h1>
          </div>
          <p className="text-gray-400 max-w-3xl">
            Kelola pengguna, monitor sistem, dan administrasi platform KodeXGuard.
          </p>
        </div>

        {/* Tab Navigation */}
        <div className="mb-8">
          <div className="flex space-x-1 bg-gray-800/50 p-1 rounded-lg">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                    activeTab === tab.id
                      ? 'bg-cyber-green text-black'
                      : 'text-gray-300 hover:text-white hover:bg-gray-700/50'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.name}</span>
                </button>
              )
            })}
          </div>
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card border="blue" glow>
                <div className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">Total Users</p>
                      <p className="text-2xl font-bold text-white">15,847</p>
                    </div>
                    <Users className="h-8 w-8 text-blue-400" />
                  </div>
                </div>
              </Card>

              <Card border="green" glow>
                <div className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">Active Users</p>
                      <p className="text-2xl font-bold text-white">12,456</p>
                    </div>
                    <UserCheck className="h-8 w-8 text-green-400" />
                  </div>
                </div>
              </Card>

              <Card border="gold" glow>
                <div className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">Premium Users</p>
                      <p className="text-2xl font-bold text-white">3,421</p>
                    </div>
                    <Crown className="h-8 w-8 text-nusantara-gold" />
                  </div>
                </div>
              </Card>

              <Card border="blue" glow>
                <div className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">Today Scans</p>
                      <p className="text-2xl font-bold text-white">1,234</p>
                    </div>
                    <Activity className="h-8 w-8 text-purple-400" />
                  </div>
                </div>
              </Card>
            </div>
          </div>
        )}

        {/* Other tabs content */}
        {activeTab !== 'overview' && (
          <Card>
            <div className="p-8 text-center">
              <h3 className="text-xl font-bold text-white mb-2">{tabs.find(t => t.id === activeTab)?.name}</h3>
              <p className="text-gray-400">Content for this tab is coming soon...</p>
            </div>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}

export default AdminPage
