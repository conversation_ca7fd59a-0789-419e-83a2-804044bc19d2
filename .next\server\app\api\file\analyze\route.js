(()=>{var e={};e.id=8183,e.ids=[8183],e.modules={62849:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=62849,e.exports=t},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},96119:e=>{"use strict";e.exports=require("perf_hooks")},35816:e=>{"use strict";e.exports=require("process")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},95346:e=>{"use strict";e.exports=require("timers")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},30604:e=>{"use strict";e.exports=require("node:dns")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},22286:e=>{"use strict";e.exports=require("node:https")},87503:e=>{"use strict";e.exports=require("node:net")},70612:e=>{"use strict";e.exports=require("node:os")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},97742:e=>{"use strict";e.exports=require("node:process")},39630:e=>{"use strict";e.exports=require("node:querystring")},55467:e=>{"use strict";e.exports=require("node:sqlite")},84492:e=>{"use strict";e.exports=require("node:stream")},99397:e=>{"use strict";e.exports=require("node:timers/promises")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},84643:(e,t,s)=>{"use strict";s.r(t),s.d(t,{originalPathname:()=>S,patchFetch:()=>R,requestAsyncStorage:()=>f,routeModule:()=>m,serverHooks:()=>E,staticGenerationAsyncStorage:()=>w});var r={};s.r(r),s.d(r,{GET:()=>g,POST:()=>h});var a=s(49303),i=s(88716),n=s(60670),o=s(87070),c=s(84770),l=s.n(c),u=s(24544);class d{constructor(){this.malwareSignatures=[],this.webshellPatterns=[],this.secretPatterns=[],this.initializeSignatures()}initializeSignatures(){this.malwareSignatures=[{name:"Generic Trojan",type:"trojan",severity:"high",description:"Generic trojan behavior detected",pattern:"CreateRemoteThread|WriteProcessMemory|VirtualAllocEx"},{name:"Ransomware Pattern",type:"ransomware",severity:"critical",description:"Ransomware encryption behavior",pattern:"CryptEncrypt|CryptGenKey|.encrypt|.locked|ransom"},{name:"Keylogger",type:"keylogger",severity:"high",description:"Keylogging functionality detected",pattern:"SetWindowsHookEx|GetAsyncKeyState|keylog"},{name:"Backdoor",type:"backdoor",severity:"critical",description:"Backdoor functionality detected",pattern:"bind|listen|accept|reverse.*shell|cmd.exe"}],this.webshellPatterns=[{name:"PHP Webshell",type:"php",pattern:"eval\\s*\\(\\s*\\$_(?:GET|POST|REQUEST)",description:"PHP eval-based webshell",risk:"high"},{name:"ASP Webshell",type:"asp",pattern:"eval\\s*\\(\\s*request",description:"ASP eval-based webshell",risk:"high"},{name:"JSP Webshell",type:"jsp",pattern:"Runtime\\.getRuntime\\(\\)\\.exec",description:"JSP command execution webshell",risk:"high"},{name:"Generic Shell",type:"generic",pattern:"system\\s*\\(\\s*\\$_|exec\\s*\\(\\s*\\$_|shell_exec",description:"Generic command execution pattern",risk:"medium"}],this.secretPatterns=[/(?:api[_-]?key|apikey)[\s]*[:=][\s]*["\']?([a-zA-Z0-9_\-]{20,})["\']?/gi,/(?:secret[_-]?key|secretkey)[\s]*[:=][\s]*["\']?([a-zA-Z0-9_\-]{20,})["\']?/gi,/(?:access[_-]?token|accesstoken)[\s]*[:=][\s]*["\']?([a-zA-Z0-9_\-]{20,})["\']?/gi,/(?:password|passwd|pwd)[\s]*[:=][\s]*["\']?([^\s"']{8,})["\']?/gi,/(?:private[_-]?key|privatekey)[\s]*[:=][\s]*["\']?([a-zA-Z0-9+/=]{100,})["\']?/gi,/(?:database[_-]?url|db[_-]?url)[\s]*[:=][\s]*["\']?([^\s"']+)["\']?/gi]}async analyzeFile(e){Date.now();try{let t=l().createHash("sha256").update(e.file).digest("hex"),s=this.detectFileType(e.file,e.filename),r={analysisId:this.generateAnalysisId(),filename:e.filename,fileHash:t,fileSize:e.file.length,fileType:s,threatLevel:"safe",detectedThreats:[],analysisResults:{malwareSignatures:[],webshellPatterns:[],secretsFound:[],fileStructure:{},entropy:0,suspiciousStrings:[]},scanEngines:["KodeXGuard-Static","KodeXGuard-Heuristic"],confidence:0,recommendations:[]},a=e.file.toString("utf8",0,Math.min(e.file.length,1048576));return await this.performMalwareAnalysis(a,r),await this.performWebshellAnalysis(a,r),await this.performSecretAnalysis(a,r),await this.performHeuristicAnalysis(e.file,r),r.threatLevel=this.calculateThreatLevel(r),r.confidence=this.calculateConfidence(r),r.recommendations=this.generateRecommendations(r),await this.saveAnalysis(r,e.userId),r}catch(e){throw console.error("File analysis error:",e),Error("File analysis failed")}}detectFileType(e,t){let s=t.split(".").pop()?.toLowerCase()||"",r=e.slice(0,16);return 77===r[0]&&90===r[1]?"PE Executable":127===r[0]&&69===r[1]&&76===r[2]&&70===r[3]?"ELF Executable":80===r[0]&&75===r[1]?"ZIP Archive":255===r[0]&&216===r[1]?"JPEG Image":137===r[0]&&80===r[1]&&78===r[2]&&71===r[3]?"PNG Image":({php:"PHP Script",asp:"ASP Script",aspx:"ASPX Script",jsp:"JSP Script",js:"JavaScript",py:"Python Script",sh:"Shell Script",bat:"Batch File",exe:"Executable",dll:"Dynamic Library",zip:"ZIP Archive",rar:"RAR Archive",pdf:"PDF Document",doc:"Word Document",docx:"Word Document",txt:"Text File"})[s]||"Unknown"}async performMalwareAnalysis(e,t){for(let s of this.malwareSignatures){let r=RegExp(s.pattern,"gi");e.match(r)&&(t.analysisResults.malwareSignatures.push(s),t.detectedThreats.push(`Malware: ${s.name}`))}}async performWebshellAnalysis(e,t){for(let s of this.webshellPatterns){let r=RegExp(s.pattern,"gi");e.match(r)&&(t.analysisResults.webshellPatterns.push(s),t.detectedThreats.push(`Webshell: ${s.name}`))}}async performSecretAnalysis(e,t){for(let s of this.secretPatterns){let r;for(;null!==(r=s.exec(e));){let e={type:this.getSecretType(s.source),value:r[1]||r[0],confidence:.8,description:"Potential secret or credential found"};t.analysisResults.secretsFound.push(e),t.detectedThreats.push(`Secret: ${e.type}`)}}}async performHeuristicAnalysis(e,t){t.analysisResults.entropy=this.calculateEntropy(e),t.analysisResults.entropy>7.5&&t.detectedThreats.push("High entropy (possible encryption/packing)");let s=e.toString("utf8");for(let e of[/CreateProcess/gi,/WriteFile/gi,/RegSetValue/gi,/InternetOpen/gi,/HttpSendRequest/gi,/base64_decode/gi,/eval\s*\(/gi,/system\s*\(/gi,/exec\s*\(/gi]){let r=s.match(e);r&&t.analysisResults.suspiciousStrings.push(...r)}}calculateEntropy(e){let t={};for(let s=0;s<e.length;s++){let r=e[s];t[r]=(t[r]||0)+1}let s=0,r=e.length;for(let e of Object.values(t)){let t=e/r;s-=t*Math.log2(t)}return s}calculateThreatLevel(e){let t=e.detectedThreats.length,s=e.analysisResults.malwareSignatures.filter(e=>"critical"===e.severity).length,r=e.analysisResults.webshellPatterns.filter(e=>"high"===e.risk).length;return s>0||r>0?"malicious":t>3||e.analysisResults.entropy>7.5?"suspicious":"safe"}calculateConfidence(e){let t;return t=50+20*e.analysisResults.malwareSignatures.length+15*e.analysisResults.webshellPatterns.length+10*e.analysisResults.secretsFound.length,"Unknown"===e.fileType&&(t-=20),Math.min(100,Math.max(0,t))}generateRecommendations(e){let t=[];return"malicious"===e.threatLevel?(t.push("⚠️ DO NOT EXECUTE this file - it contains malicious code"),t.push("\uD83D\uDD12 Quarantine the file immediately"),t.push("\uD83D\uDD0D Scan your system for potential infections")):"suspicious"===e.threatLevel&&(t.push("⚠️ Exercise caution with this file"),t.push("\uD83D\uDD0D Perform additional analysis before execution"),t.push("\uD83D\uDEE1️ Run in a sandboxed environment if necessary")),e.analysisResults.secretsFound.length>0&&(t.push("\uD83D\uDD11 Remove or encrypt any exposed credentials"),t.push("\uD83D\uDD04 Rotate any compromised API keys or passwords")),e.analysisResults.entropy>7.5&&t.push("\uD83D\uDCCA High entropy detected - file may be packed or encrypted"),0===t.length&&t.push("✅ File appears to be safe based on current analysis"),t}getSecretType(e){return e.includes("api")?"API Key":e.includes("secret")?"Secret Key":e.includes("token")?"Access Token":e.includes("password")?"Password":e.includes("private")?"Private Key":e.includes("database")?"Database URL":"Unknown Secret"}async saveAnalysis(e,t){try{await u.db.query(`INSERT INTO file_analysis (user_id, filename, file_hash, file_size, file_type, analysis_results, threat_level, detected_threats, created_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())`,[t,e.filename,e.fileHash,e.fileSize,e.fileType,JSON.stringify(e.analysisResults),e.threatLevel,JSON.stringify(e.detectedThreats)])}catch(e){console.error("Failed to save file analysis:",e)}}async getAnalysisHistory(e,t=20){try{return await u.db.query(`SELECT * FROM file_analysis 
         WHERE user_id = ? 
         ORDER BY created_at DESC 
         LIMIT ?`,[e,t])}catch(e){return console.error("Failed to get analysis history:",e),[]}}generateAnalysisId(){return"analysis_"+Date.now()+"_"+Math.random().toString(36).substr(2,9)}}let p=new d;var y=s(90455);async function h(e){try{let t=e.headers.get("authorization"),s=t?.replace("Bearer ","");if(!s)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});let r=await y.e8.getUserByToken(s);if(!r)return o.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let a=await e.formData(),i=a.get("file"),n=a.get("analysisType")||"general";if(!i)return o.NextResponse.json({success:!1,error:"File is required",code:"MISSING_FILE"},{status:400});if(i.size>52428800)return o.NextResponse.json({success:!1,error:"File size exceeds maximum limit (50MB)",code:"FILE_TOO_LARGE"},{status:400});if(!["malware","webshell","secret","general"].includes(n))return o.NextResponse.json({success:!1,error:"Invalid analysis type",code:"INVALID_ANALYSIS_TYPE"},{status:400});let c=u.vo.getInstance(),l=new Date().toISOString().split("T")[0],d=await c.query(`SELECT COUNT(*) as count FROM file_analysis
       WHERE user_id = ? AND DATE(created_at) = ?`,[r.id,l]),h=await c.query("SELECT file_quota_daily FROM plans WHERE id = ?",[r.planId]),g=h[0]?.file_quota_daily||2,m=d[0]?.count||0;if(m>=g)return o.NextResponse.json({success:!1,error:`Daily file analysis quota exceeded. Used: ${m}/${g}`,code:"QUOTA_EXCEEDED"},{status:429});let f={file:Buffer.from(await i.arrayBuffer()),filename:i.name,userId:r.id,analysisType:n},w=await p.analyzeFile(f);return o.NextResponse.json({success:!0,data:{analysisId:w.analysisId,filename:w.filename,fileSize:w.fileSize,fileType:w.fileType,fileHash:w.fileHash,threatLevel:w.threatLevel,detectedThreats:w.detectedThreats,analysisResults:w.analysisResults,scanEngines:w.scanEngines,confidence:w.confidence,recommendations:w.recommendations,quotaUsed:m+1,quotaLimit:g},message:"File analysis completed successfully",timestamp:new Date().toISOString()})}catch(e){return console.error("File analysis error:",e),o.NextResponse.json({success:!1,error:"Internal server error",code:"INTERNAL_ERROR"},{status:500})}}async function g(e){try{let t=e.headers.get("authorization"),s=t?.replace("Bearer ","");if(!s)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});let r=await y.e8.getUserByToken(s);if(!r)return o.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let{searchParams:a}=new URL(e.url),i=parseInt(a.get("page")||"1"),n=parseInt(a.get("limit")||"20"),c=await p.getAnalysisHistory(r.id,n);return o.NextResponse.json({success:!0,data:c,pagination:{page:i,limit:n,total:c.length,totalPages:Math.ceil(c.length/n)},timestamp:new Date().toISOString()})}catch(e){return console.error("File analysis history error:",e),o.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let m=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/file/analyze/route",pathname:"/api/file/analyze",filename:"route",bundlePath:"app/api/file/analyze/route"},resolvedPagePath:"D:\\Users\\Downloads\\kodeXGuard\\app\\api\\file\\analyze\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:f,staticGenerationAsyncStorage:w,serverHooks:E}=m,S="/api/file/analyze/route";function R(){return(0,n.patchFetch)({serverHooks:E,staticGenerationAsyncStorage:w})}},90455:(e,t,s)=>{"use strict";s.d(t,{e8:()=>d});var r=s(98691),a=s(41482),i=s.n(a),n=s(84770),o=s.n(n),c=s(24544);let l=process.env.JWT_SECRET||"kodexguard-secret",u=process.env.JWT_EXPIRES_IN||"7d";class d{static async hashPassword(e){return await r.ZP.hash(e,12)}static async verifyPassword(e,t){return await r.ZP.compare(e,t)}static generateToken(e){return i().sign(e,l,{expiresIn:u})}static verifyToken(e){try{return i().verify(e,l)}catch(e){return null}}static generateApiKey(){return{apiKey:"kxg_"+o().randomBytes(32).toString("hex"),apiSecret:o().randomBytes(64).toString("hex")}}static async register(e){try{if((await c.db.query("SELECT id FROM users WHERE email = ? OR username = ?",[e.email,e.username])).length>0)return{success:!1,error:"User with this email or username already exists"};let t=await this.hashPassword(e.password),s=await c.db.query(`INSERT INTO users (username, email, password_hash, full_name, plan_id, created_at) 
         VALUES (?, ?, ?, ?, 1, NOW())`,[e.username,e.email,t,e.fullName||null]),r=await c.db.queryOne("SELECT * FROM users WHERE id = ?",[s.insertId]);if(!r)return{success:!1,error:"Failed to create user"};let a=this.generateToken({userId:r.id,username:r.username,email:r.email,role:r.role});return{success:!0,user:r,token:a,message:"User registered successfully"}}catch(e){return console.error("Registration error:",e),{success:!1,error:"Registration failed"}}}static async login(e,t){try{let s=await c.db.queryOne("SELECT * FROM users WHERE email = ? AND is_active = 1",[e]);if(!s||!await this.verifyPassword(t,s.password_hash))return{success:!1,error:"Invalid credentials"};await c.db.query("UPDATE users SET last_login = NOW() WHERE id = ?",[s.id]);let r=this.generateToken({userId:s.id,username:s.username,email:s.email,role:s.role});return{success:!0,user:s,token:r,message:"Login successful"}}catch(e){return console.error("Login error:",e),{success:!1,error:"Login failed"}}}static async getUserByToken(e){try{let t=this.verifyToken(e);if(!t)return null;return await c.db.queryOne("SELECT * FROM users WHERE id = ? AND is_active = 1",[t.userId])}catch(e){return console.error("Get user by token error:",e),null}}static async createApiKey(e,t,s=[]){try{let{apiKey:r,apiSecret:a}=this.generateApiKey(),i=await c.db.query(`INSERT INTO api_keys (user_id, key_name, api_key, api_secret, permissions, created_at) 
         VALUES (?, ?, ?, ?, ?, NOW())`,[e,t,r,a,JSON.stringify(s)]),n=await c.db.queryOne("SELECT * FROM api_keys WHERE id = ?",[i.insertId]);return{success:!0,apiKey:n,message:"API key created successfully"}}catch(e){return console.error("Create API key error:",e),{success:!1,error:"Failed to create API key"}}}static async verifyApiKey(e){try{let t=await c.db.queryOne("SELECT * FROM api_keys WHERE api_key = ? AND is_active = 1",[e]);if(!t)return null;let s=await c.db.queryOne("SELECT * FROM users WHERE id = ? AND is_active = 1",[t.userId]);if(!s)return null;return await c.db.query("UPDATE api_keys SET usage_count = usage_count + 1, last_used = NOW() WHERE id = ?",[t.id]),{user:s,apiKeyData:t}}catch(e){return console.error("Verify API key error:",e),null}}static async getUserApiKeys(e){try{return await c.db.query("SELECT * FROM api_keys WHERE user_id = ? ORDER BY created_at DESC",[e])}catch(e){return console.error("Get user API keys error:",e),[]}}static async deleteApiKey(e,t){try{return(await c.db.query("DELETE FROM api_keys WHERE id = ? AND user_id = ?",[t,e])).affectedRows>0}catch(e){return console.error("Delete API key error:",e),!1}}static async changePassword(e,t,s){try{let r=await c.db.queryOne("SELECT * FROM users WHERE id = ?",[e]);if(!r)return{success:!1,error:"User not found"};if(!await this.verifyPassword(t,r.password_hash))return{success:!1,error:"Current password is incorrect"};let a=await this.hashPassword(s);return await c.db.query("UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?",[a,e]),{success:!0,message:"Password changed successfully"}}catch(e){return console.error("Change password error:",e),{success:!1,error:"Failed to change password"}}}}},24544:(e,t,s)=>{"use strict";s.d(t,{db:()=>l,vo:()=>n});var r=s(73785),a=s(79984),i=s(31328);class n{constructor(){this.pool=r.createPool({host:process.env.DB_HOST||"localhost",port:parseInt(process.env.DB_PORT||"3306"),user:process.env.DB_USER||"root",password:process.env.DB_PASSWORD||"rootkan",database:process.env.DB_NAME||"db_kodexguard",waitForConnections:!0,connectionLimit:10,queueLimit:0,charset:"utf8mb4",timezone:"+00:00"})}static getInstance(){return n.instance||(n.instance=new n),n.instance}async query(e,t){try{let[s]=await this.pool.execute(e,t);return s}catch(e){throw console.error("Database query error:",e),e}}async transaction(e){let t=await this.pool.getConnection();try{await t.beginTransaction();let s=await e(t);return await t.commit(),s}catch(e){throw await t.rollback(),e}finally{t.release()}}async close(){await this.pool.end()}}class o{constructor(){this.client=(0,a.createClient)({url:process.env.REDIS_URL||"redis://localhost:6379",password:process.env.REDIS_PASSWORD||void 0,socket:{reconnectStrategy:e=>Math.min(50*e,500)}}),this.client.on("error",e=>{console.error("Redis Client Error:",e)}),this.client.on("connect",()=>{console.log("Redis Client Connected")})}static getInstance(){return o.instance||(o.instance=new o),o.instance}async connect(){this.client.isOpen||await this.client.connect()}async get(e){return await this.connect(),await this.client.get(e)}async set(e,t,s){await this.connect(),s?await this.client.setEx(e,s,t):await this.client.set(e,t)}async del(e){await this.connect(),await this.client.del(e)}async exists(e){return await this.connect(),await this.client.exists(e)===1}async incr(e){return await this.connect(),await this.client.incr(e)}async expire(e,t){await this.connect(),await this.client.expire(e,t)}async close(){this.client.isOpen&&await this.client.quit()}}class c{constructor(){this.client=new i.Client({node:process.env.ELASTICSEARCH_URL||"http://localhost:9200",auth:process.env.ELASTICSEARCH_USERNAME&&process.env.ELASTICSEARCH_PASSWORD?{username:process.env.ELASTICSEARCH_USERNAME,password:process.env.ELASTICSEARCH_PASSWORD}:void 0,requestTimeout:3e4,pingTimeout:3e3,sniffOnStart:!1})}static getInstance(){return c.instance||(c.instance=new c),c.instance}async index(e,t,s){try{await this.client.index({index:e,id:t,body:s})}catch(e){throw console.error("Elasticsearch index error:",e),e}}async search(e,t){try{return await this.client.search({index:e,body:t})}catch(e){throw console.error("Elasticsearch search error:",e),e}}async delete(e,t){try{await this.client.delete({index:e,id:t})}catch(e){throw console.error("Elasticsearch delete error:",e),e}}async createIndex(e,t){try{await this.client.indices.exists({index:e})||await this.client.indices.create({index:e,body:{mappings:t}})}catch(e){throw console.error("Elasticsearch create index error:",e),e}}async close(){await this.client.close()}}let l=n.getInstance();o.getInstance(),c.getInstance()}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[9276,5972,9309,2086],()=>s(84643));module.exports=r})();