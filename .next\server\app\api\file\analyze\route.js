"use strict";(()=>{var e={};e.id=183,e.ids=[183],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},19801:e=>{e.exports=require("os")},96119:e=>{e.exports=require("perf_hooks")},35816:e=>{e.exports=require("process")},76162:e=>{e.exports=require("stream")},74026:e=>{e.exports=require("string_decoder")},95346:e=>{e.exports=require("timers")},82452:e=>{e.exports=require("tls")},74175:e=>{e.exports=require("tty")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},98061:e=>{e.exports=require("node:assert")},92761:e=>{e.exports=require("node:async_hooks")},72254:e=>{e.exports=require("node:buffer")},40027:e=>{e.exports=require("node:console")},6005:e=>{e.exports=require("node:crypto")},65714:e=>{e.exports=require("node:diagnostics_channel")},30604:e=>{e.exports=require("node:dns")},15673:e=>{e.exports=require("node:events")},88849:e=>{e.exports=require("node:http")},42725:e=>{e.exports=require("node:http2")},22286:e=>{e.exports=require("node:https")},87503:e=>{e.exports=require("node:net")},70612:e=>{e.exports=require("node:os")},38846:e=>{e.exports=require("node:perf_hooks")},97742:e=>{e.exports=require("node:process")},39630:e=>{e.exports=require("node:querystring")},55467:e=>{e.exports=require("node:sqlite")},84492:e=>{e.exports=require("node:stream")},99397:e=>{e.exports=require("node:timers/promises")},31764:e=>{e.exports=require("node:tls")},41041:e=>{e.exports=require("node:url")},47261:e=>{e.exports=require("node:util")},93746:e=>{e.exports=require("node:util/types")},24086:e=>{e.exports=require("node:worker_threads")},65628:e=>{e.exports=require("node:zlib")},84643:(e,t,s)=>{s.r(t),s.d(t,{originalPathname:()=>R,patchFetch:()=>w,requestAsyncStorage:()=>g,routeModule:()=>m,serverHooks:()=>S,staticGenerationAsyncStorage:()=>x});var r={};s.r(r),s.d(r,{GET:()=>f,POST:()=>h});var a=s(49303),i=s(88716),n=s(60670),o=s(87070),l=s(84770),u=s.n(l),c=s(24544);class p{constructor(){this.malwareSignatures=[],this.webshellPatterns=[],this.secretPatterns=[],this.initializeSignatures()}initializeSignatures(){this.malwareSignatures=[{name:"Generic Trojan",type:"trojan",severity:"high",description:"Generic trojan behavior detected",pattern:"CreateRemoteThread|WriteProcessMemory|VirtualAllocEx"},{name:"Ransomware Pattern",type:"ransomware",severity:"critical",description:"Ransomware encryption behavior",pattern:"CryptEncrypt|CryptGenKey|.encrypt|.locked|ransom"},{name:"Keylogger",type:"keylogger",severity:"high",description:"Keylogging functionality detected",pattern:"SetWindowsHookEx|GetAsyncKeyState|keylog"},{name:"Backdoor",type:"backdoor",severity:"critical",description:"Backdoor functionality detected",pattern:"bind|listen|accept|reverse.*shell|cmd.exe"}],this.webshellPatterns=[{name:"PHP Webshell",type:"php",pattern:"eval\\s*\\(\\s*\\$_(?:GET|POST|REQUEST)",description:"PHP eval-based webshell",risk:"high"},{name:"ASP Webshell",type:"asp",pattern:"eval\\s*\\(\\s*request",description:"ASP eval-based webshell",risk:"high"},{name:"JSP Webshell",type:"jsp",pattern:"Runtime\\.getRuntime\\(\\)\\.exec",description:"JSP command execution webshell",risk:"high"},{name:"Generic Shell",type:"generic",pattern:"system\\s*\\(\\s*\\$_|exec\\s*\\(\\s*\\$_|shell_exec",description:"Generic command execution pattern",risk:"medium"}],this.secretPatterns=[/(?:api[_-]?key|apikey)[\s]*[:=][\s]*["\']?([a-zA-Z0-9_\-]{20,})["\']?/gi,/(?:secret[_-]?key|secretkey)[\s]*[:=][\s]*["\']?([a-zA-Z0-9_\-]{20,})["\']?/gi,/(?:access[_-]?token|accesstoken)[\s]*[:=][\s]*["\']?([a-zA-Z0-9_\-]{20,})["\']?/gi,/(?:password|passwd|pwd)[\s]*[:=][\s]*["\']?([^\s"']{8,})["\']?/gi,/(?:private[_-]?key|privatekey)[\s]*[:=][\s]*["\']?([a-zA-Z0-9+/=]{100,})["\']?/gi,/(?:database[_-]?url|db[_-]?url)[\s]*[:=][\s]*["\']?([^\s"']+)["\']?/gi]}async analyzeFile(e){Date.now();try{let t=u().createHash("sha256").update(e.file).digest("hex"),s=this.detectFileType(e.file,e.filename),r={analysisId:this.generateAnalysisId(),filename:e.filename,fileHash:t,fileSize:e.file.length,fileType:s,threatLevel:"safe",detectedThreats:[],analysisResults:{malwareSignatures:[],webshellPatterns:[],secretsFound:[],fileStructure:{},entropy:0,suspiciousStrings:[]},scanEngines:["KodeXGuard-Static","KodeXGuard-Heuristic"],confidence:0,recommendations:[]},a=e.file.toString("utf8",0,Math.min(e.file.length,1048576));return await this.performMalwareAnalysis(a,r),await this.performWebshellAnalysis(a,r),await this.performSecretAnalysis(a,r),await this.performHeuristicAnalysis(e.file,r),r.threatLevel=this.calculateThreatLevel(r),r.confidence=this.calculateConfidence(r),r.recommendations=this.generateRecommendations(r),await this.saveAnalysis(r,e.userId),r}catch(e){throw console.error("File analysis error:",e),Error("File analysis failed")}}detectFileType(e,t){let s=t.split(".").pop()?.toLowerCase()||"",r=e.slice(0,16);return 77===r[0]&&90===r[1]?"PE Executable":127===r[0]&&69===r[1]&&76===r[2]&&70===r[3]?"ELF Executable":80===r[0]&&75===r[1]?"ZIP Archive":255===r[0]&&216===r[1]?"JPEG Image":137===r[0]&&80===r[1]&&78===r[2]&&71===r[3]?"PNG Image":({php:"PHP Script",asp:"ASP Script",aspx:"ASPX Script",jsp:"JSP Script",js:"JavaScript",py:"Python Script",sh:"Shell Script",bat:"Batch File",exe:"Executable",dll:"Dynamic Library",zip:"ZIP Archive",rar:"RAR Archive",pdf:"PDF Document",doc:"Word Document",docx:"Word Document",txt:"Text File"})[s]||"Unknown"}async performMalwareAnalysis(e,t){for(let s of this.malwareSignatures){let r=RegExp(s.pattern,"gi");e.match(r)&&(t.analysisResults.malwareSignatures.push(s),t.detectedThreats.push(`Malware: ${s.name}`))}}async performWebshellAnalysis(e,t){for(let s of this.webshellPatterns){let r=RegExp(s.pattern,"gi");e.match(r)&&(t.analysisResults.webshellPatterns.push(s),t.detectedThreats.push(`Webshell: ${s.name}`))}}async performSecretAnalysis(e,t){for(let s of this.secretPatterns){let r;for(;null!==(r=s.exec(e));){let e={type:this.getSecretType(s.source),value:r[1]||r[0],confidence:.8,description:"Potential secret or credential found"};t.analysisResults.secretsFound.push(e),t.detectedThreats.push(`Secret: ${e.type}`)}}}async performHeuristicAnalysis(e,t){t.analysisResults.entropy=this.calculateEntropy(e),t.analysisResults.entropy>7.5&&t.detectedThreats.push("High entropy (possible encryption/packing)");let s=e.toString("utf8");for(let e of[/CreateProcess/gi,/WriteFile/gi,/RegSetValue/gi,/InternetOpen/gi,/HttpSendRequest/gi,/base64_decode/gi,/eval\s*\(/gi,/system\s*\(/gi,/exec\s*\(/gi]){let r=s.match(e);r&&t.analysisResults.suspiciousStrings.push(...r)}}calculateEntropy(e){let t={};for(let s=0;s<e.length;s++){let r=e[s];t[r]=(t[r]||0)+1}let s=0,r=e.length;for(let e of Object.values(t)){let t=e/r;s-=t*Math.log2(t)}return s}calculateThreatLevel(e){let t=e.detectedThreats.length,s=e.analysisResults.malwareSignatures.filter(e=>"critical"===e.severity).length,r=e.analysisResults.webshellPatterns.filter(e=>"high"===e.risk).length;return s>0||r>0?"malicious":t>3||e.analysisResults.entropy>7.5?"suspicious":"safe"}calculateConfidence(e){let t;return t=50+20*e.analysisResults.malwareSignatures.length+15*e.analysisResults.webshellPatterns.length+10*e.analysisResults.secretsFound.length,"Unknown"===e.fileType&&(t-=20),Math.min(100,Math.max(0,t))}generateRecommendations(e){let t=[];return"malicious"===e.threatLevel?(t.push("⚠️ DO NOT EXECUTE this file - it contains malicious code"),t.push("\uD83D\uDD12 Quarantine the file immediately"),t.push("\uD83D\uDD0D Scan your system for potential infections")):"suspicious"===e.threatLevel&&(t.push("⚠️ Exercise caution with this file"),t.push("\uD83D\uDD0D Perform additional analysis before execution"),t.push("\uD83D\uDEE1️ Run in a sandboxed environment if necessary")),e.analysisResults.secretsFound.length>0&&(t.push("\uD83D\uDD11 Remove or encrypt any exposed credentials"),t.push("\uD83D\uDD04 Rotate any compromised API keys or passwords")),e.analysisResults.entropy>7.5&&t.push("\uD83D\uDCCA High entropy detected - file may be packed or encrypted"),0===t.length&&t.push("✅ File appears to be safe based on current analysis"),t}getSecretType(e){return e.includes("api")?"API Key":e.includes("secret")?"Secret Key":e.includes("token")?"Access Token":e.includes("password")?"Password":e.includes("private")?"Private Key":e.includes("database")?"Database URL":"Unknown Secret"}async saveAnalysis(e,t){try{await c.db.query(`INSERT INTO file_analysis (user_id, filename, file_hash, file_size, file_type, analysis_results, threat_level, detected_threats, created_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())`,[t,e.filename,e.fileHash,e.fileSize,e.fileType,JSON.stringify(e.analysisResults),e.threatLevel,JSON.stringify(e.detectedThreats)])}catch(e){console.error("Failed to save file analysis:",e)}}async getAnalysisHistory(e,t=20){try{return await c.db.query(`SELECT * FROM file_analysis 
         WHERE user_id = ? 
         ORDER BY created_at DESC 
         LIMIT ?`,[e,t])}catch(e){return console.error("Failed to get analysis history:",e),[]}}generateAnalysisId(){return"analysis_"+Date.now()+"_"+Math.random().toString(36).substr(2,9)}}let d=new p;var y=s(90455);async function h(e){try{let t=e.headers.get("authorization"),s=t?.replace("Bearer ","");if(!s)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});let r=await y.e8.getUserByToken(s);if(!r)return o.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let a=await e.formData(),i=a.get("file"),n=a.get("analysisType")||"general";if(!i)return o.NextResponse.json({success:!1,error:"File is required",code:"MISSING_FILE"},{status:400});if(i.size>52428800)return o.NextResponse.json({success:!1,error:"File size exceeds maximum limit (50MB)",code:"FILE_TOO_LARGE"},{status:400});if(!["malware","webshell","secret","general"].includes(n))return o.NextResponse.json({success:!1,error:"Invalid analysis type",code:"INVALID_ANALYSIS_TYPE"},{status:400});let l=c.vo.getInstance(),u=new Date().toISOString().split("T")[0],p=await l.query(`SELECT COUNT(*) as count FROM file_analysis
       WHERE user_id = ? AND DATE(created_at) = ?`,[r.id,u]),h=await l.query("SELECT file_quota_daily FROM plans WHERE id = ?",[r.planId]),f=h[0]?.file_quota_daily||2,m=p[0]?.count||0;if(m>=f)return o.NextResponse.json({success:!1,error:`Daily file analysis quota exceeded. Used: ${m}/${f}`,code:"QUOTA_EXCEEDED"},{status:429});let g={file:Buffer.from(await i.arrayBuffer()),filename:i.name,userId:r.id,analysisType:n},x=await d.analyzeFile(g);return o.NextResponse.json({success:!0,data:{analysisId:x.analysisId,filename:x.filename,fileSize:x.fileSize,fileType:x.fileType,fileHash:x.fileHash,threatLevel:x.threatLevel,detectedThreats:x.detectedThreats,analysisResults:x.analysisResults,scanEngines:x.scanEngines,confidence:x.confidence,recommendations:x.recommendations,quotaUsed:m+1,quotaLimit:f},message:"File analysis completed successfully",timestamp:new Date().toISOString()})}catch(e){return console.error("File analysis error:",e),o.NextResponse.json({success:!1,error:"Internal server error",code:"INTERNAL_ERROR"},{status:500})}}async function f(e){try{let t=e.headers.get("authorization"),s=t?.replace("Bearer ","");if(!s)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});let r=await y.e8.getUserByToken(s);if(!r)return o.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let{searchParams:a}=new URL(e.url),i=parseInt(a.get("page")||"1"),n=parseInt(a.get("limit")||"20"),l=await d.getAnalysisHistory(r.id,n);return o.NextResponse.json({success:!0,data:l,pagination:{page:i,limit:n,total:l.length,totalPages:Math.ceil(l.length/n)},timestamp:new Date().toISOString()})}catch(e){return console.error("File analysis history error:",e),o.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let m=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/file/analyze/route",pathname:"/api/file/analyze",filename:"route",bundlePath:"app/api/file/analyze/route"},resolvedPagePath:"D:\\Users\\Downloads\\kodeXGuard\\app\\api\\file\\analyze\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:g,staticGenerationAsyncStorage:x,serverHooks:S}=m,R="/api/file/analyze/route";function w(){return(0,n.patchFetch)({serverHooks:S,staticGenerationAsyncStorage:x})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[216,592],()=>s(84643));module.exports=r})();