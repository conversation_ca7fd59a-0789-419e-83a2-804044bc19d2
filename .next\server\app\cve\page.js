(()=>{var e={};e.id=1336,e.ids=[1336],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19808:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>p,tree:()=>d}),t(85435),t(30829),t(35866);var a=t(23191),l=t(88716),r=t(37922),i=t.n(r),c=t(95231),n={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>c[e]);t.d(s,n);let d=["",{children:["cve",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,85435)),"D:\\Users\\Downloads\\kodeXGuard\\app\\cve\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\Users\\Downloads\\kodeXGuard\\app\\cve\\page.tsx"],x="/cve/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/cve/page",pathname:"/cve",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},92371:(e,s,t)=>{Promise.resolve().then(t.bind(t,55736))},55736:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var a=t(10326),l=t(17577),r=t(83061),i=t(2262),c=t(37202),n=t(58038),d=t(88319),o=t(17069),x=t(12714),m=t(21405),p=t(88307),h=t(31540),u=t(7027),v=t(6343);function y(){let[e]=(0,l.useState)({username:"admin",avatar:"",role:"super_admin",plan:"cybersecurity"}),[s,t]=(0,l.useState)(""),[y,b]=(0,l.useState)("all"),[g,j]=(0,l.useState)("all"),[f,N]=(0,l.useState)(!1),[w,k]=(0,l.useState)([]),[C,S]=(0,l.useState)({totalCVEs:189234,newToday:23,criticalActive:156,exploitsAvailable:2847}),Z={info:"text-blue-400 bg-blue-900/20",low:"text-green-400 bg-green-900/20",medium:"text-yellow-400 bg-yellow-900/20",high:"text-orange-400 bg-orange-900/20",critical:"text-red-400 bg-red-900/20"},V=[{id:"1",cveId:"CVE-2024-0001",description:"SQL injection vulnerability in web application login form allows remote attackers to execute arbitrary SQL commands via the username parameter.",severity:"critical",cvssScore:9.8,cvssVector:"CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H",publishedDate:"2024-01-15",modifiedDate:"2024-01-16",affectedProducts:["WebApp 1.0","WebApp 2.0","WebApp 2.1"],references:["https://example.com/advisory-001","https://nvd.nist.gov/vuln/detail/CVE-2024-0001"],exploits:[{title:"SQL Injection Exploit",url:"https://exploit-db.com/exploits/12345",type:"Public Exploit"}],trending:!0},{id:"2",cveId:"CVE-2024-0002",description:"Cross-site scripting (XSS) vulnerability in search functionality allows attackers to inject malicious scripts.",severity:"high",cvssScore:7.5,cvssVector:"CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:N/A:N",publishedDate:"2024-01-14",modifiedDate:"2024-01-14",affectedProducts:["Framework 3.0","Framework 3.1"],references:["https://example.com/advisory-002"],exploits:[],trending:!1},{id:"3",cveId:"CVE-2024-0003",description:"Remote code execution vulnerability in file upload functionality allows authenticated users to execute arbitrary code.",severity:"critical",cvssScore:9.1,cvssVector:"CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H",publishedDate:"2024-01-13",modifiedDate:"2024-01-15",affectedProducts:["CMS 4.0","CMS 4.1","CMS 4.2"],references:["https://example.com/advisory-003","https://github.com/vendor/cms/security/advisories"],exploits:[{title:"RCE via File Upload",url:"https://exploit-db.com/exploits/12346",type:"Public Exploit"},{title:"Metasploit Module",url:"https://github.com/rapid7/metasploit-framework",type:"Framework Module"}],trending:!0}],D=async()=>{N(!0),await new Promise(e=>setTimeout(e,1e3));let e=V;s&&(e=e.filter(e=>e.cveId.toLowerCase().includes(s.toLowerCase())||e.description.toLowerCase().includes(s.toLowerCase())||e.affectedProducts.some(e=>e.toLowerCase().includes(s.toLowerCase())))),"all"!==y&&(e=e.filter(e=>e.severity===y)),k(e),N(!1)},E=e=>{switch(e){case"critical":case"high":return c.Z;default:return n.Z}};return a.jsx(r.Z,{user:e,title:"CVE Intelligence",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[a.jsx(n.Z,{className:"h-8 w-8 text-cyber-green"}),(0,a.jsxs)("h1",{className:"text-3xl font-bold font-cyber text-white",children:["CVE ",a.jsx("span",{className:"cyber-text",children:"Intelligence"})]})]}),a.jsx("p",{className:"text-gray-400 max-w-3xl",children:"Database CVE terupdate harian dengan referensi lengkap, exploit availability, dan CVSS scoring. Cari CVE berdasarkan keyword, tahun, kategori, dan tingkat keparahan."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[a.jsx(i.Rm,{title:"Total CVEs",value:C.totalCVEs,icon:d.Z,color:"green",trend:{value:5,isPositive:!0}}),a.jsx(i.Rm,{title:"New Today",value:C.newToday,icon:o.Z,color:"blue",trend:{value:12,isPositive:!0}}),a.jsx(i.Rm,{title:"Critical Active",value:C.criticalActive,icon:c.Z,color:"red",trend:{value:-3,isPositive:!1}}),a.jsx(i.Rm,{title:"Exploits Available",value:C.exploitsAvailable,icon:x.Z,color:"purple",trend:{value:8,isPositive:!0}})]}),(0,a.jsxs)("div",{className:"grid lg:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{children:[a.jsx(i.Zb,{border:"green",glow:!0,children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h2",{className:"text-lg font-bold text-white mb-4",children:"Search & Filter"}),(0,a.jsxs)("div",{className:"mb-4",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Search CVEs"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx("input",{type:"text",value:s,onChange:e=>t(e.target.value),placeholder:"CVE-2024-0001, SQL injection...",className:"cyber-input flex-1 text-sm",onKeyPress:e=>"Enter"===e.key&&D()}),a.jsx("button",{onClick:D,disabled:f,className:"cyber-btn-primary px-3",children:f?a.jsx(m.Z,{className:"h-4 w-4 animate-spin"}):a.jsx(p.Z,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"mb-4",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Severity"}),(0,a.jsxs)("select",{value:y,onChange:e=>b(e.target.value),className:"cyber-input w-full text-sm",children:[a.jsx("option",{value:"all",children:"All Severities"}),a.jsx("option",{value:"critical",children:"Critical"}),a.jsx("option",{value:"high",children:"High"}),a.jsx("option",{value:"medium",children:"Medium"}),a.jsx("option",{value:"low",children:"Low"}),a.jsx("option",{value:"info",children:"Info"})]})]}),(0,a.jsxs)("div",{className:"mb-4",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Year"}),(0,a.jsxs)("select",{value:g,onChange:e=>j(e.target.value),className:"cyber-input w-full text-sm",children:[a.jsx("option",{value:"all",children:"All Years"}),a.jsx("option",{value:"2024",children:"2024"}),a.jsx("option",{value:"2023",children:"2023"}),a.jsx("option",{value:"2022",children:"2022"}),a.jsx("option",{value:"2021",children:"2021"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("button",{className:"w-full text-left p-2 rounded bg-gray-800/50 hover:bg-gray-700/50 transition-colors text-sm text-gray-300",children:"\uD83D\uDD25 Trending CVEs"}),a.jsx("button",{className:"w-full text-left p-2 rounded bg-gray-800/50 hover:bg-gray-700/50 transition-colors text-sm text-gray-300",children:"\uD83D\uDCA5 With Exploits"}),a.jsx("button",{className:"w-full text-left p-2 rounded bg-gray-800/50 hover:bg-gray-700/50 transition-colors text-sm text-gray-300",children:"\uD83C\uDD95 Last 7 Days"}),a.jsx("button",{className:"w-full text-left p-2 rounded bg-gray-800/50 hover:bg-gray-700/50 transition-colors text-sm text-gray-300",children:"⚡ CVSS 9.0+"})]})]})}),a.jsx(i.Zb,{className:"mt-6",children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Today's Summary"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"New CVEs"}),a.jsx("span",{className:"text-cyber-green font-semibold",children:"23"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Critical"}),a.jsx("span",{className:"text-red-400 font-semibold",children:"3"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"High"}),a.jsx("span",{className:"text-orange-400 font-semibold",children:"8"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Medium"}),a.jsx("span",{className:"text-yellow-400 font-semibold",children:"12"})]})]})]})})]}),a.jsx("div",{className:"lg:col-span-3",children:a.jsx(i.Zb,{children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white",children:["CVE Records (",w.length,")"]}),(0,a.jsxs)("button",{onClick:()=>{let e=new Blob([JSON.stringify(w,null,2)],{type:"application/json"}),s=URL.createObjectURL(e),t=document.createElement("a");t.href=s,t.download=`cve-export-${Date.now()}.json`,t.click()},className:"cyber-btn text-sm",children:[a.jsx(h.Z,{className:"h-4 w-4 mr-2"}),"Export"]})]}),a.jsx("div",{className:"space-y-4",children:w.map(e=>{let s=E(e.severity);return(0,a.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-4 border border-gray-700 hover:border-gray-600 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(s,{className:`h-5 w-5 ${Z[e.severity].split(" ")[0]}`}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("h3",{className:"font-bold text-white",children:e.cveId}),e.trending&&a.jsx("span",{className:"px-2 py-1 bg-red-900/50 text-red-400 rounded-full text-xs font-semibold",children:"\uD83D\uDD25 Trending"})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-400",children:["Published: ",new Date(e.publishedDate).toLocaleDateString()]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-semibold ${Z[e.severity]}`,children:e.severity.toUpperCase()}),(0,a.jsxs)("div",{className:"text-sm text-gray-400 mt-1",children:["CVSS: ",e.cvssScore]})]})]}),a.jsx("p",{className:"text-gray-300 mb-4",children:e.description}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4 mb-4",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"text-sm font-semibold text-white mb-2",children:"Affected Products"}),a.jsx("div",{className:"flex flex-wrap gap-1",children:e.affectedProducts.map((e,s)=>a.jsx("span",{className:"px-2 py-1 bg-gray-700 text-gray-300 rounded text-xs",children:e},s))})]}),e.exploits.length>0&&(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"text-sm font-semibold text-white mb-2",children:"Available Exploits"}),a.jsx("div",{className:"space-y-1",children:e.exploits.map((e,s)=>(0,a.jsxs)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"flex items-center space-x-2 text-sm text-cyber-green hover:text-cyber-blue transition-colors",children:[a.jsx(u.Z,{className:"h-3 w-3"}),a.jsx("span",{children:e.title})]},s))})]})]}),a.jsx("div",{className:"border-t border-gray-700 pt-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("div",{className:"flex space-x-4 text-sm text-gray-400",children:(0,a.jsxs)("span",{children:["CVSS Vector: ",e.cvssVector]})}),a.jsx("div",{className:"flex space-x-2",children:e.references.map((e,s)=>a.jsx("a",{href:e,target:"_blank",rel:"noopener noreferrer",className:"text-cyber-green hover:text-cyber-blue transition-colors",children:a.jsx(u.Z,{className:"h-4 w-4"})},s))})]})})]},e.id)})}),0===w.length&&!f&&(0,a.jsxs)("div",{className:"text-center py-8",children:[a.jsx(v.Z,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"No CVEs Found"}),a.jsx("p",{className:"text-gray-400",children:"Try adjusting your search criteria"})]}),f&&(0,a.jsxs)("div",{className:"text-center py-8",children:[a.jsx(m.Z,{className:"h-8 w-8 text-cyber-green animate-spin mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"Searching CVEs..."}),a.jsx("p",{className:"text-gray-400",children:"Please wait while we fetch the latest data"})]})]})})})]})]})})}},37202:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},6343:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},88319:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},31540:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},7027:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]])},12714:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},21405:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},88307:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},58038:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},17069:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},85435:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\cve\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[9276,82,7608,3061],()=>t(19808));module.exports=a})();