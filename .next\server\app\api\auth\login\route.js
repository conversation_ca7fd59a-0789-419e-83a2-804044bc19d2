/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/login/route";
exports.ids = ["app/api/auth/login/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/mysql2/lib sync recursive ^cardinal.*$":
/*!****************************************************!*\
  !*** ./node_modules/mysql2/lib/ sync ^cardinal.*$ ***!
  \****************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/mysql2/lib sync recursive ^cardinal.*$";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "perf_hooks":
/*!*****************************!*\
  !*** external "perf_hooks" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("perf_hooks");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "timers":
/*!*************************!*\
  !*** external "timers" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("timers");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:dns":
/*!***************************!*\
  !*** external "node:dns" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:dns");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http2");

/***/ }),

/***/ "node:https":
/*!*****************************!*\
  !*** external "node:https" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:https");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:os":
/*!**************************!*\
  !*** external "node:os" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:os");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:querystring");

/***/ }),

/***/ "node:sqlite":
/*!******************************!*\
  !*** external "node:sqlite" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:sqlite");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:timers/promises":
/*!***************************************!*\
  !*** external "node:timers/promises" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:timers/promises");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Users_Downloads_kodeXGuard_app_api_auth_login_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/auth/login/route.ts */ \"(rsc)/./app/api/auth/login/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/login/route\",\n        pathname: \"/api/auth/login\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/login/route\"\n    },\n    resolvedPagePath: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\api\\\\auth\\\\login\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Users_Downloads_kodeXGuard_app_api_auth_login_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/login/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/auth/login/route.ts":
/*!*************************************!*\
  !*** ./app/api/auth/login/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./lib/database.ts\");\n\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { email, password } = body;\n        // Validate input\n        if (!email || !password) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Email and password are required\",\n                code: \"MISSING_CREDENTIALS\"\n            }, {\n                status: 400\n            });\n        }\n        // Use real authentication service\n        const result = await _lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthService.login(email, password);\n        if (!result.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: result.error || \"Invalid credentials\",\n                code: \"INVALID_CREDENTIALS\"\n            }, {\n                status: 401\n            });\n        }\n        // Get user plan information\n        const db = _lib_database__WEBPACK_IMPORTED_MODULE_2__.Database.getInstance();\n        const planInfo = await db.query(\"SELECT name, slug FROM plans WHERE id = ?\", [\n            result.user?.planId\n        ]);\n        const userData = {\n            id: result.user?.id,\n            username: result.user?.username,\n            email: result.user?.email,\n            fullName: result.user?.fullName,\n            role: result.user?.role,\n            plan: planInfo[0]?.slug || \"gratis\",\n            planName: planInfo[0]?.name || \"Gratis\",\n            isActive: result.user?.isActive,\n            emailVerified: result.user?.emailVerified,\n            createdAt: result.user?.createdAt,\n            lastLogin: new Date().toISOString()\n        };\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                token: result.token,\n                user: userData,\n                expiresIn: \"7d\"\n            },\n            message: \"Login successful\",\n            timestamp: new Date().toISOString()\n        });\n        // Set HTTP-only cookie for token\n        response.cookies.set(\"auth-token\", result.token, {\n            httpOnly: true,\n            secure: \"development\" === \"production\",\n            sameSite: \"strict\",\n            maxAge: 24 * 60 * 60 // 24 hours\n        });\n        return response;\n    } catch (error) {\n        console.error(\"Login error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\",\n            code: \"INTERNAL_ERROR\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function OPTIONS() {\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 200,\n        headers: {\n            \"Access-Control-Allow-Origin\": \"*\",\n            \"Access-Control-Allow-Methods\": \"POST, OPTIONS\",\n            \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/auth/login/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: () => (/* binding */ AuthService),\n/* harmony export */   authenticateApiKey: () => (/* binding */ authenticateApiKey),\n/* harmony export */   authenticateToken: () => (/* binding */ authenticateToken),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./database */ \"(rsc)/./lib/database.ts\");\n// KodeXGuard Authentication System\n// JWT + API Key based authentication\n\n\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"kodexguard-secret\";\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || \"7d\";\nclass AuthService {\n    // Hash password\n    static async hashPassword(password) {\n        const saltRounds = 12;\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, saltRounds);\n    }\n    // Verify password\n    static async verifyPassword(password, hashedPassword) {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hashedPassword);\n    }\n    // Generate JWT token\n    static generateToken(payload) {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n            expiresIn: JWT_EXPIRES_IN\n        });\n    }\n    // Verify JWT token\n    static verifyToken(token) {\n        try {\n            return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n        } catch (error) {\n            return null;\n        }\n    }\n    // Generate API Key\n    static generateApiKey() {\n        const apiKey = \"kxg_\" + crypto__WEBPACK_IMPORTED_MODULE_2___default().randomBytes(32).toString(\"hex\");\n        const apiSecret = crypto__WEBPACK_IMPORTED_MODULE_2___default().randomBytes(64).toString(\"hex\");\n        return {\n            apiKey,\n            apiSecret\n        };\n    }\n    // Register new user\n    static async register(userData) {\n        try {\n            // Check if user already exists\n            const existingUser = await _database__WEBPACK_IMPORTED_MODULE_3__.db.query(\"SELECT id FROM users WHERE email = ? OR username = ?\", [\n                userData.email,\n                userData.username\n            ]);\n            if (existingUser.length > 0) {\n                return {\n                    success: false,\n                    error: \"User with this email or username already exists\"\n                };\n            }\n            // Hash password\n            const hashedPassword = await this.hashPassword(userData.password);\n            // Create user\n            const result = await _database__WEBPACK_IMPORTED_MODULE_3__.db.query(`INSERT INTO users (username, email, password_hash, full_name, plan_id, created_at) \n         VALUES (?, ?, ?, ?, 1, NOW())`, [\n                userData.username,\n                userData.email,\n                hashedPassword,\n                userData.fullName || null\n            ]);\n            // Get created user\n            const user = await _database__WEBPACK_IMPORTED_MODULE_3__.db.queryOne(\"SELECT * FROM users WHERE id = ?\", [\n                result.insertId\n            ]);\n            if (!user) {\n                return {\n                    success: false,\n                    error: \"Failed to create user\"\n                };\n            }\n            // Generate token\n            const token = this.generateToken({\n                userId: user.id,\n                username: user.username,\n                email: user.email,\n                role: user.role\n            });\n            return {\n                success: true,\n                user,\n                token,\n                message: \"User registered successfully\"\n            };\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            return {\n                success: false,\n                error: \"Registration failed\"\n            };\n        }\n    }\n    // Login user\n    static async login(email, password) {\n        try {\n            // Get user by email\n            const user = await _database__WEBPACK_IMPORTED_MODULE_3__.db.queryOne(\"SELECT * FROM users WHERE email = ? AND is_active = 1\", [\n                email\n            ]);\n            if (!user) {\n                return {\n                    success: false,\n                    error: \"Invalid credentials\"\n                };\n            }\n            // Verify password\n            const isValidPassword = await this.verifyPassword(password, user.password_hash);\n            if (!isValidPassword) {\n                return {\n                    success: false,\n                    error: \"Invalid credentials\"\n                };\n            }\n            // Update last login\n            await _database__WEBPACK_IMPORTED_MODULE_3__.db.query(\"UPDATE users SET last_login = NOW() WHERE id = ?\", [\n                user.id\n            ]);\n            // Generate token\n            const token = this.generateToken({\n                userId: user.id,\n                username: user.username,\n                email: user.email,\n                role: user.role\n            });\n            return {\n                success: true,\n                user,\n                token,\n                message: \"Login successful\"\n            };\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                error: \"Login failed\"\n            };\n        }\n    }\n    // Get user by token\n    static async getUserByToken(token) {\n        try {\n            const decoded = this.verifyToken(token);\n            if (!decoded) return null;\n            const user = await _database__WEBPACK_IMPORTED_MODULE_3__.db.queryOne(\"SELECT * FROM users WHERE id = ? AND is_active = 1\", [\n                decoded.userId\n            ]);\n            return user;\n        } catch (error) {\n            console.error(\"Get user by token error:\", error);\n            return null;\n        }\n    }\n    // Create API Key\n    static async createApiKey(userId, keyName, permissions = []) {\n        try {\n            const { apiKey, apiSecret } = this.generateApiKey();\n            const result = await _database__WEBPACK_IMPORTED_MODULE_3__.db.query(`INSERT INTO api_keys (user_id, key_name, api_key, api_secret, permissions, created_at) \n         VALUES (?, ?, ?, ?, ?, NOW())`, [\n                userId,\n                keyName,\n                apiKey,\n                apiSecret,\n                JSON.stringify(permissions)\n            ]);\n            const createdKey = await _database__WEBPACK_IMPORTED_MODULE_3__.db.queryOne(\"SELECT * FROM api_keys WHERE id = ?\", [\n                result.insertId\n            ]);\n            return {\n                success: true,\n                apiKey: createdKey,\n                message: \"API key created successfully\"\n            };\n        } catch (error) {\n            console.error(\"Create API key error:\", error);\n            return {\n                success: false,\n                error: \"Failed to create API key\"\n            };\n        }\n    }\n    // Verify API Key\n    static async verifyApiKey(apiKey) {\n        try {\n            const apiKeyData = await _database__WEBPACK_IMPORTED_MODULE_3__.db.queryOne(\"SELECT * FROM api_keys WHERE api_key = ? AND is_active = 1\", [\n                apiKey\n            ]);\n            if (!apiKeyData) return null;\n            const user = await _database__WEBPACK_IMPORTED_MODULE_3__.db.queryOne(\"SELECT * FROM users WHERE id = ? AND is_active = 1\", [\n                apiKeyData.userId\n            ]);\n            if (!user) return null;\n            // Update usage count\n            await _database__WEBPACK_IMPORTED_MODULE_3__.db.query(\"UPDATE api_keys SET usage_count = usage_count + 1, last_used = NOW() WHERE id = ?\", [\n                apiKeyData.id\n            ]);\n            return {\n                user,\n                apiKeyData\n            };\n        } catch (error) {\n            console.error(\"Verify API key error:\", error);\n            return null;\n        }\n    }\n    // Get user API keys\n    static async getUserApiKeys(userId) {\n        try {\n            return await _database__WEBPACK_IMPORTED_MODULE_3__.db.query(\"SELECT * FROM api_keys WHERE user_id = ? ORDER BY created_at DESC\", [\n                userId\n            ]);\n        } catch (error) {\n            console.error(\"Get user API keys error:\", error);\n            return [];\n        }\n    }\n    // Delete API key\n    static async deleteApiKey(userId, apiKeyId) {\n        try {\n            const result = await _database__WEBPACK_IMPORTED_MODULE_3__.db.query(\"DELETE FROM api_keys WHERE id = ? AND user_id = ?\", [\n                apiKeyId,\n                userId\n            ]);\n            return result.affectedRows > 0;\n        } catch (error) {\n            console.error(\"Delete API key error:\", error);\n            return false;\n        }\n    }\n    // Change password\n    static async changePassword(userId, currentPassword, newPassword) {\n        try {\n            // Get current user\n            const user = await _database__WEBPACK_IMPORTED_MODULE_3__.db.queryOne(\"SELECT * FROM users WHERE id = ?\", [\n                userId\n            ]);\n            if (!user) {\n                return {\n                    success: false,\n                    error: \"User not found\"\n                };\n            }\n            // Verify current password\n            const isValidPassword = await this.verifyPassword(currentPassword, user.password_hash);\n            if (!isValidPassword) {\n                return {\n                    success: false,\n                    error: \"Current password is incorrect\"\n                };\n            }\n            // Hash new password\n            const hashedPassword = await this.hashPassword(newPassword);\n            // Update password\n            await _database__WEBPACK_IMPORTED_MODULE_3__.db.query(\"UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?\", [\n                hashedPassword,\n                userId\n            ]);\n            return {\n                success: true,\n                message: \"Password changed successfully\"\n            };\n        } catch (error) {\n            console.error(\"Change password error:\", error);\n            return {\n                success: false,\n                error: \"Failed to change password\"\n            };\n        }\n    }\n}\n// Middleware for JWT authentication\nfunction authenticateToken(req, res, next) {\n    const authHeader = req.headers[\"authorization\"];\n    const token = authHeader && authHeader.split(\" \")[1];\n    if (!token) {\n        return res.status(401).json({\n            success: false,\n            error: \"Access token required\"\n        });\n    }\n    const decoded = AuthService.verifyToken(token);\n    if (!decoded) {\n        return res.status(403).json({\n            success: false,\n            error: \"Invalid or expired token\"\n        });\n    }\n    req.user = decoded;\n    next();\n}\n// Middleware for API key authentication\nasync function authenticateApiKey(req, res, next) {\n    const apiKey = req.headers[\"x-api-key\"] || req.query.api_key;\n    if (!apiKey) {\n        return res.status(401).json({\n            success: false,\n            error: \"API key required\"\n        });\n    }\n    const result = await AuthService.verifyApiKey(apiKey);\n    if (!result) {\n        return res.status(403).json({\n            success: false,\n            error: \"Invalid API key\"\n        });\n    }\n    req.user = result.user;\n    req.apiKey = result.apiKeyData;\n    next();\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/database.ts":
/*!*************************!*\
  !*** ./lib/database.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Cache: () => (/* binding */ Cache),\n/* harmony export */   Database: () => (/* binding */ Database),\n/* harmony export */   SearchEngine: () => (/* binding */ SearchEngine),\n/* harmony export */   cache: () => (/* binding */ cache),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   search: () => (/* binding */ search)\n/* harmony export */ });\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mysql2/promise */ \"(rsc)/./node_modules/mysql2/promise.js\");\n/* harmony import */ var redis__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! redis */ \"(rsc)/./node_modules/redis/dist/index.js\");\n/* harmony import */ var redis__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(redis__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _elastic_elasticsearch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @elastic/elasticsearch */ \"(rsc)/./node_modules/@elastic/elasticsearch/index.js\");\n/* harmony import */ var _elastic_elasticsearch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_elastic_elasticsearch__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// MySQL Database Connection\nclass Database {\n    constructor(){\n        this.pool = mysql2_promise__WEBPACK_IMPORTED_MODULE_0__.createPool({\n            host: process.env.DB_HOST || \"localhost\",\n            port: parseInt(process.env.DB_PORT || \"3306\"),\n            user: process.env.DB_USER || \"root\",\n            password: process.env.DB_PASSWORD || \"rootkan\",\n            database: process.env.DB_NAME || \"db_kodexguard\",\n            waitForConnections: true,\n            connectionLimit: 10,\n            queueLimit: 0,\n            charset: \"utf8mb4\",\n            timezone: \"+00:00\"\n        });\n    }\n    static getInstance() {\n        if (!Database.instance) {\n            Database.instance = new Database();\n        }\n        return Database.instance;\n    }\n    async query(sql, params) {\n        try {\n            const [rows] = await this.pool.execute(sql, params);\n            return rows;\n        } catch (error) {\n            console.error(\"Database query error:\", error);\n            throw error;\n        }\n    }\n    async transaction(callback) {\n        const connection = await this.pool.getConnection();\n        try {\n            await connection.beginTransaction();\n            const result = await callback(connection);\n            await connection.commit();\n            return result;\n        } catch (error) {\n            await connection.rollback();\n            throw error;\n        } finally{\n            connection.release();\n        }\n    }\n    async close() {\n        await this.pool.end();\n    }\n}\n// Redis Cache Connection\nclass Cache {\n    constructor(){\n        this.client = (0,redis__WEBPACK_IMPORTED_MODULE_1__.createClient)({\n            url: process.env.REDIS_URL || \"redis://localhost:6379\",\n            password: process.env.REDIS_PASSWORD || undefined,\n            socket: {\n                reconnectStrategy: (retries)=>Math.min(retries * 50, 500)\n            }\n        });\n        this.client.on(\"error\", (err)=>{\n            console.error(\"Redis Client Error:\", err);\n        });\n        this.client.on(\"connect\", ()=>{\n            console.log(\"Redis Client Connected\");\n        });\n    }\n    static getInstance() {\n        if (!Cache.instance) {\n            Cache.instance = new Cache();\n        }\n        return Cache.instance;\n    }\n    async connect() {\n        if (!this.client.isOpen) {\n            await this.client.connect();\n        }\n    }\n    async get(key) {\n        await this.connect();\n        return await this.client.get(key);\n    }\n    async set(key, value, ttl) {\n        await this.connect();\n        if (ttl) {\n            await this.client.setEx(key, ttl, value);\n        } else {\n            await this.client.set(key, value);\n        }\n    }\n    async del(key) {\n        await this.connect();\n        await this.client.del(key);\n    }\n    async exists(key) {\n        await this.connect();\n        return await this.client.exists(key) === 1;\n    }\n    async incr(key) {\n        await this.connect();\n        return await this.client.incr(key);\n    }\n    async expire(key, ttl) {\n        await this.connect();\n        await this.client.expire(key, ttl);\n    }\n    async close() {\n        if (this.client.isOpen) {\n            await this.client.quit();\n        }\n    }\n}\n// Elasticsearch Search Engine\nclass SearchEngine {\n    constructor(){\n        this.client = new _elastic_elasticsearch__WEBPACK_IMPORTED_MODULE_2__.Client({\n            node: process.env.ELASTICSEARCH_URL || \"http://localhost:9200\",\n            auth: process.env.ELASTICSEARCH_USERNAME && process.env.ELASTICSEARCH_PASSWORD ? {\n                username: process.env.ELASTICSEARCH_USERNAME,\n                password: process.env.ELASTICSEARCH_PASSWORD\n            } : undefined,\n            requestTimeout: 30000,\n            pingTimeout: 3000,\n            sniffOnStart: false\n        });\n    }\n    static getInstance() {\n        if (!SearchEngine.instance) {\n            SearchEngine.instance = new SearchEngine();\n        }\n        return SearchEngine.instance;\n    }\n    async index(index, id, document) {\n        try {\n            await this.client.index({\n                index,\n                id,\n                body: document\n            });\n        } catch (error) {\n            console.error(\"Elasticsearch index error:\", error);\n            throw error;\n        }\n    }\n    async search(index, query) {\n        try {\n            const response = await this.client.search({\n                index,\n                body: query\n            });\n            return response;\n        } catch (error) {\n            console.error(\"Elasticsearch search error:\", error);\n            throw error;\n        }\n    }\n    async delete(index, id) {\n        try {\n            await this.client.delete({\n                index,\n                id\n            });\n        } catch (error) {\n            console.error(\"Elasticsearch delete error:\", error);\n            throw error;\n        }\n    }\n    async createIndex(index, mapping) {\n        try {\n            const exists = await this.client.indices.exists({\n                index\n            });\n            if (!exists) {\n                await this.client.indices.create({\n                    index,\n                    body: {\n                        mappings: mapping\n                    }\n                });\n            }\n        } catch (error) {\n            console.error(\"Elasticsearch create index error:\", error);\n            throw error;\n        }\n    }\n    async close() {\n        await this.client.close();\n    }\n}\n// Export instances\nconst db = Database.getInstance();\nconst cache = Cache.getInstance();\nconst search = SearchEngine.getInstance();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/database.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@redis","vendor-chunks/apache-arrow","vendor-chunks/@elastic","vendor-chunks/undici","vendor-chunks/mysql2","vendor-chunks/semver","vendor-chunks/iconv-lite","vendor-chunks/generic-pool","vendor-chunks/jsonwebtoken","vendor-chunks/flatbuffers","vendor-chunks/jws","vendor-chunks/debug","vendor-chunks/aws-ssl-profiles","vendor-chunks/yallist","vendor-chunks/sqlstring","vendor-chunks/seq-queue","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/tslib","vendor-chunks/bcryptjs","vendor-chunks/secure-json-parse","vendor-chunks/lru-cache","vendor-chunks/long","vendor-chunks/supports-color","vendor-chunks/safer-buffer","vendor-chunks/safe-buffer","vendor-chunks/redis","vendor-chunks/named-placeholders","vendor-chunks/ms","vendor-chunks/lru.min","vendor-chunks/lodash.once","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isinteger","vendor-chunks/lodash.isboolean","vendor-chunks/lodash.includes","vendor-chunks/jwa","vendor-chunks/is-property","vendor-chunks/hpagent","vendor-chunks/has-flag","vendor-chunks/generate-function","vendor-chunks/denque","vendor-chunks/cluster-key-slot","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();