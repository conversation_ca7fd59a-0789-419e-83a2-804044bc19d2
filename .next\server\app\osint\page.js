/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/osint/page";
exports.ids = ["app/osint/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fosint%2Fpage&page=%2Fosint%2Fpage&appPaths=%2Fosint%2Fpage&pagePath=private-next-app-dir%2Fosint%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fosint%2Fpage&page=%2Fosint%2Fpage&appPaths=%2Fosint%2Fpage&pagePath=private-next-app-dir%2Fosint%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'osint',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/osint/page.tsx */ \"(rsc)/./app/osint/page.tsx\")), \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/osint/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/osint/page\",\n        pathname: \"/osint\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZvc2ludCUyRnBhZ2UmcGFnZT0lMkZvc2ludCUyRnBhZ2UmYXBwUGF0aHM9JTJGb3NpbnQlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGb3NpbnQlMkZwYWdlLnRzeCZhcHBEaXI9RCUzQSU1Q1VzZXJzJTVDRG93bmxvYWRzJTVDa29kZVhHdWFyZCU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9RCUzQSU1Q1VzZXJzJTVDRG93bmxvYWRzJTVDa29kZVhHdWFyZCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLGFBQWEsc0JBQXNCO0FBQ2lFO0FBQ3JDO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQyx1QkFBdUIsb0pBQTJGO0FBQ2xIO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EseUJBQXlCLDRJQUFzRjtBQUMvRyxvQkFBb0IsME5BQWdGO0FBQ3BHO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUN1QjtBQUM2RDtBQUNwRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUN1RDtBQUN2RDtBQUNPLHdCQUF3Qiw4R0FBa0I7QUFDakQ7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va29kZXhndWFyZC8/ZDU1YiJdLCJzb3VyY2VzQ29udGVudCI6WyJcIlRVUkJPUEFDSyB7IHRyYW5zaXRpb246IG5leHQtc3NyIH1cIjtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1wYWdlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbi8vIFdlIGluamVjdCB0aGUgdHJlZSBhbmQgcGFnZXMgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IHRyZWUgPSB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICcnLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICdvc2ludCcsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFsnX19QQUdFX18nLCB7fSwge1xuICAgICAgICAgIHBhZ2U6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFVzZXJzXFxcXERvd25sb2Fkc1xcXFxrb2RlWEd1YXJkXFxcXGFwcFxcXFxvc2ludFxcXFxwYWdlLnRzeFwiKSwgXCJEOlxcXFxVc2Vyc1xcXFxEb3dubG9hZHNcXFxca29kZVhHdWFyZFxcXFxhcHBcXFxcb3NpbnRcXFxccGFnZS50c3hcIl0sXG4gICAgICAgICAgXG4gICAgICAgIH1dXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgIFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFVzZXJzXFxcXERvd25sb2Fkc1xcXFxrb2RlWEd1YXJkXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIpLCBcIkQ6XFxcXFVzZXJzXFxcXERvd25sb2Fkc1xcXFxrb2RlWEd1YXJkXFxcXGFwcFxcXFxsYXlvdXQudHN4XCJdLFxuJ25vdC1mb3VuZCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIiksIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiXSxcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LmNoaWxkcmVuO1xuY29uc3QgcGFnZXMgPSBbXCJEOlxcXFxVc2Vyc1xcXFxEb3dubG9hZHNcXFxca29kZVhHdWFyZFxcXFxhcHBcXFxcb3NpbnRcXFxccGFnZS50c3hcIl07XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iYWxFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnlcIjtcbmNvbnN0IF9fbmV4dF9hcHBfcmVxdWlyZV9fID0gX193ZWJwYWNrX3JlcXVpcmVfX1xuY29uc3QgX19uZXh0X2FwcF9sb2FkX2NodW5rX18gPSAoKSA9PiBQcm9taXNlLnJlc29sdmUoKVxuZXhwb3J0IGNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9vc2ludC9wYWdlXCI7XG5leHBvcnQgY29uc3QgX19uZXh0X2FwcF9fID0ge1xuICAgIHJlcXVpcmU6IF9fbmV4dF9hcHBfcmVxdWlyZV9fLFxuICAgIGxvYWRDaHVuazogX19uZXh0X2FwcF9sb2FkX2NodW5rX19cbn07XG5leHBvcnQgKiBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2VudHJ5LWJhc2VcIjtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFBhZ2VSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgIHBhZ2U6IFwiL29zaW50L3BhZ2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL29zaW50XCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogXCJcIixcbiAgICAgICAgZmlsZW5hbWU6IFwiXCIsXG4gICAgICAgIGFwcFBhdGhzOiBbXVxuICAgIH0sXG4gICAgdXNlcmxhbmQ6IHtcbiAgICAgICAgbG9hZGVyVHJlZTogdHJlZVxuICAgIH1cbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcGFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fosint%2Fpage&page=%2Fosint%2Fpage&appPaths=%2Fosint%2Fpage&pagePath=private-next-app-dir%2Fosint%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Cosint%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Cosint%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/osint/page.tsx */ \"(ssr)/./app/osint/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNVc2VycyU1QyU1Q0Rvd25sb2FkcyU1QyU1Q2tvZGVYR3VhcmQlNUMlNUNhcHAlNUMlNUNvc2ludCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBMkYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb2RleGd1YXJkLz80ZGVkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcVXNlcnNcXFxcRG93bmxvYWRzXFxcXGtvZGVYR3VhcmRcXFxcYXBwXFxcXG9zaW50XFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Cosint%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(ssr)/./node_modules/next/dist/client/script.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNVc2VycyU1QyU1Q0Rvd25sb2FkcyU1QyU1Q2tvZGVYR3VhcmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q3NjcmlwdC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1VzZXJzJTVDJTVDRG93bmxvYWRzJTVDJTVDa29kZVhHdWFyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlMkMlNUMlMjJkaXNwbGF5JTVDJTIyJTNBJTVDJTIyc3dhcCU1QyUyMiUyQyU1QyUyMnByZWxvYWQlNUMlMjIlM0F0cnVlJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1VzZXJzJTVDJTVDRG93bmxvYWRzJTVDJTVDa29kZVhHdWFyZCU1QyU1Q3N0eWxlcyU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvTUFBa0giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb2RleGd1YXJkLz8wMjMwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcVXNlcnNcXFxcRG93bmxvYWRzXFxcXGtvZGVYR3VhcmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcc2NyaXB0LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/osint/page.tsx":
/*!****************************!*\
  !*** ./app/osint/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OSINTPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(ssr)/./components/DashboardLayout.tsx\");\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Card */ \"(ssr)/./components/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,CreditCard,Database,Download,Filter,Globe,Mail,MapPin,Phone,RefreshCw,Search,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,CreditCard,Database,Download,Filter,Globe,Mail,MapPin,Phone,RefreshCw,Search,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,CreditCard,Database,Download,Filter,Globe,Mail,MapPin,Phone,RefreshCw,Search,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,CreditCard,Database,Download,Filter,Globe,Mail,MapPin,Phone,RefreshCw,Search,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,CreditCard,Database,Download,Filter,Globe,Mail,MapPin,Phone,RefreshCw,Search,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,CreditCard,Database,Download,Filter,Globe,Mail,MapPin,Phone,RefreshCw,Search,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,CreditCard,Database,Download,Filter,Globe,Mail,MapPin,Phone,RefreshCw,Search,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,CreditCard,Database,Download,Filter,Globe,Mail,MapPin,Phone,RefreshCw,Search,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,CreditCard,Database,Download,Filter,Globe,Mail,MapPin,Phone,RefreshCw,Search,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,CreditCard,Database,Download,Filter,Globe,Mail,MapPin,Phone,RefreshCw,Search,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,CreditCard,Database,Download,Filter,Globe,Mail,MapPin,Phone,RefreshCw,Search,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,CreditCard,Database,Download,Filter,Globe,Mail,MapPin,Phone,RefreshCw,Search,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,CreditCard,Database,Download,Filter,Globe,Mail,MapPin,Phone,RefreshCw,Search,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,CreditCard,Database,Download,Filter,Globe,Mail,MapPin,Phone,RefreshCw,Search,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,CreditCard,Database,Download,Filter,Globe,Mail,MapPin,Phone,RefreshCw,Search,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction OSINTPage() {\n    const [user] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: \"admin\",\n        avatar: \"\",\n        role: \"super_admin\",\n        plan: \"cybersecurity\"\n    });\n    const [searchType, setSearchType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"email\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalSearches: 1247,\n        successfulFinds: 892,\n        dataSourcesActive: 15,\n        lastUpdate: \"2 minutes ago\"\n    });\n    const searchTypes = [\n        {\n            id: \"email\",\n            name: \"Email Address\",\n            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            placeholder: \"<EMAIL>\"\n        },\n        {\n            id: \"phone\",\n            name: \"Phone Number\",\n            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            placeholder: \"+62812345678\"\n        },\n        {\n            id: \"nik\",\n            name: \"NIK\",\n            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            placeholder: \"1234567890123456\"\n        },\n        {\n            id: \"npwp\",\n            name: \"NPWP\",\n            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            placeholder: \"12.345.678.9-012.345\"\n        },\n        {\n            id: \"name\",\n            name: \"Full Name\",\n            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            placeholder: \"John Doe\"\n        },\n        {\n            id: \"domain\",\n            name: \"Domain\",\n            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            placeholder: \"example.com\"\n        },\n        {\n            id: \"imei\",\n            name: \"IMEI\",\n            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            placeholder: \"123456789012345\"\n        },\n        {\n            id: \"address\",\n            name: \"Address\",\n            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            placeholder: \"Jakarta, Indonesia\"\n        }\n    ];\n    const dataSources = [\n        {\n            name: \"Dukcapil Database\",\n            status: \"active\",\n            lastUpdate: \"1 hour ago\"\n        },\n        {\n            name: \"Kemkes Database\",\n            status: \"active\",\n            lastUpdate: \"30 minutes ago\"\n        },\n        {\n            name: \"GitHub Leaked Data\",\n            status: \"active\",\n            lastUpdate: \"5 minutes ago\"\n        },\n        {\n            name: \"Social Media APIs\",\n            status: \"active\",\n            lastUpdate: \"2 minutes ago\"\n        },\n        {\n            name: \"Domain WHOIS\",\n            status: \"active\",\n            lastUpdate: \"1 minute ago\"\n        },\n        {\n            name: \"Phone Number DB\",\n            status: \"maintenance\",\n            lastUpdate: \"2 hours ago\"\n        },\n        {\n            name: \"Email Breach DB\",\n            status: \"active\",\n            lastUpdate: \"10 minutes ago\"\n        },\n        {\n            name: \"Location Services\",\n            status: \"active\",\n            lastUpdate: \"3 minutes ago\"\n        }\n    ];\n    const handleSearch = async ()=>{\n        if (!searchQuery.trim()) return;\n        setLoading(true);\n        setResults([]);\n        try {\n            // Simulate API call\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            // Mock results based on search type\n            const mockResults = [\n                {\n                    id: \"1\",\n                    type: searchType,\n                    source: \"Dukcapil Database\",\n                    data: {\n                        name: \"John Doe\",\n                        email: searchQuery,\n                        phone: \"+62812345678\",\n                        address: \"Jakarta, Indonesia\",\n                        verified: true\n                    },\n                    confidence: 95,\n                    timestamp: new Date().toISOString(),\n                    status: \"found\"\n                },\n                {\n                    id: \"2\",\n                    type: searchType,\n                    source: \"Social Media\",\n                    data: {\n                        platform: \"LinkedIn\",\n                        profile: \"https://linkedin.com/in/johndoe\",\n                        company: \"Tech Corp\",\n                        location: \"Jakarta\"\n                    },\n                    confidence: 87,\n                    timestamp: new Date().toISOString(),\n                    status: \"found\"\n                },\n                {\n                    id: \"3\",\n                    type: searchType,\n                    source: \"GitHub\",\n                    data: {\n                        username: \"johndoe\",\n                        repositories: 15,\n                        followers: 42,\n                        email_exposed: true\n                    },\n                    confidence: 78,\n                    timestamp: new Date().toISOString(),\n                    status: \"found\"\n                }\n            ];\n            setResults(mockResults);\n        } catch (error) {\n            console.error(\"Search failed:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getConfidenceColor = (confidence)=>{\n        if (confidence >= 90) return \"text-green-400\";\n        if (confidence >= 70) return \"text-yellow-400\";\n        return \"text-red-400\";\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"found\":\n                return _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\n            case \"not_found\":\n                return _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"];\n            case \"error\":\n                return _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"];\n            default:\n                return _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"];\n        }\n    };\n    const exportResults = ()=>{\n        const dataStr = JSON.stringify(results, null, 2);\n        const dataBlob = new Blob([\n            dataStr\n        ], {\n            type: \"application/json\"\n        });\n        const url = URL.createObjectURL(dataBlob);\n        const link = document.createElement(\"a\");\n        link.href = url;\n        link.download = `osint-results-${Date.now()}.json`;\n        link.click();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        user: user,\n        title: \"OSINT Investigator\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-8 w-8 text-cyber-green\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold font-cyber text-white\",\n                                    children: [\n                                        \"OSINT \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"cyber-text\",\n                                            children: \"Investigator\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 max-w-3xl\",\n                            children: \"Investigasi mendalam dengan pencarian NIK, NPWP, nomor HP, email, domain, dan tracking lokasi real-time. Akses database leaked Dukcapil, Kemkes, GitHub dan sumber data lainnya.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.StatCard, {\n                            title: \"Total Searches\",\n                            value: stats.totalSearches,\n                            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                            color: \"green\",\n                            trend: {\n                                value: 12,\n                                isPositive: true\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.StatCard, {\n                            title: \"Successful Finds\",\n                            value: stats.successfulFinds,\n                            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                            color: \"blue\",\n                            trend: {\n                                value: 8,\n                                isPositive: true\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.StatCard, {\n                            title: \"Data Sources\",\n                            value: stats.dataSourcesActive,\n                            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                            color: \"purple\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.StatCard, {\n                            title: \"Last Update\",\n                            value: stats.lastUpdate,\n                            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                            color: \"gold\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    border: \"green\",\n                                    glow: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-bold text-white mb-6\",\n                                                children: \"Search Configuration\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-300 mb-3\",\n                                                        children: \"Search Type\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-3\",\n                                                        children: searchTypes.map((type)=>{\n                                                            const Icon = type.icon;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setSearchType(type.id),\n                                                                className: `p-3 rounded-lg border transition-all duration-200 ${searchType === type.id ? \"border-cyber-green bg-cyber-green/10 text-cyber-green\" : \"border-gray-600 hover:border-gray-500 text-gray-300\"}`,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                        className: \"h-5 w-5 mx-auto mb-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                        lineNumber: 239,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs font-medium\",\n                                                                        children: type.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                        lineNumber: 240,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, type.id, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 27\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                        children: \"Search Query\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: searchQuery,\n                                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                                placeholder: searchTypes.find((t)=>t.id === searchType)?.placeholder,\n                                                                className: \"cyber-input flex-1\",\n                                                                onKeyPress: (e)=>e.key === \"Enter\" && handleSearch()\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleSearch,\n                                                                disabled: loading || !searchQuery.trim(),\n                                                                className: \"cyber-btn-primary px-6 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-5 w-5 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                    lineNumber: 267,\n                                                                    columnNumber: 27\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-5 w-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                    lineNumber: 269,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-700 pt-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            className: \"rounded bg-gray-800 border-gray-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                            lineNumber: 280,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-300\",\n                                                                            children: \"Deep Search\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                            lineNumber: 281,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                    lineNumber: 279,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            className: \"rounded bg-gray-800 border-gray-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                            lineNumber: 284,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-300\",\n                                                                            children: \"Include Social Media\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                            lineNumber: 285,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                    lineNumber: 283,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"text-sm text-cyber-green hover:text-cyber-blue transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-4 w-4 inline mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"More Filters\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, this),\n                                results.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold text-white\",\n                                                        children: [\n                                                            \"Search Results (\",\n                                                            results.length,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: exportResults,\n                                                        className: \"cyber-btn text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Export\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: results.map((result)=>{\n                                                    const StatusIcon = getStatusIcon(result.status);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-800/50 rounded-lg p-4 border border-gray-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start justify-between mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                                                className: `h-5 w-5 ${result.status === \"found\" ? \"text-green-400\" : result.status === \"not_found\" ? \"text-red-400\" : \"text-yellow-400\"}`\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                                lineNumber: 324,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"font-semibold text-white\",\n                                                                                        children: result.source\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                                        lineNumber: 330,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-400\",\n                                                                                        children: result.type\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                                        lineNumber: 331,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                                lineNumber: 329,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                        lineNumber: 323,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-right\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: `text-sm font-semibold ${getConfidenceColor(result.confidence)}`,\n                                                                                children: [\n                                                                                    result.confidence,\n                                                                                    \"% confidence\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                                lineNumber: 335,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: new Date(result.timestamp).toLocaleString()\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                                lineNumber: 338,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                        lineNumber: 334,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-900/50 rounded p-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                                    className: \"text-sm text-gray-300 whitespace-pre-wrap\",\n                                                                    children: JSON.stringify(result.data, null, 2)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                    lineNumber: 345,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, result.id, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 27\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 17\n                                }, this),\n                                loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-8 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_CreditCard_Database_Download_Filter_Globe_Mail_MapPin_Phone_RefreshCw_Search_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-8 w-8 text-cyber-green animate-spin mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white mb-2\",\n                                                children: \"Searching...\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Scanning multiple data sources for information\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-white mb-4\",\n                                                children: \"Data Sources\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: dataSources.map((source, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 bg-gray-800/50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium text-white text-sm\",\n                                                                        children: source.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                        lineNumber: 377,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-400\",\n                                                                        children: source.lastUpdate\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                        lineNumber: 378,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `px-2 py-1 rounded-full text-xs font-semibold ${source.status === \"active\" ? \"bg-green-900/50 text-green-400\" : \"bg-yellow-900/50 text-yellow-400\"}`,\n                                                                children: source.status\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                lineNumber: 380,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"mt-6\",\n                                    border: \"gold\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-white mb-4\",\n                                                children: \"Plan Usage\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"Daily Searches\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                lineNumber: 399,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white\",\n                                                                children: \"Unlimited\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                        lineNumber: 398,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"Deep Search\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-cyber-green\",\n                                                                children: \"✓ Enabled\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"Export Results\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                lineNumber: 407,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-cyber-green\",\n                                                                children: \"✓ Enabled\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                lineNumber: 408,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"API Access\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-cyber-green\",\n                                                                children: \"✓ Enabled\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n            lineNumber: 169,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\osint\\\\page.tsx\",\n        lineNumber: 168,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./app/osint/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Card.tsx":
/*!*****************************!*\
  !*** ./components/Card.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlertCard: () => (/* binding */ AlertCard),\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   FeatureCard: () => (/* binding */ FeatureCard),\n/* harmony export */   LoadingCard: () => (/* binding */ LoadingCard),\n/* harmony export */   StatCard: () => (/* binding */ StatCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ Card,StatCard,FeatureCard,LoadingCard,AlertCard auto */ \nfunction Card({ children, className = \"\", hover = false, glow = false, border = \"default\" }) {\n    const borderColors = {\n        default: \"border-gray-700\",\n        green: \"border-cyber-green\",\n        blue: \"border-cyber-blue\",\n        red: \"border-cyber-red\",\n        gold: \"border-nusantara-gold\"\n    };\n    const glowColors = {\n        default: \"\",\n        green: \"shadow-lg shadow-cyber-green/20\",\n        blue: \"shadow-lg shadow-cyber-blue/20\",\n        red: \"shadow-lg shadow-cyber-red/20\",\n        gold: \"shadow-lg shadow-nusantara-gold/20\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `\n        bg-gray-900/50 backdrop-blur-sm rounded-lg border ${borderColors[border]}\n        ${hover ? \"hover:border-cyber-green hover:shadow-lg hover:shadow-cyber-green/20 transition-all duration-300 cursor-pointer\" : \"\"}\n        ${glow ? glowColors[border] : \"\"}\n        ${className}\n      `,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\nfunction StatCard({ title, value, icon: Icon, color = \"green\", trend, loading = false }) {\n    const colors = {\n        green: \"text-cyber-green\",\n        blue: \"text-cyber-blue\",\n        red: \"text-cyber-red\",\n        purple: \"text-cyber-purple\",\n        gold: \"text-nusantara-gold\"\n    };\n    const bgColors = {\n        green: \"bg-cyber-green/10\",\n        blue: \"bg-cyber-blue/10\",\n        red: \"bg-cyber-red/10\",\n        purple: \"bg-cyber-purple/10\",\n        gold: \"bg-nusantara-gold/10\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        border: color,\n        glow: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-400\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-baseline space-x-2\",\n                                children: [\n                                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 w-20 bg-gray-700 animate-pulse rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: `text-2xl font-bold ${colors[color]}`,\n                                        children: typeof value === \"number\" ? value.toLocaleString() : value\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, this),\n                                    trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `text-sm font-medium ${trend.isPositive ? \"text-green-400\" : \"text-red-400\"}`,\n                                        children: [\n                                            trend.isPositive ? \"+\" : \"\",\n                                            trend.value,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `p-3 rounded-lg ${bgColors[color]}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            className: `h-6 w-6 ${colors[color]}`\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\nfunction FeatureCard({ title, description, icon: Icon, color = \"green\", onClick, disabled = false, badge }) {\n    const colors = {\n        green: \"text-cyber-green\",\n        blue: \"text-cyber-blue\",\n        red: \"text-cyber-red\",\n        purple: \"text-cyber-purple\",\n        gold: \"text-nusantara-gold\"\n    };\n    const bgColors = {\n        green: \"bg-cyber-green/10\",\n        blue: \"bg-cyber-blue/10\",\n        red: \"bg-cyber-red/10\",\n        purple: \"bg-cyber-purple/10\",\n        gold: \"bg-nusantara-gold/10\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        hover: !disabled && !!onClick,\n        border: color,\n        className: `relative ${disabled ? \"opacity-50 cursor-not-allowed\" : \"\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 h-full\",\n            onClick: disabled ? undefined : onClick,\n            children: [\n                badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-3 right-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `px-2 py-1 text-xs font-semibold rounded-full ${bgColors[color]} ${colors[color]}`,\n                        children: badge\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `inline-flex p-3 rounded-lg ${bgColors[color]} mb-4`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: `h-6 w-6 ${colors[color]}`\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-white mb-2\",\n                    children: title\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-400 text-sm leading-relaxed\",\n                    children: description\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this),\n                onClick && !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 pt-4 border-t border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-medium ${colors[color]} hover:underline`,\n                        children: \"Mulai Sekarang →\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\nfunction LoadingCard({ className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 animate-pulse\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 bg-gray-700 rounded-lg\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-gray-700 rounded w-3/4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-3 bg-gray-700 rounded w-1/2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                lineNumber: 184,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 183,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 182,\n        columnNumber: 5\n    }, this);\n}\nfunction AlertCard({ type = \"info\", title, message, onClose }) {\n    const styles = {\n        info: {\n            border: \"border-cyber-blue\",\n            bg: \"bg-cyber-blue/10\",\n            text: \"text-cyber-blue\",\n            icon: \"\\uD83D\\uDCA1\"\n        },\n        success: {\n            border: \"border-cyber-green\",\n            bg: \"bg-cyber-green/10\",\n            text: \"text-cyber-green\",\n            icon: \"✅\"\n        },\n        warning: {\n            border: \"border-nusantara-gold\",\n            bg: \"bg-nusantara-gold/10\",\n            text: \"text-nusantara-gold\",\n            icon: \"⚠️\"\n        },\n        error: {\n            border: \"border-cyber-red\",\n            bg: \"bg-cyber-red/10\",\n            text: \"text-cyber-red\",\n            icon: \"❌\"\n        }\n    };\n    const style = styles[type];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `border ${style.border} ${style.bg} rounded-lg p-4`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-lg\",\n                    children: style.icon\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: `font-semibold ${style.text}`,\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300 text-sm mt-1\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, this),\n                onClose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onClose,\n                    className: \"text-gray-400 hover:text-white transition-colors\",\n                    children: \"\\xd7\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 238,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 237,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/DashboardLayout.tsx":
/*!****************************************!*\
  !*** ./components/DashboardLayout.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _lib_preloader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/preloader */ \"(ssr)/./lib/preloader.ts\");\n/* harmony import */ var _lib_performance__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/performance */ \"(ssr)/./lib/performance.ts\");\n/* harmony import */ var _lib_cache__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/cache */ \"(ssr)/./lib/cache.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n// Dynamic imports for better code splitting\nconst Sidebar = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/next\"), __webpack_require__.e(\"vendor-chunks/lucide-react\"), __webpack_require__.e(\"_ssr_components_Sidebar_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ./Sidebar */ \"(ssr)/./components/Sidebar.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\DashboardLayout.tsx -> \" + \"./Sidebar\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-64 bg-gray-900 animate-pulse\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n            lineNumber: 11,\n            columnNumber: 18\n        }, undefined),\n    ssr: true\n});\nconst Navbar = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/next\"), __webpack_require__.e(\"vendor-chunks/lucide-react\"), __webpack_require__.e(\"_ssr_components_Navbar_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ./Navbar */ \"(ssr)/./components/Navbar.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\DashboardLayout.tsx -> \" + \"./Navbar\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-16 bg-gray-900 animate-pulse\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n            lineNumber: 16,\n            columnNumber: 18\n        }, undefined),\n    ssr: true\n});\nfunction DashboardLayout({ children, user, title, showSearch = true }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Performance optimizations on mount\n        const initOptimizations = async ()=>{\n            try {\n                // Mark route change complete for performance tracking\n                (0,_lib_performance__WEBPACK_IMPORTED_MODULE_4__.markRouteChangeComplete)();\n                // Preload user journey based on role and plan\n                await _lib_preloader__WEBPACK_IMPORTED_MODULE_3__.preloader.preloadUserJourney(user.role, user.plan);\n                // Preload critical data for current user\n                await _lib_cache__WEBPACK_IMPORTED_MODULE_5__.cache.preloadCriticalData(user.username);\n                // Observe links for intersection-based preloading\n                setTimeout(()=>{\n                    (0,_lib_preloader__WEBPACK_IMPORTED_MODULE_3__.observeLinks)();\n                }, 1000);\n                // Preload critical assets\n                await _lib_preloader__WEBPACK_IMPORTED_MODULE_3__.preloader.preloadCriticalAssets();\n            } catch (error) {\n                console.warn(\"Optimization initialization failed:\", error);\n            }\n        };\n        initOptimizations();\n        // Cleanup on unmount\n        return ()=>{\n            _lib_performance__WEBPACK_IMPORTED_MODULE_4__.performanceMonitor.cleanup();\n        };\n    }, [\n        user.role,\n        user.plan,\n        user.username\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-cyber\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-16 bg-gray-900 animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 27\n                }, void 0),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Navbar, {\n                    user: user,\n                    title: title,\n                    showSearch: showSearch,\n                    isLandingPage: false\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex pt-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-64 bg-gray-900 animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 29\n                        }, void 0),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Sidebar, {\n                            user: user\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 min-w-0 lg:ml-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-8 bg-gray-800 rounded animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-64 bg-gray-800 rounded animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 17\n                                    }, void 0),\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL0Rhc2hib2FyZExheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUVzRDtBQUNwQjtBQUN1QjtBQUNzQjtBQUM1QztBQUVuQyw0Q0FBNEM7QUFDNUMsTUFBTVEsVUFBVU4sd0RBQU9BLENBQUMsSUFBTSwyUkFBTzs7Ozs7O0lBQ25DTyxTQUFTLGtCQUFNLDhEQUFDQztZQUFJQyxXQUFVOzs7Ozs7SUFDOUJDLEtBQUs7O0FBR1AsTUFBTUMsU0FBU1gsd0RBQU9BLENBQUMsSUFBTSx3UkFBTzs7Ozs7O0lBQ2xDTyxTQUFTLGtCQUFNLDhEQUFDQztZQUFJQyxXQUFVOzs7Ozs7SUFDOUJDLEtBQUs7O0FBZVEsU0FBU0UsZ0JBQWdCLEVBQ3RDQyxRQUFRLEVBQ1JDLElBQUksRUFDSkMsS0FBSyxFQUNMQyxhQUFhLElBQUksRUFDSTtJQUNyQmxCLGdEQUFTQSxDQUFDO1FBQ1IscUNBQXFDO1FBQ3JDLE1BQU1tQixvQkFBb0I7WUFDeEIsSUFBSTtnQkFDRixzREFBc0Q7Z0JBQ3REYix5RUFBdUJBO2dCQUV2Qiw4Q0FBOEM7Z0JBQzlDLE1BQU1ILHFEQUFTQSxDQUFDaUIsa0JBQWtCLENBQUNKLEtBQUtLLElBQUksRUFBRUwsS0FBS00sSUFBSTtnQkFFdkQseUNBQXlDO2dCQUN6QyxNQUFNZiw2Q0FBS0EsQ0FBQ2dCLG1CQUFtQixDQUFDUCxLQUFLUSxRQUFRO2dCQUU3QyxrREFBa0Q7Z0JBQ2xEQyxXQUFXO29CQUNUckIsNERBQVlBO2dCQUNkLEdBQUc7Z0JBRUgsMEJBQTBCO2dCQUMxQixNQUFNRCxxREFBU0EsQ0FBQ3VCLHFCQUFxQjtZQUV2QyxFQUFFLE9BQU9DLE9BQU87Z0JBQ2RDLFFBQVFDLElBQUksQ0FBQyx1Q0FBdUNGO1lBQ3REO1FBQ0Y7UUFFQVI7UUFFQSxxQkFBcUI7UUFDckIsT0FBTztZQUNMZCxnRUFBa0JBLENBQUN5QixPQUFPO1FBQzVCO0lBQ0YsR0FBRztRQUFDZCxLQUFLSyxJQUFJO1FBQUVMLEtBQUtNLElBQUk7UUFBRU4sS0FBS1EsUUFBUTtLQUFDO0lBRXhDLHFCQUNFLDhEQUFDZDtRQUFJQyxXQUFVOzswQkFFYiw4REFBQ1YsMkNBQVFBO2dCQUFDOEIsd0JBQVUsOERBQUNyQjtvQkFBSUMsV0FBVTs7Ozs7OzBCQUNqQyw0RUFBQ0U7b0JBQU9HLE1BQU1BO29CQUFNQyxPQUFPQTtvQkFBT0MsWUFBWUE7b0JBQVljLGVBQWU7Ozs7Ozs7Ozs7OzBCQUczRSw4REFBQ3RCO2dCQUFJQyxXQUFVOztrQ0FFYiw4REFBQ1YsMkNBQVFBO3dCQUFDOEIsd0JBQVUsOERBQUNyQjs0QkFBSUMsV0FBVTs7Ozs7O2tDQUNqQyw0RUFBQ0g7NEJBQVFRLE1BQU1BOzs7Ozs7Ozs7OztrQ0FJakIsOERBQUNpQjt3QkFBS3RCLFdBQVU7a0NBQ2QsNEVBQUNEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ1YsMkNBQVFBO29DQUFDOEIsd0JBQ1IsOERBQUNyQjt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzs7Ozs7MERBQ2YsOERBQUNEO2dEQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7OENBR2hCSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUWpCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va29kZXhndWFyZC8uL2NvbXBvbmVudHMvRGFzaGJvYXJkTGF5b3V0LnRzeD9jNTZlIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBSZWFjdE5vZGUsIHVzZUVmZmVjdCwgU3VzcGVuc2UgfSBmcm9tICdyZWFjdCdcbmltcG9ydCBkeW5hbWljIGZyb20gJ25leHQvZHluYW1pYydcbmltcG9ydCB7IHByZWxvYWRlciwgb2JzZXJ2ZUxpbmtzIH0gZnJvbSAnQC9saWIvcHJlbG9hZGVyJ1xuaW1wb3J0IHsgcGVyZm9ybWFuY2VNb25pdG9yLCBtYXJrUm91dGVDaGFuZ2VDb21wbGV0ZSB9IGZyb20gJ0AvbGliL3BlcmZvcm1hbmNlJ1xuaW1wb3J0IHsgY2FjaGUgfSBmcm9tICdAL2xpYi9jYWNoZSdcblxuLy8gRHluYW1pYyBpbXBvcnRzIGZvciBiZXR0ZXIgY29kZSBzcGxpdHRpbmdcbmNvbnN0IFNpZGViYXIgPSBkeW5hbWljKCgpID0+IGltcG9ydCgnLi9TaWRlYmFyJyksIHtcbiAgbG9hZGluZzogKCkgPT4gPGRpdiBjbGFzc05hbWU9XCJ3LTY0IGJnLWdyYXktOTAwIGFuaW1hdGUtcHVsc2VcIiAvPixcbiAgc3NyOiB0cnVlXG59KVxuXG5jb25zdCBOYXZiYXIgPSBkeW5hbWljKCgpID0+IGltcG9ydCgnLi9OYXZiYXInKSwge1xuICBsb2FkaW5nOiAoKSA9PiA8ZGl2IGNsYXNzTmFtZT1cImgtMTYgYmctZ3JheS05MDAgYW5pbWF0ZS1wdWxzZVwiIC8+LFxuICBzc3I6IHRydWVcbn0pXG5cbmludGVyZmFjZSBEYXNoYm9hcmRMYXlvdXRQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGVcbiAgdXNlcjoge1xuICAgIHVzZXJuYW1lOiBzdHJpbmdcbiAgICBhdmF0YXI/OiBzdHJpbmdcbiAgICByb2xlOiBzdHJpbmdcbiAgICBwbGFuOiBzdHJpbmdcbiAgfVxuICB0aXRsZT86IHN0cmluZ1xuICBzaG93U2VhcmNoPzogYm9vbGVhblxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBEYXNoYm9hcmRMYXlvdXQoe1xuICBjaGlsZHJlbixcbiAgdXNlcixcbiAgdGl0bGUsXG4gIHNob3dTZWFyY2ggPSB0cnVlXG59OiBEYXNoYm9hcmRMYXlvdXRQcm9wcykge1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIFBlcmZvcm1hbmNlIG9wdGltaXphdGlvbnMgb24gbW91bnRcbiAgICBjb25zdCBpbml0T3B0aW1pemF0aW9ucyA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIC8vIE1hcmsgcm91dGUgY2hhbmdlIGNvbXBsZXRlIGZvciBwZXJmb3JtYW5jZSB0cmFja2luZ1xuICAgICAgICBtYXJrUm91dGVDaGFuZ2VDb21wbGV0ZSgpXG5cbiAgICAgICAgLy8gUHJlbG9hZCB1c2VyIGpvdXJuZXkgYmFzZWQgb24gcm9sZSBhbmQgcGxhblxuICAgICAgICBhd2FpdCBwcmVsb2FkZXIucHJlbG9hZFVzZXJKb3VybmV5KHVzZXIucm9sZSwgdXNlci5wbGFuKVxuXG4gICAgICAgIC8vIFByZWxvYWQgY3JpdGljYWwgZGF0YSBmb3IgY3VycmVudCB1c2VyXG4gICAgICAgIGF3YWl0IGNhY2hlLnByZWxvYWRDcml0aWNhbERhdGEodXNlci51c2VybmFtZSlcblxuICAgICAgICAvLyBPYnNlcnZlIGxpbmtzIGZvciBpbnRlcnNlY3Rpb24tYmFzZWQgcHJlbG9hZGluZ1xuICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICBvYnNlcnZlTGlua3MoKVxuICAgICAgICB9LCAxMDAwKVxuXG4gICAgICAgIC8vIFByZWxvYWQgY3JpdGljYWwgYXNzZXRzXG4gICAgICAgIGF3YWl0IHByZWxvYWRlci5wcmVsb2FkQ3JpdGljYWxBc3NldHMoKVxuXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLndhcm4oJ09wdGltaXphdGlvbiBpbml0aWFsaXphdGlvbiBmYWlsZWQ6JywgZXJyb3IpXG4gICAgICB9XG4gICAgfVxuXG4gICAgaW5pdE9wdGltaXphdGlvbnMoKVxuXG4gICAgLy8gQ2xlYW51cCBvbiB1bm1vdW50XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHBlcmZvcm1hbmNlTW9uaXRvci5jbGVhbnVwKClcbiAgICB9XG4gIH0sIFt1c2VyLnJvbGUsIHVzZXIucGxhbiwgdXNlci51c2VybmFtZV0pXG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmFkaWVudC1jeWJlclwiPlxuICAgICAgey8qIFRvcCBOYXZiYXIgd2l0aCBTdXNwZW5zZSAqL31cbiAgICAgIDxTdXNwZW5zZSBmYWxsYmFjaz17PGRpdiBjbGFzc05hbWU9XCJoLTE2IGJnLWdyYXktOTAwIGFuaW1hdGUtcHVsc2VcIiAvPn0+XG4gICAgICAgIDxOYXZiYXIgdXNlcj17dXNlcn0gdGl0bGU9e3RpdGxlfSBzaG93U2VhcmNoPXtzaG93U2VhcmNofSBpc0xhbmRpbmdQYWdlPXtmYWxzZX0gLz5cbiAgICAgIDwvU3VzcGVuc2U+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBwdC0xNlwiPlxuICAgICAgICB7LyogU2lkZWJhciB3aXRoIFN1c3BlbnNlICovfVxuICAgICAgICA8U3VzcGVuc2UgZmFsbGJhY2s9ezxkaXYgY2xhc3NOYW1lPVwidy02NCBiZy1ncmF5LTkwMCBhbmltYXRlLXB1bHNlXCIgLz59PlxuICAgICAgICAgIDxTaWRlYmFyIHVzZXI9e3VzZXJ9IC8+XG4gICAgICAgIDwvU3VzcGVuc2U+XG5cbiAgICAgICAgey8qIE1haW4gQ29udGVudCBBcmVhICovfVxuICAgICAgICA8bWFpbiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMCBsZzptbC0wXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOCBweS04XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbFwiPlxuICAgICAgICAgICAgICA8U3VzcGVuc2UgZmFsbGJhY2s9e1xuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtOCBiZy1ncmF5LTgwMCByb3VuZGVkIGFuaW1hdGUtcHVsc2VcIiAvPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTY0IGJnLWdyYXktODAwIHJvdW5kZWQgYW5pbWF0ZS1wdWxzZVwiIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIH0+XG4gICAgICAgICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgICAgICA8L1N1c3BlbnNlPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvbWFpbj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwiU3VzcGVuc2UiLCJkeW5hbWljIiwicHJlbG9hZGVyIiwib2JzZXJ2ZUxpbmtzIiwicGVyZm9ybWFuY2VNb25pdG9yIiwibWFya1JvdXRlQ2hhbmdlQ29tcGxldGUiLCJjYWNoZSIsIlNpZGViYXIiLCJsb2FkaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwic3NyIiwiTmF2YmFyIiwiRGFzaGJvYXJkTGF5b3V0IiwiY2hpbGRyZW4iLCJ1c2VyIiwidGl0bGUiLCJzaG93U2VhcmNoIiwiaW5pdE9wdGltaXphdGlvbnMiLCJwcmVsb2FkVXNlckpvdXJuZXkiLCJyb2xlIiwicGxhbiIsInByZWxvYWRDcml0aWNhbERhdGEiLCJ1c2VybmFtZSIsInNldFRpbWVvdXQiLCJwcmVsb2FkQ3JpdGljYWxBc3NldHMiLCJlcnJvciIsImNvbnNvbGUiLCJ3YXJuIiwiY2xlYW51cCIsImZhbGxiYWNrIiwiaXNMYW5kaW5nUGFnZSIsIm1haW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/cache.ts":
/*!**********************!*\
  !*** ./lib/cache.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CacheKeys: () => (/* binding */ CacheKeys),\n/* harmony export */   cache: () => (/* binding */ cache),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Advanced Caching System for KodeXGuard\n// Faster than McMaster-Carr with multi-layer caching\nclass AdvancedCache {\n    constructor(){\n        this.memoryCache = new Map();\n        this.diskCache = null;\n        this.stats = {\n            hits: 0,\n            misses: 0,\n            sets: 0,\n            deletes: 0,\n            size: 0\n        };\n        // Cache TTL configurations (in milliseconds)\n        this.TTL = {\n            STATIC: 24 * 60 * 60 * 1000,\n            API: 5 * 60 * 1000,\n            USER_DATA: 15 * 60 * 1000,\n            SEARCH: 10 * 60 * 1000,\n            DASHBOARD: 2 * 60 * 1000,\n            REALTIME: 30 * 1000\n        };\n        this.initDiskCache();\n        this.startCleanupInterval();\n    }\n    async initDiskCache() {\n        if (false) {}\n    }\n    // Memory Cache Operations\n    isExpired(item) {\n        return Date.now() - item.timestamp > item.ttl;\n    }\n    generateKey(prefix, identifier) {\n        return `${prefix}:${identifier}`;\n    }\n    // Get from cache with fallback chain: Memory -> Disk -> Source\n    async get(key, fallback, ttl) {\n        // Try memory cache first\n        const memoryItem = this.memoryCache.get(key);\n        if (memoryItem && !this.isExpired(memoryItem)) {\n            this.stats.hits++;\n            return memoryItem.data;\n        }\n        // Try disk cache\n        if (this.diskCache) {\n            try {\n                const diskItem = await this.diskCache.get(\"cache\", key);\n                if (diskItem && !this.isExpired(diskItem)) {\n                    // Promote to memory cache\n                    this.memoryCache.set(key, diskItem);\n                    this.stats.hits++;\n                    return diskItem.data;\n                }\n            } catch (error) {\n                console.warn(\"Disk cache read error:\", error);\n            }\n        }\n        // Cache miss - use fallback if provided\n        if (fallback) {\n            try {\n                const data = await fallback();\n                if (data !== null && data !== undefined) {\n                    await this.set(key, data, ttl || this.TTL.API);\n                }\n                this.stats.misses++;\n                return data;\n            } catch (error) {\n                console.error(\"Cache fallback error:\", error);\n                this.stats.misses++;\n                return null;\n            }\n        }\n        this.stats.misses++;\n        return null;\n    }\n    // Set cache with automatic tier management\n    async set(key, data, ttl = this.TTL.API) {\n        const item = {\n            data,\n            timestamp: Date.now(),\n            ttl,\n            version: \"1.0\"\n        };\n        // Always set in memory cache\n        this.memoryCache.set(key, item);\n        this.stats.sets++;\n        this.stats.size = this.memoryCache.size;\n        // Set in disk cache for persistence\n        if (this.diskCache && ttl > this.TTL.REALTIME) {\n            try {\n                await this.diskCache.put(\"cache\", {\n                    key,\n                    ...item\n                });\n            } catch (error) {\n                console.warn(\"Disk cache write error:\", error);\n            }\n        }\n        // Memory management - remove oldest items if cache is too large\n        if (this.memoryCache.size > 1000) {\n            this.evictOldest();\n        }\n    }\n    // Delete from all cache tiers\n    async delete(key) {\n        this.memoryCache.delete(key);\n        this.stats.deletes++;\n        this.stats.size = this.memoryCache.size;\n        if (this.diskCache) {\n            try {\n                await this.diskCache.delete(\"cache\", key);\n            } catch (error) {\n                console.warn(\"Disk cache delete error:\", error);\n            }\n        }\n    }\n    // Clear all caches\n    async clear() {\n        this.memoryCache.clear();\n        this.stats = {\n            hits: 0,\n            misses: 0,\n            sets: 0,\n            deletes: 0,\n            size: 0\n        };\n        if (this.diskCache) {\n            try {\n                await this.diskCache.clear(\"cache\");\n            } catch (error) {\n                console.warn(\"Disk cache clear error:\", error);\n            }\n        }\n    }\n    // Cache management\n    evictOldest() {\n        const entries = Array.from(this.memoryCache.entries());\n        entries.sort((a, b)=>a[1].timestamp - b[1].timestamp);\n        // Remove oldest 10% of entries\n        const toRemove = Math.floor(entries.length * 0.1);\n        for(let i = 0; i < toRemove; i++){\n            this.memoryCache.delete(entries[i][0]);\n        }\n    }\n    startCleanupInterval() {\n        if (false) {}\n    }\n    cleanup() {\n        const now = Date.now();\n        for (const [key, item] of this.memoryCache.entries()){\n            if (this.isExpired(item)) {\n                this.memoryCache.delete(key);\n            }\n        }\n        this.stats.size = this.memoryCache.size;\n    }\n    // Specialized cache methods for different data types\n    async cacheUserData(userId, data) {\n        await this.set(`user:${userId}`, data, this.TTL.USER_DATA);\n    }\n    async getUserData(userId) {\n        return await this.get(`user:${userId}`);\n    }\n    async cacheSearchResults(query, results) {\n        const key = `search:${btoa(query)}`;\n        await this.set(key, results, this.TTL.SEARCH);\n    }\n    async getSearchResults(query) {\n        const key = `search:${btoa(query)}`;\n        return await this.get(key);\n    }\n    async cacheDashboardData(userId, data) {\n        await this.set(`dashboard:${userId}`, data, this.TTL.DASHBOARD);\n    }\n    async getDashboardData(userId) {\n        return await this.get(`dashboard:${userId}`);\n    }\n    async cacheApiResponse(endpoint, params, response) {\n        const key = `api:${endpoint}:${JSON.stringify(params)}`;\n        await this.set(key, response, this.TTL.API);\n    }\n    async getApiResponse(endpoint, params) {\n        const key = `api:${endpoint}:${JSON.stringify(params)}`;\n        return await this.get(key);\n    }\n    // Cache statistics\n    getStats() {\n        return {\n            ...this.stats\n        };\n    }\n    getHitRate() {\n        const total = this.stats.hits + this.stats.misses;\n        return total > 0 ? this.stats.hits / total * 100 : 0;\n    }\n    // Preload critical data\n    async preloadCriticalData(userId) {\n        const criticalEndpoints = [\n            \"/api/user/profile\",\n            \"/api/dashboard/stats\",\n            \"/api/notifications\",\n            \"/api/user/plan\"\n        ];\n        const preloadPromises = criticalEndpoints.map(async (endpoint)=>{\n            try {\n                const response = await fetch(endpoint);\n                if (response.ok) {\n                    const data = await response.json();\n                    await this.cacheApiResponse(endpoint, {\n                        userId\n                    }, data);\n                }\n            } catch (error) {\n                console.warn(`Preload failed for ${endpoint}:`, error);\n            }\n        });\n        await Promise.allSettled(preloadPromises);\n    }\n}\n// Singleton instance\nconst cache = new AdvancedCache();\n// Cache utilities\nconst CacheKeys = {\n    USER_PROFILE: (id)=>`user:profile:${id}`,\n    DASHBOARD_STATS: (id)=>`dashboard:stats:${id}`,\n    SEARCH_RESULTS: (query)=>`search:${btoa(query)}`,\n    API_RESPONSE: (endpoint, params)=>`api:${endpoint}:${JSON.stringify(params)}`,\n    STATIC_DATA: (key)=>`static:${key}`,\n    REALTIME_DATA: (key)=>`realtime:${key}`\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (cache);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/cache.ts\n");

/***/ }),

/***/ "(ssr)/./lib/performance.ts":
/*!****************************!*\
  !*** ./lib/performance.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getPerformanceMetrics: () => (/* binding */ getPerformanceMetrics),\n/* harmony export */   getPerformanceScore: () => (/* binding */ getPerformanceScore),\n/* harmony export */   markRouteChangeComplete: () => (/* binding */ markRouteChangeComplete),\n/* harmony export */   performanceMonitor: () => (/* binding */ performanceMonitor)\n/* harmony export */ });\n// Advanced Performance Monitoring & Analytics\n// Real-time performance tracking and optimization\nclass PerformanceMonitor {\n    constructor(){\n        this.metrics = {};\n        this.budget = {\n            pageLoadTime: 2000,\n            firstContentfulPaint: 1000,\n            largestContentfulPaint: 2500,\n            firstInputDelay: 100,\n            cumulativeLayoutShift: 0.1,\n            timeToInteractive: 3000 // 3 seconds\n        };\n        this.observers = new Map();\n        this.routeStartTime = 0;\n        this.initPerformanceObservers();\n        this.trackPageLoad();\n        this.trackRouteChanges();\n    }\n    // Initialize performance observers\n    initPerformanceObservers() {\n        if (true) return;\n        // Core Web Vitals Observer\n        this.initWebVitalsObserver();\n        // Navigation Observer\n        this.initNavigationObserver();\n        // Resource Observer\n        this.initResourceObserver();\n        // Long Task Observer\n        this.initLongTaskObserver();\n    }\n    // Web Vitals Observer (FCP, LCP, FID, CLS)\n    initWebVitalsObserver() {\n        if (!(\"PerformanceObserver\" in window)) return;\n        // First Contentful Paint\n        const fcpObserver = new PerformanceObserver((list)=>{\n            const entries = list.getEntries();\n            const fcp = entries[entries.length - 1];\n            this.metrics.firstContentfulPaint = fcp.startTime;\n            this.checkBudget(\"firstContentfulPaint\", fcp.startTime);\n        });\n        fcpObserver.observe({\n            entryTypes: [\n                \"paint\"\n            ]\n        });\n        this.observers.set(\"fcp\", fcpObserver);\n        // Largest Contentful Paint\n        const lcpObserver = new PerformanceObserver((list)=>{\n            const entries = list.getEntries();\n            const lcp = entries[entries.length - 1];\n            this.metrics.largestContentfulPaint = lcp.startTime;\n            this.checkBudget(\"largestContentfulPaint\", lcp.startTime);\n        });\n        lcpObserver.observe({\n            entryTypes: [\n                \"largest-contentful-paint\"\n            ]\n        });\n        this.observers.set(\"lcp\", lcpObserver);\n        // First Input Delay\n        const fidObserver = new PerformanceObserver((list)=>{\n            const entries = list.getEntries();\n            entries.forEach((entry)=>{\n                this.metrics.firstInputDelay = entry.processingStart - entry.startTime;\n                this.checkBudget(\"firstInputDelay\", this.metrics.firstInputDelay);\n            });\n        });\n        fidObserver.observe({\n            entryTypes: [\n                \"first-input\"\n            ]\n        });\n        this.observers.set(\"fid\", fidObserver);\n        // Cumulative Layout Shift\n        let clsValue = 0;\n        const clsObserver = new PerformanceObserver((list)=>{\n            const entries = list.getEntries();\n            entries.forEach((entry)=>{\n                if (!entry.hadRecentInput) {\n                    clsValue += entry.value;\n                }\n            });\n            this.metrics.cumulativeLayoutShift = clsValue;\n            this.checkBudget(\"cumulativeLayoutShift\", clsValue);\n        });\n        clsObserver.observe({\n            entryTypes: [\n                \"layout-shift\"\n            ]\n        });\n        this.observers.set(\"cls\", clsObserver);\n    }\n    // Navigation Observer\n    initNavigationObserver() {\n        if (!(\"PerformanceObserver\" in window)) return;\n        const navObserver = new PerformanceObserver((list)=>{\n            const entries = list.getEntries();\n            entries.forEach((entry)=>{\n                this.metrics.pageLoadTime = entry.loadEventEnd - entry.fetchStart;\n                this.metrics.timeToInteractive = entry.domInteractive - entry.fetchStart;\n                this.checkBudget(\"pageLoadTime\", this.metrics.pageLoadTime);\n                this.checkBudget(\"timeToInteractive\", this.metrics.timeToInteractive);\n            });\n        });\n        navObserver.observe({\n            entryTypes: [\n                \"navigation\"\n            ]\n        });\n        this.observers.set(\"navigation\", navObserver);\n    }\n    // Resource Observer\n    initResourceObserver() {\n        if (!(\"PerformanceObserver\" in window)) return;\n        const resourceObserver = new PerformanceObserver((list)=>{\n            const entries = list.getEntries();\n            entries.forEach((entry)=>{\n                // Track slow resources\n                if (entry.duration > 1000) {\n                    console.warn(`Slow resource detected: ${entry.name} (${entry.duration}ms)`);\n                }\n                // Track API response times\n                if (entry.name.includes(\"/api/\")) {\n                    this.metrics.apiResponseTime = entry.duration;\n                }\n            });\n        });\n        resourceObserver.observe({\n            entryTypes: [\n                \"resource\"\n            ]\n        });\n        this.observers.set(\"resource\", resourceObserver);\n    }\n    // Long Task Observer\n    initLongTaskObserver() {\n        if (!(\"PerformanceObserver\" in window)) return;\n        let totalBlockingTime = 0;\n        const longTaskObserver = new PerformanceObserver((list)=>{\n            const entries = list.getEntries();\n            entries.forEach((entry)=>{\n                // Tasks longer than 50ms are considered blocking\n                if (entry.duration > 50) {\n                    totalBlockingTime += entry.duration - 50;\n                }\n            });\n            this.metrics.totalBlockingTime = totalBlockingTime;\n        });\n        longTaskObserver.observe({\n            entryTypes: [\n                \"longtask\"\n            ]\n        });\n        this.observers.set(\"longtask\", longTaskObserver);\n    }\n    // Track page load performance\n    trackPageLoad() {\n        if (true) return;\n        window.addEventListener(\"load\", ()=>{\n            // Use setTimeout to ensure all metrics are captured\n            setTimeout(()=>{\n                this.calculatePerformanceScore();\n                this.reportMetrics();\n            }, 1000);\n        });\n    }\n    // Track route changes (for SPA navigation)\n    trackRouteChanges() {\n        if (true) return;\n        // Track route change start\n        const originalPushState = history.pushState;\n        const originalReplaceState = history.replaceState;\n        history.pushState = (...args)=>{\n            this.routeStartTime = performance.now();\n            return originalPushState.apply(history, args);\n        };\n        history.replaceState = (...args)=>{\n            this.routeStartTime = performance.now();\n            return originalReplaceState.apply(history, args);\n        };\n        // Track route change end\n        window.addEventListener(\"popstate\", ()=>{\n            this.routeStartTime = performance.now();\n        });\n    }\n    // Mark route change complete\n    markRouteChangeComplete() {\n        if (this.routeStartTime > 0) {\n            this.metrics.routeChangeTime = performance.now() - this.routeStartTime;\n            this.routeStartTime = 0;\n            // Log slow route changes\n            if (this.metrics.routeChangeTime > 500) {\n                console.warn(`Slow route change: ${this.metrics.routeChangeTime}ms`);\n            }\n        }\n    }\n    // Check performance budget\n    checkBudget(metric, value) {\n        const budgetValue = this.budget[metric];\n        if (value > budgetValue) {\n            console.warn(`Performance budget exceeded for ${metric}: ${value}ms > ${budgetValue}ms`);\n            // Trigger optimization suggestions\n            this.suggestOptimizations(metric, value, budgetValue);\n        }\n    }\n    // Suggest optimizations based on metrics\n    suggestOptimizations(metric, actual, budget) {\n        const suggestions = {\n            pageLoadTime: [\n                \"Enable compression (gzip/brotli)\",\n                \"Optimize images and use WebP format\",\n                \"Implement code splitting\",\n                \"Use CDN for static assets\"\n            ],\n            firstContentfulPaint: [\n                \"Optimize critical CSS\",\n                \"Preload key resources\",\n                \"Reduce server response time\",\n                \"Eliminate render-blocking resources\"\n            ],\n            largestContentfulPaint: [\n                \"Optimize images above the fold\",\n                \"Preload LCP element\",\n                \"Reduce server response time\",\n                \"Remove unused CSS\"\n            ],\n            firstInputDelay: [\n                \"Reduce JavaScript execution time\",\n                \"Break up long tasks\",\n                \"Use web workers for heavy computations\",\n                \"Optimize third-party scripts\"\n            ],\n            cumulativeLayoutShift: [\n                \"Set size attributes on images and videos\",\n                \"Reserve space for ads and embeds\",\n                \"Avoid inserting content above existing content\",\n                \"Use CSS aspect-ratio for responsive images\"\n            ]\n        };\n        const metricSuggestions = suggestions[metric] || [];\n        console.group(`🚀 Performance Optimization Suggestions for ${metric}`);\n        console.log(`Current: ${actual}ms, Budget: ${budget}ms`);\n        metricSuggestions.forEach((suggestion)=>console.log(`• ${suggestion}`));\n        console.groupEnd();\n    }\n    // Calculate overall performance score\n    calculatePerformanceScore() {\n        const weights = {\n            firstContentfulPaint: 0.15,\n            largestContentfulPaint: 0.25,\n            firstInputDelay: 0.25,\n            cumulativeLayoutShift: 0.25,\n            totalBlockingTime: 0.1\n        };\n        let score = 100;\n        Object.entries(weights).forEach(([metric, weight])=>{\n            const value = this.metrics[metric];\n            const budget = this.budget[metric];\n            if (value && budget) {\n                const ratio = value / budget;\n                if (ratio > 1) {\n                    score -= (ratio - 1) * weight * 100;\n                }\n            }\n        });\n        return Math.max(0, Math.min(100, score));\n    }\n    // Report metrics to analytics\n    reportMetrics() {\n        const score = this.calculatePerformanceScore();\n        console.group(\"\\uD83D\\uDCCA Performance Metrics Report\");\n        console.log(\"Overall Score:\", `${score.toFixed(1)}/100`);\n        console.log(\"Metrics:\", this.metrics);\n        console.log(\"Cache Hit Rate:\", this.getCacheHitRate());\n        console.groupEnd();\n        // Send to analytics service (implement as needed)\n        this.sendToAnalytics({\n            ...this.metrics,\n            performanceScore: score,\n            timestamp: Date.now(),\n            userAgent: navigator.userAgent,\n            url: window.location.href\n        });\n    }\n    // Get cache hit rate from cache system\n    getCacheHitRate() {\n        // This would integrate with your cache system\n        return 0 // Placeholder\n        ;\n    }\n    // Send metrics to analytics service\n    sendToAnalytics(data) {\n        // Implement analytics reporting\n        // Could be Google Analytics, custom analytics, etc.\n        if (true) {\n            console.log(\"\\uD83D\\uDCC8 Analytics Data:\", data);\n        }\n    }\n    // Get current metrics\n    getMetrics() {\n        return {\n            ...this.metrics\n        };\n    }\n    // Get performance score\n    getPerformanceScore() {\n        return this.calculatePerformanceScore();\n    }\n    // Update performance budget\n    updateBudget(newBudget) {\n        this.budget = {\n            ...this.budget,\n            ...newBudget\n        };\n    }\n    // Cleanup observers\n    cleanup() {\n        this.observers.forEach((observer)=>observer.disconnect());\n        this.observers.clear();\n    }\n}\n// Singleton instance\nconst performanceMonitor = new PerformanceMonitor();\n// Utility functions\nconst markRouteChangeComplete = ()=>{\n    performanceMonitor.markRouteChangeComplete();\n};\nconst getPerformanceMetrics = ()=>{\n    return performanceMonitor.getMetrics();\n};\nconst getPerformanceScore = ()=>{\n    return performanceMonitor.getPerformanceScore();\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (performanceMonitor);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/performance.ts\n");

/***/ }),

/***/ "(ssr)/./lib/preloader.ts":
/*!**************************!*\
  !*** ./lib/preloader.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   observeLinks: () => (/* binding */ observeLinks),\n/* harmony export */   preloadRoute: () => (/* binding */ preloadRoute),\n/* harmony export */   preloadUserJourney: () => (/* binding */ preloadUserJourney),\n/* harmony export */   preloader: () => (/* binding */ preloader)\n/* harmony export */ });\n/* harmony import */ var _cache__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cache */ \"(ssr)/./lib/cache.ts\");\n// Advanced Pre-rendering & Route Prefetching System\n// McMaster-Carr level performance optimization\n\nclass AdvancedPreloader {\n    constructor(){\n        this.preloadQueue = new Map();\n        this.preloadedRoutes = new Set();\n        this.isPreloading = false;\n        this.observer = null;\n        // Route strategies for intelligent preloading\n        this.ROUTE_STRATEGIES = [\n            {\n                route: \"/dashboard\",\n                dependencies: [\n                    \"/osint\",\n                    \"/scanner\",\n                    \"/profile\"\n                ],\n                dataEndpoints: [\n                    \"/api/dashboard/stats\",\n                    \"/api/user/profile\",\n                    \"/api/notifications\"\n                ],\n                assets: [],\n                priority: 1\n            },\n            {\n                route: \"/osint\",\n                dependencies: [\n                    \"/file-analyzer\",\n                    \"/cve\"\n                ],\n                dataEndpoints: [\n                    \"/api/osint/recent\",\n                    \"/api/osint/templates\"\n                ],\n                assets: [],\n                priority: 2\n            },\n            {\n                route: \"/scanner\",\n                dependencies: [\n                    \"/cve\",\n                    \"/tools\"\n                ],\n                dataEndpoints: [\n                    \"/api/scanner/history\",\n                    \"/api/scanner/templates\"\n                ],\n                assets: [],\n                priority: 2\n            },\n            {\n                route: \"/profile\",\n                dependencies: [\n                    \"/settings\",\n                    \"/plan\"\n                ],\n                dataEndpoints: [\n                    \"/api/user/profile\",\n                    \"/api/user/api-keys\",\n                    \"/api/user/usage\"\n                ],\n                assets: [],\n                priority: 3\n            },\n            {\n                route: \"/settings\",\n                dependencies: [],\n                dataEndpoints: [\n                    \"/api/user/settings\",\n                    \"/api/user/preferences\"\n                ],\n                assets: [],\n                priority: 4\n            }\n        ];\n        this.initIntersectionObserver();\n        this.initRoutePreloading();\n    }\n    // Initialize intersection observer for link prefetching\n    initIntersectionObserver() {\n        if (true) return;\n        this.observer = new IntersectionObserver((entries)=>{\n            entries.forEach((entry)=>{\n                if (entry.isIntersecting) {\n                    const link = entry.target;\n                    const href = link.getAttribute(\"href\");\n                    if (href && !this.preloadedRoutes.has(href)) {\n                        this.preloadRoute(href, \"low\");\n                    }\n                }\n            });\n        }, {\n            rootMargin: \"100px\",\n            threshold: 0.1\n        });\n    }\n    // Initialize route preloading based on current page\n    initRoutePreloading() {\n        if (true) return;\n        // Preload critical routes immediately\n        this.preloadCriticalRoutes();\n        // Set up hover preloading\n        this.setupHoverPreloading();\n        // Preload based on user behavior patterns\n        this.setupBehaviorBasedPreloading();\n    }\n    // Preload critical routes immediately\n    async preloadCriticalRoutes() {\n        const currentPath = window.location.pathname;\n        const strategy = this.ROUTE_STRATEGIES.find((s)=>s.route === currentPath);\n        if (strategy) {\n            // Preload dependencies of current route\n            for (const dep of strategy.dependencies){\n                await this.preloadRoute(dep, \"high\");\n            }\n            // Preload data endpoints\n            for (const endpoint of strategy.dataEndpoints){\n                await this.preloadData(endpoint);\n            }\n        }\n        // Always preload dashboard if not current page\n        if (currentPath !== \"/dashboard\") {\n            await this.preloadRoute(\"/dashboard\", \"medium\");\n        }\n    }\n    // Setup hover-based preloading\n    setupHoverPreloading() {\n        if (true) return;\n        document.addEventListener(\"mouseover\", (event)=>{\n            const target = event.target;\n            const link = target.closest(\"a[href]\");\n            if (link && link.href) {\n                const href = new URL(link.href).pathname;\n                if (!this.preloadedRoutes.has(href)) {\n                    // Debounce hover preloading\n                    setTimeout(()=>{\n                        this.preloadRoute(href, \"medium\");\n                    }, 100);\n                }\n            }\n        });\n    }\n    // Setup behavior-based preloading\n    setupBehaviorBasedPreloading() {\n        if (true) return;\n        // Preload based on user's typical navigation patterns\n        const userBehavior = this.getUserBehaviorPattern();\n        // Preload likely next routes based on current page\n        const currentPath = window.location.pathname;\n        const likelyNext = this.predictNextRoutes(currentPath, userBehavior);\n        likelyNext.forEach((route, index)=>{\n            setTimeout(()=>{\n                this.preloadRoute(route, \"low\");\n            }, index * 1000) // Stagger preloading\n            ;\n        });\n    }\n    // Predict next routes based on current page and user behavior\n    predictNextRoutes(currentPath, behavior) {\n        const predictions = {\n            \"/dashboard\": [\n                \"/osint\",\n                \"/scanner\",\n                \"/profile\"\n            ],\n            \"/osint\": [\n                \"/file-analyzer\",\n                \"/cve\",\n                \"/dashboard\"\n            ],\n            \"/scanner\": [\n                \"/cve\",\n                \"/tools\",\n                \"/dashboard\"\n            ],\n            \"/profile\": [\n                \"/settings\",\n                \"/plan\",\n                \"/dashboard\"\n            ],\n            \"/\": [\n                \"/dashboard\",\n                \"/login\",\n                \"/register\"\n            ]\n        };\n        return predictions[currentPath] || [];\n    }\n    // Get user behavior pattern from localStorage\n    getUserBehaviorPattern() {\n        if (true) return {};\n        try {\n            const stored = localStorage.getItem(\"kodexguard_user_behavior\");\n            return stored ? JSON.parse(stored) : {};\n        } catch  {\n            return {};\n        }\n    }\n    // Main preload route method\n    async preloadRoute(route, priority = \"medium\") {\n        if (this.preloadedRoutes.has(route)) return;\n        try {\n            // Use Next.js router prefetch\n            if (false) {}\n            // Preload route data\n            const strategy = this.ROUTE_STRATEGIES.find((s)=>s.route === route);\n            if (strategy) {\n                // Preload data endpoints\n                const dataPromises = strategy.dataEndpoints.map((endpoint)=>this.preloadData(endpoint));\n                await Promise.allSettled(dataPromises);\n            }\n            this.preloadedRoutes.add(route);\n            console.log(`✅ Preloaded route: ${route}`);\n        } catch (error) {\n            console.warn(`❌ Failed to preload route ${route}:`, error);\n        }\n    }\n    // Preload data for endpoints\n    async preloadData(endpoint) {\n        try {\n            // Check if already cached\n            const cached = await _cache__WEBPACK_IMPORTED_MODULE_0__.cache.get(endpoint);\n            if (cached) return;\n            // Fetch and cache data\n            const response = await fetch(endpoint, {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                await _cache__WEBPACK_IMPORTED_MODULE_0__.cache.set(endpoint, data);\n                console.log(`📦 Preloaded data: ${endpoint}`);\n            }\n        } catch (error) {\n            console.warn(`❌ Failed to preload data ${endpoint}:`, error);\n        }\n    }\n    // Preload critical assets\n    async preloadCriticalAssets() {\n        const criticalAssets = [\n            \"/images/logo.svg\",\n            \"/images/cyber-bg.jpg\"\n        ];\n        const preloadPromises = criticalAssets.map((asset)=>{\n            return new Promise((resolve)=>{\n                const link = document.createElement(\"link\");\n                link.rel = \"preload\";\n                link.href = asset;\n                link.as = asset.endsWith(\".svg\") ? \"image\" : \"image\";\n                link.onload = ()=>resolve();\n                link.onerror = ()=>resolve() // Don't fail on asset errors\n                ;\n                document.head.appendChild(link);\n            });\n        });\n        await Promise.allSettled(preloadPromises);\n    }\n    // Observe links for intersection-based preloading\n    observeLinks() {\n        if (!this.observer) return;\n        const links = document.querySelectorAll('a[href^=\"/\"]');\n        links.forEach((link)=>{\n            this.observer.observe(link);\n        });\n    }\n    // Preload entire user journey\n    async preloadUserJourney(userRole, userPlan) {\n        const journeyRoutes = this.getUserJourneyRoutes(userRole, userPlan);\n        for (const [index, route] of journeyRoutes.entries()){\n            // Stagger preloading to avoid overwhelming the browser\n            setTimeout(()=>{\n                this.preloadRoute(route, index < 3 ? \"high\" : \"low\");\n            }, index * 500);\n        }\n    }\n    // Get user journey routes based on role and plan\n    getUserJourneyRoutes(userRole, userPlan) {\n        const baseJourney = [\n            \"/dashboard\",\n            \"/profile\",\n            \"/settings\"\n        ];\n        if (userPlan === \"cybersecurity\" || userPlan === \"bughunter\") {\n            return [\n                ...baseJourney,\n                \"/osint\",\n                \"/scanner\",\n                \"/file-analyzer\",\n                \"/cve\",\n                \"/playground\",\n                \"/bot\"\n            ];\n        }\n        return [\n            ...baseJourney,\n            \"/osint\",\n            \"/file-analyzer\",\n            \"/cve\",\n            \"/tools\"\n        ];\n    }\n    // Get preloading statistics\n    getStats() {\n        return {\n            preloadedRoutes: Array.from(this.preloadedRoutes),\n            queueSize: this.preloadQueue.size,\n            isPreloading: this.isPreloading,\n            cacheStats: _cache__WEBPACK_IMPORTED_MODULE_0__.cache.getStats()\n        };\n    }\n    // Clear preloaded routes\n    clearPreloaded() {\n        this.preloadedRoutes.clear();\n        this.preloadQueue.clear();\n    }\n}\n// Singleton instance\nconst preloader = new AdvancedPreloader();\n// Utility functions\nconst preloadRoute = (route, priority)=>{\n    return preloader.preloadRoute(route, priority);\n};\nconst preloadUserJourney = (userRole, userPlan)=>{\n    return preloader.preloadUserJourney(userRole, userPlan);\n};\nconst observeLinks = ()=>{\n    preloader.observeLinks();\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (preloader);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvcHJlbG9hZGVyLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFBLG9EQUFvRDtBQUNwRCwrQ0FBK0M7QUFFaEI7QUFpQi9CLE1BQU1DO0lBNkNKQyxhQUFjO2FBNUNOQyxlQUFrRCxJQUFJQzthQUN0REMsa0JBQStCLElBQUlDO2FBQ25DQyxlQUF3QjthQUN4QkMsV0FBd0M7UUFFaEQsOENBQThDO2FBQzdCQyxtQkFBMkM7WUFDMUQ7Z0JBQ0VDLE9BQU87Z0JBQ1BDLGNBQWM7b0JBQUM7b0JBQVU7b0JBQVk7aUJBQVc7Z0JBQ2hEQyxlQUFlO29CQUFDO29CQUF3QjtvQkFBcUI7aUJBQXFCO2dCQUNsRkMsUUFBUSxFQUFFO2dCQUNWQyxVQUFVO1lBQ1o7WUFDQTtnQkFDRUosT0FBTztnQkFDUEMsY0FBYztvQkFBQztvQkFBa0I7aUJBQU87Z0JBQ3hDQyxlQUFlO29CQUFDO29CQUFxQjtpQkFBdUI7Z0JBQzVEQyxRQUFRLEVBQUU7Z0JBQ1ZDLFVBQVU7WUFDWjtZQUNBO2dCQUNFSixPQUFPO2dCQUNQQyxjQUFjO29CQUFDO29CQUFRO2lCQUFTO2dCQUNoQ0MsZUFBZTtvQkFBQztvQkFBd0I7aUJBQXlCO2dCQUNqRUMsUUFBUSxFQUFFO2dCQUNWQyxVQUFVO1lBQ1o7WUFDQTtnQkFDRUosT0FBTztnQkFDUEMsY0FBYztvQkFBQztvQkFBYTtpQkFBUTtnQkFDcENDLGVBQWU7b0JBQUM7b0JBQXFCO29CQUFzQjtpQkFBa0I7Z0JBQzdFQyxRQUFRLEVBQUU7Z0JBQ1ZDLFVBQVU7WUFDWjtZQUNBO2dCQUNFSixPQUFPO2dCQUNQQyxjQUFjLEVBQUU7Z0JBQ2hCQyxlQUFlO29CQUFDO29CQUFzQjtpQkFBd0I7Z0JBQzlEQyxRQUFRLEVBQUU7Z0JBQ1ZDLFVBQVU7WUFDWjtTQUNEO1FBR0MsSUFBSSxDQUFDQyx3QkFBd0I7UUFDN0IsSUFBSSxDQUFDQyxtQkFBbUI7SUFDMUI7SUFFQSx3REFBd0Q7SUFDaERELDJCQUFpQztRQUN2QyxJQUFJLElBQWtCLEVBQWE7UUFFbkMsSUFBSSxDQUFDUCxRQUFRLEdBQUcsSUFBSVMscUJBQ2xCLENBQUNDO1lBQ0NBLFFBQVFDLE9BQU8sQ0FBQyxDQUFDQztnQkFDZixJQUFJQSxNQUFNQyxjQUFjLEVBQUU7b0JBQ3hCLE1BQU1DLE9BQU9GLE1BQU1HLE1BQU07b0JBQ3pCLE1BQU1DLE9BQU9GLEtBQUtHLFlBQVksQ0FBQztvQkFDL0IsSUFBSUQsUUFBUSxDQUFDLElBQUksQ0FBQ25CLGVBQWUsQ0FBQ3FCLEdBQUcsQ0FBQ0YsT0FBTzt3QkFDM0MsSUFBSSxDQUFDRyxZQUFZLENBQUNILE1BQU07b0JBQzFCO2dCQUNGO1lBQ0Y7UUFDRixHQUNBO1lBQ0VJLFlBQVk7WUFDWkMsV0FBVztRQUNiO0lBRUo7SUFFQSxvREFBb0Q7SUFDNUNiLHNCQUE0QjtRQUNsQyxJQUFJLElBQWtCLEVBQWE7UUFFbkMsc0NBQXNDO1FBQ3RDLElBQUksQ0FBQ2MscUJBQXFCO1FBRTFCLDBCQUEwQjtRQUMxQixJQUFJLENBQUNDLG9CQUFvQjtRQUV6QiwwQ0FBMEM7UUFDMUMsSUFBSSxDQUFDQyw0QkFBNEI7SUFDbkM7SUFFQSxzQ0FBc0M7SUFDdEMsTUFBY0Ysd0JBQXVDO1FBQ25ELE1BQU1HLGNBQWNDLE9BQU9DLFFBQVEsQ0FBQ0MsUUFBUTtRQUM1QyxNQUFNQyxXQUFXLElBQUksQ0FBQzVCLGdCQUFnQixDQUFDNkIsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFN0IsS0FBSyxLQUFLdUI7UUFFN0QsSUFBSUksVUFBVTtZQUNaLHdDQUF3QztZQUN4QyxLQUFLLE1BQU1HLE9BQU9ILFNBQVMxQixZQUFZLENBQUU7Z0JBQ3ZDLE1BQU0sSUFBSSxDQUFDZ0IsWUFBWSxDQUFDYSxLQUFLO1lBQy9CO1lBRUEseUJBQXlCO1lBQ3pCLEtBQUssTUFBTUMsWUFBWUosU0FBU3pCLGFBQWEsQ0FBRTtnQkFDN0MsTUFBTSxJQUFJLENBQUM4QixXQUFXLENBQUNEO1lBQ3pCO1FBQ0Y7UUFFQSwrQ0FBK0M7UUFDL0MsSUFBSVIsZ0JBQWdCLGNBQWM7WUFDaEMsTUFBTSxJQUFJLENBQUNOLFlBQVksQ0FBQyxjQUFjO1FBQ3hDO0lBQ0Y7SUFFQSwrQkFBK0I7SUFDdkJJLHVCQUE2QjtRQUNuQyxJQUFJLElBQWtCLEVBQWE7UUFFbkNZLFNBQVNDLGdCQUFnQixDQUFDLGFBQWEsQ0FBQ0M7WUFDdEMsTUFBTXRCLFNBQVNzQixNQUFNdEIsTUFBTTtZQUMzQixNQUFNRCxPQUFPQyxPQUFPdUIsT0FBTyxDQUFDO1lBRTVCLElBQUl4QixRQUFRQSxLQUFLRSxJQUFJLEVBQUU7Z0JBQ3JCLE1BQU1BLE9BQU8sSUFBSXVCLElBQUl6QixLQUFLRSxJQUFJLEVBQUVZLFFBQVE7Z0JBQ3hDLElBQUksQ0FBQyxJQUFJLENBQUMvQixlQUFlLENBQUNxQixHQUFHLENBQUNGLE9BQU87b0JBQ25DLDRCQUE0QjtvQkFDNUJ3QixXQUFXO3dCQUNULElBQUksQ0FBQ3JCLFlBQVksQ0FBQ0gsTUFBTTtvQkFDMUIsR0FBRztnQkFDTDtZQUNGO1FBQ0Y7SUFDRjtJQUVBLGtDQUFrQztJQUMxQlEsK0JBQXFDO1FBQzNDLElBQUksSUFBa0IsRUFBYTtRQUVuQyxzREFBc0Q7UUFDdEQsTUFBTWlCLGVBQWUsSUFBSSxDQUFDQyxzQkFBc0I7UUFFaEQsbURBQW1EO1FBQ25ELE1BQU1qQixjQUFjQyxPQUFPQyxRQUFRLENBQUNDLFFBQVE7UUFDNUMsTUFBTWUsYUFBYSxJQUFJLENBQUNDLGlCQUFpQixDQUFDbkIsYUFBYWdCO1FBRXZERSxXQUFXaEMsT0FBTyxDQUFDLENBQUNULE9BQU8yQztZQUN6QkwsV0FBVztnQkFDVCxJQUFJLENBQUNyQixZQUFZLENBQUNqQixPQUFPO1lBQzNCLEdBQUcyQyxRQUFRLE1BQU0scUJBQXFCOztRQUN4QztJQUNGO0lBRUEsOERBQThEO0lBQ3RERCxrQkFBa0JuQixXQUFtQixFQUFFcUIsUUFBYSxFQUFZO1FBQ3RFLE1BQU1DLGNBQTJDO1lBQy9DLGNBQWM7Z0JBQUM7Z0JBQVU7Z0JBQVk7YUFBVztZQUNoRCxVQUFVO2dCQUFDO2dCQUFrQjtnQkFBUTthQUFhO1lBQ2xELFlBQVk7Z0JBQUM7Z0JBQVE7Z0JBQVU7YUFBYTtZQUM1QyxZQUFZO2dCQUFDO2dCQUFhO2dCQUFTO2FBQWE7WUFDaEQsS0FBSztnQkFBQztnQkFBYztnQkFBVTthQUFZO1FBQzVDO1FBRUEsT0FBT0EsV0FBVyxDQUFDdEIsWUFBWSxJQUFJLEVBQUU7SUFDdkM7SUFFQSw4Q0FBOEM7SUFDdENpQix5QkFBOEI7UUFDcEMsSUFBSSxJQUFrQixFQUFhLE9BQU8sQ0FBQztRQUUzQyxJQUFJO1lBQ0YsTUFBTU0sU0FBU0MsYUFBYUMsT0FBTyxDQUFDO1lBQ3BDLE9BQU9GLFNBQVNHLEtBQUtDLEtBQUssQ0FBQ0osVUFBVSxDQUFDO1FBQ3hDLEVBQUUsT0FBTTtZQUNOLE9BQU8sQ0FBQztRQUNWO0lBQ0Y7SUFFQSw0QkFBNEI7SUFDNUIsTUFBTTdCLGFBQWFqQixLQUFhLEVBQUVJLFdBQXNDLFFBQVEsRUFBaUI7UUFDL0YsSUFBSSxJQUFJLENBQUNULGVBQWUsQ0FBQ3FCLEdBQUcsQ0FBQ2hCLFFBQVE7UUFFckMsSUFBSTtZQUNGLDhCQUE4QjtZQUM5QixJQUFJLEtBQTZEb0QsRUFBRSxFQUVsRTtZQUVELHFCQUFxQjtZQUNyQixNQUFNekIsV0FBVyxJQUFJLENBQUM1QixnQkFBZ0IsQ0FBQzZCLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRTdCLEtBQUssS0FBS0E7WUFDN0QsSUFBSTJCLFVBQVU7Z0JBQ1oseUJBQXlCO2dCQUN6QixNQUFNMkIsZUFBZTNCLFNBQVN6QixhQUFhLENBQUNxRCxHQUFHLENBQUN4QixDQUFBQSxXQUM5QyxJQUFJLENBQUNDLFdBQVcsQ0FBQ0Q7Z0JBRW5CLE1BQU15QixRQUFRQyxVQUFVLENBQUNIO1lBQzNCO1lBRUEsSUFBSSxDQUFDM0QsZUFBZSxDQUFDK0QsR0FBRyxDQUFDMUQ7WUFDekIyRCxRQUFRQyxHQUFHLENBQUMsQ0FBQyxtQkFBbUIsRUFBRTVELE1BQU0sQ0FBQztRQUMzQyxFQUFFLE9BQU82RCxPQUFPO1lBQ2RGLFFBQVFHLElBQUksQ0FBQyxDQUFDLDBCQUEwQixFQUFFOUQsTUFBTSxDQUFDLENBQUMsRUFBRTZEO1FBQ3REO0lBQ0Y7SUFFQSw2QkFBNkI7SUFDN0IsTUFBYzdCLFlBQVlELFFBQWdCLEVBQWlCO1FBQ3pELElBQUk7WUFDRiwwQkFBMEI7WUFDMUIsTUFBTWdDLFNBQVMsTUFBTXpFLHlDQUFLQSxDQUFDMEUsR0FBRyxDQUFDakM7WUFDL0IsSUFBSWdDLFFBQVE7WUFFWix1QkFBdUI7WUFDdkIsTUFBTUUsV0FBVyxNQUFNQyxNQUFNbkMsVUFBVTtnQkFDckNvQyxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7WUFDRjtZQUVBLElBQUlILFNBQVNJLEVBQUUsRUFBRTtnQkFDZixNQUFNQyxPQUFPLE1BQU1MLFNBQVNNLElBQUk7Z0JBQ2hDLE1BQU1qRix5Q0FBS0EsQ0FBQ2tGLEdBQUcsQ0FBQ3pDLFVBQVV1QztnQkFDMUJYLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLG1CQUFtQixFQUFFN0IsU0FBUyxDQUFDO1lBQzlDO1FBQ0YsRUFBRSxPQUFPOEIsT0FBTztZQUNkRixRQUFRRyxJQUFJLENBQUMsQ0FBQyx5QkFBeUIsRUFBRS9CLFNBQVMsQ0FBQyxDQUFDLEVBQUU4QjtRQUN4RDtJQUNGO0lBRUEsMEJBQTBCO0lBQzFCLE1BQU1ZLHdCQUF1QztRQUMzQyxNQUFNQyxpQkFBaUI7WUFDckI7WUFDQTtTQUVEO1FBRUQsTUFBTUMsa0JBQWtCRCxlQUFlbkIsR0FBRyxDQUFDcUIsQ0FBQUE7WUFDekMsT0FBTyxJQUFJcEIsUUFBYyxDQUFDcUI7Z0JBQ3hCLE1BQU1qRSxPQUFPcUIsU0FBUzZDLGFBQWEsQ0FBQztnQkFDcENsRSxLQUFLbUUsR0FBRyxHQUFHO2dCQUNYbkUsS0FBS0UsSUFBSSxHQUFHOEQ7Z0JBQ1poRSxLQUFLb0UsRUFBRSxHQUFHSixNQUFNSyxRQUFRLENBQUMsVUFBVSxVQUFVO2dCQUM3Q3JFLEtBQUtzRSxNQUFNLEdBQUcsSUFBTUw7Z0JBQ3BCakUsS0FBS3VFLE9BQU8sR0FBRyxJQUFNTixVQUFVLDZCQUE2Qjs7Z0JBQzVENUMsU0FBU21ELElBQUksQ0FBQ0MsV0FBVyxDQUFDekU7WUFDNUI7UUFDRjtRQUVBLE1BQU00QyxRQUFRQyxVQUFVLENBQUNrQjtJQUMzQjtJQUVBLGtEQUFrRDtJQUNsRFcsZUFBcUI7UUFDbkIsSUFBSSxDQUFDLElBQUksQ0FBQ3hGLFFBQVEsRUFBRTtRQUVwQixNQUFNeUYsUUFBUXRELFNBQVN1RCxnQkFBZ0IsQ0FBQztRQUN4Q0QsTUFBTTlFLE9BQU8sQ0FBQ0csQ0FBQUE7WUFDWixJQUFJLENBQUNkLFFBQVEsQ0FBRTJGLE9BQU8sQ0FBQzdFO1FBQ3pCO0lBQ0Y7SUFFQSw4QkFBOEI7SUFDOUIsTUFBTThFLG1CQUFtQkMsUUFBZ0IsRUFBRUMsUUFBZ0IsRUFBaUI7UUFDMUUsTUFBTUMsZ0JBQWdCLElBQUksQ0FBQ0Msb0JBQW9CLENBQUNILFVBQVVDO1FBRTFELEtBQUssTUFBTSxDQUFDakQsT0FBTzNDLE1BQU0sSUFBSTZGLGNBQWNyRixPQUFPLEdBQUk7WUFDcEQsdURBQXVEO1lBQ3ZEOEIsV0FBVztnQkFDVCxJQUFJLENBQUNyQixZQUFZLENBQUNqQixPQUFPMkMsUUFBUSxJQUFJLFNBQVM7WUFDaEQsR0FBR0EsUUFBUTtRQUNiO0lBQ0Y7SUFFQSxpREFBaUQ7SUFDekNtRCxxQkFBcUJILFFBQWdCLEVBQUVDLFFBQWdCLEVBQVk7UUFDekUsTUFBTUcsY0FBYztZQUFDO1lBQWM7WUFBWTtTQUFZO1FBRTNELElBQUlILGFBQWEsbUJBQW1CQSxhQUFhLGFBQWE7WUFDNUQsT0FBTzttQkFDRkc7Z0JBQ0g7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtRQUNIO1FBRUEsT0FBTztlQUNGQTtZQUNIO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7SUFDSDtJQUVBLDRCQUE0QjtJQUM1QkMsV0FBZ0I7UUFDZCxPQUFPO1lBQ0xyRyxpQkFBaUJzRyxNQUFNQyxJQUFJLENBQUMsSUFBSSxDQUFDdkcsZUFBZTtZQUNoRHdHLFdBQVcsSUFBSSxDQUFDMUcsWUFBWSxDQUFDMkcsSUFBSTtZQUNqQ3ZHLGNBQWMsSUFBSSxDQUFDQSxZQUFZO1lBQy9Cd0csWUFBWS9HLHlDQUFLQSxDQUFDMEcsUUFBUTtRQUM1QjtJQUNGO0lBRUEseUJBQXlCO0lBQ3pCTSxpQkFBdUI7UUFDckIsSUFBSSxDQUFDM0csZUFBZSxDQUFDNEcsS0FBSztRQUMxQixJQUFJLENBQUM5RyxZQUFZLENBQUM4RyxLQUFLO0lBQ3pCO0FBQ0Y7QUFFQSxxQkFBcUI7QUFDZCxNQUFNQyxZQUFZLElBQUlqSCxvQkFBbUI7QUFFaEQsb0JBQW9CO0FBQ2IsTUFBTTBCLGVBQWUsQ0FBQ2pCLE9BQWVJO0lBQzFDLE9BQU9vRyxVQUFVdkYsWUFBWSxDQUFDakIsT0FBT0k7QUFDdkMsRUFBQztBQUVNLE1BQU1zRixxQkFBcUIsQ0FBQ0MsVUFBa0JDO0lBQ25ELE9BQU9ZLFVBQVVkLGtCQUFrQixDQUFDQyxVQUFVQztBQUNoRCxFQUFDO0FBRU0sTUFBTU4sZUFBZTtJQUMxQmtCLFVBQVVsQixZQUFZO0FBQ3hCLEVBQUM7QUFFRCxpRUFBZWtCLFNBQVNBLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb2RleGd1YXJkLy4vbGliL3ByZWxvYWRlci50cz83NGYyIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEFkdmFuY2VkIFByZS1yZW5kZXJpbmcgJiBSb3V0ZSBQcmVmZXRjaGluZyBTeXN0ZW1cbi8vIE1jTWFzdGVyLUNhcnIgbGV2ZWwgcGVyZm9ybWFuY2Ugb3B0aW1pemF0aW9uXG5cbmltcG9ydCB7IGNhY2hlIH0gZnJvbSAnLi9jYWNoZSdcblxuaW50ZXJmYWNlIFByZWxvYWRDb25maWcge1xuICByb3V0ZXM6IHN0cmluZ1tdXG4gIHByaW9yaXR5OiAnaGlnaCcgfCAnbWVkaXVtJyB8ICdsb3cnXG4gIHByZWxvYWREYXRhOiBib29sZWFuXG4gIHByZWxvYWRBc3NldHM6IGJvb2xlYW5cbn1cblxuaW50ZXJmYWNlIFJvdXRlUHJlbG9hZFN0cmF0ZWd5IHtcbiAgcm91dGU6IHN0cmluZ1xuICBkZXBlbmRlbmNpZXM6IHN0cmluZ1tdXG4gIGRhdGFFbmRwb2ludHM6IHN0cmluZ1tdXG4gIGFzc2V0czogc3RyaW5nW11cbiAgcHJpb3JpdHk6IG51bWJlclxufVxuXG5jbGFzcyBBZHZhbmNlZFByZWxvYWRlciB7XG4gIHByaXZhdGUgcHJlbG9hZFF1ZXVlOiBNYXA8c3RyaW5nLCBSb3V0ZVByZWxvYWRTdHJhdGVneT4gPSBuZXcgTWFwKClcbiAgcHJpdmF0ZSBwcmVsb2FkZWRSb3V0ZXM6IFNldDxzdHJpbmc+ID0gbmV3IFNldCgpXG4gIHByaXZhdGUgaXNQcmVsb2FkaW5nOiBib29sZWFuID0gZmFsc2VcbiAgcHJpdmF0ZSBvYnNlcnZlcjogSW50ZXJzZWN0aW9uT2JzZXJ2ZXIgfCBudWxsID0gbnVsbFxuXG4gIC8vIFJvdXRlIHN0cmF0ZWdpZXMgZm9yIGludGVsbGlnZW50IHByZWxvYWRpbmdcbiAgcHJpdmF0ZSByZWFkb25seSBST1VURV9TVFJBVEVHSUVTOiBSb3V0ZVByZWxvYWRTdHJhdGVneVtdID0gW1xuICAgIHtcbiAgICAgIHJvdXRlOiAnL2Rhc2hib2FyZCcsXG4gICAgICBkZXBlbmRlbmNpZXM6IFsnL29zaW50JywgJy9zY2FubmVyJywgJy9wcm9maWxlJ10sXG4gICAgICBkYXRhRW5kcG9pbnRzOiBbJy9hcGkvZGFzaGJvYXJkL3N0YXRzJywgJy9hcGkvdXNlci9wcm9maWxlJywgJy9hcGkvbm90aWZpY2F0aW9ucyddLFxuICAgICAgYXNzZXRzOiBbXSxcbiAgICAgIHByaW9yaXR5OiAxXG4gICAgfSxcbiAgICB7XG4gICAgICByb3V0ZTogJy9vc2ludCcsXG4gICAgICBkZXBlbmRlbmNpZXM6IFsnL2ZpbGUtYW5hbHl6ZXInLCAnL2N2ZSddLFxuICAgICAgZGF0YUVuZHBvaW50czogWycvYXBpL29zaW50L3JlY2VudCcsICcvYXBpL29zaW50L3RlbXBsYXRlcyddLFxuICAgICAgYXNzZXRzOiBbXSxcbiAgICAgIHByaW9yaXR5OiAyXG4gICAgfSxcbiAgICB7XG4gICAgICByb3V0ZTogJy9zY2FubmVyJyxcbiAgICAgIGRlcGVuZGVuY2llczogWycvY3ZlJywgJy90b29scyddLFxuICAgICAgZGF0YUVuZHBvaW50czogWycvYXBpL3NjYW5uZXIvaGlzdG9yeScsICcvYXBpL3NjYW5uZXIvdGVtcGxhdGVzJ10sXG4gICAgICBhc3NldHM6IFtdLFxuICAgICAgcHJpb3JpdHk6IDJcbiAgICB9LFxuICAgIHtcbiAgICAgIHJvdXRlOiAnL3Byb2ZpbGUnLFxuICAgICAgZGVwZW5kZW5jaWVzOiBbJy9zZXR0aW5ncycsICcvcGxhbiddLFxuICAgICAgZGF0YUVuZHBvaW50czogWycvYXBpL3VzZXIvcHJvZmlsZScsICcvYXBpL3VzZXIvYXBpLWtleXMnLCAnL2FwaS91c2VyL3VzYWdlJ10sXG4gICAgICBhc3NldHM6IFtdLFxuICAgICAgcHJpb3JpdHk6IDNcbiAgICB9LFxuICAgIHtcbiAgICAgIHJvdXRlOiAnL3NldHRpbmdzJyxcbiAgICAgIGRlcGVuZGVuY2llczogW10sXG4gICAgICBkYXRhRW5kcG9pbnRzOiBbJy9hcGkvdXNlci9zZXR0aW5ncycsICcvYXBpL3VzZXIvcHJlZmVyZW5jZXMnXSxcbiAgICAgIGFzc2V0czogW10sXG4gICAgICBwcmlvcml0eTogNFxuICAgIH1cbiAgXVxuXG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIHRoaXMuaW5pdEludGVyc2VjdGlvbk9ic2VydmVyKClcbiAgICB0aGlzLmluaXRSb3V0ZVByZWxvYWRpbmcoKVxuICB9XG5cbiAgLy8gSW5pdGlhbGl6ZSBpbnRlcnNlY3Rpb24gb2JzZXJ2ZXIgZm9yIGxpbmsgcHJlZmV0Y2hpbmdcbiAgcHJpdmF0ZSBpbml0SW50ZXJzZWN0aW9uT2JzZXJ2ZXIoKTogdm9pZCB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm5cblxuICAgIHRoaXMub2JzZXJ2ZXIgPSBuZXcgSW50ZXJzZWN0aW9uT2JzZXJ2ZXIoXG4gICAgICAoZW50cmllcykgPT4ge1xuICAgICAgICBlbnRyaWVzLmZvckVhY2goKGVudHJ5KSA9PiB7XG4gICAgICAgICAgaWYgKGVudHJ5LmlzSW50ZXJzZWN0aW5nKSB7XG4gICAgICAgICAgICBjb25zdCBsaW5rID0gZW50cnkudGFyZ2V0IGFzIEhUTUxBbmNob3JFbGVtZW50XG4gICAgICAgICAgICBjb25zdCBocmVmID0gbGluay5nZXRBdHRyaWJ1dGUoJ2hyZWYnKVxuICAgICAgICAgICAgaWYgKGhyZWYgJiYgIXRoaXMucHJlbG9hZGVkUm91dGVzLmhhcyhocmVmKSkge1xuICAgICAgICAgICAgICB0aGlzLnByZWxvYWRSb3V0ZShocmVmLCAnbG93JylcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH0pXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICByb290TWFyZ2luOiAnMTAwcHgnLFxuICAgICAgICB0aHJlc2hvbGQ6IDAuMVxuICAgICAgfVxuICAgIClcbiAgfVxuXG4gIC8vIEluaXRpYWxpemUgcm91dGUgcHJlbG9hZGluZyBiYXNlZCBvbiBjdXJyZW50IHBhZ2VcbiAgcHJpdmF0ZSBpbml0Um91dGVQcmVsb2FkaW5nKCk6IHZvaWQge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuXG5cbiAgICAvLyBQcmVsb2FkIGNyaXRpY2FsIHJvdXRlcyBpbW1lZGlhdGVseVxuICAgIHRoaXMucHJlbG9hZENyaXRpY2FsUm91dGVzKClcblxuICAgIC8vIFNldCB1cCBob3ZlciBwcmVsb2FkaW5nXG4gICAgdGhpcy5zZXR1cEhvdmVyUHJlbG9hZGluZygpXG5cbiAgICAvLyBQcmVsb2FkIGJhc2VkIG9uIHVzZXIgYmVoYXZpb3IgcGF0dGVybnNcbiAgICB0aGlzLnNldHVwQmVoYXZpb3JCYXNlZFByZWxvYWRpbmcoKVxuICB9XG5cbiAgLy8gUHJlbG9hZCBjcml0aWNhbCByb3V0ZXMgaW1tZWRpYXRlbHlcbiAgcHJpdmF0ZSBhc3luYyBwcmVsb2FkQ3JpdGljYWxSb3V0ZXMoKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgY29uc3QgY3VycmVudFBhdGggPSB3aW5kb3cubG9jYXRpb24ucGF0aG5hbWVcbiAgICBjb25zdCBzdHJhdGVneSA9IHRoaXMuUk9VVEVfU1RSQVRFR0lFUy5maW5kKHMgPT4gcy5yb3V0ZSA9PT0gY3VycmVudFBhdGgpXG5cbiAgICBpZiAoc3RyYXRlZ3kpIHtcbiAgICAgIC8vIFByZWxvYWQgZGVwZW5kZW5jaWVzIG9mIGN1cnJlbnQgcm91dGVcbiAgICAgIGZvciAoY29uc3QgZGVwIG9mIHN0cmF0ZWd5LmRlcGVuZGVuY2llcykge1xuICAgICAgICBhd2FpdCB0aGlzLnByZWxvYWRSb3V0ZShkZXAsICdoaWdoJylcbiAgICAgIH1cblxuICAgICAgLy8gUHJlbG9hZCBkYXRhIGVuZHBvaW50c1xuICAgICAgZm9yIChjb25zdCBlbmRwb2ludCBvZiBzdHJhdGVneS5kYXRhRW5kcG9pbnRzKSB7XG4gICAgICAgIGF3YWl0IHRoaXMucHJlbG9hZERhdGEoZW5kcG9pbnQpXG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gQWx3YXlzIHByZWxvYWQgZGFzaGJvYXJkIGlmIG5vdCBjdXJyZW50IHBhZ2VcbiAgICBpZiAoY3VycmVudFBhdGggIT09ICcvZGFzaGJvYXJkJykge1xuICAgICAgYXdhaXQgdGhpcy5wcmVsb2FkUm91dGUoJy9kYXNoYm9hcmQnLCAnbWVkaXVtJylcbiAgICB9XG4gIH1cblxuICAvLyBTZXR1cCBob3Zlci1iYXNlZCBwcmVsb2FkaW5nXG4gIHByaXZhdGUgc2V0dXBIb3ZlclByZWxvYWRpbmcoKTogdm9pZCB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm5cblxuICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ21vdXNlb3ZlcicsIChldmVudCkgPT4ge1xuICAgICAgY29uc3QgdGFyZ2V0ID0gZXZlbnQudGFyZ2V0IGFzIEhUTUxFbGVtZW50XG4gICAgICBjb25zdCBsaW5rID0gdGFyZ2V0LmNsb3Nlc3QoJ2FbaHJlZl0nKSBhcyBIVE1MQW5jaG9yRWxlbWVudFxuICAgICAgXG4gICAgICBpZiAobGluayAmJiBsaW5rLmhyZWYpIHtcbiAgICAgICAgY29uc3QgaHJlZiA9IG5ldyBVUkwobGluay5ocmVmKS5wYXRobmFtZVxuICAgICAgICBpZiAoIXRoaXMucHJlbG9hZGVkUm91dGVzLmhhcyhocmVmKSkge1xuICAgICAgICAgIC8vIERlYm91bmNlIGhvdmVyIHByZWxvYWRpbmdcbiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICAgIHRoaXMucHJlbG9hZFJvdXRlKGhyZWYsICdtZWRpdW0nKVxuICAgICAgICAgIH0sIDEwMClcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0pXG4gIH1cblxuICAvLyBTZXR1cCBiZWhhdmlvci1iYXNlZCBwcmVsb2FkaW5nXG4gIHByaXZhdGUgc2V0dXBCZWhhdmlvckJhc2VkUHJlbG9hZGluZygpOiB2b2lkIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVyblxuXG4gICAgLy8gUHJlbG9hZCBiYXNlZCBvbiB1c2VyJ3MgdHlwaWNhbCBuYXZpZ2F0aW9uIHBhdHRlcm5zXG4gICAgY29uc3QgdXNlckJlaGF2aW9yID0gdGhpcy5nZXRVc2VyQmVoYXZpb3JQYXR0ZXJuKClcbiAgICBcbiAgICAvLyBQcmVsb2FkIGxpa2VseSBuZXh0IHJvdXRlcyBiYXNlZCBvbiBjdXJyZW50IHBhZ2VcbiAgICBjb25zdCBjdXJyZW50UGF0aCA9IHdpbmRvdy5sb2NhdGlvbi5wYXRobmFtZVxuICAgIGNvbnN0IGxpa2VseU5leHQgPSB0aGlzLnByZWRpY3ROZXh0Um91dGVzKGN1cnJlbnRQYXRoLCB1c2VyQmVoYXZpb3IpXG4gICAgXG4gICAgbGlrZWx5TmV4dC5mb3JFYWNoKChyb3V0ZSwgaW5kZXgpID0+IHtcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICB0aGlzLnByZWxvYWRSb3V0ZShyb3V0ZSwgJ2xvdycpXG4gICAgICB9LCBpbmRleCAqIDEwMDApIC8vIFN0YWdnZXIgcHJlbG9hZGluZ1xuICAgIH0pXG4gIH1cblxuICAvLyBQcmVkaWN0IG5leHQgcm91dGVzIGJhc2VkIG9uIGN1cnJlbnQgcGFnZSBhbmQgdXNlciBiZWhhdmlvclxuICBwcml2YXRlIHByZWRpY3ROZXh0Um91dGVzKGN1cnJlbnRQYXRoOiBzdHJpbmcsIGJlaGF2aW9yOiBhbnkpOiBzdHJpbmdbXSB7XG4gICAgY29uc3QgcHJlZGljdGlvbnM6IHsgW2tleTogc3RyaW5nXTogc3RyaW5nW10gfSA9IHtcbiAgICAgICcvZGFzaGJvYXJkJzogWycvb3NpbnQnLCAnL3NjYW5uZXInLCAnL3Byb2ZpbGUnXSxcbiAgICAgICcvb3NpbnQnOiBbJy9maWxlLWFuYWx5emVyJywgJy9jdmUnLCAnL2Rhc2hib2FyZCddLFxuICAgICAgJy9zY2FubmVyJzogWycvY3ZlJywgJy90b29scycsICcvZGFzaGJvYXJkJ10sXG4gICAgICAnL3Byb2ZpbGUnOiBbJy9zZXR0aW5ncycsICcvcGxhbicsICcvZGFzaGJvYXJkJ10sXG4gICAgICAnLyc6IFsnL2Rhc2hib2FyZCcsICcvbG9naW4nLCAnL3JlZ2lzdGVyJ11cbiAgICB9XG5cbiAgICByZXR1cm4gcHJlZGljdGlvbnNbY3VycmVudFBhdGhdIHx8IFtdXG4gIH1cblxuICAvLyBHZXQgdXNlciBiZWhhdmlvciBwYXR0ZXJuIGZyb20gbG9jYWxTdG9yYWdlXG4gIHByaXZhdGUgZ2V0VXNlckJlaGF2aW9yUGF0dGVybigpOiBhbnkge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuIHt9XG5cbiAgICB0cnkge1xuICAgICAgY29uc3Qgc3RvcmVkID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2tvZGV4Z3VhcmRfdXNlcl9iZWhhdmlvcicpXG4gICAgICByZXR1cm4gc3RvcmVkID8gSlNPTi5wYXJzZShzdG9yZWQpIDoge31cbiAgICB9IGNhdGNoIHtcbiAgICAgIHJldHVybiB7fVxuICAgIH1cbiAgfVxuXG4gIC8vIE1haW4gcHJlbG9hZCByb3V0ZSBtZXRob2RcbiAgYXN5bmMgcHJlbG9hZFJvdXRlKHJvdXRlOiBzdHJpbmcsIHByaW9yaXR5OiAnaGlnaCcgfCAnbWVkaXVtJyB8ICdsb3cnID0gJ21lZGl1bScpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICBpZiAodGhpcy5wcmVsb2FkZWRSb3V0ZXMuaGFzKHJvdXRlKSkgcmV0dXJuXG5cbiAgICB0cnkge1xuICAgICAgLy8gVXNlIE5leHQuanMgcm91dGVyIHByZWZldGNoXG4gICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgKHdpbmRvdyBhcyBhbnkpLm5leHQ/LnJvdXRlcikge1xuICAgICAgICBhd2FpdCAod2luZG93IGFzIGFueSkubmV4dC5yb3V0ZXIucHJlZmV0Y2gocm91dGUpXG4gICAgICB9XG5cbiAgICAgIC8vIFByZWxvYWQgcm91dGUgZGF0YVxuICAgICAgY29uc3Qgc3RyYXRlZ3kgPSB0aGlzLlJPVVRFX1NUUkFURUdJRVMuZmluZChzID0+IHMucm91dGUgPT09IHJvdXRlKVxuICAgICAgaWYgKHN0cmF0ZWd5KSB7XG4gICAgICAgIC8vIFByZWxvYWQgZGF0YSBlbmRwb2ludHNcbiAgICAgICAgY29uc3QgZGF0YVByb21pc2VzID0gc3RyYXRlZ3kuZGF0YUVuZHBvaW50cy5tYXAoZW5kcG9pbnQgPT4gXG4gICAgICAgICAgdGhpcy5wcmVsb2FkRGF0YShlbmRwb2ludClcbiAgICAgICAgKVxuICAgICAgICBhd2FpdCBQcm9taXNlLmFsbFNldHRsZWQoZGF0YVByb21pc2VzKVxuICAgICAgfVxuXG4gICAgICB0aGlzLnByZWxvYWRlZFJvdXRlcy5hZGQocm91dGUpXG4gICAgICBjb25zb2xlLmxvZyhg4pyFIFByZWxvYWRlZCByb3V0ZTogJHtyb3V0ZX1gKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLndhcm4oYOKdjCBGYWlsZWQgdG8gcHJlbG9hZCByb3V0ZSAke3JvdXRlfTpgLCBlcnJvcilcbiAgICB9XG4gIH1cblxuICAvLyBQcmVsb2FkIGRhdGEgZm9yIGVuZHBvaW50c1xuICBwcml2YXRlIGFzeW5jIHByZWxvYWREYXRhKGVuZHBvaW50OiBzdHJpbmcpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICB0cnkge1xuICAgICAgLy8gQ2hlY2sgaWYgYWxyZWFkeSBjYWNoZWRcbiAgICAgIGNvbnN0IGNhY2hlZCA9IGF3YWl0IGNhY2hlLmdldChlbmRwb2ludClcbiAgICAgIGlmIChjYWNoZWQpIHJldHVyblxuXG4gICAgICAvLyBGZXRjaCBhbmQgY2FjaGUgZGF0YVxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChlbmRwb2ludCwge1xuICAgICAgICBtZXRob2Q6ICdHRVQnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgIH0pXG5cbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG4gICAgICAgIGF3YWl0IGNhY2hlLnNldChlbmRwb2ludCwgZGF0YSlcbiAgICAgICAgY29uc29sZS5sb2coYPCfk6YgUHJlbG9hZGVkIGRhdGE6ICR7ZW5kcG9pbnR9YClcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS53YXJuKGDinYwgRmFpbGVkIHRvIHByZWxvYWQgZGF0YSAke2VuZHBvaW50fTpgLCBlcnJvcilcbiAgICB9XG4gIH1cblxuICAvLyBQcmVsb2FkIGNyaXRpY2FsIGFzc2V0c1xuICBhc3luYyBwcmVsb2FkQ3JpdGljYWxBc3NldHMoKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgY29uc3QgY3JpdGljYWxBc3NldHMgPSBbXG4gICAgICAnL2ltYWdlcy9sb2dvLnN2ZycsXG4gICAgICAnL2ltYWdlcy9jeWJlci1iZy5qcGcnLFxuICAgICAgLy8gQWRkIG1vcmUgY3JpdGljYWwgYXNzZXRzXG4gICAgXVxuXG4gICAgY29uc3QgcHJlbG9hZFByb21pc2VzID0gY3JpdGljYWxBc3NldHMubWFwKGFzc2V0ID0+IHtcbiAgICAgIHJldHVybiBuZXcgUHJvbWlzZTx2b2lkPigocmVzb2x2ZSkgPT4ge1xuICAgICAgICBjb25zdCBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnbGluaycpXG4gICAgICAgIGxpbmsucmVsID0gJ3ByZWxvYWQnXG4gICAgICAgIGxpbmsuaHJlZiA9IGFzc2V0XG4gICAgICAgIGxpbmsuYXMgPSBhc3NldC5lbmRzV2l0aCgnLnN2ZycpID8gJ2ltYWdlJyA6ICdpbWFnZSdcbiAgICAgICAgbGluay5vbmxvYWQgPSAoKSA9PiByZXNvbHZlKClcbiAgICAgICAgbGluay5vbmVycm9yID0gKCkgPT4gcmVzb2x2ZSgpIC8vIERvbid0IGZhaWwgb24gYXNzZXQgZXJyb3JzXG4gICAgICAgIGRvY3VtZW50LmhlYWQuYXBwZW5kQ2hpbGQobGluaylcbiAgICAgIH0pXG4gICAgfSlcblxuICAgIGF3YWl0IFByb21pc2UuYWxsU2V0dGxlZChwcmVsb2FkUHJvbWlzZXMpXG4gIH1cblxuICAvLyBPYnNlcnZlIGxpbmtzIGZvciBpbnRlcnNlY3Rpb24tYmFzZWQgcHJlbG9hZGluZ1xuICBvYnNlcnZlTGlua3MoKTogdm9pZCB7XG4gICAgaWYgKCF0aGlzLm9ic2VydmVyKSByZXR1cm5cblxuICAgIGNvbnN0IGxpbmtzID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvckFsbCgnYVtocmVmXj1cIi9cIl0nKVxuICAgIGxpbmtzLmZvckVhY2gobGluayA9PiB7XG4gICAgICB0aGlzLm9ic2VydmVyIS5vYnNlcnZlKGxpbmspXG4gICAgfSlcbiAgfVxuXG4gIC8vIFByZWxvYWQgZW50aXJlIHVzZXIgam91cm5leVxuICBhc3luYyBwcmVsb2FkVXNlckpvdXJuZXkodXNlclJvbGU6IHN0cmluZywgdXNlclBsYW46IHN0cmluZyk6IFByb21pc2U8dm9pZD4ge1xuICAgIGNvbnN0IGpvdXJuZXlSb3V0ZXMgPSB0aGlzLmdldFVzZXJKb3VybmV5Um91dGVzKHVzZXJSb2xlLCB1c2VyUGxhbilcbiAgICBcbiAgICBmb3IgKGNvbnN0IFtpbmRleCwgcm91dGVdIG9mIGpvdXJuZXlSb3V0ZXMuZW50cmllcygpKSB7XG4gICAgICAvLyBTdGFnZ2VyIHByZWxvYWRpbmcgdG8gYXZvaWQgb3ZlcndoZWxtaW5nIHRoZSBicm93c2VyXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgdGhpcy5wcmVsb2FkUm91dGUocm91dGUsIGluZGV4IDwgMyA/ICdoaWdoJyA6ICdsb3cnKVxuICAgICAgfSwgaW5kZXggKiA1MDApXG4gICAgfVxuICB9XG5cbiAgLy8gR2V0IHVzZXIgam91cm5leSByb3V0ZXMgYmFzZWQgb24gcm9sZSBhbmQgcGxhblxuICBwcml2YXRlIGdldFVzZXJKb3VybmV5Um91dGVzKHVzZXJSb2xlOiBzdHJpbmcsIHVzZXJQbGFuOiBzdHJpbmcpOiBzdHJpbmdbXSB7XG4gICAgY29uc3QgYmFzZUpvdXJuZXkgPSBbJy9kYXNoYm9hcmQnLCAnL3Byb2ZpbGUnLCAnL3NldHRpbmdzJ11cbiAgICBcbiAgICBpZiAodXNlclBsYW4gPT09ICdjeWJlcnNlY3VyaXR5JyB8fCB1c2VyUGxhbiA9PT0gJ2J1Z2h1bnRlcicpIHtcbiAgICAgIHJldHVybiBbXG4gICAgICAgIC4uLmJhc2VKb3VybmV5LFxuICAgICAgICAnL29zaW50JyxcbiAgICAgICAgJy9zY2FubmVyJyxcbiAgICAgICAgJy9maWxlLWFuYWx5emVyJyxcbiAgICAgICAgJy9jdmUnLFxuICAgICAgICAnL3BsYXlncm91bmQnLFxuICAgICAgICAnL2JvdCdcbiAgICAgIF1cbiAgICB9XG4gICAgXG4gICAgcmV0dXJuIFtcbiAgICAgIC4uLmJhc2VKb3VybmV5LFxuICAgICAgJy9vc2ludCcsXG4gICAgICAnL2ZpbGUtYW5hbHl6ZXInLFxuICAgICAgJy9jdmUnLFxuICAgICAgJy90b29scydcbiAgICBdXG4gIH1cblxuICAvLyBHZXQgcHJlbG9hZGluZyBzdGF0aXN0aWNzXG4gIGdldFN0YXRzKCk6IGFueSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIHByZWxvYWRlZFJvdXRlczogQXJyYXkuZnJvbSh0aGlzLnByZWxvYWRlZFJvdXRlcyksXG4gICAgICBxdWV1ZVNpemU6IHRoaXMucHJlbG9hZFF1ZXVlLnNpemUsXG4gICAgICBpc1ByZWxvYWRpbmc6IHRoaXMuaXNQcmVsb2FkaW5nLFxuICAgICAgY2FjaGVTdGF0czogY2FjaGUuZ2V0U3RhdHMoKVxuICAgIH1cbiAgfVxuXG4gIC8vIENsZWFyIHByZWxvYWRlZCByb3V0ZXNcbiAgY2xlYXJQcmVsb2FkZWQoKTogdm9pZCB7XG4gICAgdGhpcy5wcmVsb2FkZWRSb3V0ZXMuY2xlYXIoKVxuICAgIHRoaXMucHJlbG9hZFF1ZXVlLmNsZWFyKClcbiAgfVxufVxuXG4vLyBTaW5nbGV0b24gaW5zdGFuY2VcbmV4cG9ydCBjb25zdCBwcmVsb2FkZXIgPSBuZXcgQWR2YW5jZWRQcmVsb2FkZXIoKVxuXG4vLyBVdGlsaXR5IGZ1bmN0aW9uc1xuZXhwb3J0IGNvbnN0IHByZWxvYWRSb3V0ZSA9IChyb3V0ZTogc3RyaW5nLCBwcmlvcml0eT86ICdoaWdoJyB8ICdtZWRpdW0nIHwgJ2xvdycpID0+IHtcbiAgcmV0dXJuIHByZWxvYWRlci5wcmVsb2FkUm91dGUocm91dGUsIHByaW9yaXR5KVxufVxuXG5leHBvcnQgY29uc3QgcHJlbG9hZFVzZXJKb3VybmV5ID0gKHVzZXJSb2xlOiBzdHJpbmcsIHVzZXJQbGFuOiBzdHJpbmcpID0+IHtcbiAgcmV0dXJuIHByZWxvYWRlci5wcmVsb2FkVXNlckpvdXJuZXkodXNlclJvbGUsIHVzZXJQbGFuKVxufVxuXG5leHBvcnQgY29uc3Qgb2JzZXJ2ZUxpbmtzID0gKCkgPT4ge1xuICBwcmVsb2FkZXIub2JzZXJ2ZUxpbmtzKClcbn1cblxuZXhwb3J0IGRlZmF1bHQgcHJlbG9hZGVyXG4iXSwibmFtZXMiOlsiY2FjaGUiLCJBZHZhbmNlZFByZWxvYWRlciIsImNvbnN0cnVjdG9yIiwicHJlbG9hZFF1ZXVlIiwiTWFwIiwicHJlbG9hZGVkUm91dGVzIiwiU2V0IiwiaXNQcmVsb2FkaW5nIiwib2JzZXJ2ZXIiLCJST1VURV9TVFJBVEVHSUVTIiwicm91dGUiLCJkZXBlbmRlbmNpZXMiLCJkYXRhRW5kcG9pbnRzIiwiYXNzZXRzIiwicHJpb3JpdHkiLCJpbml0SW50ZXJzZWN0aW9uT2JzZXJ2ZXIiLCJpbml0Um91dGVQcmVsb2FkaW5nIiwiSW50ZXJzZWN0aW9uT2JzZXJ2ZXIiLCJlbnRyaWVzIiwiZm9yRWFjaCIsImVudHJ5IiwiaXNJbnRlcnNlY3RpbmciLCJsaW5rIiwidGFyZ2V0IiwiaHJlZiIsImdldEF0dHJpYnV0ZSIsImhhcyIsInByZWxvYWRSb3V0ZSIsInJvb3RNYXJnaW4iLCJ0aHJlc2hvbGQiLCJwcmVsb2FkQ3JpdGljYWxSb3V0ZXMiLCJzZXR1cEhvdmVyUHJlbG9hZGluZyIsInNldHVwQmVoYXZpb3JCYXNlZFByZWxvYWRpbmciLCJjdXJyZW50UGF0aCIsIndpbmRvdyIsImxvY2F0aW9uIiwicGF0aG5hbWUiLCJzdHJhdGVneSIsImZpbmQiLCJzIiwiZGVwIiwiZW5kcG9pbnQiLCJwcmVsb2FkRGF0YSIsImRvY3VtZW50IiwiYWRkRXZlbnRMaXN0ZW5lciIsImV2ZW50IiwiY2xvc2VzdCIsIlVSTCIsInNldFRpbWVvdXQiLCJ1c2VyQmVoYXZpb3IiLCJnZXRVc2VyQmVoYXZpb3JQYXR0ZXJuIiwibGlrZWx5TmV4dCIsInByZWRpY3ROZXh0Um91dGVzIiwiaW5kZXgiLCJiZWhhdmlvciIsInByZWRpY3Rpb25zIiwic3RvcmVkIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsIkpTT04iLCJwYXJzZSIsIm5leHQiLCJyb3V0ZXIiLCJwcmVmZXRjaCIsImRhdGFQcm9taXNlcyIsIm1hcCIsIlByb21pc2UiLCJhbGxTZXR0bGVkIiwiYWRkIiwiY29uc29sZSIsImxvZyIsImVycm9yIiwid2FybiIsImNhY2hlZCIsImdldCIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwib2siLCJkYXRhIiwianNvbiIsInNldCIsInByZWxvYWRDcml0aWNhbEFzc2V0cyIsImNyaXRpY2FsQXNzZXRzIiwicHJlbG9hZFByb21pc2VzIiwiYXNzZXQiLCJyZXNvbHZlIiwiY3JlYXRlRWxlbWVudCIsInJlbCIsImFzIiwiZW5kc1dpdGgiLCJvbmxvYWQiLCJvbmVycm9yIiwiaGVhZCIsImFwcGVuZENoaWxkIiwib2JzZXJ2ZUxpbmtzIiwibGlua3MiLCJxdWVyeVNlbGVjdG9yQWxsIiwib2JzZXJ2ZSIsInByZWxvYWRVc2VySm91cm5leSIsInVzZXJSb2xlIiwidXNlclBsYW4iLCJqb3VybmV5Um91dGVzIiwiZ2V0VXNlckpvdXJuZXlSb3V0ZXMiLCJiYXNlSm91cm5leSIsImdldFN0YXRzIiwiQXJyYXkiLCJmcm9tIiwicXVldWVTaXplIiwic2l6ZSIsImNhY2hlU3RhdHMiLCJjbGVhclByZWxvYWRlZCIsImNsZWFyIiwicHJlbG9hZGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/preloader.ts\n");

/***/ }),

/***/ "(rsc)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7cffc49d1683\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb2RleGd1YXJkLy4vc3R5bGVzL2dsb2JhbHMuY3NzPzZkZDQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3Y2ZmYzQ5ZDE2ODNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_preload_true_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"preload\":true}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"preload\\\":true}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_preload_true_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_preload_true_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/script */ \"(rsc)/./node_modules/next/dist/api/script.js\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../styles/globals.css */ \"(rsc)/./styles/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"KodeXGuard - Platform Cybersecurity & Bug Hunting\",\n    description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting dengan dukungan Bot WhatsApp/Telegram\",\n    keywords: \"cybersecurity, bug hunting, OSINT, vulnerability scanner, CVE, penetration testing, security tools\",\n    authors: [\n        {\n            name: \"KodeXGuard Team\"\n        }\n    ],\n    creator: \"KodeXGuard\",\n    publisher: \"KodeXGuard\",\n    robots: \"index, follow\",\n    openGraph: {\n        type: \"website\",\n        locale: \"id_ID\",\n        url: \"https://kodexguard.com\",\n        title: \"KodeXGuard - Platform Cybersecurity & Bug Hunting\",\n        description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting\",\n        siteName: \"KodeXGuard\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"KodeXGuard - Platform Cybersecurity & Bug Hunting\",\n        description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting\"\n    },\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#00ff41\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"id\",\n        className: \"dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//fonts.gstatic.com\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#00ff41\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"black-translucent\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_preload_true_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} min-h-screen bg-gradient-cyber text-white antialiased`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"matrix-bg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-b from-transparent via-cyber-dark/20 to-cyber-dark\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        id: \"performance-init\",\n                        strategy: \"afterInteractive\",\n                        dangerouslySetInnerHTML: {\n                            __html: `\n              // Initialize performance monitoring\n              if (typeof window !== 'undefined') {\n                // Register service worker\n                if ('serviceWorker' in navigator && '${\"development\"}' === 'production') {\n                  navigator.serviceWorker.register('/sw.js')\n                    .then(registration => console.log('SW registered:', registration))\n                    .catch(error => console.log('SW registration failed:', error));\n                }\n\n                // Performance monitoring\n                const perfObserver = new PerformanceObserver((list) => {\n                  const entries = list.getEntries();\n                  entries.forEach(entry => {\n                    if (entry.entryType === 'navigation') {\n                      console.log('Page Load Time:', entry.loadEventEnd - entry.fetchStart, 'ms');\n                    }\n                  });\n                });\n\n                if ('PerformanceObserver' in window) {\n                  perfObserver.observe({ entryTypes: ['navigation', 'paint'] });\n                }\n              }\n            `\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        id: \"matrix-effect\",\n                        strategy: \"lazyOnload\",\n                        dangerouslySetInnerHTML: {\n                            __html: `\n              // Matrix Rain Effect\n              function createMatrixRain() {\n                const canvas = document.createElement('canvas');\n                const ctx = canvas.getContext('2d');\n                canvas.style.position = 'fixed';\n                canvas.style.top = '0';\n                canvas.style.left = '0';\n                canvas.style.width = '100%';\n                canvas.style.height = '100%';\n                canvas.style.pointerEvents = 'none';\n                canvas.style.zIndex = '-1';\n                canvas.style.opacity = '0.1';\n                document.body.appendChild(canvas);\n                \n                canvas.width = window.innerWidth;\n                canvas.height = window.innerHeight;\n                \n                const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';\n                const charArray = chars.split('');\n                const fontSize = 14;\n                const columns = canvas.width / fontSize;\n                const drops = [];\n                \n                for (let i = 0; i < columns; i++) {\n                  drops[i] = 1;\n                }\n                \n                function draw() {\n                  ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';\n                  ctx.fillRect(0, 0, canvas.width, canvas.height);\n                  \n                  ctx.fillStyle = '#00ff41';\n                  ctx.font = fontSize + 'px monospace';\n                  \n                  for (let i = 0; i < drops.length; i++) {\n                    const text = charArray[Math.floor(Math.random() * charArray.length)];\n                    ctx.fillText(text, i * fontSize, drops[i] * fontSize);\n                    \n                    if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {\n                      drops[i] = 0;\n                    }\n                    drops[i]++;\n                  }\n                }\n                \n                setInterval(draw, 50);\n                \n                window.addEventListener('resize', () => {\n                  canvas.width = window.innerWidth;\n                  canvas.height = window.innerHeight;\n                });\n              }\n              \n              // Initialize matrix effect after page load\n              if (document.readyState === 'loading') {\n                document.addEventListener('DOMContentLoaded', createMatrixRain);\n              } else {\n                createMatrixRain();\n              }\n            `\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/osint/page.tsx":
/*!****************************!*\
  !*** ./app/osint/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\osint\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@opentelemetry","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fosint%2Fpage&page=%2Fosint%2Fpage&appPaths=%2Fosint%2Fpage&pagePath=private-next-app-dir%2Fosint%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();