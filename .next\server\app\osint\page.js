(()=>{var e={};e.id=388,e.ids=[388],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},76996:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>l,originalPathname:()=>b,pages:()=>d,routeModule:()=>p,tree:()=>m}),s(62503),s(30829),s(35866);var n=s(23191),r=s(88716),o=s(37922),a=s.n(o),x=s(95231),i={};for(let e in x)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>x[e]);s.d(t,i);let m=["",{children:["osint",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.t.bind(s,62503,23)),"D:\\Users\\Downloads\\kodeXGuard\\app\\osint\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\Users\\Downloads\\kodeXGuard\\app\\osint\\page.tsx"],b="/osint/page",l={require:s,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/osint/page",pathname:"/osint",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:m}})},62503:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError: \n  \x1b[38;2;255;30;30m\xd7\x1b[0m Expected ';', '}' or <eof>\n    ╭─[\x1b[38;2;92;157;255;1;4mD:\\Users\\Downloads\\kodeXGuard\\app\\osint\\page.tsx\x1b[0m:46:1]\n \x1b[2m46\x1b[0m │       const [results, setResults] = useState<OSINTResult[]>([])\n \x1b[2m47\x1b[0m │       const [deepSearch, setDeepSearch] = useState(false)\n \x1b[2m48\x1b[0m │       const [includeSocialMedia, setIncludeSocialMedia] = useState(true)\n \x1b[2m49\x1b[0m │ \x1b[38;2;246;87;248m╭\x1b[0m\x1b[38;2;246;87;248m─\x1b[0m\x1b[38;2;246;87;248m▶\x1b[0m     dataSourcesActive: 15,\n \x1b[2m50\x1b[0m │ \x1b[38;2;246;87;248m├\x1b[0m\x1b[38;2;246;87;248m─\x1b[0m\x1b[38;2;246;87;248m▶\x1b[0m     lastUpdate: '2 minutes ago'\n    \xb7 \x1b[38;2;246;87;248m╰\x1b[0m\x1b[38;2;246;87;248m───\x1b[0m\x1b[38;2;30;201;212m              ─\x1b[0m\n    \xb7 \x1b[38;2;246;87;248m╰\x1b[0m\x1b[38;2;246;87;248m───\x1b[0m\x1b[38;2;246;87;248m─\x1b[0m \x1b[38;2;246;87;248mThis is the expression part of an expression statement\x1b[0m\n \x1b[2m51\x1b[0m │       })\n \x1b[2m52\x1b[0m │     \n \x1b[2m52\x1b[0m │       const searchTypes = [\r\n    ╰────\n\n\nCaused by:\n    Syntax Error")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),n=t.X(0,[216,592],()=>s(76996));module.exports=n})();