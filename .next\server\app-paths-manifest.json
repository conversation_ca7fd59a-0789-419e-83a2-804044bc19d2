{"/_not-found/page": "app/_not-found/page.js", "/admin/page": "app/admin/page.js", "/api/auth/login/route": "app/api/auth/login/route.js", "/api/cve/search/route": "app/api/cve/search/route.js", "/api/auth/register/route": "app/api/auth/register/route.js", "/api/dashboard/stats/route": "app/api/dashboard/stats/route.js", "/api/file/analyze/route": "app/api/file/analyze/route.js", "/api/notifications/[id]/read/route": "app/api/notifications/[id]/read/route.js", "/api/notifications/mark-all-read/route": "app/api/notifications/mark-all-read/route.js", "/api/notifications/route": "app/api/notifications/route.js", "/api/osint/recent/route": "app/api/osint/recent/route.js", "/api/osint/search/route": "app/api/osint/search/route.js", "/api/osint/templates/route": "app/api/osint/templates/route.js", "/api/scan/[scanId]/results/route": "app/api/scan/[scanId]/results/route.js", "/api/scan/vulnerability/route": "app/api/scan/vulnerability/route.js", "/api/scanner/history/route": "app/api/scanner/history/route.js", "/api/scanner/templates/route": "app/api/scanner/templates/route.js", "/api/user/api-keys/route": "app/api/user/api-keys/route.js", "/api/user/plan/route": "app/api/user/plan/route.js", "/api/user/preferences/route": "app/api/user/preferences/route.js", "/api/user/profile/route": "app/api/user/profile/route.js", "/api/user/settings/route": "app/api/user/settings/route.js", "/api/user/usage/route": "app/api/user/usage/route.js", "/bot/page": "app/bot/page.js", "/cve/page": "app/cve/page.js", "/dashboard/page": "app/dashboard/page.js", "/docs/page": "app/docs/page.js", "/dorking/page": "app/dorking/page.js", "/file-analyzer/page": "app/file-analyzer/page.js", "/leaderboard/page": "app/leaderboard/page.js", "/login/page": "app/login/page.js", "/notifications/page": "app/notifications/page.js", "/osint/page": "app/osint/page.js", "/page": "app/page.js", "/plan/page": "app/plan/page.js", "/playground/page": "app/playground/page.js", "/profile/page": "app/profile/page.js", "/register/page": "app/register/page.js", "/scanner/page": "app/scanner/page.js", "/settings/page": "app/settings/page.js", "/tools/page": "app/tools/page.js"}