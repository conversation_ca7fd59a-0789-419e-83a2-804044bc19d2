"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/osint/templates/route";
exports.ids = ["app/api/osint/templates/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fosint%2Ftemplates%2Froute&page=%2Fapi%2Fosint%2Ftemplates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fosint%2Ftemplates%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fosint%2Ftemplates%2Froute&page=%2Fapi%2Fosint%2Ftemplates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fosint%2Ftemplates%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Users_Downloads_kodeXGuard_app_api_osint_templates_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/osint/templates/route.ts */ \"(rsc)/./app/api/osint/templates/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/osint/templates/route\",\n        pathname: \"/api/osint/templates\",\n        filename: \"route\",\n        bundlePath: \"app/api/osint/templates/route\"\n    },\n    resolvedPagePath: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\api\\\\osint\\\\templates\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Users_Downloads_kodeXGuard_app_api_osint_templates_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/osint/templates/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fosint%2Ftemplates%2Froute&page=%2Fapi%2Fosint%2Ftemplates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fosint%2Ftemplates%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/osint/templates/route.ts":
/*!******************************************!*\
  !*** ./app/api/osint/templates/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function GET(request) {\n    try {\n        // Get auth token\n        const authHeader = request.headers.get(\"authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        // Simple token validation for testing\n        if (!token.startsWith(\"kxg_\")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid token\"\n            }, {\n                status: 401\n            });\n        }\n        // Mock OSINT templates data\n        const templates = [\n            {\n                id: \"osint_template_001\",\n                name: \"Person Investigation\",\n                description: \"Comprehensive person lookup using multiple identity sources\",\n                category: \"person\",\n                searchTypes: [\n                    \"email\",\n                    \"phone\",\n                    \"name\",\n                    \"nik\",\n                    \"npwp\"\n                ],\n                sources: [\n                    \"Dukcapil Database\",\n                    \"Kemkes Database\",\n                    \"Social Media\",\n                    \"Email Breach DB\",\n                    \"Phone Number DB\"\n                ],\n                settings: {\n                    deepSearch: true,\n                    includeSocialMedia: true,\n                    includeBreaches: true,\n                    maxResults: 50,\n                    confidenceThreshold: 70\n                },\n                isDefault: true,\n                createdAt: new Date(Date.now() - 86400000 * 30).toISOString(),\n                updatedAt: new Date(Date.now() - 86400000 * 5).toISOString(),\n                usageCount: 234\n            },\n            {\n                id: \"osint_template_002\",\n                name: \"Business Intelligence\",\n                description: \"Corporate and business entity investigation template\",\n                category: \"business\",\n                searchTypes: [\n                    \"domain\",\n                    \"email\",\n                    \"name\"\n                ],\n                sources: [\n                    \"Domain WHOIS\",\n                    \"Social Media\",\n                    \"GitHub\",\n                    \"Email Breach DB\"\n                ],\n                settings: {\n                    deepSearch: true,\n                    includeSocialMedia: false,\n                    includeBreaches: true,\n                    maxResults: 100,\n                    confidenceThreshold: 60\n                },\n                isDefault: true,\n                createdAt: new Date(Date.now() - 86400000 * 45).toISOString(),\n                updatedAt: new Date(Date.now() - 86400000 * 3).toISOString(),\n                usageCount: 156\n            },\n            {\n                id: \"osint_template_003\",\n                name: \"Domain Analysis\",\n                description: \"Complete domain and infrastructure investigation\",\n                category: \"domain\",\n                searchTypes: [\n                    \"domain\",\n                    \"email\"\n                ],\n                sources: [\n                    \"Domain WHOIS\",\n                    \"GitHub\",\n                    \"Social Media\"\n                ],\n                settings: {\n                    deepSearch: true,\n                    includeSocialMedia: false,\n                    includeBreaches: false,\n                    maxResults: 25,\n                    confidenceThreshold: 80\n                },\n                isDefault: true,\n                createdAt: new Date(Date.now() - 86400000 * 20).toISOString(),\n                updatedAt: new Date(Date.now() - 86400000 * 2).toISOString(),\n                usageCount: 89\n            },\n            {\n                id: \"osint_template_004\",\n                name: \"Device Tracking\",\n                description: \"Mobile device and IMEI investigation template\",\n                category: \"device\",\n                searchTypes: [\n                    \"imei\",\n                    \"phone\"\n                ],\n                sources: [\n                    \"Phone Number DB\",\n                    \"Location Services\"\n                ],\n                settings: {\n                    deepSearch: false,\n                    includeSocialMedia: false,\n                    includeBreaches: false,\n                    maxResults: 10,\n                    confidenceThreshold: 90\n                },\n                isDefault: true,\n                createdAt: new Date(Date.now() - 86400000 * 15).toISOString(),\n                updatedAt: new Date(Date.now() - 86400000 * 1).toISOString(),\n                usageCount: 67\n            },\n            {\n                id: \"osint_template_005\",\n                name: \"Quick Lookup\",\n                description: \"Fast basic search for quick information gathering\",\n                category: \"person\",\n                searchTypes: [\n                    \"email\",\n                    \"phone\"\n                ],\n                sources: [\n                    \"Social Media\",\n                    \"Email Breach DB\"\n                ],\n                settings: {\n                    deepSearch: false,\n                    includeSocialMedia: true,\n                    includeBreaches: true,\n                    maxResults: 15,\n                    confidenceThreshold: 50\n                },\n                isDefault: true,\n                createdAt: new Date(Date.now() - 86400000 * 60).toISOString(),\n                updatedAt: new Date(Date.now() - 86400000 * 7).toISOString(),\n                usageCount: 345\n            },\n            {\n                id: \"osint_template_006\",\n                name: \"Social Media Deep Dive\",\n                description: \"Comprehensive social media investigation template\",\n                category: \"custom\",\n                searchTypes: [\n                    \"email\",\n                    \"phone\",\n                    \"name\"\n                ],\n                sources: [\n                    \"Social Media\",\n                    \"GitHub\"\n                ],\n                settings: {\n                    deepSearch: true,\n                    includeSocialMedia: true,\n                    includeBreaches: false,\n                    maxResults: 75,\n                    confidenceThreshold: 40\n                },\n                isDefault: false,\n                createdAt: new Date(Date.now() - 86400000 * 10).toISOString(),\n                updatedAt: new Date(Date.now() - 86400000 * 1).toISOString(),\n                usageCount: 123\n            }\n        ];\n        // Sort by usage count (most used first)\n        templates.sort((a, b)=>b.usageCount - a.usageCount);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: templates,\n            meta: {\n                total: templates.length,\n                default: templates.filter((t)=>t.isDefault).length,\n                custom: templates.filter((t)=>!t.isDefault).length,\n                categories: {\n                    person: templates.filter((t)=>t.category === \"person\").length,\n                    business: templates.filter((t)=>t.category === \"business\").length,\n                    domain: templates.filter((t)=>t.category === \"domain\").length,\n                    device: templates.filter((t)=>t.category === \"device\").length,\n                    custom: templates.filter((t)=>t.category === \"custom\").length\n                }\n            },\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"OSINT templates API error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        // Get auth token\n        const authHeader = request.headers.get(\"authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        // Simple token validation for testing\n        if (!token.startsWith(\"kxg_\")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid token\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { name, description, category, searchTypes, sources, settings } = body;\n        // Validate input\n        if (!name || !description || !category || !searchTypes || !sources) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Name, description, category, searchTypes, and sources are required\"\n            }, {\n                status: 400\n            });\n        }\n        // Create new template\n        const newTemplate = {\n            id: `osint_template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            name,\n            description,\n            category,\n            searchTypes,\n            sources,\n            settings: settings || {\n                deepSearch: false,\n                includeSocialMedia: true,\n                includeBreaches: true,\n                maxResults: 25,\n                confidenceThreshold: 60\n            },\n            isDefault: false,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString(),\n            usageCount: 0\n        };\n        // In a real app, save to database\n        // For demo, just return the created template\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: newTemplate,\n            message: \"OSINT template created successfully\",\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"Create OSINT template error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/osint/templates/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fosint%2Ftemplates%2Froute&page=%2Fapi%2Fosint%2Ftemplates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fosint%2Ftemplates%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();