"use strict";(()=>{var e={};e.id=996,e.ids=[996],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},99313:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>g,patchFetch:()=>f,requestAsyncStorage:()=>p,routeModule:()=>c,serverHooks:()=>m,staticGenerationAsyncStorage:()=>d});var a={};r.r(a),r.d(a,{GET:()=>u,POST:()=>l});var s=r(49303),n=r(88716),o=r(60670),i=r(87070);async function u(e){try{let t=e.headers.get("authorization"),r=t?.replace("Bearer ","");if(!r)return i.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!r.startsWith("kxg_"))return i.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let a=[{id:"1",type:"success",title:"Scan Completed",message:"Vulnerability scan for example.com completed successfully. Found 3 vulnerabilities.",timestamp:new Date().toISOString(),read:!1,action:{label:"View Results",url:"/scanner"}},{id:"2",type:"warning",title:"Quota Warning",message:"You have used 80% of your daily OSINT quota (40/50 searches).",timestamp:new Date(Date.now()-18e5).toISOString(),read:!1,action:{label:"Upgrade Plan",url:"/plan"}},{id:"3",type:"info",title:"New CVE Alert",message:"CVE-2024-1234 affects your monitored technologies. Severity: High.",timestamp:new Date(Date.now()-36e5).toISOString(),read:!1,action:{label:"View CVE",url:"/cve"}},{id:"4",type:"success",title:"File Analysis Complete",message:"malware_sample.exe analysis completed. Threat level: Malicious.",timestamp:new Date(Date.now()-72e5).toISOString(),read:!0,action:{label:"View Report",url:"/file-analyzer"}},{id:"5",type:"error",title:"Scan Failed",message:"OSINT search for target failed due to rate limiting. Please try again later.",timestamp:new Date(Date.now()-108e5).toISOString(),read:!0},{id:"6",type:"info",title:"Welcome to KodeXGuard",message:"Your account has been successfully created. Start exploring our cybersecurity tools!",timestamp:new Date(Date.now()-864e5).toISOString(),read:!0,action:{label:"Get Started",url:"/dashboard"}}];return a.sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime()),i.NextResponse.json({success:!0,data:a,meta:{total:a.length,unread:a.filter(e=>!e.read).length},timestamp:new Date().toISOString()})}catch(e){return console.error("Notifications API error:",e),i.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}async function l(e){try{let t=e.headers.get("authorization"),r=t?.replace("Bearer ","");if(!r)return i.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!r.startsWith("kxg_"))return i.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let{type:a,title:s,message:n,action:o}=await e.json();if(!a||!s||!n)return i.NextResponse.json({success:!1,error:"Type, title, and message are required"},{status:400});let u={id:`notif_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,type:a,title:s,message:n,timestamp:new Date().toISOString(),read:!1,action:o};return i.NextResponse.json({success:!0,data:u,message:"Notification created successfully",timestamp:new Date().toISOString()})}catch(e){return console.error("Create notification error:",e),i.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/notifications/route",pathname:"/api/notifications",filename:"route",bundlePath:"app/api/notifications/route"},resolvedPagePath:"D:\\Users\\Downloads\\kodeXGuard\\app\\api\\notifications\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:p,staticGenerationAsyncStorage:d,serverHooks:m}=c,g="/api/notifications/route";function f(){return(0,o.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:d})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[216,592],()=>r(99313));module.exports=a})();