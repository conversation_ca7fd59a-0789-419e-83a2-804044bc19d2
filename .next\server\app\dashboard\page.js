(()=>{var e={};e.id=702,e.ids=[702],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},88123:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>u,tree:()=>c}),s(69521),s(30829),s(35866);var a=s(23191),r=s(88716),i=s(37922),n=s.n(i),l=s(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,69521)),"D:\\Users\\Downloads\\kodeXGuard\\app\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\Users\\Downloads\\kodeXGuard\\app\\dashboard\\page.tsx"],x="/dashboard/page",m={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},79014:(e,t,s)=>{Promise.resolve().then(s.bind(s,71053))},71053:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var a=s(10326),r=s(17577),i=s(60463),n=s(2262),l=s(88307),o=s(94244),c=s(6530),d=s(58038),x=s(50732),m=s(92498),u=s(37202),h=s(48998),g=s(54659),p=s(66697),b=s(17069),j=s(67382);function y(e,t={immediate:!0}){let[s,a]=(0,r.useState)(null),[i,n]=(0,r.useState)(!1),[l,o]=(0,r.useState)(null),{token:c,isAuthenticated:d}=(0,j.a)();return{data:s,loading:i,error:l,refetch:async()=>{if(!d||!c){o("Authentication required");return}try{n(!0),o(null);let t=await fetch(e,{headers:{Authorization:`Bearer ${c}`,"Content-Type":"application/json"}}),s=await t.json();s.success&&s.data?a(s.data):o(s.error||"Failed to fetch data")}catch(e){o(e instanceof Error?e.message:"Network error")}finally{n(!1)}},mutate:async(t="POST",s)=>{if(!d||!c)throw Error("Authentication required");try{n(!0),o(null);let r=await fetch(e,{method:t,headers:{Authorization:`Bearer ${c}`,"Content-Type":"application/json"},body:s?JSON.stringify(s):void 0}),i=await r.json();if(i.success)return i.data&&a(i.data),i;throw Error(i.error||"Request failed")}catch(t){let e=t instanceof Error?t.message:"Network error";throw o(e),Error(e)}finally{n(!1)}}}}var f=s(35047);function v({children:e,requiredRole:t=[],requiredPlan:s=[],fallbackPath:r="/login"}){let{user:i,isAuthenticated:n,isLoading:l}=(0,j.a)(),o=(0,f.useRouter)();if(l)return a.jsx("div",{className:"min-h-screen bg-gradient-cyber flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-cyber-green mx-auto"}),a.jsx("p",{className:"mt-4 text-gray-400",children:"Authenticating..."})]})});if(!n)return a.jsx("div",{className:"min-h-screen bg-gradient-cyber flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-red-500 text-6xl mb-4",children:"\uD83D\uDD12"}),a.jsx("h1",{className:"text-2xl font-bold text-white mb-2",children:"Access Denied"}),a.jsx("p",{className:"text-gray-400 mb-4",children:"Please log in to access this page"}),a.jsx("button",{onClick:()=>o.push("/login"),className:"bg-cyber-green text-black px-6 py-2 rounded-lg font-semibold hover:bg-green-400 transition-colors",children:"Go to Login"})]})});if(i){if(t.length>0&&!t.includes(i.role))return a.jsx("div",{className:"min-h-screen bg-gradient-cyber flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-yellow-500 text-6xl mb-4",children:"⚠️"}),a.jsx("h1",{className:"text-2xl font-bold text-white mb-2",children:"Insufficient Permissions"}),(0,a.jsxs)("p",{className:"text-gray-400 mb-4",children:["This feature requires ",t.join(" or ")," role"]}),a.jsx("button",{onClick:()=>o.push("/dashboard"),className:"bg-cyber-green text-black px-6 py-2 rounded-lg font-semibold hover:bg-green-400 transition-colors",children:"Back to Dashboard"})]})});if(s.length>0&&!s.includes(i.plan))return a.jsx("div",{className:"min-h-screen bg-gradient-cyber flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-purple-500 text-6xl mb-4",children:"\uD83D\uDC8E"}),a.jsx("h1",{className:"text-2xl font-bold text-white mb-2",children:"Upgrade Required"}),(0,a.jsxs)("p",{className:"text-gray-400 mb-4",children:["This feature requires ",s.join(" or ")," plan"]}),(0,a.jsxs)("div",{className:"space-x-4",children:[a.jsx("button",{onClick:()=>o.push("/upgrade"),className:"bg-cyber-green text-black px-6 py-2 rounded-lg font-semibold hover:bg-green-400 transition-colors",children:"Upgrade Plan"}),a.jsx("button",{onClick:()=>o.push("/dashboard"),className:"bg-gray-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-gray-500 transition-colors",children:"Back to Dashboard"})]})]})})}return a.jsx(a.Fragment,{children:e})}function N(){let{user:e,isAuthenticated:t}=(0,j.a)(),{data:s,loading:f,error:N}=y("/api/dashboard/stats"),{data:w,loading:P}=y("/api/user/profile");if(!t)return a.jsx(v,{children:a.jsx("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-cyber-green mx-auto"}),a.jsx("p",{className:"mt-4 text-gray-400",children:"Loading..."})]})})});let[k,A]=(0,r.useState)([{id:1,type:"scan",title:"Vulnerability scan completed",target:"example.com",severity:"high",time:"2 minutes ago"},{id:2,type:"osint",title:"OSINT search completed",target:"<EMAIL>",severity:"medium",time:"15 minutes ago"},{id:3,type:"file",title:"File analysis completed",target:"suspicious.php",severity:"critical",time:"1 hour ago"}]),D=[{title:"OSINT Investigator",description:"Investigasi mendalam dengan pencarian NIK, NPWP, nomor HP, email, dan domain",icon:l.Z,color:"green",href:"/osint"},{title:"Vulnerability Scanner",description:"Deteksi SQLi, XSS, LFI, RCE dengan scoring CVSS dan mapping CVE",icon:o.Z,color:"red",href:"/scanner"},{title:"File Analyzer",description:"Analisis webshell, malware, dan deteksi secret dalam berbagai format file",icon:c.Z,color:"blue",href:"/file-analyzer"},{title:"CVE Intelligence",description:"Database CVE terupdate dengan Google dorking dan payload generator",icon:d.Z,color:"purple",href:"/cve"},{title:"Bot Integration",description:"WhatsApp & Telegram bot untuk monitoring dan notifikasi real-time",icon:x.Z,color:"gold",href:"/bot"},{title:"API Playground",description:"Testing environment dengan Swagger docs dan request builder",icon:m.Z,color:"green",href:"/playground"}],S=e=>{switch(e){case"critical":return"text-red-400";case"high":return"text-orange-400";case"medium":return"text-yellow-400";case"low":return"text-blue-400";default:return"text-gray-400"}},C=e=>{switch(e){case"critical":case"high":return u.Z;case"medium":return h.Z;default:return g.Z}};return a.jsx(v,{children:a.jsx(i.Z,{user:e,title:"Dashboard",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold font-cyber text-white mb-2",children:["Welcome back, ",a.jsx("span",{className:"cyber-text",children:e.username})]}),a.jsx("p",{className:"text-gray-400",children:"Monitor your cybersecurity activities and manage your tools from here."})]}),a.jsx("div",{className:"mb-8",children:a.jsx(n.XO,{type:"success",title:"System Status: Online",message:"All systems are operational. Last updated: 2 minutes ago"})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[a.jsx(n.Rm,{title:"Total Scans",value:stats.totalScans,icon:p.Z,color:"green",trend:{value:12,isPositive:!0},loading:stats.loading}),a.jsx(n.Rm,{title:"Vulnerabilities Found",value:stats.vulnerabilitiesFound,icon:o.Z,color:"red",trend:{value:8,isPositive:!0},loading:stats.loading}),a.jsx(n.Rm,{title:"Files Analyzed",value:stats.filesAnalyzed,icon:c.Z,color:"blue",trend:{value:15,isPositive:!0},loading:stats.loading}),a.jsx(n.Rm,{title:"API Calls",value:stats.apiCalls,icon:b.Z,color:"purple",trend:{value:23,isPositive:!0},loading:stats.loading})]}),(0,a.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2",children:[a.jsx("h2",{className:"text-xl font-bold text-white mb-6",children:"Quick Access"}),a.jsx("div",{className:"grid md:grid-cols-2 gap-6",children:D.map((e,t)=>a.jsx(n.TT,{title:e.title,description:e.description,icon:e.icon,color:e.color,onClick:()=>window.location.href=e.href},t))})]}),(0,a.jsxs)("div",{children:[a.jsx("h2",{className:"text-xl font-bold text-white mb-6",children:"Recent Activity"}),a.jsx(n.Zb,{children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("div",{className:"space-y-4",children:k.map(e=>{let t=C(e.severity);return(0,a.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg bg-gray-800/50",children:[a.jsx(t,{className:`h-5 w-5 mt-0.5 ${S(e.severity)}`}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[a.jsx("p",{className:"text-sm font-medium text-white",children:e.title}),a.jsx("p",{className:"text-sm text-gray-400 truncate",children:e.target}),a.jsx("p",{className:"text-xs text-gray-500",children:e.time})]}),a.jsx("span",{className:`px-2 py-1 text-xs font-semibold rounded-full ${"critical"===e.severity?"bg-red-900/50 text-red-400":"high"===e.severity?"bg-orange-900/50 text-orange-400":"medium"===e.severity?"bg-yellow-900/50 text-yellow-400":"bg-blue-900/50 text-blue-400"}`,children:e.severity})]},e.id)})}),a.jsx("div",{className:"mt-4 pt-4 border-t border-gray-700",children:a.jsx("button",{className:"w-full text-sm text-cyber-green hover:text-cyber-blue transition-colors",children:"View All Activity →"})})]})}),a.jsx(n.Zb,{className:"mt-6",border:"gold",glow:!0,children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"Current Plan"}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[a.jsx("span",{className:"text-2xl font-bold nusantara-gold",children:e.plan.charAt(0).toUpperCase()+e.plan.slice(1)}),a.jsx("span",{className:"px-3 py-1 bg-nusantara-gold/20 text-nusantara-gold rounded-full text-sm font-semibold",children:"Active"})]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm text-gray-400",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{children:"Daily Scans"}),a.jsx("span",{className:"text-white",children:"Unlimited"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{children:"API Calls"}),a.jsx("span",{className:"text-white",children:"100,000/day"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{children:"File Upload"}),a.jsx("span",{className:"text-white",children:"1GB"})]})]}),a.jsx("button",{className:"w-full mt-4 cyber-btn text-sm",children:"Manage Plan"})]})})]})]})]})})})}},69521:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\dashboard\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[216,592],()=>s(88123));module.exports=a})();