/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/playground/page";
exports.ids = ["app/playground/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fplayground%2Fpage&page=%2Fplayground%2Fpage&appPaths=%2Fplayground%2Fpage&pagePath=private-next-app-dir%2Fplayground%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fplayground%2Fpage&page=%2Fplayground%2Fpage&appPaths=%2Fplayground%2Fpage&pagePath=private-next-app-dir%2Fplayground%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'playground',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/playground/page.tsx */ \"(rsc)/./app/playground/page.tsx\")), \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/playground/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/playground/page\",\n        pathname: \"/playground\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fplayground%2Fpage&page=%2Fplayground%2Fpage&appPaths=%2Fplayground%2Fpage&pagePath=private-next-app-dir%2Fplayground%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Cplayground%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Cplayground%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/playground/page.tsx */ \"(ssr)/./app/playground/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNVc2VycyU1QyU1Q0Rvd25sb2FkcyU1QyU1Q2tvZGVYR3VhcmQlNUMlNUNhcHAlNUMlNUNwbGF5Z3JvdW5kJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDhKQUFnRyIsInNvdXJjZXMiOlsid2VicGFjazovL2tvZGV4Z3VhcmQvPzU3NDgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxVc2Vyc1xcXFxEb3dubG9hZHNcXFxca29kZVhHdWFyZFxcXFxhcHBcXFxccGxheWdyb3VuZFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Cplayground%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNVc2VycyU1QyU1Q0Rvd25sb2FkcyU1QyU1Q2tvZGVYR3VhcmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNhcHAtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNVc2VycyU1QyU1Q0Rvd25sb2FkcyU1QyU1Q2tvZGVYR3VhcmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDVXNlcnMlNUMlNUNEb3dubG9hZHMlNUMlNUNrb2RlWEd1YXJkJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1VzZXJzJTVDJTVDRG93bmxvYWRzJTVDJTVDa29kZVhHdWFyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2xheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1VzZXJzJTVDJTVDRG93bmxvYWRzJTVDJTVDa29kZVhHdWFyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q25vdC1mb3VuZC1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDVXNlcnMlNUMlNUNEb3dubG9hZHMlNUMlNUNrb2RlWEd1YXJkJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQWtJO0FBQ2xJO0FBQ0Esb09BQW1JO0FBQ25JO0FBQ0EsME9BQXNJO0FBQ3RJO0FBQ0Esd09BQXFJO0FBQ3JJO0FBQ0Esa1BBQTBJO0FBQzFJO0FBQ0Esc1FBQW9KIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va29kZXhndWFyZC8/ODk4YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFVzZXJzXFxcXERvd25sb2Fkc1xcXFxrb2RlWEd1YXJkXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcYXBwLXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcVXNlcnNcXFxcRG93bmxvYWRzXFxcXGtvZGVYR3VhcmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcVXNlcnNcXFxcRG93bmxvYWRzXFxcXGtvZGVYR3VhcmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcVXNlcnNcXFxcRG93bmxvYWRzXFxcXGtvZGVYR3VhcmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxVc2Vyc1xcXFxEb3dubG9hZHNcXFxca29kZVhHdWFyZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG5vdC1mb3VuZC1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcVXNlcnNcXFxcRG93bmxvYWRzXFxcXGtvZGVYR3VhcmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/playground/page.tsx":
/*!*********************************!*\
  !*** ./app/playground/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PlaygroundPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(ssr)/./components/DashboardLayout.tsx\");\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Card */ \"(ssr)/./components/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Code,Copy,Database,Edit,Globe,Key,Play,RefreshCw,Save,Star,Trash2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Code,Copy,Database,Edit,Globe,Key,Play,RefreshCw,Save,Star,Trash2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Code,Copy,Database,Edit,Globe,Key,Play,RefreshCw,Save,Star,Trash2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Code,Copy,Database,Edit,Globe,Key,Play,RefreshCw,Save,Star,Trash2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Code,Copy,Database,Edit,Globe,Key,Play,RefreshCw,Save,Star,Trash2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Code,Copy,Database,Edit,Globe,Key,Play,RefreshCw,Save,Star,Trash2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Code,Copy,Database,Edit,Globe,Key,Play,RefreshCw,Save,Star,Trash2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Code,Copy,Database,Edit,Globe,Key,Play,RefreshCw,Save,Star,Trash2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Code,Copy,Database,Edit,Globe,Key,Play,RefreshCw,Save,Star,Trash2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Code,Copy,Database,Edit,Globe,Key,Play,RefreshCw,Save,Star,Trash2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Code,Copy,Database,Edit,Globe,Key,Play,RefreshCw,Save,Star,Trash2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Code,Copy,Database,Edit,Globe,Key,Play,RefreshCw,Save,Star,Trash2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Code,Copy,Database,Edit,Globe,Key,Play,RefreshCw,Save,Star,Trash2,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction PlaygroundPage() {\n    const [user] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: \"admin\",\n        avatar: \"\",\n        role: \"super_admin\",\n        plan: \"cybersecurity\"\n    });\n    const [selectedEndpoint, setSelectedEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [requestMethod, setRequestMethod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"GET\");\n    const [requestUrl, setRequestUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [requestHeaders, setRequestHeaders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('{\\n  \"Authorization\": \"Bearer your-api-key\",\\n  \"Content-Type\": \"application/json\"\\n}');\n    const [requestBody, setRequestBody] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"{}\");\n    const [response, setResponse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"playground\");\n    const [savedRequests, setSavedRequests] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const apiEndpoints = [\n        {\n            id: \"1\",\n            method: \"POST\",\n            path: \"/api/auth/login\",\n            description: \"Authenticate user and get access token\",\n            category: \"Authentication\",\n            requiresAuth: false,\n            parameters: [\n                {\n                    name: \"email\",\n                    type: \"string\",\n                    required: true,\n                    description: \"User email address\"\n                },\n                {\n                    name: \"password\",\n                    type: \"string\",\n                    required: true,\n                    description: \"User password\"\n                }\n            ],\n            example: {\n                request: {\n                    email: \"<EMAIL>\",\n                    password: \"admin123\"\n                },\n                response: {\n                    token: \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\",\n                    user: {\n                        id: \"1\",\n                        username: \"admin\",\n                        role: \"super_admin\"\n                    }\n                }\n            }\n        },\n        {\n            id: \"2\",\n            method: \"GET\",\n            path: \"/api/user/profile\",\n            description: \"Get current user profile information\",\n            category: \"User\",\n            requiresAuth: true,\n            parameters: [],\n            example: {\n                request: {},\n                response: {\n                    id: \"1\",\n                    username: \"admin\",\n                    email: \"<EMAIL>\",\n                    plan: \"cybersecurity\",\n                    stats: {\n                        totalScans: 1247\n                    }\n                }\n            }\n        },\n        {\n            id: \"3\",\n            method: \"POST\",\n            path: \"/api/scan/vulnerability\",\n            description: \"Start a new vulnerability scan\",\n            category: \"Scanning\",\n            requiresAuth: true,\n            parameters: [\n                {\n                    name: \"target\",\n                    type: \"string\",\n                    required: true,\n                    description: \"Target URL to scan\"\n                },\n                {\n                    name: \"scanTypes\",\n                    type: \"array\",\n                    required: true,\n                    description: \"Types of vulnerabilities to scan for\"\n                },\n                {\n                    name: \"maxDepth\",\n                    type: \"number\",\n                    required: false,\n                    description: \"Maximum crawl depth (default: 3)\"\n                }\n            ],\n            example: {\n                request: {\n                    target: \"https://example.com\",\n                    scanTypes: [\n                        \"sqli\",\n                        \"xss\",\n                        \"lfi\"\n                    ],\n                    maxDepth: 3\n                },\n                response: {\n                    scanId: \"scan_123456\",\n                    status: \"started\",\n                    estimatedTime: \"5-10 minutes\"\n                }\n            }\n        },\n        {\n            id: \"4\",\n            method: \"GET\",\n            path: \"/api/scan/{scanId}/results\",\n            description: \"Get vulnerability scan results\",\n            category: \"Scanning\",\n            requiresAuth: true,\n            parameters: [\n                {\n                    name: \"scanId\",\n                    type: \"string\",\n                    required: true,\n                    description: \"Scan ID from scan initiation\"\n                }\n            ],\n            example: {\n                request: {},\n                response: {\n                    scanId: \"scan_123456\",\n                    status: \"completed\",\n                    vulnerabilities: [\n                        {\n                            type: \"sqli\",\n                            severity: \"high\",\n                            url: \"https://example.com/login\"\n                        }\n                    ]\n                }\n            }\n        },\n        {\n            id: \"5\",\n            method: \"POST\",\n            path: \"/api/osint/search\",\n            description: \"Perform OSINT investigation\",\n            category: \"OSINT\",\n            requiresAuth: true,\n            parameters: [\n                {\n                    name: \"query\",\n                    type: \"string\",\n                    required: true,\n                    description: \"Search query (email, phone, domain, etc.)\"\n                },\n                {\n                    name: \"type\",\n                    type: \"string\",\n                    required: true,\n                    description: \"Search type (email, phone, nik, domain, etc.)\"\n                },\n                {\n                    name: \"deepSearch\",\n                    type: \"boolean\",\n                    required: false,\n                    description: \"Enable deep search (default: false)\"\n                }\n            ],\n            example: {\n                request: {\n                    query: \"<EMAIL>\",\n                    type: \"email\",\n                    deepSearch: true\n                },\n                response: {\n                    results: [\n                        {\n                            source: \"Dukcapil\",\n                            data: {\n                                name: \"John Doe\",\n                                verified: true\n                            },\n                            confidence: 95\n                        }\n                    ]\n                }\n            }\n        },\n        {\n            id: \"6\",\n            method: \"POST\",\n            path: \"/api/file/analyze\",\n            description: \"Analyze uploaded file for threats\",\n            category: \"File Analysis\",\n            requiresAuth: true,\n            parameters: [\n                {\n                    name: \"file\",\n                    type: \"file\",\n                    required: true,\n                    description: \"File to analyze (multipart/form-data)\"\n                },\n                {\n                    name: \"analysisType\",\n                    type: \"string\",\n                    required: false,\n                    description: \"Type of analysis (malware, webshell, secret, general)\"\n                }\n            ],\n            example: {\n                request: {\n                    file: \"suspicious.php\",\n                    analysisType: \"webshell\"\n                },\n                response: {\n                    analysisId: \"analysis_789\",\n                    threatLevel: \"suspicious\",\n                    results: {\n                        webshellDetected: true,\n                        type: \"PHP Webshell\"\n                    }\n                }\n            }\n        },\n        {\n            id: \"7\",\n            method: \"GET\",\n            path: \"/api/cve/search\",\n            description: \"Search CVE database\",\n            category: \"CVE Intelligence\",\n            requiresAuth: true,\n            parameters: [\n                {\n                    name: \"query\",\n                    type: \"string\",\n                    required: false,\n                    description: \"Search query\"\n                },\n                {\n                    name: \"severity\",\n                    type: \"string\",\n                    required: false,\n                    description: \"Filter by severity (critical, high, medium, low)\"\n                },\n                {\n                    name: \"year\",\n                    type: \"string\",\n                    required: false,\n                    description: \"Filter by year\"\n                },\n                {\n                    name: \"limit\",\n                    type: \"number\",\n                    required: false,\n                    description: \"Number of results (default: 20)\"\n                }\n            ],\n            example: {\n                request: {\n                    query: \"SQL injection\",\n                    severity: \"critical\",\n                    limit: 10\n                },\n                response: {\n                    total: 156,\n                    cves: [\n                        {\n                            cveId: \"CVE-2024-0001\",\n                            severity: \"critical\",\n                            cvssScore: 9.8,\n                            description: \"SQL injection vulnerability...\"\n                        }\n                    ]\n                }\n            }\n        }\n    ];\n    const executeRequest = async ()=>{\n        setLoading(true);\n        setResponse(\"\");\n        try {\n            // Simulate API call\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            const mockResponse = {\n                status: 200,\n                statusText: \"OK\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"X-RateLimit-Remaining\": \"99\",\n                    \"X-Response-Time\": \"142ms\"\n                },\n                data: selectedEndpoint?.example.response || {\n                    message: \"Success\",\n                    timestamp: new Date().toISOString()\n                }\n            };\n            setResponse(JSON.stringify(mockResponse, null, 2));\n        } catch (error) {\n            setResponse(JSON.stringify({\n                status: 500,\n                error: \"Internal Server Error\",\n                message: \"An error occurred while processing your request\"\n            }, null, 2));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const saveRequest = ()=>{\n        const newRequest = {\n            id: Date.now().toString(),\n            name: `${requestMethod} ${requestUrl}`,\n            method: requestMethod,\n            url: requestUrl,\n            headers: JSON.parse(requestHeaders),\n            body: requestBody,\n            createdAt: new Date().toISOString(),\n            favorite: false\n        };\n        setSavedRequests((prev)=>[\n                ...prev,\n                newRequest\n            ]);\n    };\n    const loadRequest = (request)=>{\n        setRequestMethod(request.method);\n        setRequestUrl(request.url);\n        setRequestHeaders(JSON.stringify(request.headers, null, 2));\n        setRequestBody(request.body);\n        setActiveTab(\"playground\");\n    };\n    const toggleFavorite = (requestId)=>{\n        setSavedRequests((prev)=>prev.map((req)=>req.id === requestId ? {\n                    ...req,\n                    favorite: !req.favorite\n                } : req));\n    };\n    const deleteRequest = (requestId)=>{\n        setSavedRequests((prev)=>prev.filter((req)=>req.id !== requestId));\n    };\n    const copyToClipboard = (text)=>{\n        navigator.clipboard.writeText(text);\n    };\n    const categories = [\n        ...new Set(apiEndpoints.map((ep)=>ep.category))\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        user: user,\n        title: \"API Playground\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-8 w-8 text-cyber-green\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold font-cyber text-white\",\n                                    children: [\n                                        \"API \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"cyber-text\",\n                                            children: \"Playground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 max-w-3xl\",\n                            children: \"Interactive API testing environment dengan Swagger documentation, request builder, dan endpoint management. Test semua endpoint KodeXGuard dengan real-time response.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.StatCard, {\n                            title: \"API Endpoints\",\n                            value: apiEndpoints.length,\n                            icon: _barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                            color: \"green\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.StatCard, {\n                            title: \"Categories\",\n                            value: categories.length,\n                            icon: _barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                            color: \"blue\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.StatCard, {\n                            title: \"Saved Requests\",\n                            value: savedRequests.length,\n                            icon: _barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                            color: \"purple\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.StatCard, {\n                            title: \"API Calls Today\",\n                            value: \"1,247\",\n                            icon: _barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                            color: \"gold\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-1 bg-gray-800/50 p-1 rounded-lg\",\n                        children: [\n                            {\n                                id: \"playground\",\n                                name: \"API Playground\",\n                                icon: _barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                            },\n                            {\n                                id: \"docs\",\n                                name: \"Documentation\",\n                                icon: _barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                            },\n                            {\n                                id: \"saved\",\n                                name: \"Saved Requests\",\n                                icon: _barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                            }\n                        ].map((tab)=>{\n                            const Icon = tab.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(tab.id),\n                                className: `flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${activeTab === tab.id ? \"bg-cyber-green text-black\" : \"text-gray-300 hover:text-white hover:bg-gray-700/50\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: tab.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 19\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 11\n                }, this),\n                activeTab === \"playground\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    border: \"green\",\n                                    glow: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-bold text-white mb-6\",\n                                                children: \"Request Builder\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid md:grid-cols-4 gap-4 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                children: \"Method\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: requestMethod,\n                                                                onChange: (e)=>setRequestMethod(e.target.value),\n                                                                className: \"cyber-input w-full\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"GET\",\n                                                                        children: \"GET\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 350,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"POST\",\n                                                                        children: \"POST\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 351,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"PUT\",\n                                                                        children: \"PUT\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 352,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"DELETE\",\n                                                                        children: \"DELETE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 353,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"md:col-span-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                children: \"URL\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: requestUrl,\n                                                                onChange: (e)=>setRequestUrl(e.target.value),\n                                                                placeholder: \"https://api.kodexguard.com/v1/endpoint\",\n                                                                className: \"cyber-input w-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                        children: \"Headers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: requestHeaders,\n                                                        onChange: (e)=>setRequestHeaders(e.target.value),\n                                                        className: \"cyber-input w-full h-24 font-mono text-sm\",\n                                                        placeholder: '{\"Authorization\": \"Bearer your-api-key\"}'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 21\n                                            }, this),\n                                            (requestMethod === \"POST\" || requestMethod === \"PUT\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                        children: \"Request Body\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: requestBody,\n                                                        onChange: (e)=>setRequestBody(e.target.value),\n                                                        className: \"cyber-input w-full h-32 font-mono text-sm\",\n                                                        placeholder: '{\"key\": \"value\"}'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: executeRequest,\n                                                        disabled: loading || !requestUrl,\n                                                        className: \"cyber-btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 401,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                \"Executing...\"\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                \"Send Request\"\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: saveRequest,\n                                                        disabled: !requestUrl,\n                                                        className: \"cyber-btn disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Save\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>copyToClipboard(requestUrl),\n                                                        className: \"cyber-btn\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 423,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Copy URL\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 17\n                                }, this),\n                                response && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold text-white\",\n                                                        children: \"Response\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>copyToClipboard(response),\n                                                        className: \"cyber-btn text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Copy\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-900/50 rounded-lg p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    className: \"text-sm text-cyber-green whitespace-pre-wrap overflow-x-auto\",\n                                                    children: response\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-white mb-4\",\n                                            children: \"API Endpoints\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-semibold text-gray-400 mb-2\",\n                                                            children: category\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        apiEndpoints.filter((ep)=>ep.category === category).map((endpoint)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>{\n                                                                    setSelectedEndpoint(endpoint);\n                                                                    setRequestMethod(endpoint.method);\n                                                                    setRequestUrl(`https://api.kodexguard.com/v1${endpoint.path}`);\n                                                                    setRequestBody(JSON.stringify(endpoint.example.request, null, 2));\n                                                                },\n                                                                className: `w-full text-left p-3 rounded-lg border transition-all duration-200 ${selectedEndpoint?.id === endpoint.id ? \"border-cyber-green bg-cyber-green/10\" : \"border-gray-700 hover:border-gray-600\"}`,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between mb-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: `px-2 py-1 rounded text-xs font-semibold ${endpoint.method === \"GET\" ? \"bg-blue-900/50 text-blue-400\" : endpoint.method === \"POST\" ? \"bg-green-900/50 text-green-400\" : endpoint.method === \"PUT\" ? \"bg-yellow-900/50 text-yellow-400\" : \"bg-red-900/50 text-red-400\"}`,\n                                                                                children: endpoint.method\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 479,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            endpoint.requiresAuth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"h-3 w-3 text-gray-400\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                                lineNumber: 488,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 478,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium text-white\",\n                                                                        children: endpoint.path\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 491,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                                        children: endpoint.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 492,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, endpoint.id, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 464,\n                                                                columnNumber: 29\n                                                            }, this))\n                                                    ]\n                                                }, category, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                            lineNumber: 455,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 13\n                }, this),\n                activeTab === \"docs\" && selectedEndpoint && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `px-3 py-1 rounded font-semibold ${selectedEndpoint.method === \"GET\" ? \"bg-blue-900/50 text-blue-400\" : selectedEndpoint.method === \"POST\" ? \"bg-green-900/50 text-green-400\" : selectedEndpoint.method === \"PUT\" ? \"bg-yellow-900/50 text-yellow-400\" : \"bg-red-900/50 text-red-400\"}`,\n                                        children: selectedEndpoint.method\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-white\",\n                                        children: selectedEndpoint.path\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 19\n                                    }, this),\n                                    selectedEndpoint.requiresAuth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 bg-orange-900/50 text-orange-400 rounded text-sm\",\n                                        children: \"\\uD83D\\uDD12 Auth Required\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300 mb-6\",\n                                children: selectedEndpoint.description\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 17\n                            }, this),\n                            selectedEndpoint.parameters.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white mb-4\",\n                                        children: \"Parameters\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 530,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"overflow-x-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                            className: \"w-full text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"border-b border-gray-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-2 text-gray-400\",\n                                                                children: \"Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 535,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-2 text-gray-400\",\n                                                                children: \"Type\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 536,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-2 text-gray-400\",\n                                                                children: \"Required\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 537,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-2 text-gray-400\",\n                                                                children: \"Description\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 538,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                    children: selectedEndpoint.parameters.map((param, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"border-b border-gray-800\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-2 text-white font-mono\",\n                                                                    children: param.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 544,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-2 text-cyber-green\",\n                                                                    children: param.type\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 545,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-2\",\n                                                                    children: param.required ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-400\",\n                                                                        children: \"Yes\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 548,\n                                                                        columnNumber: 35\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400\",\n                                                                        children: \"No\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                        lineNumber: 550,\n                                                                        columnNumber: 35\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 546,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-2 text-gray-300\",\n                                                                    children: param.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 553,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, idx, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 29\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 529,\n                                columnNumber: 19\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                children: \"Example Request\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 565,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-900/50 rounded-lg p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    className: \"text-sm text-gray-300 whitespace-pre-wrap\",\n                                                    children: JSON.stringify(selectedEndpoint.example.request, null, 2)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 567,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 566,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                children: \"Example Response\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 573,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-900/50 rounded-lg p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    className: \"text-sm text-cyber-green whitespace-pre-wrap\",\n                                                    children: JSON.stringify(selectedEndpoint.example.response, null, 2)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                    lineNumber: 575,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 574,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 563,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 506,\n                    columnNumber: 13\n                }, this),\n                activeTab === \"saved\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-white mb-6\",\n                                children: \"Saved Requests\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 17\n                            }, this),\n                            savedRequests.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white mb-2\",\n                                        children: \"No Saved Requests\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Save your API requests to access them later\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 594,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 591,\n                                columnNumber: 19\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: savedRequests.map((request)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-800/50 rounded-lg p-4 border border-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `px-2 py-1 rounded text-xs font-semibold ${request.method === \"GET\" ? \"bg-blue-900/50 text-blue-400\" : request.method === \"POST\" ? \"bg-green-900/50 text-green-400\" : request.method === \"PUT\" ? \"bg-yellow-900/50 text-yellow-400\" : \"bg-red-900/50 text-red-400\"}`,\n                                                                children: request.method\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 605,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-white\",\n                                                                children: request.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 613,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            request.favorite && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4 text-yellow-400 fill-current\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 604,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>toggleFavorite(request.id),\n                                                                className: `text-gray-400 hover:text-yellow-400 transition-colors ${request.favorite ? \"text-yellow-400\" : \"\"}`,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 625,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 619,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>loadRequest(request),\n                                                                className: \"text-gray-400 hover:text-cyber-green transition-colors\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 631,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 627,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>deleteRequest(request.id),\n                                                                className: \"text-gray-400 hover:text-red-400 transition-colors\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Code_Copy_Database_Edit_Globe_Key_Play_RefreshCw_Save_Star_Trash2_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                    lineNumber: 637,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                                lineNumber: 633,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                        lineNumber: 618,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-300 mb-2\",\n                                                children: request.url\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 641,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: [\n                                                    \"Saved: \",\n                                                    new Date(request.createdAt).toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                                lineNumber: 642,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, request.id, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 23\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                                lineNumber: 597,\n                                columnNumber: 19\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                        lineNumber: 588,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n                    lineNumber: 587,\n                    columnNumber: 13\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n            lineNumber: 262,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\playground\\\\page.tsx\",\n        lineNumber: 261,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/playground/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Card.tsx":
/*!*****************************!*\
  !*** ./components/Card.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlertCard: () => (/* binding */ AlertCard),\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   FeatureCard: () => (/* binding */ FeatureCard),\n/* harmony export */   LoadingCard: () => (/* binding */ LoadingCard),\n/* harmony export */   StatCard: () => (/* binding */ StatCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ Card,StatCard,FeatureCard,LoadingCard,AlertCard auto */ \nfunction Card({ children, className = \"\", hover = false, glow = false, border = \"default\" }) {\n    const borderColors = {\n        default: \"border-gray-700\",\n        green: \"border-cyber-green\",\n        blue: \"border-cyber-blue\",\n        red: \"border-cyber-red\",\n        gold: \"border-nusantara-gold\"\n    };\n    const glowColors = {\n        default: \"\",\n        green: \"shadow-lg shadow-cyber-green/20\",\n        blue: \"shadow-lg shadow-cyber-blue/20\",\n        red: \"shadow-lg shadow-cyber-red/20\",\n        gold: \"shadow-lg shadow-nusantara-gold/20\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `\n        bg-gray-900/50 backdrop-blur-sm rounded-lg border ${borderColors[border]}\n        ${hover ? \"hover:border-cyber-green hover:shadow-lg hover:shadow-cyber-green/20 transition-all duration-300 cursor-pointer\" : \"\"}\n        ${glow ? glowColors[border] : \"\"}\n        ${className}\n      `,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\nfunction StatCard({ title, value, icon: Icon, color = \"green\", trend, loading = false }) {\n    const colors = {\n        green: \"text-cyber-green\",\n        blue: \"text-cyber-blue\",\n        red: \"text-cyber-red\",\n        purple: \"text-cyber-purple\",\n        gold: \"text-nusantara-gold\"\n    };\n    const bgColors = {\n        green: \"bg-cyber-green/10\",\n        blue: \"bg-cyber-blue/10\",\n        red: \"bg-cyber-red/10\",\n        purple: \"bg-cyber-purple/10\",\n        gold: \"bg-nusantara-gold/10\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        border: color,\n        glow: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-400\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-baseline space-x-2\",\n                                children: [\n                                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 w-20 bg-gray-700 animate-pulse rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: `text-2xl font-bold ${colors[color]}`,\n                                        children: typeof value === \"number\" ? value.toLocaleString() : value\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, this),\n                                    trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `text-sm font-medium ${trend.isPositive ? \"text-green-400\" : \"text-red-400\"}`,\n                                        children: [\n                                            trend.isPositive ? \"+\" : \"\",\n                                            trend.value,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `p-3 rounded-lg ${bgColors[color]}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            className: `h-6 w-6 ${colors[color]}`\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\nfunction FeatureCard({ title, description, icon: Icon, color = \"green\", onClick, disabled = false, badge }) {\n    const colors = {\n        green: \"text-cyber-green\",\n        blue: \"text-cyber-blue\",\n        red: \"text-cyber-red\",\n        purple: \"text-cyber-purple\",\n        gold: \"text-nusantara-gold\"\n    };\n    const bgColors = {\n        green: \"bg-cyber-green/10\",\n        blue: \"bg-cyber-blue/10\",\n        red: \"bg-cyber-red/10\",\n        purple: \"bg-cyber-purple/10\",\n        gold: \"bg-nusantara-gold/10\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        hover: !disabled && !!onClick,\n        border: color,\n        className: `relative ${disabled ? \"opacity-50 cursor-not-allowed\" : \"\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 h-full\",\n            onClick: disabled ? undefined : onClick,\n            children: [\n                badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-3 right-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `px-2 py-1 text-xs font-semibold rounded-full ${bgColors[color]} ${colors[color]}`,\n                        children: badge\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `inline-flex p-3 rounded-lg ${bgColors[color]} mb-4`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: `h-6 w-6 ${colors[color]}`\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-white mb-2\",\n                    children: title\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-400 text-sm leading-relaxed\",\n                    children: description\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this),\n                onClick && !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 pt-4 border-t border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-medium ${colors[color]} hover:underline`,\n                        children: \"Mulai Sekarang →\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\nfunction LoadingCard({ className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 animate-pulse\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 bg-gray-700 rounded-lg\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-gray-700 rounded w-3/4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-3 bg-gray-700 rounded w-1/2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                lineNumber: 184,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 183,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 182,\n        columnNumber: 5\n    }, this);\n}\nfunction AlertCard({ type = \"info\", title, message, onClose }) {\n    const styles = {\n        info: {\n            border: \"border-cyber-blue\",\n            bg: \"bg-cyber-blue/10\",\n            text: \"text-cyber-blue\",\n            icon: \"\\uD83D\\uDCA1\"\n        },\n        success: {\n            border: \"border-cyber-green\",\n            bg: \"bg-cyber-green/10\",\n            text: \"text-cyber-green\",\n            icon: \"✅\"\n        },\n        warning: {\n            border: \"border-nusantara-gold\",\n            bg: \"bg-nusantara-gold/10\",\n            text: \"text-nusantara-gold\",\n            icon: \"⚠️\"\n        },\n        error: {\n            border: \"border-cyber-red\",\n            bg: \"bg-cyber-red/10\",\n            text: \"text-cyber-red\",\n            icon: \"❌\"\n        }\n    };\n    const style = styles[type];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `border ${style.border} ${style.bg} rounded-lg p-4`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-lg\",\n                    children: style.icon\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: `font-semibold ${style.text}`,\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300 text-sm mt-1\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, this),\n                onClose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onClose,\n                    className: \"text-gray-400 hover:text-white transition-colors\",\n                    children: \"\\xd7\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 238,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 237,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/DashboardLayout.tsx":
/*!****************************************!*\
  !*** ./components/DashboardLayout.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./components/Sidebar.tsx\");\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Navbar */ \"(ssr)/./components/Navbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction DashboardLayout({ children, user, title, showSearch = true }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-cyber flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                user: user\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-w-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        user: user,\n                        title: title,\n                        showSearch: showSearch\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 pt-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL0Rhc2hib2FyZExheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRytCO0FBQ0Y7QUFjZCxTQUFTRSxnQkFBZ0IsRUFDdENDLFFBQVEsRUFDUkMsSUFBSSxFQUNKQyxLQUFLLEVBQ0xDLGFBQWEsSUFBSSxFQUNJO0lBQ3JCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFFYiw4REFBQ1IsZ0RBQU9BO2dCQUFDSSxNQUFNQTs7Ozs7OzBCQUdmLDhEQUFDRztnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNQLCtDQUFNQTt3QkFBQ0csTUFBTUE7d0JBQU1DLE9BQU9BO3dCQUFPQyxZQUFZQTs7Ozs7O2tDQUc5Qyw4REFBQ0c7d0JBQUtELFdBQVU7a0NBQ2QsNEVBQUNEOzRCQUFJQyxXQUFVO3NDQUNaTDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNYiIsInNvdXJjZXMiOlsid2VicGFjazovL2tvZGV4Z3VhcmQvLi9jb21wb25lbnRzL0Rhc2hib2FyZExheW91dC50c3g/YzU2ZSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgU2lkZWJhciBmcm9tICcuL1NpZGViYXInXG5pbXBvcnQgTmF2YmFyIGZyb20gJy4vTmF2YmFyJ1xuXG5pbnRlcmZhY2UgRGFzaGJvYXJkTGF5b3V0UHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3ROb2RlXG4gIHVzZXI6IHtcbiAgICB1c2VybmFtZTogc3RyaW5nXG4gICAgYXZhdGFyPzogc3RyaW5nXG4gICAgcm9sZTogc3RyaW5nXG4gICAgcGxhbjogc3RyaW5nXG4gIH1cbiAgdGl0bGU/OiBzdHJpbmdcbiAgc2hvd1NlYXJjaD86IGJvb2xlYW5cbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRGFzaGJvYXJkTGF5b3V0KHtcbiAgY2hpbGRyZW4sXG4gIHVzZXIsXG4gIHRpdGxlLFxuICBzaG93U2VhcmNoID0gdHJ1ZVxufTogRGFzaGJvYXJkTGF5b3V0UHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmFkaWVudC1jeWJlciBmbGV4XCI+XG4gICAgICB7LyogU2lkZWJhciAqL31cbiAgICAgIDxTaWRlYmFyIHVzZXI9e3VzZXJ9IC8+XG5cbiAgICAgIHsvKiBNYWluIENvbnRlbnQgQXJlYSAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXggZmxleC1jb2wgbWluLXctMFwiPlxuICAgICAgICB7LyogVG9wIE5hdmJhciAqL31cbiAgICAgICAgPE5hdmJhciB1c2VyPXt1c2VyfSB0aXRsZT17dGl0bGV9IHNob3dTZWFyY2g9e3Nob3dTZWFyY2h9IC8+XG5cbiAgICAgICAgey8qIFBhZ2UgQ29udGVudCAqL31cbiAgICAgICAgPG1haW4gY2xhc3NOYW1lPVwiZmxleC0xIHB0LTE2XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOCBweS04XCI+XG4gICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvbWFpbj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiU2lkZWJhciIsIk5hdmJhciIsIkRhc2hib2FyZExheW91dCIsImNoaWxkcmVuIiwidXNlciIsInRpdGxlIiwic2hvd1NlYXJjaCIsImRpdiIsImNsYXNzTmFtZSIsIm1haW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Navbar.tsx":
/*!*******************************!*\
  !*** ./components/Navbar.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-search.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Navbar({ user }) {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isProfileOpen, setIsProfileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const navigation = [\n        {\n            name: \"Dashboard\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            name: \"OSINT\",\n            href: \"/osint\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            name: \"Scanner\",\n            href: \"/scanner\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            name: \"File Analyzer\",\n            href: \"/file-analyzer\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            name: \"CVE Intel\",\n            href: \"/cve\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            name: \"Playground\",\n            href: \"/playground\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            name: \"Bot Center\",\n            href: \"/bot\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            name: \"Leaderboard\",\n            href: \"/leaderboard\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        }\n    ];\n    const isActive = (href)=>pathname === href;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 w-full z-50 bg-cyber-dark/90 backdrop-blur-md border-b border-gray-800\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-8 w-8 text-cyber-green animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold font-cyber\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"cyber-text\",\n                                                children: \"Kode\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"nusantara-gold\",\n                                                children: \"X\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white\",\n                                                children: \"Guard\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-1\",\n                            children: user ? // Authenticated Navigation\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    navigation.map((item)=>{\n                                        const Icon = item.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: item.href,\n                                            className: `flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${isActive(item.href) ? \"bg-cyber-green/20 text-cyber-green border border-cyber-green/30\" : \"text-gray-300 hover:text-cyber-green hover:bg-gray-800/50\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, item.name, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 21\n                                        }, this);\n                                    }),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsProfileOpen(!isProfileOpen),\n                                                className: \"flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium text-gray-300 hover:text-cyber-green hover:bg-gray-800/50 transition-all duration-200\",\n                                                children: [\n                                                    user.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: user.avatar,\n                                                        alt: user.username,\n                                                        className: \"h-6 w-6 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: user.username\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs px-2 py-1 rounded-full bg-cyber-green/20 text-cyber-green\",\n                                                        children: user.plan.toUpperCase()\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 19\n                                            }, this),\n                                            isProfileOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-0 mt-2 w-48 bg-gray-900 border border-gray-700 rounded-lg shadow-lg py-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/profile\",\n                                                        className: \"flex items-center space-x-2 px-4 py-2 text-sm text-gray-300 hover:text-cyber-green hover:bg-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 110,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Profile\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 111,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/settings\",\n                                                        className: \"flex items-center space-x-2 px-4 py-2 text-sm text-gray-300 hover:text-cyber-green hover:bg-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 117,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Settings\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 118,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    user.role === \"super_admin\" || user.role === \"admin\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/admin\",\n                                                        className: \"flex items-center space-x-2 px-4 py-2 text-sm text-gray-300 hover:text-cyber-green hover:bg-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Admin Panel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 126,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 25\n                                                    }, this) : null,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                        className: \"my-1 border-gray-700\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{},\n                                                        className: \"flex items-center space-x-2 w-full px-4 py-2 text-sm text-red-400 hover:text-red-300 hover:bg-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 134,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Logout\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 135,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : // Guest Navigation\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/docs\",\n                                        className: \"text-gray-300 hover:text-cyber-green transition-colors\",\n                                        children: \"Dokumentasi\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/pricing\",\n                                        className: \"text-gray-300 hover:text-cyber-green transition-colors\",\n                                        children: \"Harga\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/login\",\n                                        className: \"cyber-btn\",\n                                        children: \"Masuk\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/register\",\n                                        className: \"cyber-btn-primary\",\n                                        children: \"Daftar\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsOpen(!isOpen),\n                                className: \"text-gray-300 hover:text-cyber-green p-2\",\n                                children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 25\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 53\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden bg-cyber-dark/95 backdrop-blur-md border-t border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-2 pt-2 pb-3 space-y-1\",\n                    children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            navigation.map((item)=>{\n                                const Icon = item.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: item.href,\n                                    className: `flex items-center space-x-3 px-3 py-2 rounded-lg text-base font-medium transition-all duration-200 ${isActive(item.href) ? \"bg-cyber-green/20 text-cyber-green border border-cyber-green/30\" : \"text-gray-300 hover:text-cyber-green hover:bg-gray-800/50\"}`,\n                                    onClick: ()=>setIsOpen(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 21\n                                }, this);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                className: \"my-2 border-gray-700\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/profile\",\n                                className: \"flex items-center space-x-3 px-3 py-2 rounded-lg text-base font-medium text-gray-300 hover:text-cyber-green hover:bg-gray-800/50\",\n                                onClick: ()=>setIsOpen(false),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Profile (\",\n                                            user.username,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{},\n                                className: \"flex items-center space-x-3 w-full px-3 py-2 rounded-lg text-base font-medium text-red-400 hover:text-red-300 hover:bg-gray-800/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Logout\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/docs\",\n                                className: \"block px-3 py-2 rounded-lg text-base font-medium text-gray-300 hover:text-cyber-green hover:bg-gray-800/50\",\n                                onClick: ()=>setIsOpen(false),\n                                children: \"Dokumentasi\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/pricing\",\n                                className: \"block px-3 py-2 rounded-lg text-base font-medium text-gray-300 hover:text-cyber-green hover:bg-gray-800/50\",\n                                onClick: ()=>setIsOpen(false),\n                                children: \"Harga\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/login\",\n                                className: \"block px-3 py-2 rounded-lg text-base font-medium cyber-btn text-center\",\n                                onClick: ()=>setIsOpen(false),\n                                children: \"Masuk\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/register\",\n                                className: \"block px-3 py-2 rounded-lg text-base font-medium cyber-btn-primary text-center\",\n                                onClick: ()=>setIsOpen(false),\n                                children: \"Daftar\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                lineNumber: 174,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Sidebar.tsx":
/*!********************************!*\
  !*** ./components/Sidebar.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-search.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Sidebar({ user }) {\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobileOpen, setIsMobileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const menuItems = [\n        {\n            id: \"dashboard\",\n            name: \"Dashboard\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            id: \"osint\",\n            name: \"OSINT Investigator\",\n            href: \"/osint\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            badge: \"HOT\"\n        },\n        {\n            id: \"scanner\",\n            name: \"Vulnerability Scanner\",\n            href: \"/scanner\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            premium: true\n        },\n        {\n            id: \"file-analyzer\",\n            name: \"File Analyzer\",\n            href: \"/file-analyzer\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            id: \"cve\",\n            name: \"CVE Intelligence\",\n            href: \"/cve\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            badge: \"NEW\"\n        },\n        {\n            id: \"dorking\",\n            name: \"Google Dorking\",\n            href: \"/dorking\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            id: \"tools\",\n            name: \"Security Tools\",\n            href: \"/tools\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            id: \"bot\",\n            name: \"Bot Center\",\n            href: \"/bot\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            premium: true,\n            adminOnly: true\n        },\n        {\n            id: \"playground\",\n            name: \"API Playground\",\n            href: \"/playground\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            premium: true\n        },\n        {\n            id: \"leaderboard\",\n            name: \"Leaderboard\",\n            href: \"/leaderboard\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            id: \"profile\",\n            name: \"Profile & API Keys\",\n            href: \"/profile\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n        },\n        {\n            id: \"plan\",\n            name: \"Plans & Billing\",\n            href: \"/plan\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n        },\n        {\n            id: \"docs\",\n            name: \"Documentation\",\n            href: \"/docs\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n        }\n    ];\n    // Filter menu items based on user role and plan\n    const filteredMenuItems = menuItems.filter((item)=>{\n        if (item.adminOnly && ![\n            \"super_admin\",\n            \"admin\"\n        ].includes(user.role)) {\n            return false;\n        }\n        if (item.premium && user.plan === \"gratis\") {\n            return false;\n        }\n        return true;\n    });\n    const getPlanColor = (plan)=>{\n        switch(plan){\n            case \"cybersecurity\":\n                return \"text-red-400 bg-red-900/20\";\n            case \"bughunter\":\n                return \"text-purple-400 bg-purple-900/20\";\n            case \"hobby\":\n                return \"text-green-400 bg-green-900/20\";\n            case \"pelajar\":\n                return \"text-blue-400 bg-blue-900/20\";\n            default:\n                return \"text-gray-400 bg-gray-900/20\";\n        }\n    };\n    const getPlanIcon = (plan)=>{\n        switch(plan){\n            case \"cybersecurity\":\n                return _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"];\n            case \"bughunter\":\n                return _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"];\n            case \"hobby\":\n                return _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"];\n            default:\n                return _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"];\n        }\n    };\n    // Close mobile sidebar when route changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsMobileOpen(false);\n    }, [\n        pathname\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isMobileOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 z-40 lg:hidden\",\n                onClick: ()=>setIsMobileOpen(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 182,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsMobileOpen(true),\n                className: \"fixed top-4 left-4 z-50 lg:hidden bg-gray-800 text-white p-2 rounded-lg border border-gray-700 hover:bg-gray-700 transition-colors\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `\n        fixed lg:relative top-0 left-0 h-full lg:h-screen bg-gray-900/95 backdrop-blur-sm border-r border-gray-800 z-50 lg:z-auto\n        transition-all duration-300 ease-in-out flex-shrink-0\n        ${isCollapsed ? \"w-16\" : \"w-64\"}\n        ${isMobileOpen ? \"translate-x-0\" : \"-translate-x-full lg:translate-x-0\"}\n      `,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 border-b border-gray-800\",\n                        children: [\n                            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-cyber-green rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-5 w-5 text-black\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-bold text-white font-cyber\",\n                                        children: \"KodeXGuard\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsMobileOpen(false),\n                                        className: \"lg:hidden text-gray-400 hover:text-white transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsCollapsed(!isCollapsed),\n                                        className: \"hidden lg:block text-gray-400 hover:text-white transition-colors\",\n                                        children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-800\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                user.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: user.avatar,\n                                    alt: user.username,\n                                    className: \"w-10 h-10 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 rounded-full bg-cyber-green/20 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-cyber-green font-bold\",\n                                        children: user.username.charAt(0).toUpperCase()\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 15\n                                }, this),\n                                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium text-white truncate\",\n                                            children: user.username\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1\",\n                                            children: [\n                                                (()=>{\n                                                    const PlanIcon = getPlanIcon(user.plan);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlanIcon, {\n                                                        className: \"h-3 w-3 text-nusantara-gold\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 28\n                                                    }, this);\n                                                })(),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `text-xs px-2 py-1 rounded-full font-semibold ${getPlanColor(user.plan)}`,\n                                                    children: user.plan.toUpperCase()\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex-1 overflow-y-auto py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1 px-2\",\n                            children: filteredMenuItems.map((item)=>{\n                                const Icon = item.icon;\n                                const isActive = pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: item.href,\n                                    className: `\n                    group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200\n                    ${isActive ? \"bg-cyber-green text-black\" : \"text-gray-300 hover:text-white hover:bg-gray-800/50\"}\n                    ${isCollapsed ? \"justify-center\" : \"justify-start\"}\n                  `,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: `h-5 w-5 flex-shrink-0 ${isCollapsed ? \"\" : \"mr-3\"}`\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 19\n                                        }, this),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex-1\",\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1\",\n                                                    children: [\n                                                        item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: `\n                            px-2 py-1 text-xs font-bold rounded-full\n                            ${item.badge === \"HOT\" ? \"bg-red-500 text-white\" : \"bg-blue-500 text-white\"}\n                          `,\n                                                            children: item.badge\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        item.premium && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-3 w-3 text-nusantara-gold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        item.adminOnly && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"h-3 w-3 text-orange-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true),\n                                        isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute left-16 bg-gray-800 text-white px-2 py-1 rounded-lg text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50\",\n                                            children: [\n                                                item.name,\n                                                item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `ml-2 px-1 py-0.5 text-xs rounded ${item.badge === \"HOT\" ? \"bg-red-500\" : \"bg-blue-500\"}`,\n                                                    children: item.badge\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, item.id, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-gray-800 p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: `\n              w-full flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors\n              ${isCollapsed ? \"justify-center\" : \"justify-start\"}\n            `,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: `h-5 w-5 ${isCollapsed ? \"\" : \"mr-3\"}`\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Notifications\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 32\n                                        }, this),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full\",\n                                            children: \"3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: `\n              w-full flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors\n              ${isCollapsed ? \"justify-center\" : \"justify-start\"}\n            `,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: `h-5 w-5 ${isCollapsed ? \"\" : \"mr-3\"}`\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Settings\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 32\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: `\n              w-full flex items-center px-3 py-2 text-sm font-medium text-red-400 hover:text-red-300 hover:bg-red-900/20 rounded-lg transition-colors\n              ${isCollapsed ? \"justify-center\" : \"justify-start\"}\n            `,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            className: `h-5 w-5 ${isCollapsed ? \"\" : \"mr-3\"}`\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Logout\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 32\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Sidebar.tsx\n");

/***/ }),

/***/ "(rsc)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7cffc49d1683\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb2RleGd1YXJkLy4vc3R5bGVzL2dsb2JhbHMuY3NzPzZkZDQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3Y2ZmYzQ5ZDE2ODNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(rsc)/./styles/globals.css\");\n\n\n\nconst metadata = {\n    title: \"KodeXGuard - Platform Cybersecurity & Bug Hunting\",\n    description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting dengan dukungan Bot WhatsApp/Telegram\",\n    keywords: \"cybersecurity, bug hunting, OSINT, vulnerability scanner, CVE, penetration testing, security tools\",\n    authors: [\n        {\n            name: \"KodeXGuard Team\"\n        }\n    ],\n    creator: \"KodeXGuard\",\n    publisher: \"KodeXGuard\",\n    robots: \"index, follow\",\n    openGraph: {\n        type: \"website\",\n        locale: \"id_ID\",\n        url: \"https://kodexguard.com\",\n        title: \"KodeXGuard - Platform Cybersecurity & Bug Hunting\",\n        description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting\",\n        siteName: \"KodeXGuard\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"KodeXGuard - Platform Cybersecurity & Bug Hunting\",\n        description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting\"\n    },\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#00ff41\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"id\",\n        className: \"dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} min-h-screen bg-gradient-cyber text-white antialiased`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"matrix-bg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-b from-transparent via-cyber-dark/20 to-cyber-dark\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n              // Matrix Rain Effect\n              function createMatrixRain() {\n                const canvas = document.createElement('canvas');\n                const ctx = canvas.getContext('2d');\n                canvas.style.position = 'fixed';\n                canvas.style.top = '0';\n                canvas.style.left = '0';\n                canvas.style.width = '100%';\n                canvas.style.height = '100%';\n                canvas.style.pointerEvents = 'none';\n                canvas.style.zIndex = '-1';\n                canvas.style.opacity = '0.1';\n                document.body.appendChild(canvas);\n                \n                canvas.width = window.innerWidth;\n                canvas.height = window.innerHeight;\n                \n                const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';\n                const charArray = chars.split('');\n                const fontSize = 14;\n                const columns = canvas.width / fontSize;\n                const drops = [];\n                \n                for (let i = 0; i < columns; i++) {\n                  drops[i] = 1;\n                }\n                \n                function draw() {\n                  ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';\n                  ctx.fillRect(0, 0, canvas.width, canvas.height);\n                  \n                  ctx.fillStyle = '#00ff41';\n                  ctx.font = fontSize + 'px monospace';\n                  \n                  for (let i = 0; i < drops.length; i++) {\n                    const text = charArray[Math.floor(Math.random() * charArray.length)];\n                    ctx.fillText(text, i * fontSize, drops[i] * fontSize);\n                    \n                    if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {\n                      drops[i] = 0;\n                    }\n                    drops[i]++;\n                  }\n                }\n                \n                setInterval(draw, 50);\n                \n                window.addEventListener('resize', () => {\n                  canvas.width = window.innerWidth;\n                  canvas.height = window.innerHeight;\n                });\n              }\n              \n              // Initialize matrix effect after page load\n              if (document.readyState === 'loading') {\n                document.addEventListener('DOMContentLoaded', createMatrixRain);\n              } else {\n                createMatrixRain();\n              }\n            `\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/playground/page.tsx":
/*!*********************************!*\
  !*** ./app/playground/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\playground\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fplayground%2Fpage&page=%2Fplayground%2Fpage&appPaths=%2Fplayground%2Fpage&pagePath=private-next-app-dir%2Fplayground%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();