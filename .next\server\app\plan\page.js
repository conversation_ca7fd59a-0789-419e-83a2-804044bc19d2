(()=>{var e={};e.id=1927,e.ids=[1927],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},95614:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>s.a,__next_app__:()=>h,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),n(10259),n(30829),n(35866);var r=n(23191),a=n(88716),i=n(37922),s=n.n(i),o=n(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);n.d(t,l);let d=["",{children:["plan",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.t.bind(n,10259,23)),"D:\\Users\\Downloads\\kodeXGuard\\app\\plan\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,35866,23)),"next/dist/client/components/not-found-error"]}],c=["D:\\Users\\Downloads\\kodeXGuard\\app\\plan\\page.tsx"],u="/plan/page",h={require:n,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/plan/page",pathname:"/plan",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},60546:(e,t,n)=>{Promise.resolve().then(n.bind(n,67382))},27807:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,12994,23)),Promise.resolve().then(n.t.bind(n,96114,23)),Promise.resolve().then(n.t.bind(n,9727,23)),Promise.resolve().then(n.t.bind(n,79671,23)),Promise.resolve().then(n.t.bind(n,41868,23)),Promise.resolve().then(n.t.bind(n,84759,23))},35303:()=>{},67382:(e,t,n)=>{"use strict";n.d(t,{AuthProvider:()=>s,a:()=>o});var r=n(10326),a=n(17577);let i=(0,a.createContext)(void 0);function s({children:e}){let[t,n]=(0,a.useState)(null),[s,o]=(0,a.useState)(null),[l,d]=(0,a.useState)(!0),c=async(e,t)=>{try{d(!0);let r=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})}),a=await r.json();if(a.success&&a.token)return o(a.token),n(a.user),!0;return!1}catch(e){return console.error("Login error:",e),!1}finally{d(!1)}},u=async()=>{try{let e=await fetch("/api/auth/refresh",{method:"POST",headers:{Authorization:`Bearer ${s}`}}),t=await e.json();t.success&&t.token&&o(t.token)}catch(e){console.error("Token refresh error:",e),h()}},h=()=>{n(null),o(null)};return r.jsx(i.Provider,{value:{user:t,token:s,login:c,logout:h,isLoading:l,isAuthenticated:!!t&&!!s,refreshToken:u},children:e})}function o(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},30829:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>d,metadata:()=>l});var r=n(19510),a=n(77366),i=n.n(a);n(7633);var s=n(68570);let o=(0,s.createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\contexts\AuthContext.tsx#AuthProvider`);(0,s.createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\contexts\AuthContext.tsx#useAuth`);let l={title:"KodeXGuard - Platform Cybersecurity & Bug Hunting",description:"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting dengan dukungan Bot WhatsApp/Telegram",keywords:"cybersecurity, bug hunting, OSINT, vulnerability scanner, CVE, penetration testing, security tools",authors:[{name:"KodeXGuard Team"}],creator:"KodeXGuard",publisher:"KodeXGuard",robots:"index, follow",openGraph:{type:"website",locale:"id_ID",url:"https://kodexguard.com",title:"KodeXGuard - Platform Cybersecurity & Bug Hunting",description:"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting",siteName:"KodeXGuard"},twitter:{card:"summary_large_image",title:"KodeXGuard - Platform Cybersecurity & Bug Hunting",description:"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting"},viewport:"width=device-width, initial-scale=1",themeColor:"#00ff41"};function d({children:e}){return(0,r.jsxs)("html",{lang:"id",className:"dark",children:[(0,r.jsxs)("head",{children:[r.jsx("link",{rel:"icon",href:"/favicon.ico"}),r.jsx("link",{rel:"apple-touch-icon",href:"/apple-touch-icon.png"}),r.jsx("link",{rel:"manifest",href:"/manifest.json"})]}),(0,r.jsxs)("body",{className:`${i().className} min-h-screen bg-gradient-cyber text-white antialiased`,children:[r.jsx("div",{className:"matrix-bg",children:r.jsx("div",{className:"absolute inset-0 bg-gradient-to-b from-transparent via-cyber-dark/20 to-cyber-dark"})}),r.jsx("div",{className:"relative z-10",children:r.jsx(o,{children:e})}),r.jsx("script",{dangerouslySetInnerHTML:{__html:`
              // Matrix Rain Effect
              function createMatrixRain() {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.style.position = 'fixed';
                canvas.style.top = '0';
                canvas.style.left = '0';
                canvas.style.width = '100%';
                canvas.style.height = '100%';
                canvas.style.pointerEvents = 'none';
                canvas.style.zIndex = '-1';
                canvas.style.opacity = '0.1';
                document.body.appendChild(canvas);
                
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
                
                const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';
                const charArray = chars.split('');
                const fontSize = 14;
                const columns = canvas.width / fontSize;
                const drops = [];
                
                for (let i = 0; i < columns; i++) {
                  drops[i] = 1;
                }
                
                function draw() {
                  ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
                  ctx.fillRect(0, 0, canvas.width, canvas.height);
                  
                  ctx.fillStyle = '#00ff41';
                  ctx.font = fontSize + 'px monospace';
                  
                  for (let i = 0; i < drops.length; i++) {
                    const text = charArray[Math.floor(Math.random() * charArray.length)];
                    ctx.fillText(text, i * fontSize, drops[i] * fontSize);
                    
                    if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
                      drops[i] = 0;
                    }
                    drops[i]++;
                  }
                }
                
                setInterval(draw, 50);
                
                window.addEventListener('resize', () => {
                  canvas.width = window.innerWidth;
                  canvas.height = window.innerHeight;
                });
              }
              
              // Initialize matrix effect after page load
              if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', createMatrixRain);
              } else {
                createMatrixRain();
              }
            `}})]})]})}},10259:()=>{throw Error('Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError: \n  \x1b[31mx\x1b[0m Unexpected token `DashboardLayout`. Expected jsx identifier\n     ,-[\x1b[36;1;4mD:\\Users\\Downloads\\kodeXGuard\\app\\plan\\page.tsx\x1b[0m:264:1]\n \x1b[2m264\x1b[0m |   }\n \x1b[2m265\x1b[0m | \n \x1b[2m266\x1b[0m |   return (\n \x1b[2m267\x1b[0m |     <DashboardLayout user={user} title="Plans & Billing">\n     : \x1b[31;1m     ^^^^^^^^^^^^^^^\x1b[0m\n \x1b[2m268\x1b[0m |       <div>\n \x1b[2m269\x1b[0m |           {/* Header */}\n \x1b[2m270\x1b[0m |           <div className="mb-8 text-center">\n     `----\n\n\nCaused by:\n    Syntax Error')},7633:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[9276,82],()=>n(95614));module.exports=r})();