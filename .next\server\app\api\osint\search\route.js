"use strict";(()=>{var e={};e.id=944,e.ids=[944],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},96119:e=>{e.exports=require("perf_hooks")},35816:e=>{e.exports=require("process")},76162:e=>{e.exports=require("stream")},74026:e=>{e.exports=require("string_decoder")},95346:e=>{e.exports=require("timers")},82452:e=>{e.exports=require("tls")},74175:e=>{e.exports=require("tty")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},98061:e=>{e.exports=require("node:assert")},92761:e=>{e.exports=require("node:async_hooks")},72254:e=>{e.exports=require("node:buffer")},40027:e=>{e.exports=require("node:console")},6005:e=>{e.exports=require("node:crypto")},65714:e=>{e.exports=require("node:diagnostics_channel")},30604:e=>{e.exports=require("node:dns")},15673:e=>{e.exports=require("node:events")},88849:e=>{e.exports=require("node:http")},42725:e=>{e.exports=require("node:http2")},22286:e=>{e.exports=require("node:https")},87503:e=>{e.exports=require("node:net")},70612:e=>{e.exports=require("node:os")},38846:e=>{e.exports=require("node:perf_hooks")},97742:e=>{e.exports=require("node:process")},39630:e=>{e.exports=require("node:querystring")},55467:e=>{e.exports=require("node:sqlite")},84492:e=>{e.exports=require("node:stream")},99397:e=>{e.exports=require("node:timers/promises")},31764:e=>{e.exports=require("node:tls")},41041:e=>{e.exports=require("node:url")},47261:e=>{e.exports=require("node:util")},93746:e=>{e.exports=require("node:util/types")},24086:e=>{e.exports=require("node:worker_threads")},65628:e=>{e.exports=require("node:zlib")},67238:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>S,patchFetch:()=>q,requestAsyncStorage:()=>f,routeModule:()=>m,serverHooks:()=>y,staticGenerationAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>h,POST:()=>p});var a=t(49303),o=t(88716),n=t(60670),i=t(87070),c=t(29712),u=t(24544);class l{constructor(){this.dataSources=new Map,this.initializeDataSources()}initializeDataSources(){this.dataSources.set("dukcapil",{name:"Dukcapil Database",type:"government",enabled:!0,confidence:95,searchFunction:this.searchDukcapil.bind(this)}),this.dataSources.set("kemkes",{name:"Kemkes Database",type:"government",enabled:!0,confidence:90,searchFunction:this.searchKemkes.bind(this)}),this.dataSources.set("github",{name:"GitHub Leaked Data",type:"public",enabled:!0,confidence:80,searchFunction:this.searchGitHub.bind(this)}),this.dataSources.set("social",{name:"Social Media OSINT",type:"social",enabled:!0,confidence:70,searchFunction:this.searchSocialMedia.bind(this)}),this.dataSources.set("domain",{name:"Domain Intelligence",type:"technical",enabled:!0,confidence:85,searchFunction:this.searchDomain.bind(this)}),this.dataSources.set("phone",{name:"Phone Intelligence",type:"telecom",enabled:!0,confidence:75,searchFunction:this.searchPhone.bind(this)})}async search(e){let r=Date.now();try{if(!e.query||!e.type)return{success:!1,results:[],sources:[],confidenceScore:0,searchTime:0,totalResults:0,error:"Invalid search parameters"};let t=this.getRelevantSources(e.type,e.sources).map(async r=>{try{let t=await r.searchFunction(e.query);return{source:r.name,confidence:r.confidence,data:t,success:!0}}catch(e){return console.error(`Search failed for ${r.name}:`,e),{source:r.name,confidence:0,data:null,success:!1,error:e instanceof Error?e.message:"Unknown error"}}}),s=await Promise.allSettled(t),a=[],o=[],n=0,i=0;s.forEach((r,t)=>{if("fulfilled"===r.status&&r.value.success&&r.value.data){let t=r.value;a.push({id:0,userId:e.userId,searchType:e.type,searchQuery:e.query,results:t.data,sources:[t.source],confidenceScore:t.confidence,createdAt:new Date}),o.push(t.source),n+=t.confidence,i++}});let c=i>0?n/i:0,u=Date.now()-r;return a.length>0&&await this.saveSearchResults(e,a),{success:!0,results:a,sources:o,confidenceScore:Math.round(c),searchTime:u,totalResults:a.length}}catch(e){return console.error("OSINT search error:",e),{success:!1,results:[],sources:[],confidenceScore:0,searchTime:Date.now()-r,totalResults:0,error:e instanceof Error?e.message:"Search failed"}}}getRelevantSources(e,r){let t=Array.from(this.dataSources.values()).filter(e=>e.enabled);if(r&&r.length>0)return t.filter(e=>r.some(r=>e.name.toLowerCase().includes(r.toLowerCase())));switch(e){case"nik":case"npwp":case"name":return t.filter(e=>["government","public"].includes(e.type));case"email":return t.filter(e=>["public","social"].includes(e.type));case"phone":case"imei":return t.filter(e=>["telecom","government"].includes(e.type));case"domain":return t.filter(e=>"technical"===e.type);default:return t}}async searchDukcapil(e){await new Promise(e=>setTimeout(e,1e3+2e3*Math.random()));let r={found:Math.random()>.3,data:{name:"John Doe",nik:"1234567890123456",birthDate:"1990-01-01",address:"Jakarta, Indonesia",verified:!0,lastUpdated:new Date().toISOString()}};return r.found?r.data:null}async searchKemkes(e){await new Promise(e=>setTimeout(e,800+1500*Math.random()));let r={found:Math.random()>.4,data:{name:"John Doe",healthId:"H123456789",vaccineStatus:"Complete",lastVisit:"2024-01-15",verified:!0}};return r.found?r.data:null}async searchGitHub(e){try{let r=await c.Z.get("https://api.github.com/search/code",{params:{q:`"${e}" filename:*.txt OR filename:*.csv OR filename:*.json`,sort:"indexed",order:"desc"},headers:{Accept:"application/vnd.github.v3+json","User-Agent":"KodeXGuard-OSINT"},timeout:1e4});return r.data.items?.slice(0,5)||null}catch(e){return console.error("GitHub search error:",e),null}}async searchSocialMedia(e){await new Promise(e=>setTimeout(e,1500+2e3*Math.random()));let r=["Facebook","Twitter","Instagram","LinkedIn"].map(e=>({platform:e,profiles:Math.floor(5*Math.random()),lastActivity:new Date(Date.now()-2592e6*Math.random()).toISOString()})).filter(e=>e.profiles>0);return r.length>0?r:null}async searchDomain(e){try{let r=e.replace(/^https?:\/\//,"").split("/")[0];return await new Promise(e=>setTimeout(e,1e3)),{domain:r,registrar:"Example Registrar",createdDate:"2020-01-01",expiryDate:"2025-01-01",nameservers:["ns1.example.com","ns2.example.com"],status:"Active"}}catch(e){return null}}async searchPhone(e){return await new Promise(e=>setTimeout(e,1200)),Math.random()>.2?{number:e,country:"Indonesia",carrier:"Telkomsel",type:"Mobile",location:"Jakarta",verified:!0}:null}async saveSearchResults(e,r){try{for(let t of r)await u.db.query(`INSERT INTO osint_results (user_id, search_type, search_query, results, sources, confidence_score, created_at)
           VALUES (?, ?, ?, ?, ?, ?, NOW())`,[e.userId,e.type,e.query,JSON.stringify(t.results),JSON.stringify(t.sources),t.confidenceScore])}catch(e){console.error("Failed to save OSINT results:",e)}}async getSearchHistory(e,r=20,t=0){try{return await u.db.query(`SELECT * FROM osint_results 
         WHERE user_id = ? 
         ORDER BY created_at DESC 
         LIMIT ? OFFSET ?`,[e,r,t])}catch(e){return console.error("Failed to get OSINT history:",e),[]}}async trackLocation(e,r){try{return await new Promise(e=>setTimeout(e,2e3)),{target:e,type:r,location:{latitude:-6.2088+(Math.random()-.5)*.1,longitude:106.8456+(Math.random()-.5)*.1,accuracy:Math.floor(1e3*Math.random())+100,address:"Jakarta, Indonesia",timestamp:new Date().toISOString()},confidence:Math.floor(40*Math.random())+60}}catch(e){return console.error("Location tracking error:",e),null}}}let d=new l;async function p(e){try{let r=e.headers.get("authorization"),t=r?.replace("Bearer ","");if(!t)return i.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!t.startsWith("kxg_"))return i.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let{query:s,type:a,deepSearch:o=!1,sources:n=[]}=await e.json();if(!s||!a)return i.NextResponse.json({success:!1,error:"Query and type are required",code:"MISSING_PARAMETERS"},{status:400});if(!["email","phone","nik","npwp","name","domain","imei","address"].includes(a))return i.NextResponse.json({success:!1,error:"Invalid search type",code:"INVALID_TYPE"},{status:400});let c=await d.search({query:s,type:a,deepSearch:o,sources:n,userId:1});return i.NextResponse.json({success:!0,data:{searchId:`osint_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,query:s,type:a,results:c.results,sources:c.sources,confidenceScore:c.confidenceScore,searchTime:c.searchTime,totalResults:c.totalResults,quotaUsed:1,quotaLimit:100},message:`Found ${c.totalResults} results`,timestamp:new Date().toISOString()})}catch(e){return console.error("OSINT search error:",e),i.NextResponse.json({success:!1,error:"Internal server error",code:"INTERNAL_ERROR"},{status:500})}}async function h(e){try{let r=e.headers.get("authorization"),t=r?.replace("Bearer ","");if(!t)return i.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!t.startsWith("kxg_"))return i.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let{searchParams:s}=new URL(e.url),a=parseInt(s.get("page")||"1"),o=parseInt(s.get("limit")||"20"),n=[{id:1,searchType:"email",searchQuery:"<EMAIL>",results:{found:!0},sources:["Dukcapil","GitHub"],confidenceScore:85,createdAt:new Date().toISOString()},{id:2,searchType:"phone",searchQuery:"+62812345678",results:{found:!0},sources:["Telecom","Social Media"],confidenceScore:92,createdAt:new Date(Date.now()-36e5).toISOString()}];return i.NextResponse.json({success:!0,data:n,pagination:{page:a,limit:o,total:n.length,totalPages:Math.ceil(n.length/o)},timestamp:new Date().toISOString()})}catch(e){return console.error("OSINT history error:",e),i.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let m=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/osint/search/route",pathname:"/api/osint/search",filename:"route",bundlePath:"app/api/osint/search/route"},resolvedPagePath:"D:\\Users\\Downloads\\kodeXGuard\\app\\api\\osint\\search\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:f,staticGenerationAsyncStorage:x,serverHooks:y}=m,S="/api/osint/search/route";function q(){return(0,n.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:x})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[216,592],()=>t(67238));module.exports=s})();