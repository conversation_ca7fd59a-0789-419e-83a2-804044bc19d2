"use strict";exports.id=6361,exports.ids=[6361],exports.modules={16361:(e,t,a)=>{a.r(t),a.d(t,{default:()=>N});var s=a(10326),r=a(17577),l=a(90434),c=a(35047),i=a(54659),n=a(37202),d=a(91470),o=a(18019),x=a(58038),h=a(88307),m=a(54014),y=a(6507),u=a(26092),g=a(79635),p=a(88378),f=a(71810),b=a(94019),j=a(90748);function N({user:e,showSearch:t=!0,title:a,isLandingPage:N=!1}){let[v,k]=(0,r.useState)(!1),[w,Z]=(0,r.useState)(!1),[C,z]=(0,r.useState)(!1),[M,S]=(0,r.useState)([]),[P,A]=(0,r.useState)(0);(0,c.usePathname)();let q=(0,c.useRouter)(),I=async e=>{try{let t=localStorage.getItem("auth-token");if(!t)return;await fetch(`/api/notifications/${e}/read`,{method:"POST",headers:{Authorization:`Bearer ${t}`}}),S(t=>t.map(t=>t.id===e?{...t,read:!0}:t))}catch(t){console.error("Failed to mark notification as read:",t),S(t=>t.map(t=>t.id===e?{...t,read:!0}:t))}},L=e=>{switch(e){case"success":return i.Z;case"warning":return n.Z;case"error":return d.Z;default:return o.Z}},$=e=>{switch(e){case"success":return"text-green-500";case"warning":return"text-yellow-500";case"error":return"text-red-500";default:return"text-blue-500"}},R=[{name:"Home",href:"/"},{name:"Features",href:"/#features"},{name:"Pricing",href:"/#pricing"},{name:"About",href:"/#about"},{name:"Contact",href:"/#contact"}];return(0,s.jsxs)("nav",{className:`fixed top-0 left-0 right-0 z-40 bg-gray-900/95 backdrop-blur-sm border-b border-gray-800 ${N?"lg:px-8":""}`,children:[(0,s.jsxs)("div",{className:`flex justify-between items-center h-16 ${N?"max-w-7xl mx-auto px-4 sm:px-6":"px-4 lg:pl-0"}`,children:[(0,s.jsxs)("div",{className:"flex items-center space-x-8",children:[!N&&s.jsx("div",{className:"w-12 lg:hidden"}),(0,s.jsxs)(l.default,{href:"/",className:"flex items-center space-x-2",children:[s.jsx("div",{className:"w-8 h-8 bg-cyber-green rounded-lg flex items-center justify-center",children:s.jsx(x.Z,{className:"h-5 w-5 text-black"})}),s.jsx("span",{className:"font-bold text-white text-xl font-cyber",children:"KodeXGuard"})]}),N&&s.jsx("div",{className:"hidden lg:flex items-center space-x-8",children:R.map(e=>s.jsx(l.default,{href:e.href,className:"text-gray-300 hover:text-cyber-green transition-colors duration-200 font-medium",children:e.name},e.name))}),!N&&a&&s.jsx("h1",{className:"text-xl font-bold text-white font-cyber hidden lg:block",children:a})]}),!N&&t&&s.jsx("div",{className:"hidden lg:flex flex-1 max-w-lg mx-8",children:(0,s.jsxs)("div",{className:"relative w-full",children:[s.jsx(h.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),s.jsx("input",{type:"text",placeholder:"Search across all tools...",className:"w-full pl-10 pr-4 py-2 bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyber-green focus:border-transparent"})]})}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[N&&(0,s.jsxs)("button",{className:"hidden lg:flex items-center space-x-1 text-gray-300 hover:text-white transition-colors",children:[s.jsx(m.Z,{className:"h-4 w-4"}),s.jsx("span",{className:"text-sm",children:"ID"})]}),!N&&t&&s.jsx("button",{className:"lg:hidden text-gray-400 hover:text-white transition-colors",children:s.jsx(h.Z,{className:"h-5 w-5"})}),!N&&(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("button",{onClick:()=>z(!C),className:"relative text-gray-400 hover:text-white transition-colors",children:[s.jsx(y.Z,{className:"h-5 w-5"}),P>0&&s.jsx("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center",children:P>9?"9+":P})]}),C&&(0,s.jsxs)("div",{className:"absolute right-0 mt-2 w-80 bg-gray-800 border border-gray-700 rounded-lg shadow-xl z-50 max-h-96 overflow-hidden",children:[(0,s.jsxs)("div",{className:"p-3 border-b border-gray-700 flex items-center justify-between",children:[s.jsx("h3",{className:"text-sm font-semibold text-white",children:"Notifications"}),s.jsx(l.default,{href:"/notifications",className:"text-xs text-cyber-green hover:text-cyber-green/80 transition-colors",onClick:()=>z(!1),children:"View All"})]}),s.jsx("div",{className:"max-h-80 overflow-y-auto",children:0===M.length?s.jsx("div",{className:"p-4 text-center text-gray-400 text-sm",children:"No notifications"}):M.map(e=>{let t=L(e.type);return s.jsx("div",{className:`p-3 border-b border-gray-700 last:border-b-0 hover:bg-gray-700/50 transition-colors cursor-pointer ${e.read?"":"bg-gray-700/20"}`,onClick:()=>{e.read||I(e.id),e.action&&(q.push(e.action.url),z(!1))},children:(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[s.jsx(t,{className:`flex-shrink-0 h-4 w-4 mt-0.5 ${$(e.type)}`}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[s.jsx("h4",{className:`text-sm font-medium ${e.read?"text-gray-300":"text-white"}`,children:e.title}),s.jsx("span",{className:"text-xs text-gray-400",children:new Date(e.timestamp).toLocaleTimeString("id-ID",{hour:"2-digit",minute:"2-digit"})})]}),s.jsx("p",{className:"text-xs text-gray-400 mt-1 line-clamp-2",children:e.message}),e.action&&(0,s.jsxs)("span",{className:"inline-block mt-2 text-xs text-cyber-green",children:[e.action.label," →"]})]})]})},e.id)})})]})]}),e?(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("button",{onClick:()=>Z(!w),className:"flex items-center space-x-2 text-gray-300 hover:text-white transition-colors",children:[e.avatar?s.jsx("img",{src:e.avatar,alt:e.username,className:"h-8 w-8 rounded-full"}):s.jsx("div",{className:"h-8 w-8 bg-cyber-green rounded-full flex items-center justify-center",children:s.jsx("span",{className:"text-black font-semibold text-sm",children:e.username.charAt(0).toUpperCase()})}),s.jsx("span",{className:"hidden sm:block font-medium",children:e.username}),("cybersecurity"===e.plan||"bughunter"===e.plan)&&s.jsx(u.Z,{className:"h-4 w-4 text-nusantara-gold"})]}),w&&(0,s.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-gray-800 rounded-lg shadow-lg border border-gray-700 py-1 z-50",children:[(0,s.jsxs)("div",{className:"px-4 py-2 border-b border-gray-700",children:[s.jsx("p",{className:"text-sm font-medium text-white",children:e.username}),s.jsx("p",{className:"text-xs text-gray-400",children:e.role?.replace("_"," ").toUpperCase()||"User"}),(0,s.jsxs)("p",{className:"text-xs text-cyber-green",children:[e.plan?.toUpperCase()," Plan"]})]}),(0,s.jsxs)(l.default,{href:"/profile",className:"flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white",onClick:()=>Z(!1),children:[s.jsx(g.Z,{className:"h-4 w-4 mr-2"}),"Profile & API Keys"]}),(0,s.jsxs)(l.default,{href:"/settings",className:"flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white",onClick:()=>Z(!1),children:[s.jsx(p.Z,{className:"h-4 w-4 mr-2"}),"Settings"]}),("super_admin"===e.role||"admin"===e.role)&&(0,s.jsxs)(l.default,{href:"/admin",className:"flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white",onClick:()=>Z(!1),children:[s.jsx(x.Z,{className:"h-4 w-4 mr-2"}),"Admin Panel"]}),s.jsx("hr",{className:"my-1 border-gray-700"}),(0,s.jsxs)("button",{onClick:()=>{localStorage.removeItem("auth-token"),q.push("/login")},className:"flex items-center w-full px-4 py-2 text-sm text-red-400 hover:bg-gray-700 text-left",children:[s.jsx(f.Z,{className:"h-4 w-4 mr-2"}),"Logout"]})]})]}):(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(l.default,{href:"/login",className:"cyber-btn",children:"Login"}),s.jsx(l.default,{href:"/register",className:"cyber-btn-primary",children:"Register"})]}),N&&s.jsx("button",{onClick:()=>k(!v),className:"lg:hidden text-gray-400 hover:text-white transition-colors",children:v?s.jsx(b.Z,{className:"h-6 w-6"}):s.jsx(j.Z,{className:"h-6 w-6"})})]})]}),N&&v&&s.jsx("div",{className:"lg:hidden border-t border-gray-800 bg-gray-900/98",children:(0,s.jsxs)("div",{className:"px-4 pt-2 pb-3 space-y-1",children:[R.map(e=>s.jsx(l.default,{href:e.href,className:"block px-3 py-2 text-gray-300 hover:text-cyber-green hover:bg-gray-800 rounded-md transition-colors",onClick:()=>k(!1),children:e.name},e.name)),s.jsx("div",{className:"border-t border-gray-700 pt-2 mt-2",children:(0,s.jsxs)("button",{className:"flex items-center px-3 py-2 text-gray-300 hover:text-white transition-colors",children:[s.jsx(m.Z,{className:"h-4 w-4 mr-2"}),"Language: Indonesia"]})}),!e&&(0,s.jsxs)("div",{className:"border-t border-gray-700 pt-2 mt-2 space-y-2",children:[s.jsx(l.default,{href:"/login",className:"block px-3 py-2 text-center cyber-btn",onClick:()=>k(!1),children:"Login"}),s.jsx(l.default,{href:"/register",className:"block px-3 py-2 text-center cyber-btn-primary",onClick:()=>k(!1),children:"Register"})]})]})})]})}},37202:(e,t,a)=>{a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},6507:(e,t,a)=>{a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},54659:(e,t,a)=>{a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},26092:(e,t,a)=>{a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]])},54014:(e,t,a)=>{a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},18019:(e,t,a)=>{a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},71810:(e,t,a)=>{a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},90748:(e,t,a)=>{a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},88307:(e,t,a)=>{a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},88378:(e,t,a)=>{a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},58038:(e,t,a)=>{a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},79635:(e,t,a)=>{a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},91470:(e,t,a)=>{a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},94019:(e,t,a)=>{a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},35047:(e,t,a)=>{var s=a(77389);a.o(s,"usePathname")&&a.d(t,{usePathname:function(){return s.usePathname}}),a.o(s,"useRouter")&&a.d(t,{useRouter:function(){return s.useRouter}})}};