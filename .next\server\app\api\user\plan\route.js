"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/user/plan/route";
exports.ids = ["app/api/user/plan/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fplan%2Froute&page=%2Fapi%2Fuser%2Fplan%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fplan%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fplan%2Froute&page=%2Fapi%2Fuser%2Fplan%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fplan%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Users_Downloads_kodeXGuard_app_api_user_plan_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/user/plan/route.ts */ \"(rsc)/./app/api/user/plan/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/user/plan/route\",\n        pathname: \"/api/user/plan\",\n        filename: \"route\",\n        bundlePath: \"app/api/user/plan/route\"\n    },\n    resolvedPagePath: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\api\\\\user\\\\plan\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Users_Downloads_kodeXGuard_app_api_user_plan_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/user/plan/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fplan%2Froute&page=%2Fapi%2Fuser%2Fplan%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fplan%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/user/plan/route.ts":
/*!************************************!*\
  !*** ./app/api/user/plan/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function GET(request) {\n    try {\n        // Get auth token\n        const authHeader = request.headers.get(\"authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        // Simple token validation for testing\n        if (!token.startsWith(\"kxg_\")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid token\"\n            }, {\n                status: 401\n            });\n        }\n        // Mock user plan data\n        const userPlan = {\n            current: {\n                id: \"plan_cybersecurity_001\",\n                name: \"Cybersecurity Pro\",\n                type: \"cybersecurity\",\n                price: 2500000,\n                currency: \"IDR\",\n                billing: \"yearly\",\n                startDate: new Date(Date.now() - ******** * 30).toISOString(),\n                endDate: new Date(Date.now() + ******** * 335).toISOString(),\n                isActive: true,\n                autoRenew: true\n            },\n            features: {\n                scans: {\n                    limit: null,\n                    used: 45,\n                    resetDate: new Date(Date.now() + ******** * 5).toISOString()\n                },\n                osintSearches: {\n                    limit: null,\n                    used: 123,\n                    resetDate: new Date(Date.now() + ******** * 5).toISOString()\n                },\n                apiCalls: {\n                    limit: 100000,\n                    used: 28560,\n                    resetDate: new Date(Date.now() + ******** * 5).toISOString()\n                },\n                storage: {\n                    limit: 1000,\n                    used: 156.7,\n                    resetDate: new Date(Date.now() + ******** * 335).toISOString()\n                },\n                teamMembers: {\n                    limit: 50,\n                    used: 8\n                },\n                features: [\n                    \"Unlimited Vulnerability Scans\",\n                    \"Unlimited OSINT Searches\",\n                    \"Advanced Reporting & Analytics\",\n                    \"API Access (100k calls/month)\",\n                    \"Priority Support\",\n                    \"Custom Scan Templates\",\n                    \"Team Collaboration (50 members)\",\n                    \"White-label Reports\",\n                    \"Advanced Integrations\",\n                    \"Custom Webhooks\",\n                    \"Compliance Reports\",\n                    \"Advanced Security Features\"\n                ]\n            },\n            usage: {\n                thisMonth: {\n                    scans: 45,\n                    osintSearches: 123,\n                    apiCalls: 28560,\n                    storageUsed: 12.3\n                },\n                lastMonth: {\n                    scans: 67,\n                    osintSearches: 189,\n                    apiCalls: 34120,\n                    storageUsed: 8.9\n                }\n            },\n            billing: {\n                nextBillingDate: new Date(Date.now() + ******** * 335).toISOString(),\n                lastPayment: {\n                    amount: 2500000,\n                    date: new Date(Date.now() - ******** * 30).toISOString(),\n                    method: \"Bank Transfer\",\n                    status: \"success\"\n                },\n                paymentMethod: {\n                    type: \"bank\",\n                    last4: \"1234\",\n                    brand: \"BCA\"\n                }\n            },\n            availableUpgrades: [\n                {\n                    id: \"plan_enterprise_001\",\n                    name: \"Enterprise\",\n                    type: \"cybersecurity\",\n                    price: 5000000,\n                    currency: \"IDR\",\n                    billing: \"yearly\",\n                    features: [\n                        \"Everything in Cybersecurity Pro\",\n                        \"Unlimited Team Members\",\n                        \"Dedicated Support Manager\",\n                        \"Custom Integrations\",\n                        \"On-premise Deployment\",\n                        \"Advanced Compliance Reports\",\n                        \"Custom SLA\"\n                    ],\n                    popular: true\n                }\n            ]\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: userPlan,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"User plan API error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        // Get auth token\n        const authHeader = request.headers.get(\"authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        // Simple token validation for testing\n        if (!token.startsWith(\"kxg_\")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid token\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { planId, billing, paymentMethod } = body;\n        // Validate input\n        if (!planId || !billing) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Plan ID and billing type are required\"\n            }, {\n                status: 400\n            });\n        }\n        // In a real app, process the plan upgrade/change\n        // For demo, just return success\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Plan upgrade initiated successfully\",\n            data: {\n                planId,\n                billing,\n                paymentMethod,\n                status: \"pending\",\n                estimatedActivation: new Date(Date.now() + 3600000).toISOString() // 1 hour\n            },\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"Plan upgrade error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/user/plan/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fplan%2Froute&page=%2Fapi%2Fuser%2Fplan%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fplan%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();