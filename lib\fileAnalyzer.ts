// KodeXGuard File Analysis Engine
// Advanced malware detection and file security analysis

import crypto from 'crypto'
import { db } from './database'
import type { FileAnalysis, ThreatLevel } from '@/types'

export interface FileAnalysisRequest {
  file: Buffer
  filename: string
  userId: number
  analysisType?: 'malware' | 'webshell' | 'secret' | 'general'
}

export interface FileAnalysisResult {
  analysisId: string
  filename: string
  fileHash: string
  fileSize: number
  fileType: string
  threatLevel: ThreatLevel
  detectedThreats: string[]
  analysisResults: {
    malwareSignatures: MalwareSignature[]
    webshellPatterns: WebshellPattern[]
    secretsFound: SecretPattern[]
    fileStructure: any
    entropy: number
    suspiciousStrings: string[]
  }
  scanEngines: string[]
  confidence: number
  recommendations: string[]
}

export interface MalwareSignature {
  name: string
  type: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  description: string
  pattern: string
  offset?: number
}

export interface WebshellPattern {
  name: string
  type: string
  pattern: string
  description: string
  risk: 'low' | 'medium' | 'high'
}

export interface SecretPattern {
  type: string
  value: string
  confidence: number
  description: string
}

export class FileAnalyzer {
  private malwareSignatures: MalwareSignature[] = []
  private webshellPatterns: WebshellPattern[] = []
  private secretPatterns: RegExp[] = []

  constructor() {
    this.initializeSignatures()
  }

  // Initialize detection signatures
  private initializeSignatures(): void {
    // Malware signatures
    this.malwareSignatures = [
      {
        name: 'Generic Trojan',
        type: 'trojan',
        severity: 'high',
        description: 'Generic trojan behavior detected',
        pattern: 'CreateRemoteThread|WriteProcessMemory|VirtualAllocEx'
      },
      {
        name: 'Ransomware Pattern',
        type: 'ransomware',
        severity: 'critical',
        description: 'Ransomware encryption behavior',
        pattern: 'CryptEncrypt|CryptGenKey|\.encrypt|\.locked|ransom'
      },
      {
        name: 'Keylogger',
        type: 'keylogger',
        severity: 'high',
        description: 'Keylogging functionality detected',
        pattern: 'SetWindowsHookEx|GetAsyncKeyState|keylog'
      },
      {
        name: 'Backdoor',
        type: 'backdoor',
        severity: 'critical',
        description: 'Backdoor functionality detected',
        pattern: 'bind|listen|accept|reverse.*shell|cmd\.exe'
      }
    ]

    // Webshell patterns
    this.webshellPatterns = [
      {
        name: 'PHP Webshell',
        type: 'php',
        pattern: 'eval\\s*\\(\\s*\\$_(?:GET|POST|REQUEST)',
        description: 'PHP eval-based webshell',
        risk: 'high'
      },
      {
        name: 'ASP Webshell',
        type: 'asp',
        pattern: 'eval\\s*\\(\\s*request',
        description: 'ASP eval-based webshell',
        risk: 'high'
      },
      {
        name: 'JSP Webshell',
        type: 'jsp',
        pattern: 'Runtime\\.getRuntime\\(\\)\\.exec',
        description: 'JSP command execution webshell',
        risk: 'high'
      },
      {
        name: 'Generic Shell',
        type: 'generic',
        pattern: 'system\\s*\\(\\s*\\$_|exec\\s*\\(\\s*\\$_|shell_exec',
        description: 'Generic command execution pattern',
        risk: 'medium'
      }
    ]

    // Secret patterns
    this.secretPatterns = [
      /(?:api[_-]?key|apikey)[\s]*[:=][\s]*["\']?([a-zA-Z0-9_\-]{20,})["\']?/gi,
      /(?:secret[_-]?key|secretkey)[\s]*[:=][\s]*["\']?([a-zA-Z0-9_\-]{20,})["\']?/gi,
      /(?:access[_-]?token|accesstoken)[\s]*[:=][\s]*["\']?([a-zA-Z0-9_\-]{20,})["\']?/gi,
      /(?:password|passwd|pwd)[\s]*[:=][\s]*["\']?([^\s"']{8,})["\']?/gi,
      /(?:private[_-]?key|privatekey)[\s]*[:=][\s]*["\']?([a-zA-Z0-9+/=]{100,})["\']?/gi,
      /(?:database[_-]?url|db[_-]?url)[\s]*[:=][\s]*["\']?([^\s"']+)["\']?/gi
    ]
  }

  // Analyze file
  async analyzeFile(request: FileAnalysisRequest): Promise<FileAnalysisResult> {
    const startTime = Date.now()
    
    try {
      // Calculate file hash
      const fileHash = crypto.createHash('sha256').update(request.file).digest('hex')
      
      // Detect file type
      const fileType = this.detectFileType(request.file, request.filename)
      
      // Initialize analysis result
      const result: FileAnalysisResult = {
        analysisId: this.generateAnalysisId(),
        filename: request.filename,
        fileHash,
        fileSize: request.file.length,
        fileType,
        threatLevel: 'safe',
        detectedThreats: [],
        analysisResults: {
          malwareSignatures: [],
          webshellPatterns: [],
          secretsFound: [],
          fileStructure: {},
          entropy: 0,
          suspiciousStrings: []
        },
        scanEngines: ['KodeXGuard-Static', 'KodeXGuard-Heuristic'],
        confidence: 0,
        recommendations: []
      }

      // Convert buffer to string for pattern matching
      const fileContent = request.file.toString('utf8', 0, Math.min(request.file.length, 1024 * 1024)) // Limit to 1MB

      // Perform different types of analysis
      await this.performMalwareAnalysis(fileContent, result)
      await this.performWebshellAnalysis(fileContent, result)
      await this.performSecretAnalysis(fileContent, result)
      await this.performHeuristicAnalysis(request.file, result)

      // Calculate overall threat level
      result.threatLevel = this.calculateThreatLevel(result)
      
      // Calculate confidence score
      result.confidence = this.calculateConfidence(result)

      // Generate recommendations
      result.recommendations = this.generateRecommendations(result)

      // Save analysis to database
      await this.saveAnalysis(result, request.userId)

      return result

    } catch (error) {
      console.error('File analysis error:', error)
      throw new Error('File analysis failed')
    }
  }

  // Detect file type
  private detectFileType(buffer: Buffer, filename: string): string {
    const extension = filename.split('.').pop()?.toLowerCase() || ''
    
    // Check magic bytes
    const magicBytes = buffer.slice(0, 16)
    
    if (magicBytes[0] === 0x4D && magicBytes[1] === 0x5A) return 'PE Executable'
    if (magicBytes[0] === 0x7F && magicBytes[1] === 0x45 && magicBytes[2] === 0x4C && magicBytes[3] === 0x46) return 'ELF Executable'
    if (magicBytes[0] === 0x50 && magicBytes[1] === 0x4B) return 'ZIP Archive'
    if (magicBytes[0] === 0xFF && magicBytes[1] === 0xD8) return 'JPEG Image'
    if (magicBytes[0] === 0x89 && magicBytes[1] === 0x50 && magicBytes[2] === 0x4E && magicBytes[3] === 0x47) return 'PNG Image'

    // Fallback to extension
    const typeMap: { [key: string]: string } = {
      'php': 'PHP Script',
      'asp': 'ASP Script',
      'aspx': 'ASPX Script',
      'jsp': 'JSP Script',
      'js': 'JavaScript',
      'py': 'Python Script',
      'sh': 'Shell Script',
      'bat': 'Batch File',
      'exe': 'Executable',
      'dll': 'Dynamic Library',
      'zip': 'ZIP Archive',
      'rar': 'RAR Archive',
      'pdf': 'PDF Document',
      'doc': 'Word Document',
      'docx': 'Word Document',
      'txt': 'Text File'
    }

    return typeMap[extension] || 'Unknown'
  }

  // Perform malware analysis
  private async performMalwareAnalysis(content: string, result: FileAnalysisResult): Promise<void> {
    for (const signature of this.malwareSignatures) {
      const regex = new RegExp(signature.pattern, 'gi')
      const matches = content.match(regex)
      
      if (matches) {
        result.analysisResults.malwareSignatures.push(signature)
        result.detectedThreats.push(`Malware: ${signature.name}`)
      }
    }
  }

  // Perform webshell analysis
  private async performWebshellAnalysis(content: string, result: FileAnalysisResult): Promise<void> {
    for (const pattern of this.webshellPatterns) {
      const regex = new RegExp(pattern.pattern, 'gi')
      const matches = content.match(regex)
      
      if (matches) {
        result.analysisResults.webshellPatterns.push(pattern)
        result.detectedThreats.push(`Webshell: ${pattern.name}`)
      }
    }
  }

  // Perform secret analysis
  private async performSecretAnalysis(content: string, result: FileAnalysisResult): Promise<void> {
    for (const pattern of this.secretPatterns) {
      let match
      while ((match = pattern.exec(content)) !== null) {
        const secret: SecretPattern = {
          type: this.getSecretType(pattern.source),
          value: match[1] || match[0],
          confidence: 0.8,
          description: 'Potential secret or credential found'
        }
        
        result.analysisResults.secretsFound.push(secret)
        result.detectedThreats.push(`Secret: ${secret.type}`)
      }
    }
  }

  // Perform heuristic analysis
  private async performHeuristicAnalysis(buffer: Buffer, result: FileAnalysisResult): Promise<void> {
    // Calculate entropy
    result.analysisResults.entropy = this.calculateEntropy(buffer)
    
    // High entropy might indicate encryption/packing
    if (result.analysisResults.entropy > 7.5) {
      result.detectedThreats.push('High entropy (possible encryption/packing)')
    }

    // Look for suspicious strings
    const content = buffer.toString('utf8')
    const suspiciousPatterns = [
      /CreateProcess/gi,
      /WriteFile/gi,
      /RegSetValue/gi,
      /InternetOpen/gi,
      /HttpSendRequest/gi,
      /base64_decode/gi,
      /eval\s*\(/gi,
      /system\s*\(/gi,
      /exec\s*\(/gi
    ]

    for (const pattern of suspiciousPatterns) {
      const matches = content.match(pattern)
      if (matches) {
        result.analysisResults.suspiciousStrings.push(...matches)
      }
    }
  }

  // Calculate entropy
  private calculateEntropy(buffer: Buffer): number {
    const frequency: { [key: number]: number } = {}
    
    for (let i = 0; i < buffer.length; i++) {
      const byte = buffer[i]
      frequency[byte] = (frequency[byte] || 0) + 1
    }

    let entropy = 0
    const length = buffer.length

    for (const count of Object.values(frequency)) {
      const probability = count / length
      entropy -= probability * Math.log2(probability)
    }

    return entropy
  }

  // Calculate threat level
  private calculateThreatLevel(result: FileAnalysisResult): ThreatLevel {
    const threats = result.detectedThreats.length
    const criticalSignatures = result.analysisResults.malwareSignatures.filter(s => s.severity === 'critical').length
    const highRiskWebshells = result.analysisResults.webshellPatterns.filter(p => p.risk === 'high').length

    if (criticalSignatures > 0 || highRiskWebshells > 0) {
      return 'malicious'
    }

    if (threats > 3 || result.analysisResults.entropy > 7.5) {
      return 'suspicious'
    }

    return 'safe'
  }

  // Calculate confidence score
  private calculateConfidence(result: FileAnalysisResult): number {
    let confidence = 50 // Base confidence

    // Increase confidence based on detections
    confidence += result.analysisResults.malwareSignatures.length * 20
    confidence += result.analysisResults.webshellPatterns.length * 15
    confidence += result.analysisResults.secretsFound.length * 10

    // Decrease confidence for unknown file types
    if (result.fileType === 'Unknown') {
      confidence -= 20
    }

    return Math.min(100, Math.max(0, confidence))
  }

  // Generate recommendations
  private generateRecommendations(result: FileAnalysisResult): string[] {
    const recommendations: string[] = []

    if (result.threatLevel === 'malicious') {
      recommendations.push('⚠️ DO NOT EXECUTE this file - it contains malicious code')
      recommendations.push('🔒 Quarantine the file immediately')
      recommendations.push('🔍 Scan your system for potential infections')
    } else if (result.threatLevel === 'suspicious') {
      recommendations.push('⚠️ Exercise caution with this file')
      recommendations.push('🔍 Perform additional analysis before execution')
      recommendations.push('🛡️ Run in a sandboxed environment if necessary')
    }

    if (result.analysisResults.secretsFound.length > 0) {
      recommendations.push('🔑 Remove or encrypt any exposed credentials')
      recommendations.push('🔄 Rotate any compromised API keys or passwords')
    }

    if (result.analysisResults.entropy > 7.5) {
      recommendations.push('📊 High entropy detected - file may be packed or encrypted')
    }

    if (recommendations.length === 0) {
      recommendations.push('✅ File appears to be safe based on current analysis')
    }

    return recommendations
  }

  // Get secret type from pattern
  private getSecretType(pattern: string): string {
    if (pattern.includes('api')) return 'API Key'
    if (pattern.includes('secret')) return 'Secret Key'
    if (pattern.includes('token')) return 'Access Token'
    if (pattern.includes('password')) return 'Password'
    if (pattern.includes('private')) return 'Private Key'
    if (pattern.includes('database')) return 'Database URL'
    return 'Unknown Secret'
  }

  // Save analysis to database
  private async saveAnalysis(result: FileAnalysisResult, userId: number): Promise<void> {
    try {
      await db.query(
        `INSERT INTO file_analysis (user_id, filename, file_hash, file_size, file_type, analysis_results, threat_level, detected_threats, created_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())`,
        [
          userId,
          result.filename,
          result.fileHash,
          result.fileSize,
          result.fileType,
          JSON.stringify(result.analysisResults),
          result.threatLevel,
          JSON.stringify(result.detectedThreats)
        ]
      )
    } catch (error) {
      console.error('Failed to save file analysis:', error)
    }
  }

  // Get analysis history
  async getAnalysisHistory(userId: number, limit: number = 20): Promise<FileAnalysis[]> {
    try {
      return await db.query<FileAnalysis>(
        `SELECT * FROM file_analysis 
         WHERE user_id = ? 
         ORDER BY created_at DESC 
         LIMIT ?`,
        [userId, limit]
      )
    } catch (error) {
      console.error('Failed to get analysis history:', error)
      return []
    }
  }

  // Generate analysis ID
  private generateAnalysisId(): string {
    return 'analysis_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }
}

// Export singleton instance
export const fileAnalyzer = new FileAnalyzer()
export default FileAnalyzer
