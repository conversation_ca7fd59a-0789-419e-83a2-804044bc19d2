import { NextRequest, NextResponse } from 'next/server'
import { fileAnalyzer } from '@/lib/fileAnalyzer'
import { AuthService } from '@/lib/auth'
import { Database } from '@/lib/database'

export async function POST(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Verify token and get user
    const user = await AuthService.getUserByToken(token)
    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const analysisType = formData.get('analysisType') as string || 'general'

    if (!file) {
      return NextResponse.json(
        {
          success: false,
          error: 'File is required',
          code: 'MISSING_FILE'
        },
        { status: 400 }
      )
    }

    // Validate file size (max 50MB)
    const maxSize = 50 * 1024 * 1024 // 50MB
    if (file.size > maxSize) {
      return NextResponse.json(
        {
          success: false,
          error: 'File size exceeds maximum limit (50MB)',
          code: 'FILE_TOO_LARGE'
        },
        { status: 400 }
      )
    }

    // Validate analysis type
    const validTypes = ['malware', 'webshell', 'secret', 'general']
    if (!validTypes.includes(analysisType)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid analysis type',
          code: 'INVALID_ANALYSIS_TYPE'
        },
        { status: 400 }
      )
    }

    // Check user quota
    const db = Database.getInstance()
    const today = new Date().toISOString().split('T')[0]

    const todayUsage = await db.query(
      `SELECT COUNT(*) as count FROM file_analysis
       WHERE user_id = ? AND DATE(created_at) = ?`,
      [user.id, today]
    )

    // Get user plan quotas
    const planInfo = await db.query(
      'SELECT file_quota_daily FROM plans WHERE id = ?',
      [user.planId]
    )

    const dailyQuota = planInfo[0]?.file_quota_daily || 2
    const usedToday = todayUsage[0]?.count || 0

    if (usedToday >= dailyQuota) {
      return NextResponse.json({
        success: false,
        error: `Daily file analysis quota exceeded. Used: ${usedToday}/${dailyQuota}`,
        code: 'QUOTA_EXCEEDED'
      }, { status: 429 })
    }

    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer())

    // Perform file analysis
    const analysisRequest = {
      file: buffer,
      filename: file.name,
      userId: user.id,
      analysisType: analysisType as any
    }

    const analysisResult = await fileAnalyzer.analyzeFile(analysisRequest)

    return NextResponse.json({
      success: true,
      data: {
        analysisId: analysisResult.analysisId,
        filename: analysisResult.filename,
        fileSize: analysisResult.fileSize,
        fileType: analysisResult.fileType,
        fileHash: analysisResult.fileHash,
        threatLevel: analysisResult.threatLevel,
        detectedThreats: analysisResult.detectedThreats,
        analysisResults: analysisResult.analysisResults,
        scanEngines: analysisResult.scanEngines,
        confidence: analysisResult.confidence,
        recommendations: analysisResult.recommendations,
        quotaUsed: usedToday + 1,
        quotaLimit: dailyQuota
      },
      message: 'File analysis completed successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('File analysis error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Verify token and get user
    const user = await AuthService.getUserByToken(token)
    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    // Get analysis history
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')

    const history = await fileAnalyzer.getAnalysisHistory(user.id, limit)

    return NextResponse.json({
      success: true,
      data: history,
      pagination: {
        page,
        limit,
        total: history.length,
        totalPages: Math.ceil(history.length / limit)
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('File analysis history error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}
