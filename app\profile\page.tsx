'use client'

import { useState, useEffect } from 'react'
import Navbar from '@/components/Navbar'
import { Card, StatCard, AlertCard } from '@/components/Card'
import {
  User,
  Key,
  Settings,
  Save,
  Copy,
  Eye,
  EyeOff,
  Plus,
  Trash2,
  Edit,
  Shield,
  Activity,
  Calendar,
  Mail,
  Phone,
  MapPin,
  Globe,
  Camera,
  RefreshCw,
  CheckCircle,
  XCircle,
  Clock,
  Zap
} from 'lucide-react'

interface APIKey {
  id: string
  name: string
  keyPrefix: string
  permissions: string[]
  isActive: boolean
  lastUsed?: string
  usageCount: number
  rateLimit: number
  createdAt: string
  expiresAt?: string
}

interface UserProfile {
  id: string
  username: string
  email: string
  fullName: string
  avatar?: string
  bio?: string
  role: string
  plan: string
  planExpiry?: string
  isActive: boolean
  emailVerified: boolean
  createdAt: string
  lastLogin: string
  stats: {
    totalScans: number
    vulnerabilitiesFound: number
    filesAnalyzed: number
    apiCalls: number
    score: number
    rank: number
  }
}

export default function ProfilePage() {
  const [user] = useState({
    username: 'admin',
    avatar: '',
    role: 'super_admin',
    plan: 'cybersecurity'
  })

  const [activeTab, setActiveTab] = useState('profile')
  const [profile, setProfile] = useState<UserProfile>({
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'Super Administrator',
    avatar: '',
    bio: 'Platform creator and cybersecurity enthusiast. Building tools to make the internet safer.',
    role: 'super_admin',
    plan: 'cybersecurity',
    planExpiry: '2024-12-31',
    isActive: true,
    emailVerified: true,
    createdAt: '2023-01-01',
    lastLogin: '2024-01-15T10:30:00Z',
    stats: {
      totalScans: 1247,
      vulnerabilitiesFound: 89,
      filesAnalyzed: 456,
      apiCalls: 28560,
      score: 9870,
      rank: 4
    }
  })

  const [apiKeys, setApiKeys] = useState<APIKey[]>([
    {
      id: '1',
      name: 'Production API',
      keyPrefix: 'kxg_prod_',
      permissions: ['scan:read', 'scan:write', 'osint:read', 'file:analyze'],
      isActive: true,
      lastUsed: '2 hours ago',
      usageCount: 15420,
      rateLimit: 10000,
      createdAt: '2023-06-15',
      expiresAt: '2024-06-15'
    },
    {
      id: '2',
      name: 'Development API',
      keyPrefix: 'kxg_dev_',
      permissions: ['scan:read', 'osint:read'],
      isActive: true,
      lastUsed: '1 day ago',
      usageCount: 2847,
      rateLimit: 1000,
      createdAt: '2023-08-20'
    },
    {
      id: '3',
      name: 'Bot Integration',
      keyPrefix: 'kxg_bot_',
      permissions: ['scan:write', 'user:read'],
      isActive: false,
      lastUsed: '1 week ago',
      usageCount: 567,
      rateLimit: 5000,
      createdAt: '2023-10-10',
      expiresAt: '2024-10-10'
    }
  ])

  const [editingProfile, setEditingProfile] = useState(false)
  const [showNewKeyModal, setShowNewKeyModal] = useState(false)
  const [newKeyName, setNewKeyName] = useState('')
  const [newKeyPermissions, setNewKeyPermissions] = useState<string[]>([])
  const [showApiKey, setShowApiKey] = useState<string | null>(null)

  const availablePermissions = [
    { id: 'scan:read', name: 'Read Scan Results', description: 'View vulnerability scan results' },
    { id: 'scan:write', name: 'Create Scans', description: 'Start new vulnerability scans' },
    { id: 'osint:read', name: 'OSINT Search', description: 'Perform OSINT investigations' },
    { id: 'file:analyze', name: 'File Analysis', description: 'Analyze files for threats' },
    { id: 'cve:read', name: 'CVE Database', description: 'Access CVE intelligence data' },
    { id: 'user:read', name: 'User Info', description: 'Read user profile information' },
    { id: 'admin:read', name: 'Admin Access', description: 'Administrative operations (Admin only)' }
  ]

  const handleProfileSave = () => {
    setEditingProfile(false)
    // In real app, save to API
  }

  const createAPIKey = () => {
    if (!newKeyName.trim() || newKeyPermissions.length === 0) return

    const newKey: APIKey = {
      id: Date.now().toString(),
      name: newKeyName,
      keyPrefix: 'kxg_' + Math.random().toString(36).substr(2, 8) + '_',
      permissions: newKeyPermissions,
      isActive: true,
      usageCount: 0,
      rateLimit: user.plan === 'cybersecurity' ? 100000 : user.plan === 'bug_hunter' ? 10000 : 1000,
      createdAt: new Date().toISOString()
    }

    setApiKeys(prev => [...prev, newKey])
    setNewKeyName('')
    setNewKeyPermissions([])
    setShowNewKeyModal(false)
  }

  const toggleAPIKey = (keyId: string) => {
    setApiKeys(prev => prev.map(key =>
      key.id === keyId ? { ...key, isActive: !key.isActive } : key
    ))
  }

  const deleteAPIKey = (keyId: string) => {
    setApiKeys(prev => prev.filter(key => key.id !== keyId))
  }

  const copyAPIKey = (keyPrefix: string) => {
    const fullKey = keyPrefix + 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
    navigator.clipboard.writeText(fullKey)
  }

  const getPermissionColor = (permission: string) => {
    if (permission.includes('admin')) return 'bg-red-900/50 text-red-400'
    if (permission.includes('write')) return 'bg-orange-900/50 text-orange-400'
    return 'bg-blue-900/50 text-blue-400'
  }

  return (
    <div className="min-h-screen bg-gradient-cyber">
      <Navbar user={user} />

      <div className="pt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <User className="h-8 w-8 text-cyber-green" />
              <h1 className="text-3xl font-bold font-cyber text-white">
                Profile & <span className="cyber-text">API Keys</span>
              </h1>
            </div>
            <p className="text-gray-400 max-w-3xl">
              Kelola profil pengguna, statistik aktivitas, dan API keys untuk integrasi dengan aplikasi eksternal.
              Monitor penggunaan API dan atur permissions sesuai kebutuhan.
            </p>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <StatCard
              title="Total Scans"
              value={profile.stats.totalScans}
              icon={Shield}
              color="green"
              trend={{ value: 12, isPositive: true }}
            />
            <StatCard
              title="Vulnerabilities Found"
              value={profile.stats.vulnerabilitiesFound}
              icon={Activity}
              color="red"
            />
            <StatCard
              title="API Calls"
              value={profile.stats.apiCalls}
              icon={Zap}
              color="purple"
              trend={{ value: 23, isPositive: true }}
            />
            <StatCard
              title="Hunter Rank"
              value={`#${profile.stats.rank}`}
              icon={User}
              color="gold"
            />
          </div>

          {/* Tab Navigation */}
          <div className="mb-8">
            <div className="flex space-x-1 bg-gray-800/50 p-1 rounded-lg">
              {[
                { id: 'profile', name: 'Profile Settings', icon: User },
                { id: 'apikeys', name: 'API Keys', icon: Key },
                { id: 'activity', name: 'Activity Log', icon: Activity }
              ].map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                      activeTab === tab.id
                        ? 'bg-cyber-green text-black'
                        : 'text-gray-300 hover:text-white hover:bg-gray-700/50'
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    <span>{tab.name}</span>
                  </button>
                )
              })}
            </div>
          </div>

          {/* Profile Tab */}
          {activeTab === 'profile' && (
            <div className="grid lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <Card border="green" glow>
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-6">
                      <h2 className="text-xl font-bold text-white">Profile Information</h2>
                      <button
                        onClick={() => setEditingProfile(!editingProfile)}
                        className="cyber-btn text-sm"
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        {editingProfile ? 'Cancel' : 'Edit Profile'}
                      </button>
                    </div>

                    <div className="space-y-6">
                      {/* Avatar */}
                      <div className="flex items-center space-x-6">
                        <div className="relative">
                          {profile.avatar ? (
                            <img src={profile.avatar} alt={profile.username} className="h-20 w-20 rounded-full" />
                          ) : (
                            <div className="h-20 w-20 rounded-full bg-cyber-green/20 flex items-center justify-center">
                              <span className="text-2xl font-bold text-cyber-green">
                                {profile.username.charAt(0).toUpperCase()}
                              </span>
                            </div>
                          )}
                          {editingProfile && (
                            <button className="absolute bottom-0 right-0 bg-cyber-green text-black rounded-full p-2 hover:bg-cyber-blue transition-colors">
                              <Camera className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-white">{profile.fullName}</h3>
                          <p className="text-gray-400">@{profile.username}</p>
                          <div className="flex items-center space-x-2 mt-2">
                            <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                              profile.role === 'super_admin' ? 'bg-red-900/50 text-red-400' :
                              profile.role === 'admin' ? 'bg-orange-900/50 text-orange-400' :
                              'bg-blue-900/50 text-blue-400'
                            }`}>
                              {profile.role.replace('_', ' ').toUpperCase()}
                            </span>
                            <span className="px-2 py-1 bg-nusantara-gold/20 text-nusantara-gold rounded-full text-xs font-semibold">
                              {profile.plan.toUpperCase()}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Form Fields */}
                      <div className="grid md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            Full Name
                          </label>
                          <input
                            type="text"
                            value={profile.fullName}
                            onChange={(e) => setProfile(prev => ({ ...prev, fullName: e.target.value }))}
                            disabled={!editingProfile}
                            className="cyber-input w-full disabled:opacity-50"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            Username
                          </label>
                          <input
                            type="text"
                            value={profile.username}
                            onChange={(e) => setProfile(prev => ({ ...prev, username: e.target.value }))}
                            disabled={!editingProfile}
                            className="cyber-input w-full disabled:opacity-50"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            Email Address
                          </label>
                          <div className="relative">
                            <input
                              type="email"
                              value={profile.email}
                              onChange={(e) => setProfile(prev => ({ ...prev, email: e.target.value }))}
                              disabled={!editingProfile}
                              className="cyber-input w-full disabled:opacity-50"
                            />
                            {profile.emailVerified && (
                              <CheckCircle className="absolute right-3 top-3 h-5 w-5 text-green-400" />
                            )}
                          </div>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            Plan Expiry
                          </label>
                          <input
                            type="text"
                            value={profile.planExpiry ? new Date(profile.planExpiry).toLocaleDateString() : 'Never'}
                            disabled
                            className="cyber-input w-full disabled:opacity-50"
                          />
                        </div>
                      </div>

                      {/* Bio */}
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          Bio
                        </label>
                        <textarea
                          value={profile.bio || ''}
                          onChange={(e) => setProfile(prev => ({ ...prev, bio: e.target.value }))}
                          disabled={!editingProfile}
                          className="cyber-input w-full h-24 disabled:opacity-50"
                          placeholder="Tell us about yourself..."
                        />
                      </div>

                      {editingProfile && (
                        <div className="flex space-x-3">
                          <button
                            onClick={handleProfileSave}
                            className="cyber-btn-primary"
                          >
                            <Save className="h-4 w-4 mr-2" />
                            Save Changes
                          </button>
                          <button
                            onClick={() => setEditingProfile(false)}
                            className="cyber-btn"
                          >
                            Cancel
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </Card>
              </div>

              {/* Account Info */}
              <div>
                <Card>
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-white mb-4">Account Information</h3>
                    <div className="space-y-4">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Member Since</span>
                        <span className="text-white">{new Date(profile.createdAt).toLocaleDateString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Last Login</span>
                        <span className="text-white">{new Date(profile.lastLogin).toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Account Status</span>
                        <span className={`font-semibold ${profile.isActive ? 'text-green-400' : 'text-red-400'}`}>
                          {profile.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Email Verified</span>
                        <span className={`font-semibold ${profile.emailVerified ? 'text-green-400' : 'text-red-400'}`}>
                          {profile.emailVerified ? 'Verified' : 'Unverified'}
                        </span>
                      </div>
                    </div>
                  </div>
                </Card>

                <Card className="mt-6" border="gold">
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-white mb-4">Hunter Statistics</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Hunter Score</span>
                        <span className="text-cyber-green font-bold">{profile.stats.score.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Global Rank</span>
                        <span className="text-nusantara-gold font-bold">#{profile.stats.rank}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Files Analyzed</span>
                        <span className="text-white">{profile.stats.filesAnalyzed.toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          )}

          {/* API Keys Tab */}
          {activeTab === 'apikeys' && (
            <div className="grid lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <Card border="green" glow>
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-6">
                      <h2 className="text-xl font-bold text-white">API Keys</h2>
                      <button
                        onClick={() => setShowNewKeyModal(true)}
                        className="cyber-btn-primary"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Create New Key
                      </button>
                    </div>

                    <div className="space-y-4">
                      {apiKeys.map((apiKey) => (
                        <div
                          key={apiKey.id}
                          className="bg-gray-800/50 rounded-lg p-4 border border-gray-700"
                        >
                          <div className="flex items-center justify-between mb-4">
                            <div>
                              <div className="flex items-center space-x-3">
                                <h3 className="font-bold text-white">{apiKey.name}</h3>
                                <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                                  apiKey.isActive ? 'bg-green-900/50 text-green-400' : 'bg-red-900/50 text-red-400'
                                }`}>
                                  {apiKey.isActive ? 'Active' : 'Inactive'}
                                </span>
                              </div>
                              <div className="text-sm text-gray-400 mt-1">
                                Created: {new Date(apiKey.createdAt).toLocaleDateString()}
                                {apiKey.expiresAt && (
                                  <span> • Expires: {new Date(apiKey.expiresAt).toLocaleDateString()}</span>
                                )}
                              </div>
                            </div>
                            <div className="flex space-x-2">
                              <button
                                onClick={() => toggleAPIKey(apiKey.id)}
                                className={`text-sm px-3 py-1 rounded transition-colors ${
                                  apiKey.isActive
                                    ? 'bg-red-600 hover:bg-red-700 text-white'
                                    : 'bg-green-600 hover:bg-green-700 text-white'
                                }`}
                              >
                                {apiKey.isActive ? 'Disable' : 'Enable'}
                              </button>
                              <button
                                onClick={() => deleteAPIKey(apiKey.id)}
                                className="text-red-400 hover:text-red-300 transition-colors"
                              >
                                <Trash2 className="h-4 w-4" />
                              </button>
                            </div>
                          </div>

                          <div className="space-y-3">
                            {/* API Key */}
                            <div>
                              <label className="block text-sm font-medium text-gray-300 mb-2">
                                API Key
                              </label>
                              <div className="flex items-center space-x-2">
                                <div className="flex-1 bg-gray-900/50 rounded p-3 font-mono text-sm">
                                  {showApiKey === apiKey.id
                                    ? apiKey.keyPrefix + 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
                                    : apiKey.keyPrefix + '••••••••••••••••••••••••••••••••'
                                  }
                                </div>
                                <button
                                  onClick={() => setShowApiKey(showApiKey === apiKey.id ? null : apiKey.id)}
                                  className="cyber-btn text-sm"
                                >
                                  {showApiKey === apiKey.id ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                                </button>
                                <button
                                  onClick={() => copyAPIKey(apiKey.keyPrefix)}
                                  className="cyber-btn text-sm"
                                >
                                  <Copy className="h-4 w-4" />
                                </button>
                              </div>
                            </div>

                            {/* Permissions */}
                            <div>
                              <label className="block text-sm font-medium text-gray-300 mb-2">
                                Permissions
                              </label>
                              <div className="flex flex-wrap gap-2">
                                {apiKey.permissions.map((permission, idx) => (
                                  <span
                                    key={idx}
                                    className={`px-2 py-1 rounded-full text-xs font-semibold ${getPermissionColor(permission)}`}
                                  >
                                    {permission}
                                  </span>
                                ))}
                              </div>
                            </div>

                            {/* Usage Stats */}
                            <div className="grid grid-cols-3 gap-4 text-sm">
                              <div>
                                <span className="text-gray-400">Usage Count:</span>
                                <span className="text-white ml-2 font-semibold">{apiKey.usageCount.toLocaleString()}</span>
                              </div>
                              <div>
                                <span className="text-gray-400">Rate Limit:</span>
                                <span className="text-white ml-2 font-semibold">{apiKey.rateLimit.toLocaleString()}/day</span>
                              </div>
                              <div>
                                <span className="text-gray-400">Last Used:</span>
                                <span className="text-white ml-2 font-semibold">{apiKey.lastUsed || 'Never'}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </Card>
              </div>

              {/* API Info */}
              <div>
                <Card>
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-white mb-4">API Usage</h3>
                    <div className="space-y-4">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Total API Keys</span>
                        <span className="text-white font-semibold">{apiKeys.length}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Active Keys</span>
                        <span className="text-green-400 font-semibold">{apiKeys.filter(k => k.isActive).length}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Total Calls</span>
                        <span className="text-white font-semibold">{apiKeys.reduce((sum, key) => sum + key.usageCount, 0).toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Daily Limit</span>
                        <span className="text-cyber-green font-semibold">100,000</span>
                      </div>
                    </div>
                  </div>
                </Card>

                <Card className="mt-6">
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-white mb-4">API Documentation</h3>
                    <div className="space-y-2 text-sm">
                      <p className="text-gray-300">
                        Use your API keys to integrate KodeXGuard with your applications.
                      </p>
                      <div className="space-y-1">
                        <p className="text-gray-400">• Base URL: <code className="text-cyber-green">https://api.kodexguard.com/v1</code></p>
                        <p className="text-gray-400">• Authentication: Bearer token</p>
                        <p className="text-gray-400">• Rate limiting: Per API key</p>
                        <p className="text-gray-400">• Response format: JSON</p>
                      </div>
                      <button className="cyber-btn text-sm w-full mt-4">
                        View Full Documentation
                      </button>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          )}

          {/* Activity Log Tab */}
          {activeTab === 'activity' && (
            <Card>
              <div className="p-6">
                <h2 className="text-xl font-bold text-white mb-6">Activity Log</h2>
                <div className="space-y-4">
                  {[
                    { action: 'Vulnerability scan completed', target: 'https://example.com', time: '2 hours ago', type: 'scan' },
                    { action: 'API key created', target: 'Production API', time: '1 day ago', type: 'api' },
                    { action: 'Profile updated', target: 'Bio information', time: '2 days ago', type: 'profile' },
                    { action: 'OSINT search performed', target: '<EMAIL>', time: '3 days ago', type: 'osint' },
                    { action: 'File analyzed', target: 'suspicious.php', time: '1 week ago', type: 'file' }
                  ].map((activity, idx) => (
                    <div key={idx} className="flex items-center space-x-4 p-4 bg-gray-800/50 rounded-lg">
                      <div className={`p-2 rounded-full ${
                        activity.type === 'scan' ? 'bg-blue-900/50' :
                        activity.type === 'api' ? 'bg-green-900/50' :
                        activity.type === 'profile' ? 'bg-purple-900/50' :
                        activity.type === 'osint' ? 'bg-yellow-900/50' :
                        'bg-red-900/50'
                      }`}>
                        {activity.type === 'scan' && <Shield className="h-4 w-4 text-blue-400" />}
                        {activity.type === 'api' && <Key className="h-4 w-4 text-green-400" />}
                        {activity.type === 'profile' && <User className="h-4 w-4 text-purple-400" />}
                        {activity.type === 'osint' && <Globe className="h-4 w-4 text-yellow-400" />}
                        {activity.type === 'file' && <Shield className="h-4 w-4 text-red-400" />}
                      </div>
                      <div className="flex-1">
                        <div className="font-medium text-white">{activity.action}</div>
                        <div className="text-sm text-gray-400">{activity.target}</div>
                      </div>
                      <div className="text-sm text-gray-400">{activity.time}</div>
                    </div>
                  ))}
                </div>
              </div>
            </Card>
          )}

          {/* New API Key Modal */}
          {showNewKeyModal && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
              <Card className="w-full max-w-md mx-4" border="green">
                <div className="p-6">
                  <h3 className="text-lg font-bold text-white mb-4">Create New API Key</h3>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Key Name
                      </label>
                      <input
                        type="text"
                        value={newKeyName}
                        onChange={(e) => setNewKeyName(e.target.value)}
                        placeholder="My API Key"
                        className="cyber-input w-full"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Permissions
                      </label>
                      <div className="space-y-2 max-h-48 overflow-y-auto">
                        {availablePermissions.map((permission) => (
                          <label
                            key={permission.id}
                            className="flex items-start space-x-3 cursor-pointer"
                          >
                            <input
                              type="checkbox"
                              checked={newKeyPermissions.includes(permission.id)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setNewKeyPermissions(prev => [...prev, permission.id])
                                } else {
                                  setNewKeyPermissions(prev => prev.filter(p => p !== permission.id))
                                }
                              }}
                              className="mt-1 rounded bg-gray-800 border-gray-600"
                            />
                            <div>
                              <div className="text-sm font-medium text-white">{permission.name}</div>
                              <div className="text-xs text-gray-400">{permission.description}</div>
                            </div>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="flex space-x-3 mt-6">
                    <button
                      onClick={createAPIKey}
                      disabled={!newKeyName.trim() || newKeyPermissions.length === 0}
                      className="cyber-btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Create Key
                    </button>
                    <button
                      onClick={() => setShowNewKeyModal(false)}
                      className="cyber-btn"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}