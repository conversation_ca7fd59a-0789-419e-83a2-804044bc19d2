(()=>{var e={};e.id=90,e.ids=[90],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4882:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d}),t(19495),t(30829),t(35866);var a=t(23191),i=t(88716),r=t(37922),l=t.n(r),n=t(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["dorking",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,19495)),"D:\\Users\\Downloads\\kodeXGuard\\app\\dorking\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\Users\\Downloads\\kodeXGuard\\app\\dorking\\page.tsx"],x="/dorking/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/dorking/page",pathname:"/dorking",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},91236:(e,s,t)=>{Promise.resolve().then(t.bind(t,17874))},17874:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var a=t(10326),i=t(17577),r=t(60463),l=t(2262),n=t(88307),c=t(53468),d=t(96015),o=t(58038),x=t(17069),m=t(21405),h=t(94893),g=t(31215),p=t(43810),u=t(7027),b=t(12714),j=t(37358),y=t(33734),f=t(6507),N=t(54659);function v(){let[e]=(0,i.useState)({username:"admin",avatar:"",role:"super_admin",plan:"cybersecurity"}),[s,t]=(0,i.useState)("dorking"),[v,w]=(0,i.useState)("all"),[k,C]=(0,i.useState)(""),[P,S]=(0,i.useState)([]),[Z,D]=(0,i.useState)(!1),[E,q]=(0,i.useState)([]),A=[{id:"1",name:"Admin Login Pages",category:"Authentication",description:"Find admin login pages and panels",query:"inurl:admin inurl:login",risk:"medium",tags:["admin","login","panel"],usage:1247},{id:"2",name:"Database Files",category:"Database",description:"Exposed database files and backups",query:'filetype:sql "INSERT INTO" site:*.com',risk:"critical",tags:["database","sql","backup"],usage:892},{id:"3",name:"Config Files",category:"Configuration",description:"Configuration files with sensitive data",query:'filetype:conf OR filetype:config "password"',risk:"high",tags:["config","password","sensitive"],usage:756},{id:"4",name:"Directory Listing",category:"Directory",description:"Open directory listings",query:'intitle:"Index of /" "Parent Directory"',risk:"medium",tags:["directory","listing","open"],usage:634},{id:"5",name:"Log Files",category:"Logs",description:"Exposed log files with sensitive information",query:'filetype:log "password" OR "username"',risk:"high",tags:["logs","password","username"],usage:523},{id:"6",name:"PHP Info Pages",category:"Information",description:"PHP info pages revealing server information",query:'inurl:phpinfo.php "PHP Version"',risk:"medium",tags:["php","info","server"],usage:445},{id:"7",name:"Backup Files",category:"Backup",description:"Database and website backup files",query:'filetype:bak OR filetype:backup "database"',risk:"critical",tags:["backup","database","files"],usage:389},{id:"8",name:"Error Messages",category:"Errors",description:"Error messages revealing system information",query:'"Warning: mysql_connect()" "Access denied"',risk:"low",tags:["error","mysql","warning"],usage:312}],V=[{cveId:"CVE-2024-0001",title:"Critical SQL Injection in WebApp Framework",severity:"critical",cvssScore:9.8,publishedDate:"2024-01-15",affectedProducts:["WebApp Framework 2.x","WebApp Framework 3.x"],description:"A critical SQL injection vulnerability allows remote attackers to execute arbitrary SQL commands.",trending:!0},{cveId:"CVE-2024-0002",title:"Remote Code Execution in CMS Platform",severity:"critical",cvssScore:9.5,publishedDate:"2024-01-14",affectedProducts:["CMS Platform 1.0-4.2"],description:"Unauthenticated remote code execution via file upload functionality.",trending:!0},{cveId:"CVE-2024-0003",title:"Cross-Site Scripting in E-commerce Plugin",severity:"high",cvssScore:7.2,publishedDate:"2024-01-13",affectedProducts:["E-commerce Plugin 2.1","E-commerce Plugin 2.2"],description:"Stored XSS vulnerability in product review functionality.",trending:!1},{cveId:"CVE-2024-0004",title:"Authentication Bypass in API Gateway",severity:"high",cvssScore:8.1,publishedDate:"2024-01-12",affectedProducts:["API Gateway 1.x"],description:"Authentication bypass allows unauthorized access to protected endpoints.",trending:!1},{cveId:"CVE-2024-0005",title:"Path Traversal in File Manager",severity:"medium",cvssScore:6.5,publishedDate:"2024-01-11",affectedProducts:["File Manager Pro 3.0"],description:"Directory traversal vulnerability allows access to arbitrary files.",trending:!1}],I=[...new Set(A.map(e=>e.category))],T="all"===v?A:A.filter(e=>e.category===v),_=async e=>{D(!0),S([]);try{await new Promise(e=>setTimeout(e,2e3)),S([{id:"1",title:"Admin Login Panel - Company XYZ",url:"https://example.com/admin/login.php",snippet:"Admin login panel for Company XYZ management system. Please enter your credentials to access the admin area.",domain:"example.com",risk:"medium",timestamp:"2 hours ago"},{id:"2",title:"Database Backup File - backup.sql",url:"https://test-site.com/backup/backup.sql",snippet:"Database backup file containing user credentials and sensitive information. File size: 2.3MB",domain:"test-site.com",risk:"critical",timestamp:"5 hours ago"},{id:"3",title:"Configuration File - config.php",url:"https://demo.org/config/config.php",snippet:"Configuration file with database credentials and API keys exposed.",domain:"demo.org",risk:"high",timestamp:"1 day ago"}])}catch(e){console.error("Search error:",e)}finally{D(!1)}},F=e=>{E.includes(e)||q(s=>[...s,e])},R=e=>{navigator.clipboard.writeText(e)},G=e=>{switch(e){case"critical":return"text-red-400 bg-red-900/20";case"high":return"text-orange-400 bg-orange-900/20";case"medium":return"text-yellow-400 bg-yellow-900/20";case"low":return"text-blue-400 bg-blue-900/20";default:return"text-gray-400 bg-gray-900/20"}},L=e=>{switch(e){case"critical":return"text-red-400 bg-red-900/20";case"high":return"text-orange-400 bg-orange-900/20";case"medium":return"text-yellow-400 bg-yellow-900/20";case"low":return"text-blue-400 bg-blue-900/20";default:return"text-gray-400 bg-gray-900/20"}};return a.jsx(r.Z,{user:e,title:"Google Dorking & CVE Harian",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[a.jsx(n.Z,{className:"h-8 w-8 text-cyber-green"}),(0,a.jsxs)("h1",{className:"text-3xl font-bold font-cyber text-white",children:["Google ",a.jsx("span",{className:"cyber-text",children:"Dorking"})," & CVE Harian"]})]}),a.jsx("p",{className:"text-gray-400 max-w-3xl",children:"Advanced Google dorking dengan preset queries untuk security research dan CVE intelligence harian. Temukan exposed files, admin panels, dan vulnerability terbaru."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[a.jsx(l.Rm,{title:"Dork Presets",value:A.length,icon:c.Z,color:"green"}),a.jsx(l.Rm,{title:"Saved Queries",value:E.length,icon:d.Z,color:"blue"}),a.jsx(l.Rm,{title:"CVE Today",value:V.length,icon:o.Z,color:"red"}),a.jsx(l.Rm,{title:"Trending CVE",value:V.filter(e=>e.trending).length,icon:x.Z,color:"gold"})]}),a.jsx("div",{className:"mb-8",children:a.jsx("div",{className:"flex space-x-1 bg-gray-800/50 p-1 rounded-lg",children:[{id:"dorking",name:"Google Dorking",icon:n.Z},{id:"cve",name:"CVE Harian",icon:o.Z}].map(e=>{let i=e.icon;return(0,a.jsxs)("button",{onClick:()=>t(e.id),className:`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 ${s===e.id?"bg-cyber-green text-black":"text-gray-300 hover:text-white hover:bg-gray-700/50"}`,children:[a.jsx(i,{className:"h-4 w-4"}),a.jsx("span",{children:e.name})]},e.id)})})}),"dorking"===s&&(0,a.jsxs)("div",{className:"grid lg:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-3",children:[a.jsx(l.Zb,{border:"green",glow:!0,className:"mb-6",children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h2",{className:"text-xl font-bold text-white mb-4",children:"Custom Google Dork"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex space-x-3",children:[a.jsx("input",{type:"text",value:k,onChange:e=>C(e.target.value),placeholder:'Enter custom dork query (e.g., filetype:sql "INSERT INTO")',className:"cyber-input flex-1"}),a.jsx("button",{onClick:()=>_(k),disabled:Z||!k.trim(),className:"cyber-btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:Z?(0,a.jsxs)(a.Fragment,{children:[a.jsx(m.Z,{className:"h-4 w-4 mr-2 animate-spin"}),"Searching..."]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(h.Z,{className:"h-4 w-4 mr-2"}),"Search"]})})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>F(k),disabled:!k.trim(),className:"cyber-btn text-sm disabled:opacity-50",children:[a.jsx(g.Z,{className:"h-4 w-4 mr-2"}),"Save Query"]}),(0,a.jsxs)("button",{onClick:()=>R(k),disabled:!k.trim(),className:"cyber-btn text-sm disabled:opacity-50",children:[a.jsx(p.Z,{className:"h-4 w-4 mr-2"}),"Copy"]})]})]})]})}),P.length>0&&a.jsx(l.Zb,{className:"mb-6",children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Search Results"}),a.jsx("div",{className:"space-y-4",children:P.map(e=>(0,a.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-4 border border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[a.jsx("h4",{className:"font-semibold text-white",children:e.title}),a.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-semibold ${G(e.risk)}`,children:e.risk.toUpperCase()})]}),(0,a.jsxs)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"text-cyber-green hover:text-cyber-blue transition-colors text-sm",children:[e.url,a.jsx(u.Z,{className:"h-3 w-3 ml-1 inline"})]})]}),a.jsx("div",{className:"text-xs text-gray-400",children:e.timestamp})]}),a.jsx("p",{className:"text-gray-300 text-sm mb-2",children:e.snippet}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-400",children:[(0,a.jsxs)("span",{children:["Domain: ",e.domain]}),a.jsx("button",{className:"text-cyber-green hover:text-white transition-colors",children:a.jsx(b.Z,{className:"h-4 w-4"})})]})]},e.id))})]})}),a.jsx(l.Zb,{children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[a.jsx("h2",{className:"text-xl font-bold text-white",children:"Dork Presets"}),(0,a.jsxs)("select",{value:v,onChange:e=>w(e.target.value),className:"cyber-input text-sm",children:[a.jsx("option",{value:"all",children:"All Categories"}),I.map(e=>a.jsx("option",{value:e,children:e},e))]})]}),a.jsx("div",{className:"grid md:grid-cols-2 gap-4",children:T.map(e=>(0,a.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-4 border border-gray-700 hover:border-gray-600 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[a.jsx("h3",{className:"font-semibold text-white",children:e.name}),a.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-semibold ${G(e.risk)}`,children:e.risk.toUpperCase()})]}),a.jsx("p",{className:"text-sm text-gray-400",children:e.description})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-400",children:[e.usage," uses"]})]}),a.jsx("div",{className:"bg-gray-900/50 rounded p-3 mb-3",children:a.jsx("code",{className:"text-sm text-cyber-green",children:e.query})}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("div",{className:"flex flex-wrap gap-1",children:e.tags.map((e,s)=>a.jsx("span",{className:"px-2 py-1 bg-gray-700 text-gray-300 rounded text-xs",children:e},s))}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx("button",{onClick:()=>R(e.query),className:"text-gray-400 hover:text-white transition-colors",children:a.jsx(p.Z,{className:"h-4 w-4"})}),a.jsx("button",{onClick:()=>{C(e.query),_(e.query)},className:"text-gray-400 hover:text-cyber-green transition-colors",children:a.jsx(h.Z,{className:"h-4 w-4"})})]})]})]},e.id))})]})})]}),(0,a.jsxs)("div",{children:[a.jsx(l.Zb,{children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Saved Queries"}),0===E.length?a.jsx("p",{className:"text-gray-400 text-sm",children:"No saved queries yet"}):a.jsx("div",{className:"space-y-2",children:E.map((e,s)=>(0,a.jsxs)("div",{className:"bg-gray-800/50 rounded p-3 text-sm",children:[a.jsx("code",{className:"text-cyber-green",children:e}),(0,a.jsxs)("div",{className:"flex space-x-2 mt-2",children:[a.jsx("button",{onClick:()=>R(e),className:"text-gray-400 hover:text-white transition-colors",children:a.jsx(p.Z,{className:"h-3 w-3"})}),a.jsx("button",{onClick:()=>{C(e),_(e)},className:"text-gray-400 hover:text-cyber-green transition-colors",children:a.jsx(h.Z,{className:"h-3 w-3"})})]})]},s))})]})}),a.jsx(l.Zb,{className:"mt-6",children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Dorking Tips"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm text-gray-300",children:[(0,a.jsxs)("p",{children:["• Use ",a.jsx("code",{className:"text-cyber-green",children:"site:"})," to target specific domains"]}),(0,a.jsxs)("p",{children:["• ",a.jsx("code",{className:"text-cyber-green",children:"filetype:"})," searches for specific file types"]}),(0,a.jsxs)("p",{children:["• ",a.jsx("code",{className:"text-cyber-green",children:"inurl:"})," finds text in URLs"]}),(0,a.jsxs)("p",{children:["• ",a.jsx("code",{className:"text-cyber-green",children:"intitle:"})," searches page titles"]}),a.jsx("p",{children:"• Use quotes for exact phrases"}),a.jsx("p",{children:"• Combine operators with AND/OR"})]})]})})]})]}),"cve"===s&&(0,a.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[a.jsx("div",{className:"lg:col-span-2",children:a.jsx(l.Zb,{border:"red",glow:!0,children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[a.jsx("h2",{className:"text-xl font-bold text-white",children:"CVE Intelligence Harian"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-400",children:[a.jsx(j.Z,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:["Updated: ",new Date().toLocaleDateString()]})]})]}),a.jsx("div",{className:"space-y-4",children:V.map(e=>(0,a.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-4 border border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[a.jsx("h3",{className:"font-bold text-white",children:e.cveId}),a.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-semibold ${L(e.severity)}`,children:e.severity.toUpperCase()}),(0,a.jsxs)("span",{className:"px-2 py-1 bg-gray-700 text-gray-300 rounded-full text-xs font-semibold",children:["CVSS ",e.cvssScore]}),e.trending&&(0,a.jsxs)("span",{className:"px-2 py-1 bg-red-900/50 text-red-400 rounded-full text-xs font-semibold flex items-center",children:[a.jsx(x.Z,{className:"h-3 w-3 mr-1"}),"Trending"]})]}),a.jsx("h4",{className:"font-semibold text-white mb-2",children:e.title}),a.jsx("p",{className:"text-gray-300 text-sm mb-3",children:e.description})]}),a.jsx("div",{className:"text-xs text-gray-400",children:new Date(e.publishedDate).toLocaleDateString()})]}),(0,a.jsxs)("div",{className:"mb-3",children:[a.jsx("h5",{className:"text-sm font-semibold text-gray-300 mb-2",children:"Affected Products:"}),a.jsx("div",{className:"flex flex-wrap gap-2",children:e.affectedProducts.map((e,s)=>a.jsx("span",{className:"px-2 py-1 bg-blue-900/50 text-blue-400 rounded text-xs",children:e},s))})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("button",{className:"cyber-btn text-xs",children:[a.jsx(u.Z,{className:"h-3 w-3 mr-1"}),"View Details"]}),(0,a.jsxs)("button",{className:"cyber-btn text-xs",children:[a.jsx(n.Z,{className:"h-3 w-3 mr-1"}),"Search Exploits"]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx("button",{className:"text-gray-400 hover:text-yellow-400 transition-colors",children:a.jsx(y.Z,{className:"h-4 w-4"})}),a.jsx("button",{className:"text-gray-400 hover:text-white transition-colors",children:a.jsx(p.Z,{className:"h-4 w-4"})})]})]})]},e.cveId))})]})})}),(0,a.jsxs)("div",{children:[a.jsx(l.Zb,{children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"CVE Statistics"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Total CVE Today"}),a.jsx("span",{className:"text-white font-semibold",children:V.length})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Critical"}),a.jsx("span",{className:"text-red-400 font-semibold",children:V.filter(e=>"critical"===e.severity).length})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"High"}),a.jsx("span",{className:"text-orange-400 font-semibold",children:V.filter(e=>"high"===e.severity).length})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Medium"}),a.jsx("span",{className:"text-yellow-400 font-semibold",children:V.filter(e=>"medium"===e.severity).length})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Trending"}),a.jsx("span",{className:"text-cyber-green font-semibold",children:V.filter(e=>e.trending).length})]})]})]})}),a.jsx(l.Zb,{className:"mt-6",border:"gold",children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Quick Actions"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{className:"cyber-btn w-full text-sm",children:[a.jsx(m.Z,{className:"h-4 w-4 mr-2"}),"Refresh CVE Feed"]}),(0,a.jsxs)("button",{className:"cyber-btn w-full text-sm",children:[a.jsx(Download,{className:"h-4 w-4 mr-2"}),"Export CVE List"]}),(0,a.jsxs)("button",{className:"cyber-btn w-full text-sm",children:[a.jsx(f.Z,{className:"h-4 w-4 mr-2"}),"Setup Alerts"]})]})]})}),a.jsx(l.Zb,{className:"mt-6",children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"CVE Sources"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(N.Z,{className:"h-4 w-4 text-green-400"}),a.jsx("span",{className:"text-gray-300",children:"NIST NVD"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(N.Z,{className:"h-4 w-4 text-green-400"}),a.jsx("span",{className:"text-gray-300",children:"MITRE CVE"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(N.Z,{className:"h-4 w-4 text-green-400"}),a.jsx("span",{className:"text-gray-300",children:"Exploit-DB"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(N.Z,{className:"h-4 w-4 text-green-400"}),a.jsx("span",{className:"text-gray-300",children:"Security Advisories"})]})]})]})})]})]})]})})}},19495:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\dorking\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[216,592],()=>t(4882));module.exports=a})();