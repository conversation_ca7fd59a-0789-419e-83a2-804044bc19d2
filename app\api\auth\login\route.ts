import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, password } = body

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Email and password are required',
          code: 'MISSING_CREDENTIALS'
        },
        { status: 400 }
      )
    }

    // Demo credentials for testing
    const validCredentials = [
      { email: '<EMAIL>', password: 'admin123', role: 'super_admin', plan: 'cybersecurity' },
      { email: '<EMAIL>', password: 'user123', role: 'user', plan: 'hobby' },
      { email: '<EMAIL>', password: 'hunter123', role: 'user', plan: 'bughunter' }
    ]

    const user = validCredentials.find(
      cred => cred.email === email && cred.password === password
    )

    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid email or password',
          code: 'INVALID_CREDENTIALS'
        },
        { status: 401 }
      )
    }

    // Generate mock JWT token
    const token = `kxg_token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Mock user data
    const userData = {
      id: Math.random().toString(36).substr(2, 9),
      username: user.email.split('@')[0],
      email: user.email,
      role: user.role,
      plan: user.plan,
      isActive: true,
      emailVerified: true,
      createdAt: '2023-01-01T00:00:00Z',
      lastLogin: new Date().toISOString(),
      stats: {
        totalScans: Math.floor(Math.random() * 1000) + 100,
        vulnerabilitiesFound: Math.floor(Math.random() * 50) + 10,
        filesAnalyzed: Math.floor(Math.random() * 200) + 50,
        apiCalls: Math.floor(Math.random() * 10000) + 1000,
        score: Math.floor(Math.random() * 5000) + 1000,
        rank: Math.floor(Math.random() * 100) + 1
      }
    }

    const response = NextResponse.json({
      success: true,
      data: {
        token,
        user: userData,
        expiresIn: '24h'
      },
      message: 'Login successful',
      timestamp: new Date().toISOString()
    })

    // Set HTTP-only cookie for token
    response.cookies.set('auth-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 // 24 hours
    })

    return response

  } catch (error) {
    console.error('Login error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
