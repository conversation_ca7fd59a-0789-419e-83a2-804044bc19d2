(()=>{var e={};e.id=758,e.ids=[758],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},48289:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>h,tree:()=>c}),t(6688),t(30829),t(35866);var a=t(23191),l=t(88716),r=t(37922),n=t.n(r),i=t(95231),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(s,d);let c=["",{children:["bot",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,6688)),"D:\\Users\\Downloads\\kodeXGuard\\app\\bot\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\Users\\Downloads\\kodeXGuard\\app\\bot\\page.tsx"],m="/bot/page",x={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/bot/page",pathname:"/bot",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},56966:(e,s,t)=>{Promise.resolve().then(t.bind(t,90046))},90046:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>b});var a=t(10326),l=t(17577),r=t(83061),n=t(2262),i=t(54659),d=t(21405),c=t(91470),o=t(58038),m=t(66697),x=t(39730),h=t(50732),p=t(76557);let u=(0,p.Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);var g=t(24061),j=t(83855),y=t(98091);let v=(0,p.Z)("QrCode",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]]);function b(){let[e]=(0,l.useState)({username:"admin",avatar:"",role:"super_admin",plan:"cybersecurity"}),[s,t]=(0,l.useState)([{id:"1",type:"whatsapp",name:"KodeXGuard WhatsApp Bot",status:"connected",lastSeen:"2 minutes ago",messagesSent:1247,usersServed:89},{id:"2",type:"telegram",name:"KodeXGuard Telegram Bot",status:"disconnected",lastSeen:"1 hour ago",messagesSent:856,usersServed:156}]),[p,b]=(0,l.useState)([{id:"1",botId:"1",from:"+62812345678",message:"/scan https://example.com",response:"Starting vulnerability scan for https://example.com...",timestamp:"2024-01-15T10:30:00Z",type:"scan"},{id:"2",botId:"1",from:"+62812345679",message:"/status",response:"Your plan: Bug Hunter\nDaily scans: Unlimited\nAPI calls: 8,456/10,000",timestamp:"2024-01-15T10:25:00Z",type:"status"},{id:"3",botId:"2",from:"@username123",message:"/help",response:"Available commands:\n/scan <url> - Start vulnerability scan\n/status - Check your plan status\n/help - Show this help",timestamp:"2024-01-15T10:20:00Z",type:"help"}]),[N,w]=(0,l.useState)(!1),[f,k]=(0,l.useState)(null),[Z,M]=(0,l.useState)(""),[S,C]=(0,l.useState)("whatsapp"),B={totalBots:s.length,activeBots:s.filter(e=>"connected"===e.status).length,totalMessages:p.length,totalUsers:s.reduce((e,s)=>e+s.usersServed,0)},I=e=>{t(s=>s.map(s=>s.id===e?{...s,status:"connecting"}:s)),setTimeout(()=>{t(s=>s.map(s=>s.id===e?{...s,status:"connected",lastSeen:"Just now",qrCode:"whatsapp"===s.type?P():void 0}:s)),s.find(s=>s.id===e)?.type==="whatsapp"&&(w(!0),k(e))},2e3)},A=e=>{t(s=>s.map(s=>s.id===e?{...s,status:"disconnected"}:s))},P=()=>"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2ZmZiIvPgogIDx0ZXh0IHg9IjEwMCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LXNpemU9IjE0Ij5RUiBDb2RlPC90ZXh0Pgo8L3N2Zz4K",_=e=>{t(s=>s.filter(s=>s.id!==e)),b(s=>s.filter(s=>s.botId!==e))},D=e=>{switch(e){case"connected":return i.Z;case"connecting":return d.Z;default:return c.Z}},q=e=>{switch(e){case"connected":return"text-green-400";case"connecting":return"text-yellow-400";default:return"text-red-400"}},R=e=>{switch(e){case"scan":return o.Z;case"status":return m.Z;case"help":return x.Z;default:return c.Z}};return a.jsx(r.Z,{user:e,title:"Bot Center",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[a.jsx(h.Z,{className:"h-8 w-8 text-cyber-green"}),(0,a.jsxs)("h1",{className:"text-3xl font-bold font-cyber text-white",children:["Bot ",a.jsx("span",{className:"cyber-text",children:"Center"})]})]}),a.jsx("p",{className:"text-gray-400 max-w-3xl",children:"Kelola bot WhatsApp dan Telegram untuk eksekusi scan, monitoring, dan notifikasi real-time. Hanya Super Admin yang dapat menghubungkan bot, namun semua user dapat menggunakan sesuai plan."})]}),"super_admin"!==e.role&&a.jsx("div",{className:"mb-8",children:a.jsx(n.XO,{type:"warning",title:"Limited Access",message:"Only Super Admins can manage bot connections. You can view bot status and message history."})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[a.jsx(n.Rm,{title:"Total Bots",value:B.totalBots,icon:h.Z,color:"green"}),a.jsx(n.Rm,{title:"Active Bots",value:B.activeBots,icon:i.Z,color:"blue"}),a.jsx(n.Rm,{title:"Messages Sent",value:B.totalMessages,icon:u,color:"purple"}),a.jsx(n.Rm,{title:"Users Served",value:B.totalUsers,icon:g.Z,color:"gold"})]}),(0,a.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2",children:["super_admin"===e.role&&a.jsx(n.Zb,{border:"green",glow:!0,className:"mb-6",children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h2",{className:"text-xl font-bold text-white mb-4",children:"Add New Bot"}),(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Bot Name"}),a.jsx("input",{type:"text",value:Z,onChange:e=>M(e.target.value),placeholder:"My Bot",className:"cyber-input w-full"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Bot Type"}),(0,a.jsxs)("select",{value:S,onChange:e=>C(e.target.value),className:"cyber-input w-full",children:[a.jsx("option",{value:"whatsapp",children:"WhatsApp"}),a.jsx("option",{value:"telegram",children:"Telegram"})]})]}),a.jsx("div",{className:"flex items-end",children:(0,a.jsxs)("button",{onClick:()=>{if(!Z.trim())return;let e={id:Date.now().toString(),type:S,name:Z,status:"disconnected",lastSeen:"Never",messagesSent:0,usersServed:0};t(s=>[...s,e]),M("")},disabled:!Z.trim(),className:"cyber-btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed",children:[a.jsx(j.Z,{className:"h-4 w-4 mr-2"}),"Add Bot"]})})]})]})}),a.jsx(n.Zb,{children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h2",{className:"text-xl font-bold text-white mb-6",children:"Bot Configurations"}),a.jsx("div",{className:"space-y-4",children:s.map(s=>{let t=D(s.status);return(0,a.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-4 border border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:["whatsapp"===s.type?a.jsx(x.Z,{className:"h-6 w-6 text-green-400"}):a.jsx(u,{className:"h-6 w-6 text-blue-400"}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-semibold text-white",children:s.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-400 capitalize",children:[s.type," Bot"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(t,{className:`h-5 w-5 ${q(s.status)} ${"connecting"===s.status?"animate-spin":""}`}),a.jsx("span",{className:`text-sm font-medium ${q(s.status)}`,children:s.status.charAt(0).toUpperCase()+s.status.slice(1)})]}),"super_admin"===e.role&&(0,a.jsxs)("div",{className:"flex space-x-2",children:["connected"===s.status?a.jsx("button",{onClick:()=>A(s.id),className:"text-red-400 hover:text-red-300 transition-colors",children:a.jsx(c.Z,{className:"h-5 w-5"})}):a.jsx("button",{onClick:()=>I(s.id),disabled:"connecting"===s.status,className:"text-green-400 hover:text-green-300 transition-colors disabled:opacity-50",children:a.jsx(i.Z,{className:"h-5 w-5"})}),a.jsx("button",{onClick:()=>_(s.id),className:"text-red-400 hover:text-red-300 transition-colors",children:a.jsx(y.Z,{className:"h-5 w-5"})})]})]})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-gray-400",children:"Last Seen:"}),a.jsx("span",{className:"text-white ml-2",children:s.lastSeen})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-gray-400",children:"Messages:"}),a.jsx("span",{className:"text-white ml-2",children:s.messagesSent.toLocaleString()})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-gray-400",children:"Users:"}),a.jsx("span",{className:"text-white ml-2",children:s.usersServed.toLocaleString()})]})]}),"connected"===s.status&&a.jsx("div",{className:"mt-4 p-3 bg-green-900/20 rounded-lg border border-green-700",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(i.Z,{className:"h-4 w-4 text-green-400"}),a.jsx("span",{className:"text-sm text-green-400 font-medium",children:"Bot is online and ready to receive commands"})]})})]},s.id)})})]})}),a.jsx(n.Zb,{className:"mt-6",children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h3",{className:"text-lg font-bold text-white mb-6",children:"Recent Bot Messages"}),a.jsx("div",{className:"space-y-4",children:p.map(e=>{let t=R(e.type),l=s.find(s=>s.id===e.botId);return(0,a.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-4 border border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(t,{className:"h-5 w-5 text-cyber-green"}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium text-white",children:e.from}),(0,a.jsxs)("div",{className:"text-sm text-gray-400",children:["via ",l?.name," • ",new Date(e.timestamp).toLocaleString()]})]})]}),a.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-semibold ${"scan"===e.type?"bg-blue-900/50 text-blue-400":"status"===e.type?"bg-green-900/50 text-green-400":"help"===e.type?"bg-purple-900/50 text-purple-400":"bg-red-900/50 text-red-400"}`,children:e.type})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-sm text-gray-400",children:"Message:"}),a.jsx("div",{className:"text-sm text-gray-300 bg-gray-900/50 rounded p-2 mt-1",children:e.message})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-sm text-gray-400",children:"Response:"}),a.jsx("div",{className:"text-sm text-cyber-green bg-gray-900/50 rounded p-2 mt-1",children:e.response})]})]})]},e.id)})})]})})]}),(0,a.jsxs)("div",{children:[a.jsx(n.Zb,{children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Available Commands"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"p-3 bg-gray-800/50 rounded-lg",children:[a.jsx("div",{className:"font-medium text-white",children:"/scan <url>"}),a.jsx("div",{className:"text-sm text-gray-400",children:"Start vulnerability scan"})]}),(0,a.jsxs)("div",{className:"p-3 bg-gray-800/50 rounded-lg",children:[a.jsx("div",{className:"font-medium text-white",children:"/status"}),a.jsx("div",{className:"text-sm text-gray-400",children:"Check plan status and usage"})]}),(0,a.jsxs)("div",{className:"p-3 bg-gray-800/50 rounded-lg",children:[a.jsx("div",{className:"font-medium text-white",children:"/help"}),a.jsx("div",{className:"text-sm text-gray-400",children:"Show available commands"})]}),(0,a.jsxs)("div",{className:"p-3 bg-gray-800/50 rounded-lg",children:[a.jsx("div",{className:"font-medium text-white",children:"/results <id>"}),a.jsx("div",{className:"text-sm text-gray-400",children:"Get scan results"})]})]})]})}),N&&f&&a.jsx(n.Zb,{className:"mt-6",border:"green",glow:!0,children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[a.jsx("h3",{className:"text-lg font-bold text-white",children:"WhatsApp QR Code"}),a.jsx("button",{onClick:()=>w(!1),className:"text-gray-400 hover:text-white",children:"\xd7"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"bg-white p-4 rounded-lg inline-block mb-4",children:a.jsx(v,{className:"h-32 w-32 text-black"})}),a.jsx("p",{className:"text-sm text-gray-400 mb-4",children:"Scan this QR code with WhatsApp to connect the bot"}),(0,a.jsxs)("button",{className:"cyber-btn text-sm w-full",children:[a.jsx(d.Z,{className:"h-4 w-4 mr-2"}),"Regenerate QR"]})]})]})}),a.jsx(n.Zb,{className:"mt-6",border:"gold",children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Bot Usage"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Plan Access"}),a.jsx("span",{className:"text-cyber-green",children:"✓ Enabled"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Daily Commands"}),a.jsx("span",{className:"text-white",children:"Unlimited"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Scan via Bot"}),a.jsx("span",{className:"text-cyber-green",children:"✓ Enabled"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Real-time Notifications"}),a.jsx("span",{className:"text-cyber-green",children:"✓ Enabled"})]})]})]})})]})]})]})})}},66697:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},50732:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},54659:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},39730:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]])},83855:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},21405:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},58038:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},98091:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},24061:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},91470:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},6688:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\bot\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[9276,82,7608,3061],()=>t(48289));module.exports=a})();