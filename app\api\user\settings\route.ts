import { NextRequest, NextResponse } from 'next/server'

interface UserSettings {
  notifications: {
    email: boolean
    push: boolean
    scanComplete: boolean
    quotaWarning: boolean
    securityAlerts: boolean
    weeklyReport: boolean
    marketingEmails: boolean
  }
  security: {
    twoFactorAuth: boolean
    sessionTimeout: number // in hours
    ipWhitelist: string[]
    loginNotifications: boolean
    passwordExpiry: number // in days
  }
  preferences: {
    theme: 'dark' | 'light' | 'auto'
    language: string
    timezone: string
    dateFormat: string
    timeFormat: '12h' | '24h'
    autoSave: boolean
  }
  api: {
    rateLimit: number
    allowedOrigins: string[]
    webhookUrl?: string
    enableLogging: boolean
  }
  privacy: {
    profileVisibility: 'public' | 'private' | 'team'
    shareUsageStats: boolean
    allowAnalytics: boolean
    dataRetention: number // in days
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Simple token validation for testing
    if (!token.startsWith('kxg_')) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    // Mock user settings data
    const userSettings: UserSettings = {
      notifications: {
        email: true,
        push: true,
        scanComplete: true,
        quotaWarning: true,
        securityAlerts: true,
        weeklyReport: false,
        marketingEmails: false
      },
      security: {
        twoFactorAuth: false,
        sessionTimeout: 24,
        ipWhitelist: ['*************', '*********'],
        loginNotifications: true,
        passwordExpiry: 90
      },
      preferences: {
        theme: 'dark',
        language: 'id',
        timezone: 'Asia/Jakarta',
        dateFormat: 'DD/MM/YYYY',
        timeFormat: '24h',
        autoSave: true
      },
      api: {
        rateLimit: 1000,
        allowedOrigins: ['https://kodexguard.com', 'https://app.kodexguard.com'],
        webhookUrl: 'https://webhook.example.com/kodexguard',
        enableLogging: true
      },
      privacy: {
        profileVisibility: 'private',
        shareUsageStats: false,
        allowAnalytics: true,
        dataRetention: 365
      }
    }

    return NextResponse.json({
      success: true,
      data: userSettings,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('User settings API error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Simple token validation for testing
    if (!token.startsWith('kxg_')) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    const body = await request.json()
    const { notifications, security, preferences, api, privacy } = body

    // Validate input
    if (!notifications && !security && !preferences && !api && !privacy) {
      return NextResponse.json({
        success: false,
        error: 'At least one settings category is required'
      }, { status: 400 })
    }

    // In a real app, update the user settings in database
    // For demo, just return success with updated data
    
    const updatedSettings = {
      notifications: notifications || {
        email: true,
        push: true,
        scanComplete: true,
        quotaWarning: true,
        securityAlerts: true,
        weeklyReport: false,
        marketingEmails: false
      },
      security: security || {
        twoFactorAuth: false,
        sessionTimeout: 24,
        ipWhitelist: ['*************'],
        loginNotifications: true,
        passwordExpiry: 90
      },
      preferences: preferences || {
        theme: 'dark',
        language: 'id',
        timezone: 'Asia/Jakarta',
        dateFormat: 'DD/MM/YYYY',
        timeFormat: '24h',
        autoSave: true
      },
      api: api || {
        rateLimit: 1000,
        allowedOrigins: ['https://kodexguard.com'],
        enableLogging: true
      },
      privacy: privacy || {
        profileVisibility: 'private',
        shareUsageStats: false,
        allowAnalytics: true,
        dataRetention: 365
      },
      updatedAt: new Date().toISOString()
    }

    return NextResponse.json({
      success: true,
      data: updatedSettings,
      message: 'Settings updated successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Update settings error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Simple token validation for testing
    if (!token.startsWith('kxg_')) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    const body = await request.json()
    const { category, setting, value } = body

    // Validate input
    if (!category || !setting || value === undefined) {
      return NextResponse.json({
        success: false,
        error: 'Category, setting, and value are required'
      }, { status: 400 })
    }

    // In a real app, update specific setting in database
    // For demo, just return success
    
    return NextResponse.json({
      success: true,
      message: `Setting ${category}.${setting} updated to ${value}`,
      data: {
        category,
        setting,
        value,
        updatedAt: new Date().toISOString()
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Patch setting error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}
