"use strict";(()=>{var e={};e.id=663,e.ids=[663],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},93601:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>m,patchFetch:()=>f,requestAsyncStorage:()=>c,routeModule:()=>p,serverHooks:()=>l,staticGenerationAsyncStorage:()=>d});var a={};t.r(a),t.d(a,{POST:()=>u});var s=t(49303),o=t(88716),n=t(60670),i=t(87070);async function u(e){try{let r=e.headers.get("authorization"),t=r?.replace("Bearer ","");if(!t)return i.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!t.startsWith("kxg_"))return i.NextResponse.json({success:!1,error:"Invalid token"},{status:401});return i.NextResponse.json({success:!0,message:"All notifications marked as read",timestamp:new Date().toISOString()})}catch(e){return console.error("Mark all notifications as read error:",e),i.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/notifications/mark-all-read/route",pathname:"/api/notifications/mark-all-read",filename:"route",bundlePath:"app/api/notifications/mark-all-read/route"},resolvedPagePath:"D:\\Users\\Downloads\\kodeXGuard\\app\\api\\notifications\\mark-all-read\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:c,staticGenerationAsyncStorage:d,serverHooks:l}=p,m="/api/notifications/mark-all-read/route";function f(){return(0,n.patchFetch)({serverHooks:l,staticGenerationAsyncStorage:d})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[216,592],()=>t(93601));module.exports=a})();