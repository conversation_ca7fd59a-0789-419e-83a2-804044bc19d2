/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/settings/page";
exports.ids = ["app/settings/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'settings',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/settings/page.tsx */ \"(rsc)/./app/settings/page.tsx\")), \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/settings/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/settings/page\",\n        pathname: \"/settings\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/settings/page.tsx */ \"(ssr)/./app/settings/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNVc2VycyU1QyU1Q0Rvd25sb2FkcyU1QyU1Q2tvZGVYR3VhcmQlNUMlNUNhcHAlNUMlNUNzZXR0aW5ncyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwSkFBOEYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb2RleGd1YXJkLz9mZGM0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcVXNlcnNcXFxcRG93bmxvYWRzXFxcXGtvZGVYR3VhcmRcXFxcYXBwXFxcXHNldHRpbmdzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/settings/page.tsx":
/*!*******************************!*\
  !*** ./app/settings/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SettingsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(ssr)/./components/DashboardLayout.tsx\");\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Card */ \"(ssr)/./components/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_Lock_RefreshCw_Save_Settings_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Lock,RefreshCw,Save,Settings,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Lock_RefreshCw_Save_Settings_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Lock,RefreshCw,Save,Settings,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Lock_RefreshCw_Save_Settings_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Lock,RefreshCw,Save,Settings,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Lock_RefreshCw_Save_Settings_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Lock,RefreshCw,Save,Settings,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Lock_RefreshCw_Save_Settings_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Lock,RefreshCw,Save,Settings,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Lock_RefreshCw_Save_Settings_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Lock,RefreshCw,Save,Settings,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction SettingsPage() {\n    const [user] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: \"admin\",\n        avatar: \"\",\n        role: \"super_admin\",\n        plan: \"cybersecurity\"\n    });\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"general\");\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        general: {\n            language: \"id\",\n            timezone: \"Asia/Jakarta\",\n            theme: \"dark\",\n            autoSave: true\n        },\n        notifications: {\n            emailNotifications: true,\n            pushNotifications: true,\n            scanAlerts: true,\n            securityAlerts: true,\n            weeklyReport: false,\n            marketingEmails: false\n        },\n        security: {\n            twoFactorAuth: false,\n            sessionTimeout: 30,\n            loginAlerts: true,\n            apiKeyExpiry: 365,\n            passwordExpiry: 90\n        },\n        privacy: {\n            profileVisibility: \"private\",\n            activityTracking: true,\n            dataSharing: false,\n            analyticsOptOut: false\n        }\n    });\n    const [showApiKey, setShowApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSettingChange = (category, key, value)=>{\n        setSettings((prev)=>({\n                ...prev,\n                [category]: {\n                    ...prev[category],\n                    [key]: value\n                }\n            }));\n    };\n    const saveSettings = async ()=>{\n        setSaving(true);\n        // Simulate API call\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        setSaving(false);\n    };\n    const resetSettings = ()=>{\n        // Reset to default settings\n        setSettings({\n            general: {\n                language: \"id\",\n                timezone: \"Asia/Jakarta\",\n                theme: \"dark\",\n                autoSave: true\n            },\n            notifications: {\n                emailNotifications: true,\n                pushNotifications: true,\n                scanAlerts: true,\n                securityAlerts: true,\n                weeklyReport: false,\n                marketingEmails: false\n            },\n            security: {\n                twoFactorAuth: false,\n                sessionTimeout: 30,\n                loginAlerts: true,\n                apiKeyExpiry: 365,\n                passwordExpiry: 90\n            },\n            privacy: {\n                profileVisibility: \"private\",\n                activityTracking: true,\n                dataSharing: false,\n                analyticsOptOut: false\n            }\n        });\n    };\n    const tabs = [\n        {\n            id: \"general\",\n            name: \"General\",\n            icon: _barrel_optimize_names_Bell_Lock_RefreshCw_Save_Settings_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            id: \"notifications\",\n            name: \"Notifications\",\n            icon: _barrel_optimize_names_Bell_Lock_RefreshCw_Save_Settings_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            id: \"security\",\n            name: \"Security\",\n            icon: _barrel_optimize_names_Bell_Lock_RefreshCw_Save_Settings_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            id: \"privacy\",\n            name: \"Privacy\",\n            icon: _barrel_optimize_names_Bell_Lock_RefreshCw_Save_Settings_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        user: user,\n        title: \"Settings\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Lock_RefreshCw_Save_Settings_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-8 w-8 text-cyber-green\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold font-cyber text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"cyber-text\",\n                                        children: \"Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 max-w-3xl\",\n                            children: \"Kelola pengaturan akun, notifikasi, keamanan, dan privasi untuk pengalaman KodeXGuard yang optimal.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-1 bg-gray-800/50 p-1 rounded-lg\",\n                        children: tabs.map((tab)=>{\n                            const Icon = tab.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(tab.id),\n                                className: `flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${activeTab === tab.id ? \"bg-cyber-green text-black\" : \"text-gray-300 hover:text-white hover:bg-gray-700/50\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: tab.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3\",\n                            children: [\n                                activeTab === \"general\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    border: \"green\",\n                                    glow: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-bold text-white mb-6\",\n                                                children: \"General Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid md:grid-cols-2 gap-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"Language\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 176,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: settings.general.language,\n                                                                        onChange: (e)=>handleSettingChange(\"general\", \"language\", e.target.value),\n                                                                        className: \"cyber-input w-full\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"id\",\n                                                                                children: \"Bahasa Indonesia\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 184,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"en\",\n                                                                                children: \"English\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 185,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 179,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 175,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"Timezone\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 190,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: settings.general.timezone,\n                                                                        onChange: (e)=>handleSettingChange(\"general\", \"timezone\", e.target.value),\n                                                                        className: \"cyber-input w-full\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"Asia/Jakarta\",\n                                                                                children: \"Asia/Jakarta (WIB)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 198,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"Asia/Makassar\",\n                                                                                children: \"Asia/Makassar (WITA)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 199,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"Asia/Jayapura\",\n                                                                                children: \"Asia/Jayapura (WIT)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 200,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"UTC\",\n                                                                                children: \"UTC\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 201,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 193,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 189,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                children: \"Theme\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-4\",\n                                                                children: [\n                                                                    \"dark\",\n                                                                    \"light\",\n                                                                    \"auto\"\n                                                                ].map((theme)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"flex items-center space-x-2 cursor-pointer\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"radio\",\n                                                                                name: \"theme\",\n                                                                                value: theme,\n                                                                                checked: settings.general.theme === theme,\n                                                                                onChange: (e)=>handleSettingChange(\"general\", \"theme\", e.target.value),\n                                                                                className: \"text-cyber-green\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 213,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-300 capitalize\",\n                                                                                children: theme\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 221,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, theme, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 212,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-medium text-white\",\n                                                                        children: \"Auto Save\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 229,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-400\",\n                                                                        children: \"Automatically save your work\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 230,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        checked: settings.general.autoSave,\n                                                                        onChange: (e)=>handleSettingChange(\"general\", \"autoSave\", e.target.checked),\n                                                                        className: \"sr-only peer\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 233,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyber-green\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 239,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, this),\n                                activeTab === \"notifications\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    border: \"blue\",\n                                    glow: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-bold text-white mb-6\",\n                                                children: \"Notification Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: Object.entries(settings.notifications).map(([key, value])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-medium text-white capitalize\",\n                                                                        children: key.replace(/([A-Z])/g, \" $1\").trim()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-400\",\n                                                                        children: [\n                                                                            key === \"emailNotifications\" && \"Receive notifications via email\",\n                                                                            key === \"pushNotifications\" && \"Receive push notifications in browser\",\n                                                                            key === \"scanAlerts\" && \"Get notified when scans complete\",\n                                                                            key === \"securityAlerts\" && \"Important security notifications\",\n                                                                            key === \"weeklyReport\" && \"Weekly activity summary\",\n                                                                            key === \"marketingEmails\" && \"Product updates and promotions\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 260,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        checked: value,\n                                                                        onChange: (e)=>handleSettingChange(\"notifications\", key, e.target.checked),\n                                                                        className: \"sr-only peer\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 270,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyber-green\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 276,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, key, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 15\n                                }, this),\n                                activeTab === \"security\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    border: \"red\",\n                                    glow: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-bold text-white mb-6\",\n                                                children: \"Security Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-medium text-white\",\n                                                                        children: \"Two-Factor Authentication\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 294,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-400\",\n                                                                        children: \"Add an extra layer of security\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 295,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        checked: settings.security.twoFactorAuth,\n                                                                        onChange: (e)=>handleSettingChange(\"security\", \"twoFactorAuth\", e.target.checked),\n                                                                        className: \"sr-only peer\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 298,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyber-green\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 304,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid md:grid-cols-2 gap-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"Session Timeout (minutes)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 310,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        value: settings.security.sessionTimeout,\n                                                                        onChange: (e)=>handleSettingChange(\"security\", \"sessionTimeout\", parseInt(e.target.value)),\n                                                                        className: \"cyber-input w-full\",\n                                                                        min: \"5\",\n                                                                        max: \"480\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 313,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"API Key Expiry (days)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 324,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        value: settings.security.apiKeyExpiry,\n                                                                        onChange: (e)=>handleSettingChange(\"security\", \"apiKeyExpiry\", parseInt(e.target.value)),\n                                                                        className: \"cyber-input w-full\",\n                                                                        min: \"30\",\n                                                                        max: \"365\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 327,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-medium text-white\",\n                                                                        children: \"Login Alerts\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-400\",\n                                                                        children: \"Get notified of new login attempts\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 341,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        checked: settings.security.loginAlerts,\n                                                                        onChange: (e)=>handleSettingChange(\"security\", \"loginAlerts\", e.target.checked),\n                                                                        className: \"sr-only peer\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 344,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyber-green\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 350,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 15\n                                }, this),\n                                activeTab === \"privacy\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    border: \"purple\",\n                                    glow: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-bold text-white mb-6\",\n                                                children: \"Privacy Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                children: \"Profile Visibility\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: settings.privacy.profileVisibility,\n                                                                onChange: (e)=>handleSettingChange(\"privacy\", \"profileVisibility\", e.target.value),\n                                                                className: \"cyber-input w-full\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"public\",\n                                                                        children: \"Public\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 374,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"private\",\n                                                                        children: \"Private\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 375,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"friends\",\n                                                                        children: \"Friends Only\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 376,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-medium text-white\",\n                                                                        children: \"Activity Tracking\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 382,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-400\",\n                                                                        children: \"Allow tracking for analytics\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        checked: settings.privacy.activityTracking,\n                                                                        onChange: (e)=>handleSettingChange(\"privacy\", \"activityTracking\", e.target.checked),\n                                                                        className: \"sr-only peer\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 386,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyber-green\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 392,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-medium text-white\",\n                                                                        children: \"Data Sharing\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 398,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-400\",\n                                                                        children: \"Share anonymized data for research\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 399,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        checked: settings.privacy.dataSharing,\n                                                                        onChange: (e)=>handleSettingChange(\"privacy\", \"dataSharing\", e.target.checked),\n                                                                        className: \"sr-only peer\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 402,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyber-green\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 408,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-white mb-4\",\n                                                children: \"Quick Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: saveSettings,\n                                                        disabled: saving,\n                                                        className: \"cyber-btn-primary w-full disabled:opacity-50\",\n                                                        children: saving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Lock_RefreshCw_Save_Settings_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 430,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Saving...\"\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Lock_RefreshCw_Save_Settings_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 435,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Save Settings\"\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: resetSettings,\n                                                        className: \"cyber-btn w-full\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Lock_RefreshCw_Save_Settings_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Reset to Default\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-white mb-4\",\n                                                children: \"Account Info\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"Plan:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-cyber-green font-semibold\",\n                                                                children: user.plan.toUpperCase()\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"Role:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white\",\n                                                                children: user.role.replace(\"_\", \" \").toUpperCase()\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400\",\n                                                                children: \"Status:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-400\",\n                                                                children: \"Active\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n            lineNumber: 128,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\settings\\\\page.tsx\",\n        lineNumber: 127,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvc2V0dGluZ3MvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFZ0M7QUFDMEI7QUFDRztBQW9CeEM7QUFFTixTQUFTUztJQUN0QixNQUFNLENBQUNDLEtBQUssR0FBR1YsK0NBQVFBLENBQUM7UUFDdEJXLFVBQVU7UUFDVkMsUUFBUTtRQUNSQyxNQUFNO1FBQ05DLE1BQU07SUFDUjtJQUVBLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHaEIsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDaUIsVUFBVUMsWUFBWSxHQUFHbEIsK0NBQVFBLENBQUM7UUFDdkNtQixTQUFTO1lBQ1BDLFVBQVU7WUFDVkMsVUFBVTtZQUNWQyxPQUFPO1lBQ1BDLFVBQVU7UUFDWjtRQUNBQyxlQUFlO1lBQ2JDLG9CQUFvQjtZQUNwQkMsbUJBQW1CO1lBQ25CQyxZQUFZO1lBQ1pDLGdCQUFnQjtZQUNoQkMsY0FBYztZQUNkQyxpQkFBaUI7UUFDbkI7UUFDQUMsVUFBVTtZQUNSQyxlQUFlO1lBQ2ZDLGdCQUFnQjtZQUNoQkMsYUFBYTtZQUNiQyxjQUFjO1lBQ2RDLGdCQUFnQjtRQUNsQjtRQUNBQyxTQUFTO1lBQ1BDLG1CQUFtQjtZQUNuQkMsa0JBQWtCO1lBQ2xCQyxhQUFhO1lBQ2JDLGlCQUFpQjtRQUNuQjtJQUNGO0lBRUEsTUFBTSxDQUFDQyxZQUFZQyxjQUFjLEdBQUczQywrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUM0QyxRQUFRQyxVQUFVLEdBQUc3QywrQ0FBUUEsQ0FBQztJQUVyQyxNQUFNOEMsc0JBQXNCLENBQUNDLFVBQWtCQyxLQUFhQztRQUMxRC9CLFlBQVlnQyxDQUFBQSxPQUFTO2dCQUNuQixHQUFHQSxJQUFJO2dCQUNQLENBQUNILFNBQVMsRUFBRTtvQkFDVixHQUFHRyxJQUFJLENBQUNILFNBQThCO29CQUN0QyxDQUFDQyxJQUFJLEVBQUVDO2dCQUNUO1lBQ0Y7SUFDRjtJQUVBLE1BQU1FLGVBQWU7UUFDbkJOLFVBQVU7UUFDVixvQkFBb0I7UUFDcEIsTUFBTSxJQUFJTyxRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTO1FBQ2pEUixVQUFVO0lBQ1o7SUFFQSxNQUFNVSxnQkFBZ0I7UUFDcEIsNEJBQTRCO1FBQzVCckMsWUFBWTtZQUNWQyxTQUFTO2dCQUNQQyxVQUFVO2dCQUNWQyxVQUFVO2dCQUNWQyxPQUFPO2dCQUNQQyxVQUFVO1lBQ1o7WUFDQUMsZUFBZTtnQkFDYkMsb0JBQW9CO2dCQUNwQkMsbUJBQW1CO2dCQUNuQkMsWUFBWTtnQkFDWkMsZ0JBQWdCO2dCQUNoQkMsY0FBYztnQkFDZEMsaUJBQWlCO1lBQ25CO1lBQ0FDLFVBQVU7Z0JBQ1JDLGVBQWU7Z0JBQ2ZDLGdCQUFnQjtnQkFDaEJDLGFBQWE7Z0JBQ2JDLGNBQWM7Z0JBQ2RDLGdCQUFnQjtZQUNsQjtZQUNBQyxTQUFTO2dCQUNQQyxtQkFBbUI7Z0JBQ25CQyxrQkFBa0I7Z0JBQ2xCQyxhQUFhO2dCQUNiQyxpQkFBaUI7WUFDbkI7UUFDRjtJQUNGO0lBRUEsTUFBTWUsT0FBTztRQUNYO1lBQUVDLElBQUk7WUFBV0MsTUFBTTtZQUFXQyxNQUFNeEQsb0hBQVFBO1FBQUM7UUFDakQ7WUFBRXNELElBQUk7WUFBaUJDLE1BQU07WUFBaUJDLE1BQU10RCxvSEFBSUE7UUFBQztRQUN6RDtZQUFFb0QsSUFBSTtZQUFZQyxNQUFNO1lBQVlDLE1BQU12RCxvSEFBTUE7UUFBQztRQUNqRDtZQUFFcUQsSUFBSTtZQUFXQyxNQUFNO1lBQVdDLE1BQU1yRCxvSEFBSUE7UUFBQztLQUM5QztJQUVELHFCQUNFLDhEQUFDTCxtRUFBZUE7UUFBQ1MsTUFBTUE7UUFBTWtELE9BQU07a0JBQ2pDLDRFQUFDQzs7OEJBRUMsOERBQUNBO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDM0Qsb0hBQVFBO29DQUFDMkQsV0FBVTs7Ozs7OzhDQUNwQiw4REFBQ0M7b0NBQUdELFdBQVU7OENBQ1osNEVBQUNFO3dDQUFLRixXQUFVO2tEQUFhOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FHakMsOERBQUNHOzRCQUFFSCxXQUFVO3NDQUEwQjs7Ozs7Ozs7Ozs7OzhCQU16Qyw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVO2tDQUNaTixLQUFLVSxHQUFHLENBQUMsQ0FBQ0M7NEJBQ1QsTUFBTUMsT0FBT0QsSUFBSVIsSUFBSTs0QkFDckIscUJBQ0UsOERBQUNVO2dDQUVDQyxTQUFTLElBQU10RCxhQUFhbUQsSUFBSVYsRUFBRTtnQ0FDbENLLFdBQVcsQ0FBQyx5RkFBeUYsRUFDbkcvQyxjQUFjb0QsSUFBSVYsRUFBRSxHQUNoQiw4QkFDQSxzREFDTCxDQUFDOztrREFFRiw4REFBQ1c7d0NBQUtOLFdBQVU7Ozs7OztrREFDaEIsOERBQUNFO2tEQUFNRyxJQUFJVCxJQUFJOzs7Ozs7OytCQVRWUyxJQUFJVixFQUFFOzs7Ozt3QkFZakI7Ozs7Ozs7Ozs7OzhCQUlKLDhEQUFDSTtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOztnQ0FFWi9DLGNBQWMsMkJBQ2IsOERBQUNiLGtEQUFJQTtvQ0FBQ3FFLFFBQU87b0NBQVFDLElBQUk7OENBQ3ZCLDRFQUFDWDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNXO2dEQUFHWCxXQUFVOzBEQUFvQzs7Ozs7OzBEQUVsRCw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEOztrRkFDQyw4REFBQ2E7d0VBQU1aLFdBQVU7a0ZBQStDOzs7Ozs7a0ZBR2hFLDhEQUFDYTt3RUFDQzFCLE9BQU9oQyxTQUFTRSxPQUFPLENBQUNDLFFBQVE7d0VBQ2hDd0QsVUFBVSxDQUFDQyxJQUFNL0Isb0JBQW9CLFdBQVcsWUFBWStCLEVBQUVDLE1BQU0sQ0FBQzdCLEtBQUs7d0VBQzFFYSxXQUFVOzswRkFFViw4REFBQ2lCO2dGQUFPOUIsT0FBTTswRkFBSzs7Ozs7OzBGQUNuQiw4REFBQzhCO2dGQUFPOUIsT0FBTTswRkFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBFQUl2Qiw4REFBQ1k7O2tGQUNDLDhEQUFDYTt3RUFBTVosV0FBVTtrRkFBK0M7Ozs7OztrRkFHaEUsOERBQUNhO3dFQUNDMUIsT0FBT2hDLFNBQVNFLE9BQU8sQ0FBQ0UsUUFBUTt3RUFDaEN1RCxVQUFVLENBQUNDLElBQU0vQixvQkFBb0IsV0FBVyxZQUFZK0IsRUFBRUMsTUFBTSxDQUFDN0IsS0FBSzt3RUFDMUVhLFdBQVU7OzBGQUVWLDhEQUFDaUI7Z0ZBQU85QixPQUFNOzBGQUFlOzs7Ozs7MEZBQzdCLDhEQUFDOEI7Z0ZBQU85QixPQUFNOzBGQUFnQjs7Ozs7OzBGQUM5Qiw4REFBQzhCO2dGQUFPOUIsT0FBTTswRkFBZ0I7Ozs7OzswRkFDOUIsOERBQUM4QjtnRkFBTzlCLE9BQU07MEZBQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUFLMUIsOERBQUNZOzswRUFDQyw4REFBQ2E7Z0VBQU1aLFdBQVU7MEVBQStDOzs7Ozs7MEVBR2hFLDhEQUFDRDtnRUFBSUMsV0FBVTswRUFDWjtvRUFBQztvRUFBUTtvRUFBUztpRUFBTyxDQUFDSSxHQUFHLENBQUMsQ0FBQzVDLHNCQUM5Qiw4REFBQ29EO3dFQUFrQlosV0FBVTs7MEZBQzNCLDhEQUFDa0I7Z0ZBQ0NDLE1BQUs7Z0ZBQ0x2QixNQUFLO2dGQUNMVCxPQUFPM0I7Z0ZBQ1A0RCxTQUFTakUsU0FBU0UsT0FBTyxDQUFDRyxLQUFLLEtBQUtBO2dGQUNwQ3NELFVBQVUsQ0FBQ0MsSUFBTS9CLG9CQUFvQixXQUFXLFNBQVMrQixFQUFFQyxNQUFNLENBQUM3QixLQUFLO2dGQUN2RWEsV0FBVTs7Ozs7OzBGQUVaLDhEQUFDRTtnRkFBS0YsV0FBVTswRkFBNEJ4Qzs7Ozs7Ozt1RUFUbENBOzs7Ozs7Ozs7Ozs7Ozs7O2tFQWVsQiw4REFBQ3VDO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7O2tGQUNDLDhEQUFDc0I7d0VBQUdyQixXQUFVO2tGQUFpQzs7Ozs7O2tGQUMvQyw4REFBQ0c7d0VBQUVILFdBQVU7a0ZBQXdCOzs7Ozs7Ozs7Ozs7MEVBRXZDLDhEQUFDWTtnRUFBTVosV0FBVTs7a0ZBQ2YsOERBQUNrQjt3RUFDQ0MsTUFBSzt3RUFDTEMsU0FBU2pFLFNBQVNFLE9BQU8sQ0FBQ0ksUUFBUTt3RUFDbENxRCxVQUFVLENBQUNDLElBQU0vQixvQkFBb0IsV0FBVyxZQUFZK0IsRUFBRUMsTUFBTSxDQUFDSSxPQUFPO3dFQUM1RXBCLFdBQVU7Ozs7OztrRkFFWiw4REFBQ0Q7d0VBQUlDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dDQVMxQi9DLGNBQWMsaUNBQ2IsOERBQUNiLGtEQUFJQTtvQ0FBQ3FFLFFBQU87b0NBQU9DLElBQUk7OENBQ3RCLDRFQUFDWDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNXO2dEQUFHWCxXQUFVOzBEQUFvQzs7Ozs7OzBEQUVsRCw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQ1pzQixPQUFPQyxPQUFPLENBQUNwRSxTQUFTTyxhQUFhLEVBQUUwQyxHQUFHLENBQUMsQ0FBQyxDQUFDbEIsS0FBS0MsTUFBTSxpQkFDdkQsOERBQUNZO3dEQUFjQyxXQUFVOzswRUFDdkIsOERBQUNEOztrRkFDQyw4REFBQ3NCO3dFQUFHckIsV0FBVTtrRkFDWGQsSUFBSXNDLE9BQU8sQ0FBQyxZQUFZLE9BQU9DLElBQUk7Ozs7OztrRkFFdEMsOERBQUN0Qjt3RUFBRUgsV0FBVTs7NEVBQ1ZkLFFBQVEsd0JBQXdCOzRFQUNoQ0EsUUFBUSx1QkFBdUI7NEVBQy9CQSxRQUFRLGdCQUFnQjs0RUFDeEJBLFFBQVEsb0JBQW9COzRFQUM1QkEsUUFBUSxrQkFBa0I7NEVBQzFCQSxRQUFRLHFCQUFxQjs7Ozs7Ozs7Ozs7OzswRUFHbEMsOERBQUMwQjtnRUFBTVosV0FBVTs7a0ZBQ2YsOERBQUNrQjt3RUFDQ0MsTUFBSzt3RUFDTEMsU0FBU2pDO3dFQUNUMkIsVUFBVSxDQUFDQyxJQUFNL0Isb0JBQW9CLGlCQUFpQkUsS0FBSzZCLEVBQUVDLE1BQU0sQ0FBQ0ksT0FBTzt3RUFDM0VwQixXQUFVOzs7Ozs7a0ZBRVosOERBQUNEO3dFQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7O3VEQXJCVGQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQ0ErQm5CakMsY0FBYyw0QkFDYiw4REFBQ2Isa0RBQUlBO29DQUFDcUUsUUFBTztvQ0FBTUMsSUFBSTs4Q0FDckIsNEVBQUNYO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ1c7Z0RBQUdYLFdBQVU7MERBQW9DOzs7Ozs7MERBRWxELDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7O2tGQUNDLDhEQUFDc0I7d0VBQUdyQixXQUFVO2tGQUFpQzs7Ozs7O2tGQUMvQyw4REFBQ0c7d0VBQUVILFdBQVU7a0ZBQXdCOzs7Ozs7Ozs7Ozs7MEVBRXZDLDhEQUFDWTtnRUFBTVosV0FBVTs7a0ZBQ2YsOERBQUNrQjt3RUFDQ0MsTUFBSzt3RUFDTEMsU0FBU2pFLFNBQVNjLFFBQVEsQ0FBQ0MsYUFBYTt3RUFDeEM0QyxVQUFVLENBQUNDLElBQU0vQixvQkFBb0IsWUFBWSxpQkFBaUIrQixFQUFFQyxNQUFNLENBQUNJLE9BQU87d0VBQ2xGcEIsV0FBVTs7Ozs7O2tGQUVaLDhEQUFDRDt3RUFBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQUluQiw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDs7a0ZBQ0MsOERBQUNhO3dFQUFNWixXQUFVO2tGQUErQzs7Ozs7O2tGQUdoRSw4REFBQ2tCO3dFQUNDQyxNQUFLO3dFQUNMaEMsT0FBT2hDLFNBQVNjLFFBQVEsQ0FBQ0UsY0FBYzt3RUFDdkMyQyxVQUFVLENBQUNDLElBQU0vQixvQkFBb0IsWUFBWSxrQkFBa0IwQyxTQUFTWCxFQUFFQyxNQUFNLENBQUM3QixLQUFLO3dFQUMxRmEsV0FBVTt3RUFDVjJCLEtBQUk7d0VBQ0pDLEtBQUk7Ozs7Ozs7Ozs7OzswRUFJUiw4REFBQzdCOztrRkFDQyw4REFBQ2E7d0VBQU1aLFdBQVU7a0ZBQStDOzs7Ozs7a0ZBR2hFLDhEQUFDa0I7d0VBQ0NDLE1BQUs7d0VBQ0xoQyxPQUFPaEMsU0FBU2MsUUFBUSxDQUFDSSxZQUFZO3dFQUNyQ3lDLFVBQVUsQ0FBQ0MsSUFBTS9CLG9CQUFvQixZQUFZLGdCQUFnQjBDLFNBQVNYLEVBQUVDLE1BQU0sQ0FBQzdCLEtBQUs7d0VBQ3hGYSxXQUFVO3dFQUNWMkIsS0FBSTt3RUFDSkMsS0FBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQUtWLDhEQUFDN0I7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDs7a0ZBQ0MsOERBQUNzQjt3RUFBR3JCLFdBQVU7a0ZBQWlDOzs7Ozs7a0ZBQy9DLDhEQUFDRzt3RUFBRUgsV0FBVTtrRkFBd0I7Ozs7Ozs7Ozs7OzswRUFFdkMsOERBQUNZO2dFQUFNWixXQUFVOztrRkFDZiw4REFBQ2tCO3dFQUNDQyxNQUFLO3dFQUNMQyxTQUFTakUsU0FBU2MsUUFBUSxDQUFDRyxXQUFXO3dFQUN0QzBDLFVBQVUsQ0FBQ0MsSUFBTS9CLG9CQUFvQixZQUFZLGVBQWUrQixFQUFFQyxNQUFNLENBQUNJLE9BQU87d0VBQ2hGcEIsV0FBVTs7Ozs7O2tGQUVaLDhEQUFDRDt3RUFBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0NBUzFCL0MsY0FBYywyQkFDYiw4REFBQ2Isa0RBQUlBO29DQUFDcUUsUUFBTztvQ0FBU0MsSUFBSTs4Q0FDeEIsNEVBQUNYO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ1c7Z0RBQUdYLFdBQVU7MERBQW9DOzs7Ozs7MERBRWxELDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEOzswRUFDQyw4REFBQ2E7Z0VBQU1aLFdBQVU7MEVBQStDOzs7Ozs7MEVBR2hFLDhEQUFDYTtnRUFDQzFCLE9BQU9oQyxTQUFTb0IsT0FBTyxDQUFDQyxpQkFBaUI7Z0VBQ3pDc0MsVUFBVSxDQUFDQyxJQUFNL0Isb0JBQW9CLFdBQVcscUJBQXFCK0IsRUFBRUMsTUFBTSxDQUFDN0IsS0FBSztnRUFDbkZhLFdBQVU7O2tGQUVWLDhEQUFDaUI7d0VBQU85QixPQUFNO2tGQUFTOzs7Ozs7a0ZBQ3ZCLDhEQUFDOEI7d0VBQU85QixPQUFNO2tGQUFVOzs7Ozs7a0ZBQ3hCLDhEQUFDOEI7d0VBQU85QixPQUFNO2tGQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBSTVCLDhEQUFDWTt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEOztrRkFDQyw4REFBQ3NCO3dFQUFHckIsV0FBVTtrRkFBaUM7Ozs7OztrRkFDL0MsOERBQUNHO3dFQUFFSCxXQUFVO2tGQUF3Qjs7Ozs7Ozs7Ozs7OzBFQUV2Qyw4REFBQ1k7Z0VBQU1aLFdBQVU7O2tGQUNmLDhEQUFDa0I7d0VBQ0NDLE1BQUs7d0VBQ0xDLFNBQVNqRSxTQUFTb0IsT0FBTyxDQUFDRSxnQkFBZ0I7d0VBQzFDcUMsVUFBVSxDQUFDQyxJQUFNL0Isb0JBQW9CLFdBQVcsb0JBQW9CK0IsRUFBRUMsTUFBTSxDQUFDSSxPQUFPO3dFQUNwRnBCLFdBQVU7Ozs7OztrRkFFWiw4REFBQ0Q7d0VBQUlDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUFJbkIsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7O2tGQUNDLDhEQUFDc0I7d0VBQUdyQixXQUFVO2tGQUFpQzs7Ozs7O2tGQUMvQyw4REFBQ0c7d0VBQUVILFdBQVU7a0ZBQXdCOzs7Ozs7Ozs7Ozs7MEVBRXZDLDhEQUFDWTtnRUFBTVosV0FBVTs7a0ZBQ2YsOERBQUNrQjt3RUFDQ0MsTUFBSzt3RUFDTEMsU0FBU2pFLFNBQVNvQixPQUFPLENBQUNHLFdBQVc7d0VBQ3JDb0MsVUFBVSxDQUFDQyxJQUFNL0Isb0JBQW9CLFdBQVcsZUFBZStCLEVBQUVDLE1BQU0sQ0FBQ0ksT0FBTzt3RUFDL0VwQixXQUFVOzs7Ozs7a0ZBRVosOERBQUNEO3dFQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FVN0IsOERBQUNEOzs4Q0FDQyw4REFBQzNELGtEQUFJQTs4Q0FDSCw0RUFBQzJEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ3FCO2dEQUFHckIsV0FBVTswREFBb0M7Ozs7OzswREFDbEQsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ087d0RBQ0NDLFNBQVNuQjt3REFDVHdDLFVBQVUvQzt3REFDVmtCLFdBQVU7a0VBRVRsQix1QkFDQzs7OEVBQ0UsOERBQUNwQyxvSEFBU0E7b0VBQUNzRCxXQUFVOzs7Ozs7Z0VBQThCOzt5RkFJckQ7OzhFQUNFLDhEQUFDdkQsb0hBQUlBO29FQUFDdUQsV0FBVTs7Ozs7O2dFQUFpQjs7Ozs7Ozs7a0VBTXZDLDhEQUFDTzt3REFDQ0MsU0FBU2Y7d0RBQ1RPLFdBQVU7OzBFQUVWLDhEQUFDdEQsb0hBQVNBO2dFQUFDc0QsV0FBVTs7Ozs7OzREQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQU85Qyw4REFBQzVELGtEQUFJQTtvQ0FBQzRELFdBQVU7OENBQ2QsNEVBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ3FCO2dEQUFHckIsV0FBVTswREFBb0M7Ozs7OzswREFDbEQsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRTtnRUFBS0YsV0FBVTswRUFBZ0I7Ozs7OzswRUFDaEMsOERBQUNFO2dFQUFLRixXQUFVOzBFQUFrQ3BELEtBQUtJLElBQUksQ0FBQzhFLFdBQVc7Ozs7Ozs7Ozs7OztrRUFFekUsOERBQUMvQjt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNFO2dFQUFLRixXQUFVOzBFQUFnQjs7Ozs7OzBFQUNoQyw4REFBQ0U7Z0VBQUtGLFdBQVU7MEVBQWNwRCxLQUFLRyxJQUFJLENBQUN5RSxPQUFPLENBQUMsS0FBSyxLQUFLTSxXQUFXOzs7Ozs7Ozs7Ozs7a0VBRXZFLDhEQUFDL0I7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRTtnRUFBS0YsV0FBVTswRUFBZ0I7Ozs7OzswRUFDaEMsOERBQUNFO2dFQUFLRixXQUFVOzBFQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVVyRCIsInNvdXJjZXMiOlsid2VicGFjazovL2tvZGV4Z3VhcmQvLi9hcHAvc2V0dGluZ3MvcGFnZS50c3g/MGZiOSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCBEYXNoYm9hcmRMYXlvdXQgZnJvbSAnQC9jb21wb25lbnRzL0Rhc2hib2FyZExheW91dCdcbmltcG9ydCB7IENhcmQsIFN0YXRDYXJkLCBBbGVydENhcmQgfSBmcm9tICdAL2NvbXBvbmVudHMvQ2FyZCdcbmltcG9ydCB7IFxuICBTZXR0aW5ncywgXG4gIFVzZXIsIFxuICBTaGllbGQsXG4gIEJlbGwsXG4gIFBhbGV0dGUsXG4gIEdsb2JlLFxuICBMb2NrLFxuICBEYXRhYmFzZSxcbiAgU21hcnRwaG9uZSxcbiAgTWFpbCxcbiAgU2F2ZSxcbiAgUmVmcmVzaEN3LFxuICBFeWUsXG4gIEV5ZU9mZixcbiAgQ2hlY2ssXG4gIFgsXG4gIEFsZXJ0VHJpYW5nbGUsXG4gIEluZm9cbn0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTZXR0aW5nc1BhZ2UoKSB7XG4gIGNvbnN0IFt1c2VyXSA9IHVzZVN0YXRlKHtcbiAgICB1c2VybmFtZTogJ2FkbWluJyxcbiAgICBhdmF0YXI6ICcnLFxuICAgIHJvbGU6ICdzdXBlcl9hZG1pbicsXG4gICAgcGxhbjogJ2N5YmVyc2VjdXJpdHknXG4gIH0pXG5cbiAgY29uc3QgW2FjdGl2ZVRhYiwgc2V0QWN0aXZlVGFiXSA9IHVzZVN0YXRlKCdnZW5lcmFsJylcbiAgY29uc3QgW3NldHRpbmdzLCBzZXRTZXR0aW5nc10gPSB1c2VTdGF0ZSh7XG4gICAgZ2VuZXJhbDoge1xuICAgICAgbGFuZ3VhZ2U6ICdpZCcsXG4gICAgICB0aW1lem9uZTogJ0FzaWEvSmFrYXJ0YScsXG4gICAgICB0aGVtZTogJ2RhcmsnLFxuICAgICAgYXV0b1NhdmU6IHRydWVcbiAgICB9LFxuICAgIG5vdGlmaWNhdGlvbnM6IHtcbiAgICAgIGVtYWlsTm90aWZpY2F0aW9uczogdHJ1ZSxcbiAgICAgIHB1c2hOb3RpZmljYXRpb25zOiB0cnVlLFxuICAgICAgc2NhbkFsZXJ0czogdHJ1ZSxcbiAgICAgIHNlY3VyaXR5QWxlcnRzOiB0cnVlLFxuICAgICAgd2Vla2x5UmVwb3J0OiBmYWxzZSxcbiAgICAgIG1hcmtldGluZ0VtYWlsczogZmFsc2VcbiAgICB9LFxuICAgIHNlY3VyaXR5OiB7XG4gICAgICB0d29GYWN0b3JBdXRoOiBmYWxzZSxcbiAgICAgIHNlc3Npb25UaW1lb3V0OiAzMCxcbiAgICAgIGxvZ2luQWxlcnRzOiB0cnVlLFxuICAgICAgYXBpS2V5RXhwaXJ5OiAzNjUsXG4gICAgICBwYXNzd29yZEV4cGlyeTogOTBcbiAgICB9LFxuICAgIHByaXZhY3k6IHtcbiAgICAgIHByb2ZpbGVWaXNpYmlsaXR5OiAncHJpdmF0ZScsXG4gICAgICBhY3Rpdml0eVRyYWNraW5nOiB0cnVlLFxuICAgICAgZGF0YVNoYXJpbmc6IGZhbHNlLFxuICAgICAgYW5hbHl0aWNzT3B0T3V0OiBmYWxzZVxuICAgIH1cbiAgfSlcblxuICBjb25zdCBbc2hvd0FwaUtleSwgc2V0U2hvd0FwaUtleV0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW3NhdmluZywgc2V0U2F2aW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxuXG4gIGNvbnN0IGhhbmRsZVNldHRpbmdDaGFuZ2UgPSAoY2F0ZWdvcnk6IHN0cmluZywga2V5OiBzdHJpbmcsIHZhbHVlOiBhbnkpID0+IHtcbiAgICBzZXRTZXR0aW5ncyhwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgW2NhdGVnb3J5XToge1xuICAgICAgICAuLi5wcmV2W2NhdGVnb3J5IGFzIGtleW9mIHR5cGVvZiBwcmV2XSxcbiAgICAgICAgW2tleV06IHZhbHVlXG4gICAgICB9XG4gICAgfSkpXG4gIH1cblxuICBjb25zdCBzYXZlU2V0dGluZ3MgPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0U2F2aW5nKHRydWUpXG4gICAgLy8gU2ltdWxhdGUgQVBJIGNhbGxcbiAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTAwMCkpXG4gICAgc2V0U2F2aW5nKGZhbHNlKVxuICB9XG5cbiAgY29uc3QgcmVzZXRTZXR0aW5ncyA9ICgpID0+IHtcbiAgICAvLyBSZXNldCB0byBkZWZhdWx0IHNldHRpbmdzXG4gICAgc2V0U2V0dGluZ3Moe1xuICAgICAgZ2VuZXJhbDoge1xuICAgICAgICBsYW5ndWFnZTogJ2lkJyxcbiAgICAgICAgdGltZXpvbmU6ICdBc2lhL0pha2FydGEnLFxuICAgICAgICB0aGVtZTogJ2RhcmsnLFxuICAgICAgICBhdXRvU2F2ZTogdHJ1ZVxuICAgICAgfSxcbiAgICAgIG5vdGlmaWNhdGlvbnM6IHtcbiAgICAgICAgZW1haWxOb3RpZmljYXRpb25zOiB0cnVlLFxuICAgICAgICBwdXNoTm90aWZpY2F0aW9uczogdHJ1ZSxcbiAgICAgICAgc2NhbkFsZXJ0czogdHJ1ZSxcbiAgICAgICAgc2VjdXJpdHlBbGVydHM6IHRydWUsXG4gICAgICAgIHdlZWtseVJlcG9ydDogZmFsc2UsXG4gICAgICAgIG1hcmtldGluZ0VtYWlsczogZmFsc2VcbiAgICAgIH0sXG4gICAgICBzZWN1cml0eToge1xuICAgICAgICB0d29GYWN0b3JBdXRoOiBmYWxzZSxcbiAgICAgICAgc2Vzc2lvblRpbWVvdXQ6IDMwLFxuICAgICAgICBsb2dpbkFsZXJ0czogdHJ1ZSxcbiAgICAgICAgYXBpS2V5RXhwaXJ5OiAzNjUsXG4gICAgICAgIHBhc3N3b3JkRXhwaXJ5OiA5MFxuICAgICAgfSxcbiAgICAgIHByaXZhY3k6IHtcbiAgICAgICAgcHJvZmlsZVZpc2liaWxpdHk6ICdwcml2YXRlJyxcbiAgICAgICAgYWN0aXZpdHlUcmFja2luZzogdHJ1ZSxcbiAgICAgICAgZGF0YVNoYXJpbmc6IGZhbHNlLFxuICAgICAgICBhbmFseXRpY3NPcHRPdXQ6IGZhbHNlXG4gICAgICB9XG4gICAgfSlcbiAgfVxuXG4gIGNvbnN0IHRhYnMgPSBbXG4gICAgeyBpZDogJ2dlbmVyYWwnLCBuYW1lOiAnR2VuZXJhbCcsIGljb246IFNldHRpbmdzIH0sXG4gICAgeyBpZDogJ25vdGlmaWNhdGlvbnMnLCBuYW1lOiAnTm90aWZpY2F0aW9ucycsIGljb246IEJlbGwgfSxcbiAgICB7IGlkOiAnc2VjdXJpdHknLCBuYW1lOiAnU2VjdXJpdHknLCBpY29uOiBTaGllbGQgfSxcbiAgICB7IGlkOiAncHJpdmFjeScsIG5hbWU6ICdQcml2YWN5JywgaWNvbjogTG9jayB9XG4gIF1cblxuICByZXR1cm4gKFxuICAgIDxEYXNoYm9hcmRMYXlvdXQgdXNlcj17dXNlcn0gdGl0bGU9XCJTZXR0aW5nc1wiPlxuICAgICAgPGRpdj5cbiAgICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgbWItNFwiPlxuICAgICAgICAgICAgPFNldHRpbmdzIGNsYXNzTmFtZT1cImgtOCB3LTggdGV4dC1jeWJlci1ncmVlblwiIC8+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIGZvbnQtY3liZXIgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJjeWJlci10ZXh0XCI+U2V0dGluZ3M8L3NwYW4+XG4gICAgICAgICAgICA8L2gxPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgbWF4LXctM3hsXCI+XG4gICAgICAgICAgICBLZWxvbGEgcGVuZ2F0dXJhbiBha3VuLCBub3RpZmlrYXNpLCBrZWFtYW5hbiwgZGFuIHByaXZhc2kgdW50dWsgcGVuZ2FsYW1hbiBLb2RlWEd1YXJkIHlhbmcgb3B0aW1hbC5cbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBUYWIgTmF2aWdhdGlvbiAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMSBiZy1ncmF5LTgwMC81MCBwLTEgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAge3RhYnMubWFwKCh0YWIpID0+IHtcbiAgICAgICAgICAgICAgY29uc3QgSWNvbiA9IHRhYi5pY29uXG4gICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAga2V5PXt0YWIuaWR9XG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIodGFiLmlkKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBweC00IHB5LTIgcm91bmRlZC1sZyBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgJHtcbiAgICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSB0YWIuaWRcbiAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1jeWJlci1ncmVlbiB0ZXh0LWJsYWNrJ1xuICAgICAgICAgICAgICAgICAgICAgIDogJ3RleHQtZ3JheS0zMDAgaG92ZXI6dGV4dC13aGl0ZSBob3ZlcjpiZy1ncmF5LTcwMC81MCdcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxJY29uIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgPHNwYW4+e3RhYi5uYW1lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgfSl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBsZzpncmlkLWNvbHMtNCBnYXAtOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6Y29sLXNwYW4tM1wiPlxuICAgICAgICAgICAgey8qIEdlbmVyYWwgU2V0dGluZ3MgKi99XG4gICAgICAgICAgICB7YWN0aXZlVGFiID09PSAnZ2VuZXJhbCcgJiYgKFxuICAgICAgICAgICAgICA8Q2FyZCBib3JkZXI9XCJncmVlblwiIGdsb3c+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTZcIj5HZW5lcmFsIFNldHRpbmdzPC9oMj5cbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIG1kOmdyaWQtY29scy0yIGdhcC02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS0zMDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBMYW5ndWFnZVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3NldHRpbmdzLmdlbmVyYWwubGFuZ3VhZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlU2V0dGluZ0NoYW5nZSgnZ2VuZXJhbCcsICdsYW5ndWFnZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY3liZXItaW5wdXQgdy1mdWxsXCJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImlkXCI+QmFoYXNhIEluZG9uZXNpYTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiZW5cIj5FbmdsaXNoPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTMwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIFRpbWV6b25lXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17c2V0dGluZ3MuZ2VuZXJhbC50aW1lem9uZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVTZXR0aW5nQ2hhbmdlKCdnZW5lcmFsJywgJ3RpbWV6b25lJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjeWJlci1pbnB1dCB3LWZ1bGxcIlxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiQXNpYS9KYWthcnRhXCI+QXNpYS9KYWthcnRhIChXSUIpPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJBc2lhL01ha2Fzc2FyXCI+QXNpYS9NYWthc3NhciAoV0lUQSk8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkFzaWEvSmF5YXB1cmFcIj5Bc2lhL0pheWFwdXJhIChXSVQpPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJVVENcIj5VVEM8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS0zMDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgVGhlbWVcbiAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtbJ2RhcmsnLCAnbGlnaHQnLCAnYXV0byddLm1hcCgodGhlbWUpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGtleT17dGhlbWV9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBjdXJzb3ItcG9pbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInJhZGlvXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU9XCJ0aGVtZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17dGhlbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtzZXR0aW5ncy5nZW5lcmFsLnRoZW1lID09PSB0aGVtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlU2V0dGluZ0NoYW5nZSgnZ2VuZXJhbCcsICd0aGVtZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtY3liZXItZ3JlZW5cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBjYXBpdGFsaXplXCI+e3RoZW1lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXdoaXRlXCI+QXV0byBTYXZlPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMFwiPkF1dG9tYXRpY2FsbHkgc2F2ZSB5b3VyIHdvcms8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInJlbGF0aXZlIGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBjdXJzb3ItcG9pbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e3NldHRpbmdzLmdlbmVyYWwuYXV0b1NhdmV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlU2V0dGluZ0NoYW5nZSgnZ2VuZXJhbCcsICdhdXRvU2F2ZScsIGUudGFyZ2V0LmNoZWNrZWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJzci1vbmx5IHBlZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMSBoLTYgYmctZ3JheS03MDAgcGVlci1mb2N1czpvdXRsaW5lLW5vbmUgcm91bmRlZC1mdWxsIHBlZXIgcGVlci1jaGVja2VkOmFmdGVyOnRyYW5zbGF0ZS14LWZ1bGwgcGVlci1jaGVja2VkOmFmdGVyOmJvcmRlci13aGl0ZSBhZnRlcjpjb250ZW50LVsnJ10gYWZ0ZXI6YWJzb2x1dGUgYWZ0ZXI6dG9wLVsycHhdIGFmdGVyOmxlZnQtWzJweF0gYWZ0ZXI6Ymctd2hpdGUgYWZ0ZXI6cm91bmRlZC1mdWxsIGFmdGVyOmgtNSBhZnRlcjp3LTUgYWZ0ZXI6dHJhbnNpdGlvbi1hbGwgcGVlci1jaGVja2VkOmJnLWN5YmVyLWdyZWVuXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgey8qIE5vdGlmaWNhdGlvbnMgU2V0dGluZ3MgKi99XG4gICAgICAgICAgICB7YWN0aXZlVGFiID09PSAnbm90aWZpY2F0aW9ucycgJiYgKFxuICAgICAgICAgICAgICA8Q2FyZCBib3JkZXI9XCJibHVlXCIgZ2xvdz5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNlwiPlxuICAgICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItNlwiPk5vdGlmaWNhdGlvbiBTZXR0aW5nczwvaDI+XG4gICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgICAgICAgIHtPYmplY3QuZW50cmllcyhzZXR0aW5ncy5ub3RpZmljYXRpb25zKS5tYXAoKFtrZXksIHZhbHVlXSkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtrZXl9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC13aGl0ZSBjYXBpdGFsaXplXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2tleS5yZXBsYWNlKC8oW0EtWl0pL2csICcgJDEnKS50cmltKCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtrZXkgPT09ICdlbWFpbE5vdGlmaWNhdGlvbnMnICYmICdSZWNlaXZlIG5vdGlmaWNhdGlvbnMgdmlhIGVtYWlsJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7a2V5ID09PSAncHVzaE5vdGlmaWNhdGlvbnMnICYmICdSZWNlaXZlIHB1c2ggbm90aWZpY2F0aW9ucyBpbiBicm93c2VyJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7a2V5ID09PSAnc2NhbkFsZXJ0cycgJiYgJ0dldCBub3RpZmllZCB3aGVuIHNjYW5zIGNvbXBsZXRlJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7a2V5ID09PSAnc2VjdXJpdHlBbGVydHMnICYmICdJbXBvcnRhbnQgc2VjdXJpdHkgbm90aWZpY2F0aW9ucyd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2tleSA9PT0gJ3dlZWtseVJlcG9ydCcgJiYgJ1dlZWtseSBhY3Rpdml0eSBzdW1tYXJ5J31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7a2V5ID09PSAnbWFya2V0aW5nRW1haWxzJyAmJiAnUHJvZHVjdCB1cGRhdGVzIGFuZCBwcm9tb3Rpb25zJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwicmVsYXRpdmUgaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGN1cnNvci1wb2ludGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17dmFsdWUgYXMgYm9vbGVhbn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZVNldHRpbmdDaGFuZ2UoJ25vdGlmaWNhdGlvbnMnLCBrZXksIGUudGFyZ2V0LmNoZWNrZWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInNyLW9ubHkgcGVlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMSBoLTYgYmctZ3JheS03MDAgcGVlci1mb2N1czpvdXRsaW5lLW5vbmUgcm91bmRlZC1mdWxsIHBlZXIgcGVlci1jaGVja2VkOmFmdGVyOnRyYW5zbGF0ZS14LWZ1bGwgcGVlci1jaGVja2VkOmFmdGVyOmJvcmRlci13aGl0ZSBhZnRlcjpjb250ZW50LVsnJ10gYWZ0ZXI6YWJzb2x1dGUgYWZ0ZXI6dG9wLVsycHhdIGFmdGVyOmxlZnQtWzJweF0gYWZ0ZXI6Ymctd2hpdGUgYWZ0ZXI6cm91bmRlZC1mdWxsIGFmdGVyOmgtNSBhZnRlcjp3LTUgYWZ0ZXI6dHJhbnNpdGlvbi1hbGwgcGVlci1jaGVja2VkOmJnLWN5YmVyLWdyZWVuXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7LyogU2VjdXJpdHkgU2V0dGluZ3MgKi99XG4gICAgICAgICAgICB7YWN0aXZlVGFiID09PSAnc2VjdXJpdHknICYmIChcbiAgICAgICAgICAgICAgPENhcmQgYm9yZGVyPVwicmVkXCIgZ2xvdz5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNlwiPlxuICAgICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItNlwiPlNlY3VyaXR5IFNldHRpbmdzPC9oMj5cbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC13aGl0ZVwiPlR3by1GYWN0b3IgQXV0aGVudGljYXRpb248L2gzPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNDAwXCI+QWRkIGFuIGV4dHJhIGxheWVyIG9mIHNlY3VyaXR5PC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJyZWxhdGl2ZSBpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgY3Vyc29yLXBvaW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtzZXR0aW5ncy5zZWN1cml0eS50d29GYWN0b3JBdXRofVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZVNldHRpbmdDaGFuZ2UoJ3NlY3VyaXR5JywgJ3R3b0ZhY3RvckF1dGgnLCBlLnRhcmdldC5jaGVja2VkKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwic3Itb25seSBwZWVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTEgaC02IGJnLWdyYXktNzAwIHBlZXItZm9jdXM6b3V0bGluZS1ub25lIHJvdW5kZWQtZnVsbCBwZWVyIHBlZXItY2hlY2tlZDphZnRlcjp0cmFuc2xhdGUteC1mdWxsIHBlZXItY2hlY2tlZDphZnRlcjpib3JkZXItd2hpdGUgYWZ0ZXI6Y29udGVudC1bJyddIGFmdGVyOmFic29sdXRlIGFmdGVyOnRvcC1bMnB4XSBhZnRlcjpsZWZ0LVsycHhdIGFmdGVyOmJnLXdoaXRlIGFmdGVyOnJvdW5kZWQtZnVsbCBhZnRlcjpoLTUgYWZ0ZXI6dy01IGFmdGVyOnRyYW5zaXRpb24tYWxsIHBlZXItY2hlY2tlZDpiZy1jeWJlci1ncmVlblwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBtZDpncmlkLWNvbHMtMiBnYXAtNlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktMzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgU2Vzc2lvbiBUaW1lb3V0IChtaW51dGVzKVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3NldHRpbmdzLnNlY3VyaXR5LnNlc3Npb25UaW1lb3V0fVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZVNldHRpbmdDaGFuZ2UoJ3NlY3VyaXR5JywgJ3Nlc3Npb25UaW1lb3V0JywgcGFyc2VJbnQoZS50YXJnZXQudmFsdWUpKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY3liZXItaW5wdXQgdy1mdWxsXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbWluPVwiNVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG1heD1cIjQ4MFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktMzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgQVBJIEtleSBFeHBpcnkgKGRheXMpXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17c2V0dGluZ3Muc2VjdXJpdHkuYXBpS2V5RXhwaXJ5fVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZVNldHRpbmdDaGFuZ2UoJ3NlY3VyaXR5JywgJ2FwaUtleUV4cGlyeScsIHBhcnNlSW50KGUudGFyZ2V0LnZhbHVlKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImN5YmVyLWlucHV0IHctZnVsbFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG1pbj1cIjMwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbWF4PVwiMzY1XCJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtd2hpdGVcIj5Mb2dpbiBBbGVydHM8L2gzPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNDAwXCI+R2V0IG5vdGlmaWVkIG9mIG5ldyBsb2dpbiBhdHRlbXB0czwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwicmVsYXRpdmUgaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGN1cnNvci1wb2ludGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17c2V0dGluZ3Muc2VjdXJpdHkubG9naW5BbGVydHN9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlU2V0dGluZ0NoYW5nZSgnc2VjdXJpdHknLCAnbG9naW5BbGVydHMnLCBlLnRhcmdldC5jaGVja2VkKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwic3Itb25seSBwZWVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTEgaC02IGJnLWdyYXktNzAwIHBlZXItZm9jdXM6b3V0bGluZS1ub25lIHJvdW5kZWQtZnVsbCBwZWVyIHBlZXItY2hlY2tlZDphZnRlcjp0cmFuc2xhdGUteC1mdWxsIHBlZXItY2hlY2tlZDphZnRlcjpib3JkZXItd2hpdGUgYWZ0ZXI6Y29udGVudC1bJyddIGFmdGVyOmFic29sdXRlIGFmdGVyOnRvcC1bMnB4XSBhZnRlcjpsZWZ0LVsycHhdIGFmdGVyOmJnLXdoaXRlIGFmdGVyOnJvdW5kZWQtZnVsbCBhZnRlcjpoLTUgYWZ0ZXI6dy01IGFmdGVyOnRyYW5zaXRpb24tYWxsIHBlZXItY2hlY2tlZDpiZy1jeWJlci1ncmVlblwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHsvKiBQcml2YWN5IFNldHRpbmdzICovfVxuICAgICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ3ByaXZhY3knICYmIChcbiAgICAgICAgICAgICAgPENhcmQgYm9yZGVyPVwicHVycGxlXCIgZ2xvdz5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNlwiPlxuICAgICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItNlwiPlByaXZhY3kgU2V0dGluZ3M8L2gyPlxuICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS0zMDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgUHJvZmlsZSBWaXNpYmlsaXR5XG4gICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17c2V0dGluZ3MucHJpdmFjeS5wcm9maWxlVmlzaWJpbGl0eX1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlU2V0dGluZ0NoYW5nZSgncHJpdmFjeScsICdwcm9maWxlVmlzaWJpbGl0eScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImN5YmVyLWlucHV0IHctZnVsbFwiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cInB1YmxpY1wiPlB1YmxpYzwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cInByaXZhdGVcIj5Qcml2YXRlPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiZnJpZW5kc1wiPkZyaWVuZHMgT25seTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXdoaXRlXCI+QWN0aXZpdHkgVHJhY2tpbmc8L2gzPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNDAwXCI+QWxsb3cgdHJhY2tpbmcgZm9yIGFuYWx5dGljczwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwicmVsYXRpdmUgaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGN1cnNvci1wb2ludGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17c2V0dGluZ3MucHJpdmFjeS5hY3Rpdml0eVRyYWNraW5nfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZVNldHRpbmdDaGFuZ2UoJ3ByaXZhY3knLCAnYWN0aXZpdHlUcmFja2luZycsIGUudGFyZ2V0LmNoZWNrZWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJzci1vbmx5IHBlZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMSBoLTYgYmctZ3JheS03MDAgcGVlci1mb2N1czpvdXRsaW5lLW5vbmUgcm91bmRlZC1mdWxsIHBlZXIgcGVlci1jaGVja2VkOmFmdGVyOnRyYW5zbGF0ZS14LWZ1bGwgcGVlci1jaGVja2VkOmFmdGVyOmJvcmRlci13aGl0ZSBhZnRlcjpjb250ZW50LVsnJ10gYWZ0ZXI6YWJzb2x1dGUgYWZ0ZXI6dG9wLVsycHhdIGFmdGVyOmxlZnQtWzJweF0gYWZ0ZXI6Ymctd2hpdGUgYWZ0ZXI6cm91bmRlZC1mdWxsIGFmdGVyOmgtNSBhZnRlcjp3LTUgYWZ0ZXI6dHJhbnNpdGlvbi1hbGwgcGVlci1jaGVja2VkOmJnLWN5YmVyLWdyZWVuXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC13aGl0ZVwiPkRhdGEgU2hhcmluZzwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj5TaGFyZSBhbm9ueW1pemVkIGRhdGEgZm9yIHJlc2VhcmNoPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJyZWxhdGl2ZSBpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgY3Vyc29yLXBvaW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtzZXR0aW5ncy5wcml2YWN5LmRhdGFTaGFyaW5nfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZVNldHRpbmdDaGFuZ2UoJ3ByaXZhY3knLCAnZGF0YVNoYXJpbmcnLCBlLnRhcmdldC5jaGVja2VkKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwic3Itb25seSBwZWVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTEgaC02IGJnLWdyYXktNzAwIHBlZXItZm9jdXM6b3V0bGluZS1ub25lIHJvdW5kZWQtZnVsbCBwZWVyIHBlZXItY2hlY2tlZDphZnRlcjp0cmFuc2xhdGUteC1mdWxsIHBlZXItY2hlY2tlZDphZnRlcjpib3JkZXItd2hpdGUgYWZ0ZXI6Y29udGVudC1bJyddIGFmdGVyOmFic29sdXRlIGFmdGVyOnRvcC1bMnB4XSBhZnRlcjpsZWZ0LVsycHhdIGFmdGVyOmJnLXdoaXRlIGFmdGVyOnJvdW5kZWQtZnVsbCBhZnRlcjpoLTUgYWZ0ZXI6dy01IGFmdGVyOnRyYW5zaXRpb24tYWxsIHBlZXItY2hlY2tlZDpiZy1jeWJlci1ncmVlblwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogU2lkZWJhciAqL31cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02XCI+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItNFwiPlF1aWNrIEFjdGlvbnM8L2gzPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e3NhdmVTZXR0aW5nc31cbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3NhdmluZ31cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY3liZXItYnRuLXByaW1hcnkgdy1mdWxsIGRpc2FibGVkOm9wYWNpdHktNTBcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7c2F2aW5nID8gKFxuICAgICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8UmVmcmVzaEN3IGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMiBhbmltYXRlLXNwaW5cIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgU2F2aW5nLi4uXG4gICAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxTYXZlIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICBTYXZlIFNldHRpbmdzXG4gICAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtyZXNldFNldHRpbmdzfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjeWJlci1idG4gdy1mdWxsXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICBSZXNldCB0byBEZWZhdWx0XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cIm10LTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi00XCI+QWNjb3VudCBJbmZvPC9oMz5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMyB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDBcIj5QbGFuOjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1jeWJlci1ncmVlbiBmb250LXNlbWlib2xkXCI+e3VzZXIucGxhbi50b1VwcGVyQ2FzZSgpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+Um9sZTo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGVcIj57dXNlci5yb2xlLnJlcGxhY2UoJ18nLCAnICcpLnRvVXBwZXJDYXNlKCl9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDBcIj5TdGF0dXM6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTQwMFwiPkFjdGl2ZTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L0Rhc2hib2FyZExheW91dD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwiRGFzaGJvYXJkTGF5b3V0IiwiQ2FyZCIsIlNldHRpbmdzIiwiU2hpZWxkIiwiQmVsbCIsIkxvY2siLCJTYXZlIiwiUmVmcmVzaEN3IiwiU2V0dGluZ3NQYWdlIiwidXNlciIsInVzZXJuYW1lIiwiYXZhdGFyIiwicm9sZSIsInBsYW4iLCJhY3RpdmVUYWIiLCJzZXRBY3RpdmVUYWIiLCJzZXR0aW5ncyIsInNldFNldHRpbmdzIiwiZ2VuZXJhbCIsImxhbmd1YWdlIiwidGltZXpvbmUiLCJ0aGVtZSIsImF1dG9TYXZlIiwibm90aWZpY2F0aW9ucyIsImVtYWlsTm90aWZpY2F0aW9ucyIsInB1c2hOb3RpZmljYXRpb25zIiwic2NhbkFsZXJ0cyIsInNlY3VyaXR5QWxlcnRzIiwid2Vla2x5UmVwb3J0IiwibWFya2V0aW5nRW1haWxzIiwic2VjdXJpdHkiLCJ0d29GYWN0b3JBdXRoIiwic2Vzc2lvblRpbWVvdXQiLCJsb2dpbkFsZXJ0cyIsImFwaUtleUV4cGlyeSIsInBhc3N3b3JkRXhwaXJ5IiwicHJpdmFjeSIsInByb2ZpbGVWaXNpYmlsaXR5IiwiYWN0aXZpdHlUcmFja2luZyIsImRhdGFTaGFyaW5nIiwiYW5hbHl0aWNzT3B0T3V0Iiwic2hvd0FwaUtleSIsInNldFNob3dBcGlLZXkiLCJzYXZpbmciLCJzZXRTYXZpbmciLCJoYW5kbGVTZXR0aW5nQ2hhbmdlIiwiY2F0ZWdvcnkiLCJrZXkiLCJ2YWx1ZSIsInByZXYiLCJzYXZlU2V0dGluZ3MiLCJQcm9taXNlIiwicmVzb2x2ZSIsInNldFRpbWVvdXQiLCJyZXNldFNldHRpbmdzIiwidGFicyIsImlkIiwibmFtZSIsImljb24iLCJ0aXRsZSIsImRpdiIsImNsYXNzTmFtZSIsImgxIiwic3BhbiIsInAiLCJtYXAiLCJ0YWIiLCJJY29uIiwiYnV0dG9uIiwib25DbGljayIsImJvcmRlciIsImdsb3ciLCJoMiIsImxhYmVsIiwic2VsZWN0Iiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0Iiwib3B0aW9uIiwiaW5wdXQiLCJ0eXBlIiwiY2hlY2tlZCIsImgzIiwiT2JqZWN0IiwiZW50cmllcyIsInJlcGxhY2UiLCJ0cmltIiwicGFyc2VJbnQiLCJtaW4iLCJtYXgiLCJkaXNhYmxlZCIsInRvVXBwZXJDYXNlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/settings/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Card.tsx":
/*!*****************************!*\
  !*** ./components/Card.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlertCard: () => (/* binding */ AlertCard),\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   FeatureCard: () => (/* binding */ FeatureCard),\n/* harmony export */   LoadingCard: () => (/* binding */ LoadingCard),\n/* harmony export */   StatCard: () => (/* binding */ StatCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ Card,StatCard,FeatureCard,LoadingCard,AlertCard auto */ \nfunction Card({ children, className = \"\", hover = false, glow = false, border = \"default\" }) {\n    const borderColors = {\n        default: \"border-gray-700\",\n        green: \"border-cyber-green\",\n        blue: \"border-cyber-blue\",\n        red: \"border-cyber-red\",\n        gold: \"border-nusantara-gold\"\n    };\n    const glowColors = {\n        default: \"\",\n        green: \"shadow-lg shadow-cyber-green/20\",\n        blue: \"shadow-lg shadow-cyber-blue/20\",\n        red: \"shadow-lg shadow-cyber-red/20\",\n        gold: \"shadow-lg shadow-nusantara-gold/20\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `\n        bg-gray-900/50 backdrop-blur-sm rounded-lg border ${borderColors[border]}\n        ${hover ? \"hover:border-cyber-green hover:shadow-lg hover:shadow-cyber-green/20 transition-all duration-300 cursor-pointer\" : \"\"}\n        ${glow ? glowColors[border] : \"\"}\n        ${className}\n      `,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\nfunction StatCard({ title, value, icon: Icon, color = \"green\", trend, loading = false }) {\n    const colors = {\n        green: \"text-cyber-green\",\n        blue: \"text-cyber-blue\",\n        red: \"text-cyber-red\",\n        purple: \"text-cyber-purple\",\n        gold: \"text-nusantara-gold\"\n    };\n    const bgColors = {\n        green: \"bg-cyber-green/10\",\n        blue: \"bg-cyber-blue/10\",\n        red: \"bg-cyber-red/10\",\n        purple: \"bg-cyber-purple/10\",\n        gold: \"bg-nusantara-gold/10\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        border: color,\n        glow: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-400\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-baseline space-x-2\",\n                                children: [\n                                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 w-20 bg-gray-700 animate-pulse rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: `text-2xl font-bold ${colors[color]}`,\n                                        children: typeof value === \"number\" ? value.toLocaleString() : value\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, this),\n                                    trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `text-sm font-medium ${trend.isPositive ? \"text-green-400\" : \"text-red-400\"}`,\n                                        children: [\n                                            trend.isPositive ? \"+\" : \"\",\n                                            trend.value,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `p-3 rounded-lg ${bgColors[color]}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            className: `h-6 w-6 ${colors[color]}`\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\nfunction FeatureCard({ title, description, icon: Icon, color = \"green\", onClick, disabled = false, badge }) {\n    const colors = {\n        green: \"text-cyber-green\",\n        blue: \"text-cyber-blue\",\n        red: \"text-cyber-red\",\n        purple: \"text-cyber-purple\",\n        gold: \"text-nusantara-gold\"\n    };\n    const bgColors = {\n        green: \"bg-cyber-green/10\",\n        blue: \"bg-cyber-blue/10\",\n        red: \"bg-cyber-red/10\",\n        purple: \"bg-cyber-purple/10\",\n        gold: \"bg-nusantara-gold/10\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        hover: !disabled && !!onClick,\n        border: color,\n        className: `relative ${disabled ? \"opacity-50 cursor-not-allowed\" : \"\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 h-full\",\n            onClick: disabled ? undefined : onClick,\n            children: [\n                badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-3 right-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `px-2 py-1 text-xs font-semibold rounded-full ${bgColors[color]} ${colors[color]}`,\n                        children: badge\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `inline-flex p-3 rounded-lg ${bgColors[color]} mb-4`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: `h-6 w-6 ${colors[color]}`\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-white mb-2\",\n                    children: title\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-400 text-sm leading-relaxed\",\n                    children: description\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this),\n                onClick && !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 pt-4 border-t border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-medium ${colors[color]} hover:underline`,\n                        children: \"Mulai Sekarang →\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\nfunction LoadingCard({ className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 animate-pulse\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 bg-gray-700 rounded-lg\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-gray-700 rounded w-3/4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-3 bg-gray-700 rounded w-1/2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                lineNumber: 184,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 183,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 182,\n        columnNumber: 5\n    }, this);\n}\nfunction AlertCard({ type = \"info\", title, message, onClose }) {\n    const styles = {\n        info: {\n            border: \"border-cyber-blue\",\n            bg: \"bg-cyber-blue/10\",\n            text: \"text-cyber-blue\",\n            icon: \"\\uD83D\\uDCA1\"\n        },\n        success: {\n            border: \"border-cyber-green\",\n            bg: \"bg-cyber-green/10\",\n            text: \"text-cyber-green\",\n            icon: \"✅\"\n        },\n        warning: {\n            border: \"border-nusantara-gold\",\n            bg: \"bg-nusantara-gold/10\",\n            text: \"text-nusantara-gold\",\n            icon: \"⚠️\"\n        },\n        error: {\n            border: \"border-cyber-red\",\n            bg: \"bg-cyber-red/10\",\n            text: \"text-cyber-red\",\n            icon: \"❌\"\n        }\n    };\n    const style = styles[type];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `border ${style.border} ${style.bg} rounded-lg p-4`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-lg\",\n                    children: style.icon\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: `font-semibold ${style.text}`,\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300 text-sm mt-1\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, this),\n                onClose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onClose,\n                    className: \"text-gray-400 hover:text-white transition-colors\",\n                    children: \"\\xd7\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 238,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 237,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/DashboardLayout.tsx":
/*!****************************************!*\
  !*** ./components/DashboardLayout.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./components/Sidebar.tsx\");\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Navbar */ \"(ssr)/./components/Navbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction DashboardLayout({ children, user, title, showSearch = true }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-cyber flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                user: user\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-w-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        user: user,\n                        title: title,\n                        showSearch: showSearch\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 pt-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Navbar.tsx":
/*!*******************************!*\
  !*** ./components/Navbar.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-search.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Navbar({ user }) {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isProfileOpen, setIsProfileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const navigation = [\n        {\n            name: \"Dashboard\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            name: \"OSINT\",\n            href: \"/osint\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            name: \"Scanner\",\n            href: \"/scanner\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            name: \"File Analyzer\",\n            href: \"/file-analyzer\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            name: \"CVE Intel\",\n            href: \"/cve\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            name: \"Playground\",\n            href: \"/playground\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            name: \"Bot Center\",\n            href: \"/bot\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            name: \"Leaderboard\",\n            href: \"/leaderboard\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        }\n    ];\n    const isActive = (href)=>pathname === href;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 w-full z-50 bg-cyber-dark/90 backdrop-blur-md border-b border-gray-800\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-8 w-8 text-cyber-green animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold font-cyber\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"cyber-text\",\n                                                children: \"Kode\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"nusantara-gold\",\n                                                children: \"X\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white\",\n                                                children: \"Guard\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-1\",\n                            children: user ? // Authenticated Navigation\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    navigation.map((item)=>{\n                                        const Icon = item.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: item.href,\n                                            className: `flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${isActive(item.href) ? \"bg-cyber-green/20 text-cyber-green border border-cyber-green/30\" : \"text-gray-300 hover:text-cyber-green hover:bg-gray-800/50\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, item.name, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 21\n                                        }, this);\n                                    }),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsProfileOpen(!isProfileOpen),\n                                                className: \"flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium text-gray-300 hover:text-cyber-green hover:bg-gray-800/50 transition-all duration-200\",\n                                                children: [\n                                                    user.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: user.avatar,\n                                                        alt: user.username,\n                                                        className: \"h-6 w-6 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: user.username\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs px-2 py-1 rounded-full bg-cyber-green/20 text-cyber-green\",\n                                                        children: user.plan.toUpperCase()\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 19\n                                            }, this),\n                                            isProfileOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-0 mt-2 w-48 bg-gray-900 border border-gray-700 rounded-lg shadow-lg py-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/profile\",\n                                                        className: \"flex items-center space-x-2 px-4 py-2 text-sm text-gray-300 hover:text-cyber-green hover:bg-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 110,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Profile\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 111,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/settings\",\n                                                        className: \"flex items-center space-x-2 px-4 py-2 text-sm text-gray-300 hover:text-cyber-green hover:bg-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 117,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Settings\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 118,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    user.role === \"super_admin\" || user.role === \"admin\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/admin\",\n                                                        className: \"flex items-center space-x-2 px-4 py-2 text-sm text-gray-300 hover:text-cyber-green hover:bg-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Admin Panel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 126,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 25\n                                                    }, this) : null,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                        className: \"my-1 border-gray-700\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{},\n                                                        className: \"flex items-center space-x-2 w-full px-4 py-2 text-sm text-red-400 hover:text-red-300 hover:bg-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 134,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Logout\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 135,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : // Guest Navigation\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/docs\",\n                                        className: \"text-gray-300 hover:text-cyber-green transition-colors\",\n                                        children: \"Dokumentasi\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/pricing\",\n                                        className: \"text-gray-300 hover:text-cyber-green transition-colors\",\n                                        children: \"Harga\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/login\",\n                                        className: \"cyber-btn\",\n                                        children: \"Masuk\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/register\",\n                                        className: \"cyber-btn-primary\",\n                                        children: \"Daftar\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsOpen(!isOpen),\n                                className: \"text-gray-300 hover:text-cyber-green p-2\",\n                                children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 25\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 53\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden bg-cyber-dark/95 backdrop-blur-md border-t border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-2 pt-2 pb-3 space-y-1\",\n                    children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            navigation.map((item)=>{\n                                const Icon = item.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: item.href,\n                                    className: `flex items-center space-x-3 px-3 py-2 rounded-lg text-base font-medium transition-all duration-200 ${isActive(item.href) ? \"bg-cyber-green/20 text-cyber-green border border-cyber-green/30\" : \"text-gray-300 hover:text-cyber-green hover:bg-gray-800/50\"}`,\n                                    onClick: ()=>setIsOpen(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 21\n                                }, this);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                className: \"my-2 border-gray-700\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/profile\",\n                                className: \"flex items-center space-x-3 px-3 py-2 rounded-lg text-base font-medium text-gray-300 hover:text-cyber-green hover:bg-gray-800/50\",\n                                onClick: ()=>setIsOpen(false),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Profile (\",\n                                            user.username,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{},\n                                className: \"flex items-center space-x-3 w-full px-3 py-2 rounded-lg text-base font-medium text-red-400 hover:text-red-300 hover:bg-gray-800/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Logout\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/docs\",\n                                className: \"block px-3 py-2 rounded-lg text-base font-medium text-gray-300 hover:text-cyber-green hover:bg-gray-800/50\",\n                                onClick: ()=>setIsOpen(false),\n                                children: \"Dokumentasi\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/pricing\",\n                                className: \"block px-3 py-2 rounded-lg text-base font-medium text-gray-300 hover:text-cyber-green hover:bg-gray-800/50\",\n                                onClick: ()=>setIsOpen(false),\n                                children: \"Harga\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/login\",\n                                className: \"block px-3 py-2 rounded-lg text-base font-medium cyber-btn text-center\",\n                                onClick: ()=>setIsOpen(false),\n                                children: \"Masuk\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/register\",\n                                className: \"block px-3 py-2 rounded-lg text-base font-medium cyber-btn-primary text-center\",\n                                onClick: ()=>setIsOpen(false),\n                                children: \"Daftar\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                lineNumber: 174,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Sidebar.tsx":
/*!********************************!*\
  !*** ./components/Sidebar.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-search.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Sidebar({ user }) {\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobileOpen, setIsMobileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const menuItems = [\n        {\n            id: \"dashboard\",\n            name: \"Dashboard\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            id: \"osint\",\n            name: \"OSINT Investigator\",\n            href: \"/osint\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            badge: \"HOT\"\n        },\n        {\n            id: \"scanner\",\n            name: \"Vulnerability Scanner\",\n            href: \"/scanner\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            premium: true\n        },\n        {\n            id: \"file-analyzer\",\n            name: \"File Analyzer\",\n            href: \"/file-analyzer\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            id: \"cve\",\n            name: \"CVE Intelligence\",\n            href: \"/cve\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            badge: \"NEW\"\n        },\n        {\n            id: \"dorking\",\n            name: \"Google Dorking\",\n            href: \"/dorking\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            id: \"tools\",\n            name: \"Security Tools\",\n            href: \"/tools\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            id: \"bot\",\n            name: \"Bot Center\",\n            href: \"/bot\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            premium: true,\n            adminOnly: true\n        },\n        {\n            id: \"playground\",\n            name: \"API Playground\",\n            href: \"/playground\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            premium: true\n        },\n        {\n            id: \"leaderboard\",\n            name: \"Leaderboard\",\n            href: \"/leaderboard\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            id: \"profile\",\n            name: \"Profile & API Keys\",\n            href: \"/profile\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n        },\n        {\n            id: \"plan\",\n            name: \"Plans & Billing\",\n            href: \"/plan\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n        },\n        {\n            id: \"docs\",\n            name: \"Documentation\",\n            href: \"/docs\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n        }\n    ];\n    // Filter menu items based on user role and plan\n    const filteredMenuItems = menuItems.filter((item)=>{\n        if (item.adminOnly && ![\n            \"super_admin\",\n            \"admin\"\n        ].includes(user.role)) {\n            return false;\n        }\n        if (item.premium && user.plan === \"gratis\") {\n            return false;\n        }\n        return true;\n    });\n    const getPlanColor = (plan)=>{\n        switch(plan){\n            case \"cybersecurity\":\n                return \"text-red-400 bg-red-900/20\";\n            case \"bughunter\":\n                return \"text-purple-400 bg-purple-900/20\";\n            case \"hobby\":\n                return \"text-green-400 bg-green-900/20\";\n            case \"pelajar\":\n                return \"text-blue-400 bg-blue-900/20\";\n            default:\n                return \"text-gray-400 bg-gray-900/20\";\n        }\n    };\n    const getPlanIcon = (plan)=>{\n        switch(plan){\n            case \"cybersecurity\":\n                return _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"];\n            case \"bughunter\":\n                return _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"];\n            case \"hobby\":\n                return _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"];\n            default:\n                return _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"];\n        }\n    };\n    // Close mobile sidebar when route changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsMobileOpen(false);\n    }, [\n        pathname\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isMobileOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 z-40 lg:hidden\",\n                onClick: ()=>setIsMobileOpen(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 182,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsMobileOpen(true),\n                className: \"fixed top-4 left-4 z-50 lg:hidden bg-gray-800 text-white p-2 rounded-lg border border-gray-700 hover:bg-gray-700 transition-colors\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `\n        fixed lg:relative top-0 left-0 h-full lg:h-screen bg-gray-900/95 backdrop-blur-sm border-r border-gray-800 z-50 lg:z-auto\n        transition-all duration-300 ease-in-out flex-shrink-0\n        ${isCollapsed ? \"w-16\" : \"w-64\"}\n        ${isMobileOpen ? \"translate-x-0\" : \"-translate-x-full lg:translate-x-0\"}\n      `,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 border-b border-gray-800\",\n                        children: [\n                            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-cyber-green rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-5 w-5 text-black\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-bold text-white font-cyber\",\n                                        children: \"KodeXGuard\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsMobileOpen(false),\n                                        className: \"lg:hidden text-gray-400 hover:text-white transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsCollapsed(!isCollapsed),\n                                        className: \"hidden lg:block text-gray-400 hover:text-white transition-colors\",\n                                        children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-800\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                user.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: user.avatar,\n                                    alt: user.username,\n                                    className: \"w-10 h-10 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 rounded-full bg-cyber-green/20 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-cyber-green font-bold\",\n                                        children: user.username.charAt(0).toUpperCase()\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 15\n                                }, this),\n                                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium text-white truncate\",\n                                            children: user.username\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1\",\n                                            children: [\n                                                (()=>{\n                                                    const PlanIcon = getPlanIcon(user.plan);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlanIcon, {\n                                                        className: \"h-3 w-3 text-nusantara-gold\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 28\n                                                    }, this);\n                                                })(),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `text-xs px-2 py-1 rounded-full font-semibold ${getPlanColor(user.plan)}`,\n                                                    children: user.plan.toUpperCase()\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex-1 overflow-y-auto py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1 px-2\",\n                            children: filteredMenuItems.map((item)=>{\n                                const Icon = item.icon;\n                                const isActive = pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: item.href,\n                                    className: `\n                    group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200\n                    ${isActive ? \"bg-cyber-green text-black\" : \"text-gray-300 hover:text-white hover:bg-gray-800/50\"}\n                    ${isCollapsed ? \"justify-center\" : \"justify-start\"}\n                  `,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: `h-5 w-5 flex-shrink-0 ${isCollapsed ? \"\" : \"mr-3\"}`\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 19\n                                        }, this),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex-1\",\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1\",\n                                                    children: [\n                                                        item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: `\n                            px-2 py-1 text-xs font-bold rounded-full\n                            ${item.badge === \"HOT\" ? \"bg-red-500 text-white\" : \"bg-blue-500 text-white\"}\n                          `,\n                                                            children: item.badge\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        item.premium && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-3 w-3 text-nusantara-gold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        item.adminOnly && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"h-3 w-3 text-orange-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true),\n                                        isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute left-16 bg-gray-800 text-white px-2 py-1 rounded-lg text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50\",\n                                            children: [\n                                                item.name,\n                                                item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `ml-2 px-1 py-0.5 text-xs rounded ${item.badge === \"HOT\" ? \"bg-red-500\" : \"bg-blue-500\"}`,\n                                                    children: item.badge\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, item.id, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-gray-800 p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: `\n              w-full flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors\n              ${isCollapsed ? \"justify-center\" : \"justify-start\"}\n            `,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: `h-5 w-5 ${isCollapsed ? \"\" : \"mr-3\"}`\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Notifications\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 32\n                                        }, this),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full\",\n                                            children: \"3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: `\n              w-full flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors\n              ${isCollapsed ? \"justify-center\" : \"justify-start\"}\n            `,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: `h-5 w-5 ${isCollapsed ? \"\" : \"mr-3\"}`\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Settings\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 32\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: `\n              w-full flex items-center px-3 py-2 text-sm font-medium text-red-400 hover:text-red-300 hover:bg-red-900/20 rounded-lg transition-colors\n              ${isCollapsed ? \"justify-center\" : \"justify-start\"}\n            `,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            className: `h-5 w-5 ${isCollapsed ? \"\" : \"mr-3\"}`\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Logout\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 32\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL1NpZGViYXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFMkM7QUFDZjtBQUNpQjtBQXlCeEI7QUFxQk4sU0FBUzJCLFFBQVEsRUFBRUMsSUFBSSxFQUFnQjtJQUNwRCxNQUFNLENBQUNDLGFBQWFDLGVBQWUsR0FBRzlCLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQytCLGNBQWNDLGdCQUFnQixHQUFHaEMsK0NBQVFBLENBQUM7SUFDakQsTUFBTWlDLFdBQVc5Qiw0REFBV0E7SUFFNUIsTUFBTStCLFlBQXdCO1FBQzVCO1lBQ0VDLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLE1BQU1sQyx3UEFBZUE7UUFDdkI7UUFDQTtZQUNFK0IsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsTUFBTWpDLHdQQUFNQTtZQUNaa0MsT0FBTztRQUNUO1FBQ0E7WUFDRUosSUFBSTtZQUNKQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsTUFBTWhDLHdQQUFNQTtZQUNaa0MsU0FBUztRQUNYO1FBQ0E7WUFDRUwsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsTUFBTS9CLHdQQUFVQTtRQUNsQjtRQUNBO1lBQ0U0QixJQUFJO1lBQ0pDLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxNQUFNOUIsd1BBQVFBO1lBQ2QrQixPQUFPO1FBQ1Q7UUFDQTtZQUNFSixJQUFJO1lBQ0pDLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxNQUFNdEIsd1BBQU1BO1FBQ2Q7UUFDQTtZQUNFbUIsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsTUFBTTdCLHlQQUFNQTtRQUNkO1FBQ0E7WUFDRTBCLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLE1BQU01Qix5UEFBR0E7WUFDVDhCLFNBQVM7WUFDVEMsV0FBVztRQUNiO1FBQ0E7WUFDRU4sSUFBSTtZQUNKQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsTUFBTTNCLHlQQUFJQTtZQUNWNkIsU0FBUztRQUNYO1FBQ0E7WUFDRUwsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsTUFBTTFCLHlQQUFNQTtRQUNkO1FBQ0E7WUFDRXVCLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLE1BQU16Qix5UEFBSUE7UUFDWjtRQUNBO1lBQ0VzQixJQUFJO1lBQ0pDLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxNQUFNeEIseVBBQVVBO1FBQ2xCO1FBQ0E7WUFDRXFCLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLE1BQU12Qix5UEFBUUE7UUFDaEI7S0FDRDtJQUVELGdEQUFnRDtJQUNoRCxNQUFNMkIsb0JBQW9CUixVQUFVUyxNQUFNLENBQUNDLENBQUFBO1FBQ3pDLElBQUlBLEtBQUtILFNBQVMsSUFBSSxDQUFDO1lBQUM7WUFBZTtTQUFRLENBQUNJLFFBQVEsQ0FBQ2pCLEtBQUtrQixJQUFJLEdBQUc7WUFDbkUsT0FBTztRQUNUO1FBQ0EsSUFBSUYsS0FBS0osT0FBTyxJQUFJWixLQUFLbUIsSUFBSSxLQUFLLFVBQVU7WUFDMUMsT0FBTztRQUNUO1FBQ0EsT0FBTztJQUNUO0lBRUEsTUFBTUMsZUFBZSxDQUFDRDtRQUNwQixPQUFRQTtZQUNOLEtBQUs7Z0JBQWlCLE9BQU87WUFDN0IsS0FBSztnQkFBYSxPQUFPO1lBQ3pCLEtBQUs7Z0JBQVMsT0FBTztZQUNyQixLQUFLO2dCQUFXLE9BQU87WUFDdkI7Z0JBQVMsT0FBTztRQUNsQjtJQUNGO0lBRUEsTUFBTUUsY0FBYyxDQUFDRjtRQUNuQixPQUFRQTtZQUNOLEtBQUs7Z0JBQWlCLE9BQU8xQix5UEFBS0E7WUFDbEMsS0FBSztnQkFBYSxPQUFPQyx5UEFBR0E7WUFDNUIsS0FBSztnQkFBUyxPQUFPSSx5UEFBSUE7WUFDekI7Z0JBQVMsT0FBT2IseVBBQUlBO1FBQ3RCO0lBQ0Y7SUFFQSwwQ0FBMEM7SUFDMUNaLGdEQUFTQSxDQUFDO1FBQ1IrQixnQkFBZ0I7SUFDbEIsR0FBRztRQUFDQztLQUFTO0lBRWIscUJBQ0U7O1lBRUdGLDhCQUNDLDhEQUFDbUI7Z0JBQ0NDLFdBQVU7Z0JBQ1ZDLFNBQVMsSUFBTXBCLGdCQUFnQjs7Ozs7OzBCQUtuQyw4REFBQ3FCO2dCQUNDRCxTQUFTLElBQU1wQixnQkFBZ0I7Z0JBQy9CbUIsV0FBVTswQkFFViw0RUFBQ2hDLHlQQUFJQTtvQkFBQ2dDLFdBQVU7Ozs7Ozs7Ozs7OzBCQUlsQiw4REFBQ0Q7Z0JBQUlDLFdBQVcsQ0FBQzs7O1FBR2YsRUFBRXRCLGNBQWMsU0FBUyxPQUFPO1FBQ2hDLEVBQUVFLGVBQWUsa0JBQWtCLHFDQUFxQztNQUMxRSxDQUFDOztrQ0FFQyw4REFBQ21CO3dCQUFJQyxXQUFVOzs0QkFDWixDQUFDdEIsNkJBQ0EsOERBQUNxQjtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDN0Msd1BBQU1BOzRDQUFDNkMsV0FBVTs7Ozs7Ozs7Ozs7a0RBRXBCLDhEQUFDRzt3Q0FBS0gsV0FBVTtrREFBa0M7Ozs7Ozs7Ozs7OzswQ0FJdEQsOERBQUNEO2dDQUFJQyxXQUFVOztrREFFYiw4REFBQ0U7d0NBQ0NELFNBQVMsSUFBTXBCLGdCQUFnQjt3Q0FDL0JtQixXQUFVO2tEQUVWLDRFQUFDL0IseVBBQUNBOzRDQUFDK0IsV0FBVTs7Ozs7Ozs7Ozs7a0RBSWYsOERBQUNFO3dDQUNDRCxTQUFTLElBQU10QixlQUFlLENBQUNEO3dDQUMvQnNCLFdBQVU7a0RBRVR0Qiw0QkFDQyw4REFBQ1gseVBBQVlBOzRDQUFDaUMsV0FBVTs7Ozs7aUVBRXhCLDhEQUFDbEMseVBBQVdBOzRDQUFDa0MsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTy9CLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7O2dDQUNadkIsS0FBSzJCLE1BQU0saUJBQ1YsOERBQUNDO29DQUFJQyxLQUFLN0IsS0FBSzJCLE1BQU07b0NBQUVHLEtBQUs5QixLQUFLK0IsUUFBUTtvQ0FBRVIsV0FBVTs7Ozs7eURBRXJELDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0c7d0NBQUtILFdBQVU7a0RBQ2J2QixLQUFLK0IsUUFBUSxDQUFDQyxNQUFNLENBQUMsR0FBR0MsV0FBVzs7Ozs7Ozs7Ozs7Z0NBS3pDLENBQUNoQyw2QkFDQSw4REFBQ3FCO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQW1DdkIsS0FBSytCLFFBQVE7Ozs7OztzREFDL0QsOERBQUNUOzRDQUFJQyxXQUFVOztnREFDWDtvREFDQSxNQUFNVyxXQUFXYixZQUFZckIsS0FBS21CLElBQUk7b0RBQ3RDLHFCQUFPLDhEQUFDZTt3REFBU1gsV0FBVTs7Ozs7O2dEQUM3Qjs4REFDQSw4REFBQ0c7b0RBQUtILFdBQVcsQ0FBQyw2Q0FBNkMsRUFBRUgsYUFBYXBCLEtBQUttQixJQUFJLEVBQUUsQ0FBQzs4REFDdkZuQixLQUFLbUIsSUFBSSxDQUFDYyxXQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FTbEMsOERBQUNFO3dCQUFJWixXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTtzQ0FDWlQsa0JBQWtCc0IsR0FBRyxDQUFDLENBQUNwQjtnQ0FDdEIsTUFBTXFCLE9BQU9yQixLQUFLTixJQUFJO2dDQUN0QixNQUFNNEIsV0FBV2pDLGFBQWFXLEtBQUtQLElBQUk7Z0NBRXZDLHFCQUNFLDhEQUFDbkMsaURBQUlBO29DQUVIbUMsTUFBTU8sS0FBS1AsSUFBSTtvQ0FDZmMsV0FBVyxDQUFDOztvQkFFVixFQUFFZSxXQUNFLDhCQUNBLHNEQUNIO29CQUNELEVBQUVyQyxjQUFjLG1CQUFtQixnQkFBZ0I7a0JBQ3JELENBQUM7O3NEQUVELDhEQUFDb0M7NENBQUtkLFdBQVcsQ0FBQyxzQkFBc0IsRUFBRXRCLGNBQWMsS0FBSyxPQUFPLENBQUM7Ozs7Ozt3Q0FFcEUsQ0FBQ0EsNkJBQ0E7OzhEQUNFLDhEQUFDeUI7b0RBQUtILFdBQVU7OERBQVVQLEtBQUtSLElBQUk7Ozs7Ozs4REFHbkMsOERBQUNjO29EQUFJQyxXQUFVOzt3REFDWlAsS0FBS0wsS0FBSyxrQkFDVCw4REFBQ2U7NERBQUtILFdBQVcsQ0FBQzs7NEJBRWhCLEVBQUVQLEtBQUtMLEtBQUssS0FBSyxRQUFRLDBCQUEwQix5QkFBeUI7MEJBQzlFLENBQUM7c0VBQ0VLLEtBQUtMLEtBQUs7Ozs7Ozt3REFJZEssS0FBS0osT0FBTyxrQkFDWCw4REFBQ25CLHlQQUFLQTs0REFBQzhCLFdBQVU7Ozs7Ozt3REFHbEJQLEtBQUtILFNBQVMsa0JBQ2IsOERBQUNsQix5UEFBUUE7NERBQUM0QixXQUFVOzs7Ozs7Ozs7Ozs7Ozt3Q0FPM0J0Qiw2QkFDQyw4REFBQ3FCOzRDQUFJQyxXQUFVOztnREFDWlAsS0FBS1IsSUFBSTtnREFDVFEsS0FBS0wsS0FBSyxrQkFDVCw4REFBQ2U7b0RBQUtILFdBQVcsQ0FBQyxpQ0FBaUMsRUFDakRQLEtBQUtMLEtBQUssS0FBSyxRQUFRLGVBQWUsY0FDdkMsQ0FBQzs4REFDQ0ssS0FBS0wsS0FBSzs7Ozs7Ozs7Ozs7OzttQ0EvQ2RLLEtBQUtULEVBQUU7Ozs7OzRCQXNEbEI7Ozs7Ozs7Ozs7O2tDQUtKLDhEQUFDZTt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUViLDhEQUFDRTtvQ0FBT0YsV0FBVyxDQUFDOztjQUVsQixFQUFFdEIsY0FBYyxtQkFBbUIsZ0JBQWdCO1lBQ3JELENBQUM7O3NEQUNDLDhEQUFDSix5UEFBSUE7NENBQUMwQixXQUFXLENBQUMsUUFBUSxFQUFFdEIsY0FBYyxLQUFLLE9BQU8sQ0FBQzs7Ozs7O3dDQUN0RCxDQUFDQSw2QkFBZSw4REFBQ3lCO3NEQUFLOzs7Ozs7d0NBQ3RCLENBQUN6Qiw2QkFDQSw4REFBQ3lCOzRDQUFLSCxXQUFVO3NEQUErRDs7Ozs7Ozs7Ozs7OzhDQUtuRiw4REFBQ0U7b0NBQU9GLFdBQVcsQ0FBQzs7Y0FFbEIsRUFBRXRCLGNBQWMsbUJBQW1CLGdCQUFnQjtZQUNyRCxDQUFDOztzREFDQyw4REFBQ04seVBBQVFBOzRDQUFDNEIsV0FBVyxDQUFDLFFBQVEsRUFBRXRCLGNBQWMsS0FBSyxPQUFPLENBQUM7Ozs7Ozt3Q0FDMUQsQ0FBQ0EsNkJBQWUsOERBQUN5QjtzREFBSzs7Ozs7Ozs7Ozs7OzhDQUl6Qiw4REFBQ0Q7b0NBQU9GLFdBQVcsQ0FBQzs7Y0FFbEIsRUFBRXRCLGNBQWMsbUJBQW1CLGdCQUFnQjtZQUNyRCxDQUFDOztzREFDQyw4REFBQ0wseVBBQU1BOzRDQUFDMkIsV0FBVyxDQUFDLFFBQVEsRUFBRXRCLGNBQWMsS0FBSyxPQUFPLENBQUM7Ozs7Ozt3Q0FDeEQsQ0FBQ0EsNkJBQWUsOERBQUN5QjtzREFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9yQyIsInNvdXJjZXMiOlsid2VicGFjazovL2tvZGV4Z3VhcmQvLi9jb21wb25lbnRzL1NpZGViYXIudHN4PzZiYTkiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluaydcbmltcG9ydCB7IHVzZVBhdGhuYW1lIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJ1xuaW1wb3J0IHsgXG4gIExheW91dERhc2hib2FyZCxcbiAgU2VhcmNoLFxuICBTaGllbGQsXG4gIEZpbGVTZWFyY2gsXG4gIERhdGFiYXNlLFxuICBXcmVuY2gsXG4gIEJvdCxcbiAgQ29kZSxcbiAgVHJvcGh5LFxuICBVc2VyLFxuICBDcmVkaXRDYXJkLFxuICBCb29rT3BlbixcbiAgVGFyZ2V0LFxuICBDaGV2cm9uTGVmdCxcbiAgQ2hldnJvblJpZ2h0LFxuICBNZW51LFxuICBYLFxuICBDcm93bixcbiAgWmFwLFxuICBTZXR0aW5ncyxcbiAgTG9nT3V0LFxuICBCZWxsLFxuICBTdGFyXG59IGZyb20gJ2x1Y2lkZS1yZWFjdCdcblxuaW50ZXJmYWNlIFNpZGViYXJQcm9wcyB7XG4gIHVzZXI6IHtcbiAgICB1c2VybmFtZTogc3RyaW5nXG4gICAgYXZhdGFyPzogc3RyaW5nXG4gICAgcm9sZTogc3RyaW5nXG4gICAgcGxhbjogc3RyaW5nXG4gIH1cbn1cblxuaW50ZXJmYWNlIE1lbnVJdGVtIHtcbiAgaWQ6IHN0cmluZ1xuICBuYW1lOiBzdHJpbmdcbiAgaHJlZjogc3RyaW5nXG4gIGljb246IGFueVxuICBiYWRnZT86IHN0cmluZ1xuICBwcmVtaXVtPzogYm9vbGVhblxuICBhZG1pbk9ubHk/OiBib29sZWFuXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFNpZGViYXIoeyB1c2VyIH06IFNpZGViYXJQcm9wcykge1xuICBjb25zdCBbaXNDb2xsYXBzZWQsIHNldElzQ29sbGFwc2VkXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbaXNNb2JpbGVPcGVuLCBzZXRJc01vYmlsZU9wZW5dID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IHBhdGhuYW1lID0gdXNlUGF0aG5hbWUoKVxuXG4gIGNvbnN0IG1lbnVJdGVtczogTWVudUl0ZW1bXSA9IFtcbiAgICB7XG4gICAgICBpZDogJ2Rhc2hib2FyZCcsXG4gICAgICBuYW1lOiAnRGFzaGJvYXJkJyxcbiAgICAgIGhyZWY6ICcvZGFzaGJvYXJkJyxcbiAgICAgIGljb246IExheW91dERhc2hib2FyZFxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICdvc2ludCcsXG4gICAgICBuYW1lOiAnT1NJTlQgSW52ZXN0aWdhdG9yJyxcbiAgICAgIGhyZWY6ICcvb3NpbnQnLFxuICAgICAgaWNvbjogU2VhcmNoLFxuICAgICAgYmFkZ2U6ICdIT1QnXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJ3NjYW5uZXInLFxuICAgICAgbmFtZTogJ1Z1bG5lcmFiaWxpdHkgU2Nhbm5lcicsXG4gICAgICBocmVmOiAnL3NjYW5uZXInLFxuICAgICAgaWNvbjogU2hpZWxkLFxuICAgICAgcHJlbWl1bTogdHJ1ZVxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICdmaWxlLWFuYWx5emVyJyxcbiAgICAgIG5hbWU6ICdGaWxlIEFuYWx5emVyJyxcbiAgICAgIGhyZWY6ICcvZmlsZS1hbmFseXplcicsXG4gICAgICBpY29uOiBGaWxlU2VhcmNoXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJ2N2ZScsXG4gICAgICBuYW1lOiAnQ1ZFIEludGVsbGlnZW5jZScsXG4gICAgICBocmVmOiAnL2N2ZScsXG4gICAgICBpY29uOiBEYXRhYmFzZSxcbiAgICAgIGJhZGdlOiAnTkVXJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICdkb3JraW5nJyxcbiAgICAgIG5hbWU6ICdHb29nbGUgRG9ya2luZycsXG4gICAgICBocmVmOiAnL2RvcmtpbmcnLFxuICAgICAgaWNvbjogVGFyZ2V0XG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJ3Rvb2xzJyxcbiAgICAgIG5hbWU6ICdTZWN1cml0eSBUb29scycsXG4gICAgICBocmVmOiAnL3Rvb2xzJyxcbiAgICAgIGljb246IFdyZW5jaFxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICdib3QnLFxuICAgICAgbmFtZTogJ0JvdCBDZW50ZXInLFxuICAgICAgaHJlZjogJy9ib3QnLFxuICAgICAgaWNvbjogQm90LFxuICAgICAgcHJlbWl1bTogdHJ1ZSxcbiAgICAgIGFkbWluT25seTogdHJ1ZVxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICdwbGF5Z3JvdW5kJyxcbiAgICAgIG5hbWU6ICdBUEkgUGxheWdyb3VuZCcsXG4gICAgICBocmVmOiAnL3BsYXlncm91bmQnLFxuICAgICAgaWNvbjogQ29kZSxcbiAgICAgIHByZW1pdW06IHRydWVcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAnbGVhZGVyYm9hcmQnLFxuICAgICAgbmFtZTogJ0xlYWRlcmJvYXJkJyxcbiAgICAgIGhyZWY6ICcvbGVhZGVyYm9hcmQnLFxuICAgICAgaWNvbjogVHJvcGh5XG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJ3Byb2ZpbGUnLFxuICAgICAgbmFtZTogJ1Byb2ZpbGUgJiBBUEkgS2V5cycsXG4gICAgICBocmVmOiAnL3Byb2ZpbGUnLFxuICAgICAgaWNvbjogVXNlclxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICdwbGFuJyxcbiAgICAgIG5hbWU6ICdQbGFucyAmIEJpbGxpbmcnLFxuICAgICAgaHJlZjogJy9wbGFuJyxcbiAgICAgIGljb246IENyZWRpdENhcmRcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAnZG9jcycsXG4gICAgICBuYW1lOiAnRG9jdW1lbnRhdGlvbicsXG4gICAgICBocmVmOiAnL2RvY3MnLFxuICAgICAgaWNvbjogQm9va09wZW5cbiAgICB9XG4gIF1cblxuICAvLyBGaWx0ZXIgbWVudSBpdGVtcyBiYXNlZCBvbiB1c2VyIHJvbGUgYW5kIHBsYW5cbiAgY29uc3QgZmlsdGVyZWRNZW51SXRlbXMgPSBtZW51SXRlbXMuZmlsdGVyKGl0ZW0gPT4ge1xuICAgIGlmIChpdGVtLmFkbWluT25seSAmJiAhWydzdXBlcl9hZG1pbicsICdhZG1pbiddLmluY2x1ZGVzKHVzZXIucm9sZSkpIHtcbiAgICAgIHJldHVybiBmYWxzZVxuICAgIH1cbiAgICBpZiAoaXRlbS5wcmVtaXVtICYmIHVzZXIucGxhbiA9PT0gJ2dyYXRpcycpIHtcbiAgICAgIHJldHVybiBmYWxzZVxuICAgIH1cbiAgICByZXR1cm4gdHJ1ZVxuICB9KVxuXG4gIGNvbnN0IGdldFBsYW5Db2xvciA9IChwbGFuOiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHBsYW4pIHtcbiAgICAgIGNhc2UgJ2N5YmVyc2VjdXJpdHknOiByZXR1cm4gJ3RleHQtcmVkLTQwMCBiZy1yZWQtOTAwLzIwJ1xuICAgICAgY2FzZSAnYnVnaHVudGVyJzogcmV0dXJuICd0ZXh0LXB1cnBsZS00MDAgYmctcHVycGxlLTkwMC8yMCdcbiAgICAgIGNhc2UgJ2hvYmJ5JzogcmV0dXJuICd0ZXh0LWdyZWVuLTQwMCBiZy1ncmVlbi05MDAvMjAnXG4gICAgICBjYXNlICdwZWxhamFyJzogcmV0dXJuICd0ZXh0LWJsdWUtNDAwIGJnLWJsdWUtOTAwLzIwJ1xuICAgICAgZGVmYXVsdDogcmV0dXJuICd0ZXh0LWdyYXktNDAwIGJnLWdyYXktOTAwLzIwJ1xuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGdldFBsYW5JY29uID0gKHBsYW46IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAocGxhbikge1xuICAgICAgY2FzZSAnY3liZXJzZWN1cml0eSc6IHJldHVybiBDcm93blxuICAgICAgY2FzZSAnYnVnaHVudGVyJzogcmV0dXJuIFphcFxuICAgICAgY2FzZSAnaG9iYnknOiByZXR1cm4gU3RhclxuICAgICAgZGVmYXVsdDogcmV0dXJuIFVzZXJcbiAgICB9XG4gIH1cblxuICAvLyBDbG9zZSBtb2JpbGUgc2lkZWJhciB3aGVuIHJvdXRlIGNoYW5nZXNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBzZXRJc01vYmlsZU9wZW4oZmFsc2UpXG4gIH0sIFtwYXRobmFtZV0pXG5cbiAgcmV0dXJuIChcbiAgICA8PlxuICAgICAgey8qIE1vYmlsZSBPdmVybGF5ICovfVxuICAgICAge2lzTW9iaWxlT3BlbiAmJiAoXG4gICAgICAgIDxkaXYgXG4gICAgICAgICAgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ibGFjay81MCB6LTQwIGxnOmhpZGRlblwiXG4gICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNNb2JpbGVPcGVuKGZhbHNlKX1cbiAgICAgICAgLz5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBNb2JpbGUgTWVudSBCdXR0b24gKi99XG4gICAgICA8YnV0dG9uXG4gICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzTW9iaWxlT3Blbih0cnVlKX1cbiAgICAgICAgY2xhc3NOYW1lPVwiZml4ZWQgdG9wLTQgbGVmdC00IHotNTAgbGc6aGlkZGVuIGJnLWdyYXktODAwIHRleHQtd2hpdGUgcC0yIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTcwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICA+XG4gICAgICAgIDxNZW51IGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgPC9idXR0b24+XG5cbiAgICAgIHsvKiBTaWRlYmFyICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9e2BcbiAgICAgICAgZml4ZWQgbGc6cmVsYXRpdmUgdG9wLTAgbGVmdC0wIGgtZnVsbCBsZzpoLXNjcmVlbiBiZy1ncmF5LTkwMC85NSBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlci1yIGJvcmRlci1ncmF5LTgwMCB6LTUwIGxnOnotYXV0b1xuICAgICAgICB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXQgZmxleC1zaHJpbmstMFxuICAgICAgICAke2lzQ29sbGFwc2VkID8gJ3ctMTYnIDogJ3ctNjQnfVxuICAgICAgICAke2lzTW9iaWxlT3BlbiA/ICd0cmFuc2xhdGUteC0wJyA6ICctdHJhbnNsYXRlLXgtZnVsbCBsZzp0cmFuc2xhdGUteC0wJ31cbiAgICAgIGB9PlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwLTQgYm9yZGVyLWIgYm9yZGVyLWdyYXktODAwXCI+XG4gICAgICAgICAgeyFpc0NvbGxhcHNlZCAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctY3liZXItZ3JlZW4gcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxTaGllbGQgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWJsYWNrXCIgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtYm9sZCB0ZXh0LXdoaXRlIGZvbnQtY3liZXJcIj5Lb2RlWEd1YXJkPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgICBcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgey8qIE1vYmlsZSBDbG9zZSBCdXR0b24gKi99XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzTW9iaWxlT3BlbihmYWxzZSl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImxnOmhpZGRlbiB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgXG4gICAgICAgICAgICB7LyogRGVza3RvcCBDb2xsYXBzZSBCdXR0b24gKi99XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzQ29sbGFwc2VkKCFpc0NvbGxhcHNlZCl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImhpZGRlbiBsZzpibG9jayB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7aXNDb2xsYXBzZWQgPyAoXG4gICAgICAgICAgICAgICAgPENoZXZyb25SaWdodCBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICA8Q2hldnJvbkxlZnQgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFVzZXIgUHJvZmlsZSAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgYm9yZGVyLWIgYm9yZGVyLWdyYXktODAwXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgIHt1c2VyLmF2YXRhciA/IChcbiAgICAgICAgICAgICAgPGltZyBzcmM9e3VzZXIuYXZhdGFyfSBhbHQ9e3VzZXIudXNlcm5hbWV9IGNsYXNzTmFtZT1cInctMTAgaC0xMCByb3VuZGVkLWZ1bGxcIiAvPlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEwIGgtMTAgcm91bmRlZC1mdWxsIGJnLWN5YmVyLWdyZWVuLzIwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1jeWJlci1ncmVlbiBmb250LWJvbGRcIj5cbiAgICAgICAgICAgICAgICAgIHt1c2VyLnVzZXJuYW1lLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpfVxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgICAgXG4gICAgICAgICAgICB7IWlzQ29sbGFwc2VkICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC13aGl0ZSB0cnVuY2F0ZVwiPnt1c2VyLnVzZXJuYW1lfTwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xXCI+XG4gICAgICAgICAgICAgICAgICB7KCgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgUGxhbkljb24gPSBnZXRQbGFuSWNvbih1c2VyLnBsYW4pXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiA8UGxhbkljb24gY2xhc3NOYW1lPVwiaC0zIHctMyB0ZXh0LW51c2FudGFyYS1nb2xkXCIgLz5cbiAgICAgICAgICAgICAgICAgIH0pKCl9XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2B0ZXh0LXhzIHB4LTIgcHktMSByb3VuZGVkLWZ1bGwgZm9udC1zZW1pYm9sZCAke2dldFBsYW5Db2xvcih1c2VyLnBsYW4pfWB9PlxuICAgICAgICAgICAgICAgICAgICB7dXNlci5wbGFuLnRvVXBwZXJDYXNlKCl9XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIE5hdmlnYXRpb24gTWVudSAqL31cbiAgICAgICAgPG5hdiBjbGFzc05hbWU9XCJmbGV4LTEgb3ZlcmZsb3cteS1hdXRvIHB5LTRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMSBweC0yXCI+XG4gICAgICAgICAgICB7ZmlsdGVyZWRNZW51SXRlbXMubWFwKChpdGVtKSA9PiB7XG4gICAgICAgICAgICAgIGNvbnN0IEljb24gPSBpdGVtLmljb25cbiAgICAgICAgICAgICAgY29uc3QgaXNBY3RpdmUgPSBwYXRobmFtZSA9PT0gaXRlbS5ocmVmXG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICBrZXk9e2l0ZW0uaWR9XG4gICAgICAgICAgICAgICAgICBocmVmPXtpdGVtLmhyZWZ9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BcbiAgICAgICAgICAgICAgICAgICAgZ3JvdXAgZmxleCBpdGVtcy1jZW50ZXIgcHgtMyBweS0yIHRleHQtc20gZm9udC1tZWRpdW0gcm91bmRlZC1sZyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDBcbiAgICAgICAgICAgICAgICAgICAgJHtpc0FjdGl2ZSBcbiAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1jeWJlci1ncmVlbiB0ZXh0LWJsYWNrJyBcbiAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktMzAwIGhvdmVyOnRleHQtd2hpdGUgaG92ZXI6YmctZ3JheS04MDAvNTAnXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgJHtpc0NvbGxhcHNlZCA/ICdqdXN0aWZ5LWNlbnRlcicgOiAnanVzdGlmeS1zdGFydCd9XG4gICAgICAgICAgICAgICAgICBgfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxJY29uIGNsYXNzTmFtZT17YGgtNSB3LTUgZmxleC1zaHJpbmstMCAke2lzQ29sbGFwc2VkID8gJycgOiAnbXItMyd9YH0gLz5cbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgeyFpc0NvbGxhcHNlZCAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZmxleC0xXCI+e2l0ZW0ubmFtZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgICAgey8qIEJhZGdlcyAqL31cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW0uYmFkZ2UgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBweC0yIHB5LTEgdGV4dC14cyBmb250LWJvbGQgcm91bmRlZC1mdWxsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgJHtpdGVtLmJhZGdlID09PSAnSE9UJyA/ICdiZy1yZWQtNTAwIHRleHQtd2hpdGUnIDogJ2JnLWJsdWUtNTAwIHRleHQtd2hpdGUnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBgfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5iYWRnZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW0ucHJlbWl1bSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxDcm93biBjbGFzc05hbWU9XCJoLTMgdy0zIHRleHQtbnVzYW50YXJhLWdvbGRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW0uYWRtaW5Pbmx5ICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFNldHRpbmdzIGNsYXNzTmFtZT1cImgtMyB3LTMgdGV4dC1vcmFuZ2UtNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgey8qIFRvb2x0aXAgZm9yIGNvbGxhcHNlZCBzdGF0ZSAqL31cbiAgICAgICAgICAgICAgICAgIHtpc0NvbGxhcHNlZCAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgbGVmdC0xNiBiZy1ncmF5LTgwMCB0ZXh0LXdoaXRlIHB4LTIgcHktMSByb3VuZGVkLWxnIHRleHQtc20gb3BhY2l0eS0wIGdyb3VwLWhvdmVyOm9wYWNpdHktMTAwIHRyYW5zaXRpb24tb3BhY2l0eSBkdXJhdGlvbi0yMDAgcG9pbnRlci1ldmVudHMtbm9uZSB3aGl0ZXNwYWNlLW5vd3JhcCB6LTUwXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2l0ZW0ubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5iYWRnZSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BtbC0yIHB4LTEgcHktMC41IHRleHQteHMgcm91bmRlZCAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICBpdGVtLmJhZGdlID09PSAnSE9UJyA/ICdiZy1yZWQtNTAwJyA6ICdiZy1ibHVlLTUwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW0uYmFkZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgfSl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvbmF2PlxuXG4gICAgICAgIHsvKiBGb290ZXIgQWN0aW9ucyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItdCBib3JkZXItZ3JheS04MDAgcC00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgIHsvKiBOb3RpZmljYXRpb25zICovfVxuICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9e2BcbiAgICAgICAgICAgICAgdy1mdWxsIGZsZXggaXRlbXMtY2VudGVyIHB4LTMgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS0zMDAgaG92ZXI6dGV4dC13aGl0ZSBob3ZlcjpiZy1ncmF5LTgwMC81MCByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzXG4gICAgICAgICAgICAgICR7aXNDb2xsYXBzZWQgPyAnanVzdGlmeS1jZW50ZXInIDogJ2p1c3RpZnktc3RhcnQnfVxuICAgICAgICAgICAgYH0+XG4gICAgICAgICAgICAgIDxCZWxsIGNsYXNzTmFtZT17YGgtNSB3LTUgJHtpc0NvbGxhcHNlZCA/ICcnIDogJ21yLTMnfWB9IC8+XG4gICAgICAgICAgICAgIHshaXNDb2xsYXBzZWQgJiYgPHNwYW4+Tm90aWZpY2F0aW9uczwvc3Bhbj59XG4gICAgICAgICAgICAgIHshaXNDb2xsYXBzZWQgJiYgKFxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLWF1dG8gYmctcmVkLTUwMCB0ZXh0LXdoaXRlIHRleHQteHMgcHgtMiBweS0xIHJvdW5kZWQtZnVsbFwiPjM8L3NwYW4+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgey8qIFNldHRpbmdzICovfVxuICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9e2BcbiAgICAgICAgICAgICAgdy1mdWxsIGZsZXggaXRlbXMtY2VudGVyIHB4LTMgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS0zMDAgaG92ZXI6dGV4dC13aGl0ZSBob3ZlcjpiZy1ncmF5LTgwMC81MCByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzXG4gICAgICAgICAgICAgICR7aXNDb2xsYXBzZWQgPyAnanVzdGlmeS1jZW50ZXInIDogJ2p1c3RpZnktc3RhcnQnfVxuICAgICAgICAgICAgYH0+XG4gICAgICAgICAgICAgIDxTZXR0aW5ncyBjbGFzc05hbWU9e2BoLTUgdy01ICR7aXNDb2xsYXBzZWQgPyAnJyA6ICdtci0zJ31gfSAvPlxuICAgICAgICAgICAgICB7IWlzQ29sbGFwc2VkICYmIDxzcGFuPlNldHRpbmdzPC9zcGFuPn1cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgICB7LyogTG9nb3V0ICovfVxuICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9e2BcbiAgICAgICAgICAgICAgdy1mdWxsIGZsZXggaXRlbXMtY2VudGVyIHB4LTMgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtcmVkLTQwMCBob3Zlcjp0ZXh0LXJlZC0zMDAgaG92ZXI6YmctcmVkLTkwMC8yMCByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzXG4gICAgICAgICAgICAgICR7aXNDb2xsYXBzZWQgPyAnanVzdGlmeS1jZW50ZXInIDogJ2p1c3RpZnktc3RhcnQnfVxuICAgICAgICAgICAgYH0+XG4gICAgICAgICAgICAgIDxMb2dPdXQgY2xhc3NOYW1lPXtgaC01IHctNSAke2lzQ29sbGFwc2VkID8gJycgOiAnbXItMyd9YH0gLz5cbiAgICAgICAgICAgICAgeyFpc0NvbGxhcHNlZCAmJiA8c3Bhbj5Mb2dvdXQ8L3NwYW4+fVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC8+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkxpbmsiLCJ1c2VQYXRobmFtZSIsIkxheW91dERhc2hib2FyZCIsIlNlYXJjaCIsIlNoaWVsZCIsIkZpbGVTZWFyY2giLCJEYXRhYmFzZSIsIldyZW5jaCIsIkJvdCIsIkNvZGUiLCJUcm9waHkiLCJVc2VyIiwiQ3JlZGl0Q2FyZCIsIkJvb2tPcGVuIiwiVGFyZ2V0IiwiQ2hldnJvbkxlZnQiLCJDaGV2cm9uUmlnaHQiLCJNZW51IiwiWCIsIkNyb3duIiwiWmFwIiwiU2V0dGluZ3MiLCJMb2dPdXQiLCJCZWxsIiwiU3RhciIsIlNpZGViYXIiLCJ1c2VyIiwiaXNDb2xsYXBzZWQiLCJzZXRJc0NvbGxhcHNlZCIsImlzTW9iaWxlT3BlbiIsInNldElzTW9iaWxlT3BlbiIsInBhdGhuYW1lIiwibWVudUl0ZW1zIiwiaWQiLCJuYW1lIiwiaHJlZiIsImljb24iLCJiYWRnZSIsInByZW1pdW0iLCJhZG1pbk9ubHkiLCJmaWx0ZXJlZE1lbnVJdGVtcyIsImZpbHRlciIsIml0ZW0iLCJpbmNsdWRlcyIsInJvbGUiLCJwbGFuIiwiZ2V0UGxhbkNvbG9yIiwiZ2V0UGxhbkljb24iLCJkaXYiLCJjbGFzc05hbWUiLCJvbkNsaWNrIiwiYnV0dG9uIiwic3BhbiIsImF2YXRhciIsImltZyIsInNyYyIsImFsdCIsInVzZXJuYW1lIiwiY2hhckF0IiwidG9VcHBlckNhc2UiLCJQbGFuSWNvbiIsIm5hdiIsIm1hcCIsIkljb24iLCJpc0FjdGl2ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/Sidebar.tsx\n");

/***/ }),

/***/ "(rsc)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7cffc49d1683\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb2RleGd1YXJkLy4vc3R5bGVzL2dsb2JhbHMuY3NzPzZkZDQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3Y2ZmYzQ5ZDE2ODNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(rsc)/./styles/globals.css\");\n\n\n\nconst metadata = {\n    title: \"KodeXGuard - Platform Cybersecurity & Bug Hunting\",\n    description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting dengan dukungan Bot WhatsApp/Telegram\",\n    keywords: \"cybersecurity, bug hunting, OSINT, vulnerability scanner, CVE, penetration testing, security tools\",\n    authors: [\n        {\n            name: \"KodeXGuard Team\"\n        }\n    ],\n    creator: \"KodeXGuard\",\n    publisher: \"KodeXGuard\",\n    robots: \"index, follow\",\n    openGraph: {\n        type: \"website\",\n        locale: \"id_ID\",\n        url: \"https://kodexguard.com\",\n        title: \"KodeXGuard - Platform Cybersecurity & Bug Hunting\",\n        description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting\",\n        siteName: \"KodeXGuard\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"KodeXGuard - Platform Cybersecurity & Bug Hunting\",\n        description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting\"\n    },\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#00ff41\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"id\",\n        className: \"dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} min-h-screen bg-gradient-cyber text-white antialiased`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"matrix-bg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-b from-transparent via-cyber-dark/20 to-cyber-dark\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n              // Matrix Rain Effect\n              function createMatrixRain() {\n                const canvas = document.createElement('canvas');\n                const ctx = canvas.getContext('2d');\n                canvas.style.position = 'fixed';\n                canvas.style.top = '0';\n                canvas.style.left = '0';\n                canvas.style.width = '100%';\n                canvas.style.height = '100%';\n                canvas.style.pointerEvents = 'none';\n                canvas.style.zIndex = '-1';\n                canvas.style.opacity = '0.1';\n                document.body.appendChild(canvas);\n                \n                canvas.width = window.innerWidth;\n                canvas.height = window.innerHeight;\n                \n                const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';\n                const charArray = chars.split('');\n                const fontSize = 14;\n                const columns = canvas.width / fontSize;\n                const drops = [];\n                \n                for (let i = 0; i < columns; i++) {\n                  drops[i] = 1;\n                }\n                \n                function draw() {\n                  ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';\n                  ctx.fillRect(0, 0, canvas.width, canvas.height);\n                  \n                  ctx.fillStyle = '#00ff41';\n                  ctx.font = fontSize + 'px monospace';\n                  \n                  for (let i = 0; i < drops.length; i++) {\n                    const text = charArray[Math.floor(Math.random() * charArray.length)];\n                    ctx.fillText(text, i * fontSize, drops[i] * fontSize);\n                    \n                    if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {\n                      drops[i] = 0;\n                    }\n                    drops[i]++;\n                  }\n                }\n                \n                setInterval(draw, 50);\n                \n                window.addEventListener('resize', () => {\n                  canvas.width = window.innerWidth;\n                  canvas.height = window.innerHeight;\n                });\n              }\n              \n              // Initialize matrix effect after page load\n              if (document.readyState === 'loading') {\n                document.addEventListener('DOMContentLoaded', createMatrixRain);\n              } else {\n                createMatrixRain();\n              }\n            `\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/settings/page.tsx":
/*!*******************************!*\
  !*** ./app/settings/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\settings\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();