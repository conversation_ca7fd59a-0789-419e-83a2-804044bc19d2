"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/user/api-keys/route";
exports.ids = ["app/api/user/api-keys/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fapi-keys%2Froute&page=%2Fapi%2Fuser%2Fapi-keys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fapi-keys%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fapi-keys%2Froute&page=%2Fapi%2Fuser%2Fapi-keys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fapi-keys%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Users_Downloads_kodeXGuard_app_api_user_api_keys_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/user/api-keys/route.ts */ \"(rsc)/./app/api/user/api-keys/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/user/api-keys/route\",\n        pathname: \"/api/user/api-keys\",\n        filename: \"route\",\n        bundlePath: \"app/api/user/api-keys/route\"\n    },\n    resolvedPagePath: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\api\\\\user\\\\api-keys\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Users_Downloads_kodeXGuard_app_api_user_api_keys_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/user/api-keys/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fapi-keys%2Froute&page=%2Fapi%2Fuser%2Fapi-keys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fapi-keys%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/user/api-keys/route.ts":
/*!****************************************!*\
  !*** ./app/api/user/api-keys/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function GET(request) {\n    try {\n        // Get auth token\n        const authHeader = request.headers.get(\"authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        // Simple token validation for testing\n        if (!token.startsWith(\"kxg_\")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid token\"\n            }, {\n                status: 401\n            });\n        }\n        // Mock API keys data\n        const apiKeys = [\n            {\n                id: \"key_001\",\n                name: \"Production API\",\n                keyPrefix: \"kxg_prod_\",\n                isActive: true,\n                permissions: [\n                    \"scan:read\",\n                    \"scan:write\",\n                    \"osint:read\",\n                    \"osint:write\",\n                    \"notifications:read\"\n                ],\n                lastUsed: new Date(Date.now() - 3600000).toISOString(),\n                usageCount: 15420,\n                rateLimit: 1000,\n                allowedIPs: [\n                    \"*************\",\n                    \"*********\"\n                ],\n                createdAt: new Date(Date.now() - 86400000 * 180).toISOString(),\n                expiresAt: new Date(Date.now() + 86400000 * 185).toISOString() // 6 months from now\n            },\n            {\n                id: \"key_002\",\n                name: \"Development API\",\n                keyPrefix: \"kxg_dev_\",\n                isActive: true,\n                permissions: [\n                    \"scan:read\",\n                    \"osint:read\",\n                    \"notifications:read\"\n                ],\n                lastUsed: new Date(Date.now() - 86400000).toISOString(),\n                usageCount: 2340,\n                rateLimit: 100,\n                allowedIPs: [\n                    \"127.0.0.1\",\n                    \"***********/24\"\n                ],\n                createdAt: new Date(Date.now() - 86400000 * 30).toISOString()\n            },\n            {\n                id: \"key_003\",\n                name: \"Mobile App API\",\n                keyPrefix: \"kxg_mobile_\",\n                isActive: false,\n                permissions: [\n                    \"scan:read\",\n                    \"osint:read\"\n                ],\n                lastUsed: new Date(Date.now() - 86400000 * 7).toISOString(),\n                usageCount: 890,\n                rateLimit: 500,\n                allowedIPs: [],\n                createdAt: new Date(Date.now() - 86400000 * 60).toISOString(),\n                expiresAt: new Date(Date.now() + 86400000 * 305).toISOString() // 10 months from now\n            },\n            {\n                id: \"key_004\",\n                name: \"Webhook Integration\",\n                keyPrefix: \"kxg_webhook_\",\n                isActive: true,\n                permissions: [\n                    \"notifications:write\",\n                    \"scan:read\"\n                ],\n                lastUsed: new Date(Date.now() - 1800000).toISOString(),\n                usageCount: 5670,\n                rateLimit: 200,\n                allowedIPs: [\n                    \"webhook.example.com\"\n                ],\n                createdAt: new Date(Date.now() - 86400000 * 45).toISOString()\n            }\n        ];\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: apiKeys,\n            meta: {\n                total: apiKeys.length,\n                active: apiKeys.filter((key)=>key.isActive).length,\n                inactive: apiKeys.filter((key)=>!key.isActive).length,\n                totalUsage: apiKeys.reduce((sum, key)=>sum + key.usageCount, 0)\n            },\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"API keys API error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        // Get auth token\n        const authHeader = request.headers.get(\"authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        // Simple token validation for testing\n        if (!token.startsWith(\"kxg_\")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid token\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { name, permissions, rateLimit, allowedIPs, expiresAt } = body;\n        // Validate input\n        if (!name || !permissions || !Array.isArray(permissions)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Name and permissions array are required\"\n            }, {\n                status: 400\n            });\n        }\n        // Generate new API key\n        const keyId = `key_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        const keyPrefix = `kxg_${name.toLowerCase().replace(/\\s+/g, \"_\")}_`;\n        const fullKey = `${keyPrefix}${Math.random().toString(36).substr(2, 32)}`;\n        const newApiKey = {\n            id: keyId,\n            name,\n            keyPrefix,\n            fullKey,\n            isActive: true,\n            permissions,\n            usageCount: 0,\n            rateLimit: rateLimit || 1000,\n            allowedIPs: allowedIPs || [],\n            createdAt: new Date().toISOString(),\n            expiresAt\n        };\n        // In a real app, save to database\n        // For demo, just return the created key\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: newApiKey,\n            message: \"API key created successfully. Please save the full key as it will not be shown again.\",\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"Create API key error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request) {\n    try {\n        // Get auth token\n        const authHeader = request.headers.get(\"authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        // Simple token validation for testing\n        if (!token.startsWith(\"kxg_\")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid token\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { keyId } = body;\n        if (!keyId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Key ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        // In a real app, delete the API key from database\n        // For demo, just return success\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: `API key ${keyId} deleted successfully`,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"Delete API key error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/user/api-keys/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fapi-keys%2Froute&page=%2Fapi%2Fuser%2Fapi-keys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fapi-keys%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();