"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/cve/search/route";
exports.ids = ["app/api/cve/search/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcve%2Fsearch%2Froute&page=%2Fapi%2Fcve%2Fsearch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcve%2Fsearch%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcve%2Fsearch%2Froute&page=%2Fapi%2Fcve%2Fsearch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcve%2Fsearch%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Users_Downloads_kodeXGuard_app_api_cve_search_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/cve/search/route.ts */ \"(rsc)/./app/api/cve/search/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/cve/search/route\",\n        pathname: \"/api/cve/search\",\n        filename: \"route\",\n        bundlePath: \"app/api/cve/search/route\"\n    },\n    resolvedPagePath: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\api\\\\cve\\\\search\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Users_Downloads_kodeXGuard_app_api_cve_search_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/cve/search/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcve%2Fsearch%2Froute&page=%2Fapi%2Fcve%2Fsearch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcve%2Fsearch%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/cve/search/route.ts":
/*!*************************************!*\
  !*** ./app/api/cve/search/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const query = searchParams.get(\"query\") || \"\";\n        const severity = searchParams.get(\"severity\") || \"\";\n        const year = searchParams.get(\"year\") || \"\";\n        const limit = parseInt(searchParams.get(\"limit\") || \"20\");\n        const offset = parseInt(searchParams.get(\"offset\") || \"0\");\n        // Validate limit\n        if (limit > 100) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Limit cannot exceed 100\",\n                code: \"LIMIT_EXCEEDED\"\n            }, {\n                status: 400\n            });\n        }\n        // Simulate search delay\n        await new Promise((resolve)=>setTimeout(resolve, 500 + Math.random() * 1000));\n        // Generate mock CVE data\n        const mockCVEs = generateMockCVEs(query, severity, year, limit, offset);\n        const totalCVEs = 189234 // Mock total count\n        ;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                query,\n                filters: {\n                    severity,\n                    year\n                },\n                total: totalCVEs,\n                limit,\n                offset,\n                cves: mockCVEs\n            },\n            message: \"CVE search completed successfully\",\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"CVE search error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\",\n            code: \"INTERNAL_ERROR\"\n        }, {\n            status: 500\n        });\n    }\n}\nfunction generateMockCVEs(query, severity, year, limit, offset) {\n    const cves = [];\n    const severities = severity ? [\n        severity\n    ] : [\n        \"critical\",\n        \"high\",\n        \"medium\",\n        \"low\"\n    ];\n    const years = year ? [\n        year\n    ] : [\n        \"2024\",\n        \"2023\",\n        \"2022\",\n        \"2021\"\n    ];\n    for(let i = 0; i < limit; i++){\n        const cveYear = years[Math.floor(Math.random() * years.length)];\n        const cveSeverity = severities[Math.floor(Math.random() * severities.length)];\n        const cveNumber = (offset + i + 1).toString().padStart(4, \"0\");\n        const cve = {\n            cveId: `CVE-${cveYear}-${cveNumber}`,\n            description: generateCVEDescription(query, cveSeverity),\n            severity: cveSeverity,\n            cvssScore: generateCVSSScore(cveSeverity),\n            cvssVector: generateCVSSVector(),\n            publishedDate: generateRandomDate(cveYear),\n            modifiedDate: generateRandomDate(cveYear, true),\n            affectedProducts: generateAffectedProducts(),\n            references: generateReferences(cveYear, cveNumber),\n            exploits: generateExploits(cveSeverity),\n            trending: Math.random() > 0.8 // 20% chance of being trending\n        };\n        cves.push(cve);\n    }\n    return cves;\n}\nfunction generateCVEDescription(query, severity) {\n    const vulnerabilityTypes = [\n        \"SQL injection\",\n        \"Cross-site scripting (XSS)\",\n        \"Remote code execution\",\n        \"Buffer overflow\",\n        \"Authentication bypass\",\n        \"Path traversal\",\n        \"Cross-site request forgery (CSRF)\",\n        \"Server-side request forgery (SSRF)\",\n        \"XML external entity (XXE)\",\n        \"Insecure direct object reference\"\n    ];\n    const products = [\n        \"WordPress Plugin\",\n        \"Apache HTTP Server\",\n        \"Microsoft Windows\",\n        \"Linux Kernel\",\n        \"PHP Framework\",\n        \"Node.js Package\",\n        \"Java Library\",\n        \"Python Package\",\n        \"Web Application\",\n        \"Mobile Application\",\n        \"Database System\",\n        \"Network Device\"\n    ];\n    const vulnType = vulnerabilityTypes[Math.floor(Math.random() * vulnerabilityTypes.length)];\n    const product = products[Math.floor(Math.random() * products.length)];\n    let description = `${vulnType} vulnerability in ${product}`;\n    if (query && query.length > 0) {\n        description += ` related to ${query}`;\n    }\n    switch(severity){\n        case \"critical\":\n            description += \" allows remote attackers to execute arbitrary code with system privileges\";\n            break;\n        case \"high\":\n            description += \" allows remote attackers to gain unauthorized access\";\n            break;\n        case \"medium\":\n            description += \" allows attackers to bypass security restrictions\";\n            break;\n        case \"low\":\n            description += \" may allow information disclosure\";\n            break;\n    }\n    return description + \".\";\n}\nfunction generateCVSSScore(severity) {\n    const ranges = {\n        critical: [\n            9.0,\n            10.0\n        ],\n        high: [\n            7.0,\n            8.9\n        ],\n        medium: [\n            4.0,\n            6.9\n        ],\n        low: [\n            0.1,\n            3.9\n        ]\n    };\n    const range = ranges[severity] || [\n        0.1,\n        10.0\n    ];\n    const score = Math.random() * (range[1] - range[0]) + range[0];\n    return Math.round(score * 10) / 10;\n}\nfunction generateCVSSVector() {\n    const vectors = [\n        \"CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H\",\n        \"CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H\",\n        \"CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:H/I:H/A:H\",\n        \"CVSS:3.1/AV:L/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H\",\n        \"CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N\"\n    ];\n    return vectors[Math.floor(Math.random() * vectors.length)];\n}\nfunction generateRandomDate(year, isModified = false) {\n    const startDate = new Date(`${year}-01-01`);\n    const endDate = isModified ? new Date() : new Date(`${year}-12-31`);\n    const randomTime = startDate.getTime() + Math.random() * (endDate.getTime() - startDate.getTime());\n    return new Date(randomTime).toISOString().split(\"T\")[0];\n}\nfunction generateAffectedProducts() {\n    const products = [\n        \"WebApp Framework 1.0-2.5\",\n        \"CMS Platform 3.x\",\n        \"E-commerce Plugin 2.1-2.3\",\n        \"API Gateway 1.x-2.x\",\n        \"File Manager Pro 3.0\",\n        \"Security Scanner 4.2\",\n        \"Database Connector 1.5\",\n        \"Authentication Service 2.0\"\n    ];\n    const count = Math.floor(Math.random() * 3) + 1;\n    const selected = [];\n    for(let i = 0; i < count; i++){\n        const product = products[Math.floor(Math.random() * products.length)];\n        if (!selected.includes(product)) {\n            selected.push(product);\n        }\n    }\n    return selected;\n}\nfunction generateReferences(year, cveNumber) {\n    return [\n        `https://nvd.nist.gov/vuln/detail/CVE-${year}-${cveNumber}`,\n        `https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-${year}-${cveNumber}`,\n        `https://security-tracker.debian.org/tracker/CVE-${year}-${cveNumber}`,\n        `https://access.redhat.com/security/cve/CVE-${year}-${cveNumber}`\n    ];\n}\nfunction generateExploits(severity) {\n    if (severity === \"low\" || Math.random() > 0.4) {\n        return [];\n    }\n    const exploits = [];\n    const exploitCount = severity === \"critical\" ? Math.floor(Math.random() * 3) + 1 : Math.floor(Math.random() * 2) + 1;\n    for(let i = 0; i < exploitCount; i++){\n        exploits.push({\n            title: `${severity.charAt(0).toUpperCase() + severity.slice(1)} Exploit ${i + 1}`,\n            url: `https://exploit-db.com/exploits/${Math.floor(Math.random() * 50000) + 10000}`,\n            type: Math.random() > 0.5 ? \"Public Exploit\" : \"Proof of Concept\"\n        });\n    }\n    return exploits;\n}\nasync function OPTIONS() {\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 200,\n        headers: {\n            \"Access-Control-Allow-Origin\": \"*\",\n            \"Access-Control-Allow-Methods\": \"GET, OPTIONS\",\n            \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/cve/search/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@opentelemetry","vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcve%2Fsearch%2Froute&page=%2Fapi%2Fcve%2Fsearch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcve%2Fsearch%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();