import { NextRequest, NextResponse } from 'next/server'
import { osintEngine } from '@/lib/osint'
import { AuthService } from '@/lib/auth'
import { Database } from '@/lib/database'

export async function POST(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Verify token and get user
    const user = await AuthService.getUserByToken(token)
    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    const body = await request.json()
    const { query, type, deepSearch = false, sources = [] } = body

    // Validate input
    if (!query || !type) {
      return NextResponse.json(
        {
          success: false,
          error: 'Query and type are required',
          code: 'MISSING_PARAMETERS'
        },
        { status: 400 }
      )
    }

    // Validate search type
    const validTypes = ['email', 'phone', 'nik', 'npwp', 'name', 'domain', 'imei', 'address']
    if (!validTypes.includes(type)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid search type',
          code: 'INVALID_TYPE'
        },
        { status: 400 }
      )
    }

    // Check user quota
    const db = Database.getInstance()
    const today = new Date().toISOString().split('T')[0]

    const todayUsage = await db.query(
      `SELECT COUNT(*) as count FROM osint_results
       WHERE user_id = ? AND DATE(created_at) = ?`,
      [user.id, today]
    )

    // Get user plan quotas
    const planInfo = await db.query(
      'SELECT osint_quota_daily FROM plans WHERE id = ?',
      [user.planId]
    )

    const dailyQuota = planInfo[0]?.osint_quota_daily || 10
    const usedToday = todayUsage[0]?.count || 0

    if (usedToday >= dailyQuota) {
      return NextResponse.json({
        success: false,
        error: `Daily OSINT quota exceeded. Used: ${usedToday}/${dailyQuota}`,
        code: 'QUOTA_EXCEEDED'
      }, { status: 429 })
    }

    // Perform OSINT search
    const searchRequest = {
      query,
      type,
      deepSearch,
      sources,
      userId: user.id
    }

    const result = await osintEngine.search(searchRequest)

    return NextResponse.json({
      success: true,
      data: {
        searchId: `osint_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        query,
        type,
        results: result.results,
        sources: result.sources,
        confidenceScore: result.confidenceScore,
        searchTime: result.searchTime,
        totalResults: result.totalResults,
        quotaUsed: usedToday + 1,
        quotaLimit: dailyQuota
      },
      message: `Found ${result.totalResults} results`,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('OSINT search error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Verify token and get user
    const user = await AuthService.getUserByToken(token)
    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    // Get search history
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = (page - 1) * limit

    const history = await osintEngine.getSearchHistory(user.id, limit, offset)

    // Get total count
    const db = Database.getInstance()
    const totalCount = await db.query(
      'SELECT COUNT(*) as count FROM osint_results WHERE user_id = ?',
      [user.id]
    )

    const total = totalCount[0]?.count || 0
    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      success: true,
      data: history,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('OSINT history error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}
