"use strict";(()=>{var t={};t.id=41,t.ids=[41],t.modules={20399:t=>{t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:t=>{t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},28504:(t,e,a)=>{a.r(e),a.d(e,{originalPathname:()=>S,patchFetch:()=>h,requestAsyncStorage:()=>l,routeModule:()=>u,serverHooks:()=>d,staticGenerationAsyncStorage:()=>p});var s={};a.r(s),a.d(s,{GET:()=>c});var n=a(49303),i=a(88716),r=a(60670),o=a(87070);async function c(t){try{let e=t.headers.get("authorization"),a=e?.replace("Bearer ","");if(!a)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!a.startsWith("kxg_"))return o.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let s={overview:{totalScans:1247,activeScans:3,vulnerabilitiesFound:156,osintSearches:892},vulnerabilities:{critical:12,high:34,medium:67,low:89,info:23},scanActivity:{today:15,thisWeek:89,thisMonth:234,lastMonth:198},osintActivity:{today:23,thisWeek:156,thisMonth:445,lastMonth:378},topTargets:[{target:"https://example.com",scans:45,lastScan:new Date(Date.now()-36e5).toISOString(),status:"vulnerable"},{target:"https://testsite.com",scans:32,lastScan:new Date(Date.now()-72e5).toISOString(),status:"secure"},{target:"https://webapp.local",scans:28,lastScan:new Date(Date.now()-18e5).toISOString(),status:"critical"},{target:"https://api.example.com",scans:21,lastScan:new Date(Date.now()-108e5).toISOString(),status:"vulnerable"},{target:"https://mobile-app.com",scans:18,lastScan:new Date(Date.now()-144e5).toISOString(),status:"secure"}],recentActivity:[{id:"activity_001",type:"scan",title:"Vulnerability Scan Completed",description:"Scan of https://example.com found 5 vulnerabilities",timestamp:new Date(Date.now()-18e5).toISOString(),status:"warning"},{id:"activity_002",type:"osint",title:"OSINT Search Completed",description:"Email <NAME_EMAIL> returned 3 results",timestamp:new Date(Date.now()-36e5).toISOString(),status:"success"},{id:"activity_003",type:"alert",title:"Critical Vulnerability Detected",description:"SQL injection vulnerability found in login form",timestamp:new Date(Date.now()-54e5).toISOString(),status:"error"},{id:"activity_004",type:"scan",title:"Scan Started",description:"Comprehensive security scan initiated for https://webapp.local",timestamp:new Date(Date.now()-72e5).toISOString(),status:"info"},{id:"activity_005",type:"osint",title:"Phone Number Investigation",description:"Phone lookup for +628123456789 completed successfully",timestamp:new Date(Date.now()-9e6).toISOString(),status:"success"},{id:"activity_006",type:"alert",title:"Quota Warning",description:"Daily OSINT quota 80% used (40/50 searches)",timestamp:new Date(Date.now()-108e5).toISOString(),status:"warning"}],systemHealth:{scannerStatus:"online",osintStatus:"online",databaseStatus:"online",apiStatus:"online"}};return o.NextResponse.json({success:!0,data:s,timestamp:new Date().toISOString()})}catch(t){return console.error("Dashboard stats API error:",t),o.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let u=new n.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/dashboard/stats/route",pathname:"/api/dashboard/stats",filename:"route",bundlePath:"app/api/dashboard/stats/route"},resolvedPagePath:"D:\\Users\\Downloads\\kodeXGuard\\app\\api\\dashboard\\stats\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:l,staticGenerationAsyncStorage:p,serverHooks:d}=u,S="/api/dashboard/stats/route";function h(){return(0,r.patchFetch)({serverHooks:d,staticGenerationAsyncStorage:p})}}};var e=require("../../../../webpack-runtime.js");e.C(t);var a=t=>e(e.s=t),s=e.X(0,[216,592],()=>a(28504));module.exports=s})();