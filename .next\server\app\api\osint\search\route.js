"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/osint/search/route";
exports.ids = ["app/api/osint/search/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fosint%2Fsearch%2Froute&page=%2Fapi%2Fosint%2Fsearch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fosint%2Fsearch%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fosint%2Fsearch%2Froute&page=%2Fapi%2Fosint%2Fsearch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fosint%2Fsearch%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Users_Downloads_kodeXGuard_app_api_osint_search_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/osint/search/route.ts */ \"(rsc)/./app/api/osint/search/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/osint/search/route\",\n        pathname: \"/api/osint/search\",\n        filename: \"route\",\n        bundlePath: \"app/api/osint/search/route\"\n    },\n    resolvedPagePath: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\api\\\\osint\\\\search\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Users_Downloads_kodeXGuard_app_api_osint_search_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/osint/search/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fosint%2Fsearch%2Froute&page=%2Fapi%2Fosint%2Fsearch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fosint%2Fsearch%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/osint/search/route.ts":
/*!***************************************!*\
  !*** ./app/api/osint/search/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { query, type, deepSearch = false, sources = [] } = body;\n        // Validate input\n        if (!query || !type) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Query and type are required\",\n                code: \"MISSING_PARAMETERS\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate search type\n        const validTypes = [\n            \"email\",\n            \"phone\",\n            \"nik\",\n            \"npwp\",\n            \"name\",\n            \"domain\",\n            \"imei\",\n            \"address\"\n        ];\n        if (!validTypes.includes(type)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid search type\",\n                code: \"INVALID_TYPE\"\n            }, {\n                status: 400\n            });\n        }\n        // Simulate search delay\n        await new Promise((resolve)=>setTimeout(resolve, 1000 + Math.random() * 2000));\n        // Generate mock results based on search type\n        const mockResults = generateMockResults(query, type, deepSearch);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                query,\n                type,\n                deepSearch,\n                results: mockResults,\n                totalResults: mockResults.length,\n                searchTime: `${(1 + Math.random() * 2).toFixed(1)}s`,\n                sources: sources.length > 0 ? sources : [\n                    \"Dukcapil\",\n                    \"Kemkes\",\n                    \"GitHub Leaked\",\n                    \"Social Media\"\n                ]\n            },\n            message: \"Search completed successfully\",\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"OSINT search error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\",\n            code: \"INTERNAL_ERROR\"\n        }, {\n            status: 500\n        });\n    }\n}\nfunction generateMockResults(query, type, deepSearch) {\n    const baseResults = [];\n    switch(type){\n        case \"email\":\n            baseResults.push({\n                source: \"Dukcapil Database\",\n                data: {\n                    name: \"John Doe\",\n                    email: query,\n                    nik: \"3201234567890123\",\n                    address: \"Jakarta Selatan\",\n                    verified: true\n                },\n                confidence: 95,\n                timestamp: new Date().toISOString()\n            }, {\n                source: \"GitHub Leaked\",\n                data: {\n                    username: query.split(\"@\")[0],\n                    email: query,\n                    repositories: 12,\n                    lastActivity: \"2024-01-10\"\n                },\n                confidence: 87,\n                timestamp: new Date().toISOString()\n            });\n            break;\n        case \"phone\":\n            baseResults.push({\n                source: \"Telecom Database\",\n                data: {\n                    phone: query,\n                    operator: \"Telkomsel\",\n                    location: \"Jakarta\",\n                    registered: true\n                },\n                confidence: 92,\n                timestamp: new Date().toISOString()\n            }, {\n                source: \"Social Media\",\n                data: {\n                    phone: query,\n                    platform: \"WhatsApp\",\n                    lastSeen: \"2024-01-14\",\n                    profilePic: true\n                },\n                confidence: 78,\n                timestamp: new Date().toISOString()\n            });\n            break;\n        case \"nik\":\n            baseResults.push({\n                source: \"Dukcapil Database\",\n                data: {\n                    nik: query,\n                    name: \"Jane Smith\",\n                    birthDate: \"1990-05-15\",\n                    birthPlace: \"Jakarta\",\n                    address: \"Jl. Sudirman No. 123\",\n                    verified: true\n                },\n                confidence: 98,\n                timestamp: new Date().toISOString()\n            });\n            break;\n        case \"domain\":\n            baseResults.push({\n                source: \"WHOIS Database\",\n                data: {\n                    domain: query,\n                    registrar: \"GoDaddy\",\n                    createdDate: \"2020-03-15\",\n                    expiryDate: \"2025-03-15\",\n                    nameServers: [\n                        \"ns1.example.com\",\n                        \"ns2.example.com\"\n                    ]\n                },\n                confidence: 100,\n                timestamp: new Date().toISOString()\n            }, {\n                source: \"DNS Records\",\n                data: {\n                    domain: query,\n                    ipAddress: \"***********\",\n                    mxRecords: [\n                        \"mail.example.com\"\n                    ],\n                    txtRecords: [\n                        \"v=spf1 include:_spf.google.com ~all\"\n                    ]\n                },\n                confidence: 100,\n                timestamp: new Date().toISOString()\n            });\n            break;\n        default:\n            baseResults.push({\n                source: \"General Database\",\n                data: {\n                    query: query,\n                    type: type,\n                    found: true,\n                    details: \"Information found in general database\"\n                },\n                confidence: 75,\n                timestamp: new Date().toISOString()\n            });\n    }\n    // Add more results if deep search is enabled\n    if (deepSearch) {\n        baseResults.push({\n            source: \"Deep Web Search\",\n            data: {\n                query: query,\n                additionalInfo: \"Additional information from deep web sources\",\n                relatedEntities: [\n                    \"Entity 1\",\n                    \"Entity 2\"\n                ],\n                crossReferences: 3\n            },\n            confidence: 65,\n            timestamp: new Date().toISOString()\n        });\n    }\n    return baseResults;\n}\nasync function OPTIONS() {\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 200,\n        headers: {\n            \"Access-Control-Allow-Origin\": \"*\",\n            \"Access-Control-Allow-Methods\": \"POST, OPTIONS\",\n            \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/osint/search/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fosint%2Fsearch%2Froute&page=%2Fapi%2Fosint%2Fsearch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fosint%2Fsearch%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();