"use strict";exports.id=2675,exports.ids=[2675],exports.modules={22675:(e,t,a)=>{a.r(t),a.d(t,{default:()=>H});var r=a(10326),s=a(17577),l=a(90434),i=a(35047),n=a(76557);let d=(0,n.Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]]);var c=a(88307),o=a(58038),h=a(6530),x=a(88319),y=a(53468);let m=(0,n.Z)("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]]);var u=a(50732),g=a(92498),p=a(58907),b=a(79635),f=a(28916),k=a(6343),v=a(26092),j=a(3634),w=a(33734),Z=a(90748),N=a(94019),M=a(39183);let z=(0,n.Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);var $=a(88378),C=a(6507),S=a(71810);function H({user:e}){let[t,a]=(0,s.useState)(!1),[n,H]=(0,s.useState)(!1),[V,q]=(0,s.useState)(!1),[O,P]=(0,s.useState)([]),[T,I]=(0,s.useState)(0),A=(0,i.usePathname)(),B=(0,i.useRouter)(),D=async e=>{try{let t=localStorage.getItem("auth-token");if(!t)return;await fetch(`/api/notifications/${e}/read`,{method:"POST",headers:{Authorization:`Bearer ${t}`}}),P(t=>t.map(t=>t.id===e?{...t,read:!0}:t))}catch(t){console.error("Failed to mark notification as read:",t),P(t=>t.map(t=>t.id===e?{...t,read:!0}:t))}},L=async()=>{try{let e=localStorage.getItem("auth-token");if(!e)return;await fetch("/api/notifications/mark-all-read",{method:"POST",headers:{Authorization:`Bearer ${e}`}}),P(e=>e.map(e=>({...e,read:!0})))}catch(e){console.error("Failed to mark all notifications as read:",e),P(e=>e.map(e=>({...e,read:!0})))}},F=[{id:"dashboard",name:"Dashboard",href:"/dashboard",icon:d},{id:"osint",name:"OSINT Investigator",href:"/osint",icon:c.Z,badge:"HOT"},{id:"scanner",name:"Vulnerability Scanner",href:"/scanner",icon:o.Z,premium:!0},{id:"file-analyzer",name:"File Analyzer",href:"/file-analyzer",icon:h.Z},{id:"cve",name:"CVE Intelligence",href:"/cve",icon:x.Z,badge:"NEW"},{id:"dorking",name:"Google Dorking",href:"/dorking",icon:y.Z},{id:"tools",name:"Security Tools",href:"/tools",icon:m},{id:"bot",name:"Bot Center",href:"/bot",icon:u.Z,premium:!0,adminOnly:!0},{id:"playground",name:"API Playground",href:"/playground",icon:g.Z,premium:!0},{id:"leaderboard",name:"Leaderboard",href:"/leaderboard",icon:p.Z},{id:"profile",name:"Profile & API Keys",href:"/profile",icon:b.Z},{id:"plan",name:"Plans & Billing",href:"/plan",icon:f.Z},{id:"docs",name:"Documentation",href:"/docs",icon:k.Z}].filter(t=>(!t.adminOnly||!!["super_admin","admin"].includes(e.role))&&(!t.premium||"gratis"!==e.plan)),R=e=>{switch(e){case"cybersecurity":return v.Z;case"bughunter":return j.Z;case"hobby":return w.Z;default:return b.Z}};return(0,r.jsxs)(r.Fragment,{children:[n&&r.jsx("div",{className:"fixed inset-0 bg-black/50 z-40 lg:hidden",onClick:()=>H(!1)}),r.jsx("button",{onClick:()=>H(!0),className:"fixed top-4 left-4 z-50 lg:hidden bg-gray-800 text-white p-2 rounded-lg border border-gray-700 hover:bg-gray-700 transition-colors",children:r.jsx(Z.Z,{className:"h-5 w-5"})}),(0,r.jsxs)("div",{className:`
        fixed lg:sticky top-0 left-0 h-screen bg-gray-900/95 backdrop-blur-sm border-r border-gray-800 z-50 lg:z-auto
        transition-all duration-300 ease-in-out flex-shrink-0
        ${t?"w-16":"w-64"}
        ${n?"translate-x-0":"-translate-x-full lg:translate-x-0"}
        lg:h-[calc(100vh-4rem)]
      `,children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-800",children:[!t&&(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("div",{className:"w-8 h-8 bg-cyber-green rounded-lg flex items-center justify-center",children:r.jsx(o.Z,{className:"h-5 w-5 text-black"})}),r.jsx("span",{className:"font-bold text-white font-cyber",children:"KodeXGuard"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("button",{onClick:()=>H(!1),className:"lg:hidden text-gray-400 hover:text-white transition-colors",children:r.jsx(N.Z,{className:"h-5 w-5"})}),r.jsx("button",{onClick:()=>a(!t),className:"hidden lg:block text-gray-400 hover:text-white transition-colors",children:t?r.jsx(M.Z,{className:"h-5 w-5"}):r.jsx(z,{className:"h-5 w-5"})})]})]}),r.jsx("div",{className:"p-4 border-b border-gray-800",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[e.avatar?r.jsx("img",{src:e.avatar,alt:e.username,className:"w-10 h-10 rounded-full"}):r.jsx("div",{className:"w-10 h-10 rounded-full bg-cyber-green/20 flex items-center justify-center",children:r.jsx("span",{className:"text-cyber-green font-bold",children:e.username.charAt(0).toUpperCase()})}),!t&&(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[r.jsx("div",{className:"font-medium text-white truncate",children:e.username}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(()=>{let t=R(e.plan);return r.jsx(t,{className:"h-3 w-3 text-nusantara-gold"})})(),r.jsx("span",{className:`text-xs px-2 py-1 rounded-full font-semibold ${(e=>{switch(e){case"cybersecurity":return"text-red-400 bg-red-900/20";case"bughunter":return"text-purple-400 bg-purple-900/20";case"hobby":return"text-green-400 bg-green-900/20";case"pelajar":return"text-blue-400 bg-blue-900/20";default:return"text-gray-400 bg-gray-900/20"}})(e.plan)}`,children:e.plan.toUpperCase()})]})]})]})}),r.jsx("nav",{className:"flex-1 overflow-y-auto py-4",children:r.jsx("div",{className:"space-y-1 px-2",children:F.map(e=>{let a=e.icon,s=A===e.href;return(0,r.jsxs)(l.default,{href:e.href,className:`
                    group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200
                    ${s?"bg-cyber-green text-black":"text-gray-300 hover:text-white hover:bg-gray-800/50"}
                    ${t?"justify-center":"justify-start"}
                  `,children:[r.jsx(a,{className:`h-5 w-5 flex-shrink-0 ${t?"":"mr-3"}`}),!t&&(0,r.jsxs)(r.Fragment,{children:[r.jsx("span",{className:"flex-1",children:e.name}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[e.badge&&r.jsx("span",{className:`
                            px-2 py-1 text-xs font-bold rounded-full
                            ${"HOT"===e.badge?"bg-red-500 text-white":"bg-blue-500 text-white"}
                          `,children:e.badge}),e.premium&&r.jsx(v.Z,{className:"h-3 w-3 text-nusantara-gold"}),e.adminOnly&&r.jsx($.Z,{className:"h-3 w-3 text-orange-400"})]})]}),t&&(0,r.jsxs)("div",{className:"absolute left-16 bg-gray-800 text-white px-2 py-1 rounded-lg text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50",children:[e.name,e.badge&&r.jsx("span",{className:`ml-2 px-1 py-0.5 text-xs rounded ${"HOT"===e.badge?"bg-red-500":"bg-blue-500"}`,children:e.badge})]})]},e.id)})})}),r.jsx("div",{className:"border-t border-gray-800 p-4",children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{onClick:()=>q(!V),className:`
                  w-full flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors
                  ${t?"justify-center":"justify-start"}
                  ${V?"bg-gray-800/50 text-white":""}
                `,children:[(0,r.jsxs)("div",{className:"relative",children:[r.jsx(C.Z,{className:`h-5 w-5 ${t?"":"mr-3"}`}),T>0&&r.jsx("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs w-4 h-4 rounded-full flex items-center justify-center",children:T>9?"9+":T})]}),!t&&r.jsx("span",{children:"Notifications"}),!t&&T>0&&r.jsx("span",{className:"ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full",children:T})]}),V&&!t&&(0,r.jsxs)("div",{className:"absolute bottom-full left-0 right-0 mb-2 bg-gray-800 border border-gray-700 rounded-lg shadow-xl max-h-96 overflow-y-auto z-50",children:[(0,r.jsxs)("div",{className:"p-3 border-b border-gray-700 flex items-center justify-between",children:[r.jsx("h3",{className:"text-sm font-semibold text-white",children:"Notifications"}),T>0&&r.jsx("button",{onClick:L,className:"text-xs text-cyber-green hover:text-cyber-green/80 transition-colors",children:"Mark all read"})]}),r.jsx("div",{className:"max-h-80 overflow-y-auto",children:0===O.length?r.jsx("div",{className:"p-4 text-center text-gray-400 text-sm",children:"No notifications"}):O.map(e=>r.jsx("div",{className:`p-3 border-b border-gray-700 last:border-b-0 hover:bg-gray-700/50 transition-colors ${e.read?"":"bg-gray-700/20"}`,onClick:()=>!e.read&&D(e.id),children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[r.jsx("div",{className:`flex-shrink-0 w-2 h-2 rounded-full mt-2 ${"success"===e.type?"bg-green-500":"warning"===e.type?"bg-yellow-500":"error"===e.type?"bg-red-500":"bg-blue-500"}`}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("h4",{className:`text-sm font-medium ${e.read?"text-gray-300":"text-white"}`,children:e.title}),r.jsx("span",{className:"text-xs text-gray-400",children:new Date(e.timestamp).toLocaleTimeString("id-ID",{hour:"2-digit",minute:"2-digit"})})]}),r.jsx("p",{className:"text-xs text-gray-400 mt-1",children:e.message}),e.action&&(0,r.jsxs)(l.default,{href:e.action.url,className:"inline-block mt-2 text-xs text-cyber-green hover:text-cyber-green/80 transition-colors",onClick:()=>q(!1),children:[e.action.label," →"]})]})]})},e.id))})]})]}),(0,r.jsxs)(l.default,{href:"/settings",className:`
                w-full flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors
                ${t?"justify-center":"justify-start"}
                ${"/settings"===A?"bg-gray-800/50 text-white":""}
              `,children:[r.jsx($.Z,{className:`h-5 w-5 ${t?"":"mr-3"}`}),!t&&r.jsx("span",{children:"Settings"})]}),(0,r.jsxs)("button",{onClick:()=>{localStorage.removeItem("auth-token"),B.push("/login")},className:`
                w-full flex items-center px-3 py-2 text-sm font-medium text-red-400 hover:text-red-300 hover:bg-red-900/20 rounded-lg transition-colors
                ${t?"justify-center":"justify-start"}
              `,children:[r.jsx(S.Z,{className:`h-5 w-5 ${t?"":"mr-3"}`}),!t&&r.jsx("span",{children:"Logout"})]})]})})]})]})}},6507:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},6343:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},50732:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},39183:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},92498:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},28916:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},26092:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]])},88319:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},6530:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("FileSearch",[["path",{d:"M4 22h14a2 2 0 0 0 2-2V7.5L14.5 2H6a2 2 0 0 0-2 2v3",key:"am10z3"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["path",{d:"M5 17a3 3 0 1 0 0-6 3 3 0 0 0 0 6z",key:"ychnub"}],["path",{d:"m9 18-1.5-1.5",key:"1j6qii"}]])},71810:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},90748:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},88307:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},88378:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},58038:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},33734:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},53468:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},58907:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},79635:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},94019:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},3634:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},35047:(e,t,a)=>{var r=a(77389);a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}})}};