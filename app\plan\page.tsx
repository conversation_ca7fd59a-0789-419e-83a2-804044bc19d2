'use client'

import { useState } from 'react'
import Navbar from '@/components/Navbar'
import { Card, StatCard, AlertCard } from '@/components/Card'
import { 
  CreditCard, 
  Check, 
  X,
  Crown,
  Zap,
  Shield,
  Users,
  Clock,
  Star,
  ArrowRight,
  Smartphone,
  Globe,
  Database,
  Bot,
  FileSearch,
  Search,
  Bug,
  Key,
  Activity
} from 'lucide-react'

interface Plan {
  id: string
  name: string
  price: {
    monthly: number
    yearly: number
  }
  description: string
  features: string[]
  limits: {
    dailyScans: string
    fileUpload: string
    apiCalls: string
    botAccess: boolean
    support: string
  }
  popular?: boolean
  color: string
}

interface PaymentMethod {
  id: string
  name: string
  type: 'manual' | 'auto'
  description: string
  icon: string
  processingTime: string
}

export default function PlanPage() {
  const [user] = useState({
    username: 'admin',
    avatar: '',
    role: 'super_admin',
    plan: 'cybersecurity'
  })

  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly')
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null)
  const [showPaymentModal, setShowPaymentModal] = useState(false)
  const [selectedPayment, setSelectedPayment] = useState<string | null>(null)

  const plans: Plan[] = [
    {
      id: 'gratis',
      name: 'Gratis',
      price: { monthly: 0, yearly: 0 },
      description: 'Untuk pemula yang ingin mencoba fitur dasar',
      features: [
        '5 Scan per hari',
        'Basic OSINT search',
        'File upload 2MB',
        '100 API calls/hari',
        'Community support',
        'Basic CVE database'
      ],
      limits: {
        dailyScans: '5',
        fileUpload: '2MB',
        apiCalls: '100',
        botAccess: false,
        support: 'Community'
      },
      color: 'gray'
    },
    {
      id: 'pelajar',
      name: 'Pelajar',
      price: { monthly: 25000, yearly: 250000 },
      description: 'Untuk mahasiswa dan pelajar cybersecurity',
      features: [
        '50 Scan per hari',
        'Full OSINT access',
        'File upload 10MB',
        '1,000 API calls/hari',
        'Email support',
        'CVE intelligence',
        'Hash & payload tools'
      ],
      limits: {
        dailyScans: '50',
        fileUpload: '10MB',
        apiCalls: '1,000',
        botAccess: false,
        support: 'Email'
      },
      color: 'blue'
    },
    {
      id: 'hobby',
      name: 'Hobby',
      price: { monthly: 75000, yearly: 750000 },
      description: 'Untuk enthusiast dan researcher independen',
      features: [
        '200 Scan per hari',
        'Advanced OSINT',
        'File upload 50MB',
        '5,000 API calls/hari',
        'Priority email support',
        'Full CVE database',
        'Google dorking presets',
        'Export results'
      ],
      limits: {
        dailyScans: '200',
        fileUpload: '50MB',
        apiCalls: '5,000',
        botAccess: false,
        support: 'Priority Email'
      },
      popular: true,
      color: 'green'
    },
    {
      id: 'bughunter',
      name: 'Bug Hunter',
      price: { monthly: 150000, yearly: 1500000 },
      description: 'Untuk bug hunter profesional dan tim kecil',
      features: [
        'Unlimited scans',
        'Deep OSINT investigation',
        'File upload 100MB',
        '10,000 API calls/hari',
        'WhatsApp & Telegram bot',
        'Real-time notifications',
        'Advanced payload generator',
        'Scan automation',
        'Priority support'
      ],
      limits: {
        dailyScans: 'Unlimited',
        fileUpload: '100MB',
        apiCalls: '10,000',
        botAccess: true,
        support: 'Priority'
      },
      color: 'purple'
    },
    {
      id: 'cybersecurity',
      name: 'Cybersecurity',
      price: { monthly: 500000, yearly: 5000000 },
      description: 'Untuk perusahaan dan tim cybersecurity enterprise',
      features: [
        'Unlimited everything',
        'Enterprise OSINT',
        'File upload 1GB',
        '100,000 API calls/hari',
        'Multiple bot instances',
        'Custom integrations',
        'White-label options',
        'Dedicated support',
        'SLA guarantee',
        'Custom features'
      ],
      limits: {
        dailyScans: 'Unlimited',
        fileUpload: '1GB',
        apiCalls: '100,000',
        botAccess: true,
        support: 'Dedicated'
      },
      color: 'gold'
    }
  ]

  const paymentMethods: PaymentMethod[] = [
    {
      id: 'bank_transfer',
      name: 'Transfer Bank',
      type: 'manual',
      description: 'Transfer manual ke rekening bank dengan konfirmasi WhatsApp',
      icon: '🏦',
      processingTime: '1-24 jam'
    },
    {
      id: 'tripay',
      name: 'Tripay Gateway',
      type: 'auto',
      description: 'Pembayaran otomatis via QRIS, Virtual Account, E-Wallet',
      icon: '💳',
      processingTime: 'Instant'
    },
    {
      id: 'midtrans',
      name: 'Midtrans',
      type: 'auto',
      description: 'Credit card, debit card, dan berbagai metode pembayaran',
      icon: '💰',
      processingTime: 'Instant'
    },
    {
      id: 'xendit',
      name: 'Xendit',
      type: 'auto',
      description: 'Virtual account, e-wallet, dan pembayaran digital lainnya',
      icon: '🚀',
      processingTime: 'Instant'
    }
  ]

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(price)
  }

  const getPlanColor = (color: string) => {
    const colors = {
      gray: 'border-gray-600 hover:border-gray-500',
      blue: 'border-blue-600 hover:border-blue-500',
      green: 'border-cyber-green hover:border-cyber-blue',
      purple: 'border-purple-600 hover:border-purple-500',
      gold: 'border-nusantara-gold hover:border-yellow-400'
    }
    return colors[color as keyof typeof colors] || colors.gray
  }

  const getPlanBadgeColor = (color: string) => {
    const colors = {
      gray: 'bg-gray-900/50 text-gray-400',
      blue: 'bg-blue-900/50 text-blue-400',
      green: 'bg-cyber-green/20 text-cyber-green',
      purple: 'bg-purple-900/50 text-purple-400',
      gold: 'bg-nusantara-gold/20 text-nusantara-gold'
    }
    return colors[color as keyof typeof colors] || colors.gray
  }

  const handleUpgrade = (planId: string) => {
    setSelectedPlan(planId)
    setShowPaymentModal(true)
  }

  const processPayment = () => {
    // In real app, process payment here
    setShowPaymentModal(false)
    setSelectedPlan(null)
    setSelectedPayment(null)
  }

  return (
    <div className="min-h-screen bg-gradient-cyber">
      <Navbar user={user} />
      
      <div className="pt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8 text-center">
            <div className="flex items-center justify-center space-x-3 mb-4">
              <CreditCard className="h-8 w-8 text-cyber-green" />
              <h1 className="text-3xl font-bold font-cyber text-white">
                Pilih <span className="cyber-text">Plan</span> Anda
              </h1>
            </div>
            <p className="text-gray-400 max-w-3xl mx-auto">
              Upgrade ke plan premium untuk akses unlimited scan, bot integration, dan fitur enterprise. 
              Pilih plan yang sesuai dengan kebutuhan cybersecurity Anda.
            </p>
          </div>

          {/* Current Plan Status */}
          <div className="mb-8">
            <AlertCard
              type="success"
              title={`Current Plan: ${user.plan.charAt(0).toUpperCase() + user.plan.slice(1)}`}
              message="Your plan is active and all features are available. Upgrade anytime for more capabilities."
            />
          </div>

          {/* Billing Toggle */}
          <div className="flex justify-center mb-8">
            <div className="bg-gray-800/50 p-1 rounded-lg">
              <button
                onClick={() => setBillingCycle('monthly')}
                className={`px-6 py-2 rounded-lg font-medium transition-all duration-200 ${
                  billingCycle === 'monthly'
                    ? 'bg-cyber-green text-black'
                    : 'text-gray-300 hover:text-white'
                }`}
              >
                Monthly
              </button>
              <button
                onClick={() => setBillingCycle('yearly')}
                className={`px-6 py-2 rounded-lg font-medium transition-all duration-200 ${
                  billingCycle === 'yearly'
                    ? 'bg-cyber-green text-black'
                    : 'text-gray-300 hover:text-white'
                }`}
              >
                Yearly
                <span className="ml-2 px-2 py-1 bg-green-600 text-white rounded-full text-xs">
                  Save 17%
                </span>
              </button>
            </div>
          </div>

          {/* Plans Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 mb-12">
            {plans.map((plan) => (
              <Card
                key={plan.id}
                className={`relative ${getPlanColor(plan.color)} ${
                  plan.popular ? 'ring-2 ring-cyber-green' : ''
                }`}
                hover
              >
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-cyber-green text-black px-3 py-1 rounded-full text-sm font-semibold">
                      Most Popular
                    </span>
                  </div>
                )}
                
                <div className="p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-white mb-2">{plan.name}</h3>
                    <div className="mb-4">
                      <span className="text-3xl font-bold text-white">
                        {plan.price[billingCycle] === 0 
                          ? 'Gratis' 
                          : formatPrice(plan.price[billingCycle])
                        }
                      </span>
                      {plan.price[billingCycle] > 0 && (
                        <span className="text-gray-400">
                          /{billingCycle === 'monthly' ? 'bulan' : 'tahun'}
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-400">{plan.description}</p>
                  </div>

                  <div className="space-y-3 mb-6">
                    {plan.features.map((feature, idx) => (
                      <div key={idx} className="flex items-center space-x-2">
                        <Check className="h-4 w-4 text-cyber-green flex-shrink-0" />
                        <span className="text-sm text-gray-300">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <button
                    onClick={() => handleUpgrade(plan.id)}
                    disabled={user.plan === plan.id}
                    className={`w-full py-3 rounded-lg font-semibold transition-all duration-200 ${
                      user.plan === plan.id
                        ? 'bg-gray-700 text-gray-400 cursor-not-allowed'
                        : plan.popular
                        ? 'cyber-btn-primary'
                        : 'cyber-btn'
                    }`}
                  >
                    {user.plan === plan.id ? 'Current Plan' : 'Upgrade Now'}
                  </button>
                </div>
              </Card>
            ))}
          </div>

          {/* Feature Comparison */}
          <Card>
            <div className="p-6">
              <h2 className="text-xl font-bold text-white mb-6 text-center">Feature Comparison</h2>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b border-gray-700">
                      <th className="text-left py-3 text-gray-400">Features</th>
                      {plans.map((plan) => (
                        <th key={plan.id} className="text-center py-3 text-white min-w-24">
                          {plan.name}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-b border-gray-800">
                      <td className="py-3 text-gray-300">Daily Scans</td>
                      {plans.map((plan) => (
                        <td key={plan.id} className="text-center py-3 text-white">
                          {plan.limits.dailyScans}
                        </td>
                      ))}
                    </tr>
                    <tr className="border-b border-gray-800">
                      <td className="py-3 text-gray-300">File Upload</td>
                      {plans.map((plan) => (
                        <td key={plan.id} className="text-center py-3 text-white">
                          {plan.limits.fileUpload}
                        </td>
                      ))}
                    </tr>
                    <tr className="border-b border-gray-800">
                      <td className="py-3 text-gray-300">API Calls</td>
                      {plans.map((plan) => (
                        <td key={plan.id} className="text-center py-3 text-white">
                          {plan.limits.apiCalls}
                        </td>
                      ))}
                    </tr>
                    <tr className="border-b border-gray-800">
                      <td className="py-3 text-gray-300">Bot Access</td>
                      {plans.map((plan) => (
                        <td key={plan.id} className="text-center py-3">
                          {plan.limits.botAccess ? (
                            <Check className="h-4 w-4 text-cyber-green mx-auto" />
                          ) : (
                            <X className="h-4 w-4 text-red-400 mx-auto" />
                          )}
                        </td>
                      ))}
                    </tr>
                    <tr>
                      <td className="py-3 text-gray-300">Support</td>
                      {plans.map((plan) => (
                        <td key={plan.id} className="text-center py-3 text-white">
                          {plan.limits.support}
                        </td>
                      ))}
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Payment Modal */}
      {showPaymentModal && selectedPlan && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md mx-4" border="green">
            <div className="p-6">
              <h3 className="text-lg font-bold text-white mb-4">
                Upgrade to {plans.find(p => p.id === selectedPlan)?.name}
              </h3>
              
              <div className="mb-6">
                <div className="text-center p-4 bg-gray-800/50 rounded-lg">
                  <div className="text-2xl font-bold text-cyber-green">
                    {formatPrice(plans.find(p => p.id === selectedPlan)?.price[billingCycle] || 0)}
                  </div>
                  <div className="text-sm text-gray-400">
                    {billingCycle === 'monthly' ? 'per bulan' : 'per tahun'}
                  </div>
                </div>
              </div>

              <div className="space-y-3 mb-6">
                <h4 className="font-semibold text-white">Choose Payment Method:</h4>
                {paymentMethods.map((method) => (
                  <label
                    key={method.id}
                    className={`cursor-pointer border rounded-lg p-3 transition-all duration-200 flex items-center space-x-3 ${
                      selectedPayment === method.id
                        ? 'border-cyber-green bg-cyber-green/10'
                        : 'border-gray-600 hover:border-gray-500'
                    }`}
                  >
                    <input
                      type="radio"
                      name="payment"
                      value={method.id}
                      checked={selectedPayment === method.id}
                      onChange={(e) => setSelectedPayment(e.target.value)}
                      className="sr-only"
                    />
                    <span className="text-2xl">{method.icon}</span>
                    <div className="flex-1">
                      <div className="font-medium text-white">{method.name}</div>
                      <div className="text-xs text-gray-400">{method.description}</div>
                      <div className="text-xs text-cyber-green">Processing: {method.processingTime}</div>
                    </div>
                  </label>
                ))}
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={processPayment}
                  disabled={!selectedPayment}
                  className="cyber-btn-primary flex-1 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Proceed to Payment
                </button>
                <button
                  onClick={() => setShowPaymentModal(false)}
                  className="cyber-btn"
                >
                  Cancel
                </button>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  )
}
