"use strict";(()=>{var e={};e.id=8873,e.ids=[8873],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},97189:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>h,patchFetch:()=>g,requestAsyncStorage:()=>p,routeModule:()=>d,serverHooks:()=>m,staticGenerationAsyncStorage:()=>c});var o={};t.r(o),t.d(o,{OPTIONS:()=>l,POST:()=>u});var a=t(49303),s=t(88716),n=t(60670),i=t(87070);async function u(e){try{let{email:r,password:t}=await e.json();if(!r||!t)return i.NextResponse.json({success:!1,error:"Email and password are required",code:"MISSING_CREDENTIALS"},{status:400});let o=[{email:"<EMAIL>",password:"admin123",role:"super_admin",plan:"cybersecurity"},{email:"<EMAIL>",password:"user123",role:"user",plan:"hobby"},{email:"<EMAIL>",password:"hunter123",role:"user",plan:"bughunter"}].find(e=>e.email===r&&e.password===t);if(!o)return i.NextResponse.json({success:!1,error:"Invalid email or password",code:"INVALID_CREDENTIALS"},{status:401});let a=`kxg_token_${Date.now()}_${Math.random().toString(36).substring(2,11)}`,s={id:Math.random().toString(36).substring(2,11),username:o.email.split("@")[0],email:o.email,fullName:"super_admin"===o.role?"Super Administrator":"user"===o.role?"Regular User":"Security Analyst",role:o.role,plan:o.plan,isActive:!0,emailVerified:!0,createdAt:"2023-01-01T00:00:00Z",lastLogin:new Date().toISOString(),stats:{totalScans:Math.floor(1e3*Math.random())+100,vulnerabilitiesFound:Math.floor(50*Math.random())+10,filesAnalyzed:Math.floor(200*Math.random())+50,apiCalls:Math.floor(1e4*Math.random())+1e3,score:Math.floor(5e3*Math.random())+1e3,rank:Math.floor(100*Math.random())+1}},n=i.NextResponse.json({success:!0,token:a,user:s,expiresIn:"24h",message:"Login successful",timestamp:new Date().toISOString()});return n.cookies.set("auth-token",a,{httpOnly:!0,secure:!0,sameSite:"strict",maxAge:86400}),n}catch(e){return console.error("Login error:",e),i.NextResponse.json({success:!1,error:"Internal server error",code:"INTERNAL_ERROR"},{status:500})}}async function l(){return new i.NextResponse(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}let d=new a.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/auth/login/route",pathname:"/api/auth/login",filename:"route",bundlePath:"app/api/auth/login/route"},resolvedPagePath:"D:\\Users\\Downloads\\kodeXGuard\\app\\api\\auth\\login\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:p,staticGenerationAsyncStorage:c,serverHooks:m}=d,h="/api/auth/login/route";function g(){return(0,n.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:c})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[9276,5972],()=>t(97189));module.exports=o})();