import { NextRequest, NextResponse } from 'next/server'
import { AuthService } from '@/lib/auth'
import { Database } from '@/lib/database'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { username, email, password, fullName } = body

    // Validate input
    if (!username || !email || !password) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Username, email and password are required',
          code: 'MISSING_FIELDS'
        },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid email format',
          code: 'INVALID_EMAIL'
        },
        { status: 400 }
      )
    }

    // Validate password strength
    if (password.length < 6) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Password must be at least 6 characters long',
          code: 'WEAK_PASSWORD'
        },
        { status: 400 }
      )
    }

    // Check if email already exists (simple check)
    const existingEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>']
    if (existingEmails.includes(email)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Email already registered',
          code: 'EMAIL_EXISTS'
        },
        { status: 409 }
      )
    }

    // Generate token
    const token = `kxg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    const userData = {
      id: Math.floor(Math.random() * 1000),
      username: username,
      email: email,
      fullName: fullName || username,
      role: 'user',
      plan: 'gratis',
      planName: 'Gratis',
      isActive: true,
      emailVerified: false,
      createdAt: new Date().toISOString(),
      lastLogin: null
    }

    const response = NextResponse.json({
      success: true,
      data: {
        token: token,
        user: userData,
        expiresIn: '7d'
      },
      message: 'Registration successful',
      timestamp: new Date().toISOString()
    })

    // Set HTTP-only cookie for token
    response.cookies.set('auth-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 // 24 hours
    })

    return response

  } catch (error) {
    console.error('Registration error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
