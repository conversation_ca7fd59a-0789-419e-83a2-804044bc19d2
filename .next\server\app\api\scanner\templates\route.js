"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/scanner/templates/route";
exports.ids = ["app/api/scanner/templates/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fscanner%2Ftemplates%2Froute&page=%2Fapi%2Fscanner%2Ftemplates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscanner%2Ftemplates%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fscanner%2Ftemplates%2Froute&page=%2Fapi%2Fscanner%2Ftemplates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscanner%2Ftemplates%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Users_Downloads_kodeXGuard_app_api_scanner_templates_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/scanner/templates/route.ts */ \"(rsc)/./app/api/scanner/templates/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/scanner/templates/route\",\n        pathname: \"/api/scanner/templates\",\n        filename: \"route\",\n        bundlePath: \"app/api/scanner/templates/route\"\n    },\n    resolvedPagePath: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\api\\\\scanner\\\\templates\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Users_Downloads_kodeXGuard_app_api_scanner_templates_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/scanner/templates/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fscanner%2Ftemplates%2Froute&page=%2Fapi%2Fscanner%2Ftemplates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscanner%2Ftemplates%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/scanner/templates/route.ts":
/*!********************************************!*\
  !*** ./app/api/scanner/templates/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function GET(request) {\n    try {\n        // Get auth token\n        const authHeader = request.headers.get(\"authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        // Simple token validation for testing\n        if (!token.startsWith(\"kxg_\")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid token\"\n            }, {\n                status: 401\n            });\n        }\n        // Mock scan templates data\n        const templates = [\n            {\n                id: \"template_001\",\n                name: \"Quick Web Scan\",\n                description: \"Fast vulnerability scan for web applications covering common security issues\",\n                category: \"web\",\n                scanTypes: [\n                    \"sqli\",\n                    \"xss\",\n                    \"csrf\",\n                    \"directory_traversal\"\n                ],\n                settings: {\n                    maxDepth: 3,\n                    timeout: 30,\n                    threads: 5,\n                    userAgent: \"KodeXGuard Scanner v1.0\"\n                },\n                isDefault: true,\n                createdAt: new Date(Date.now() - 86400000 * 30).toISOString(),\n                updatedAt: new Date(Date.now() - 86400000 * 5).toISOString(),\n                usageCount: 156\n            },\n            {\n                id: \"template_002\",\n                name: \"Comprehensive Web Audit\",\n                description: \"Deep security audit covering all OWASP Top 10 vulnerabilities and more\",\n                category: \"web\",\n                scanTypes: [\n                    \"sqli\",\n                    \"xss\",\n                    \"csrf\",\n                    \"xxe\",\n                    \"broken_auth\",\n                    \"sensitive_data\",\n                    \"xml_injection\",\n                    \"broken_access\",\n                    \"security_misconfig\",\n                    \"vulnerable_components\"\n                ],\n                settings: {\n                    maxDepth: 10,\n                    timeout: 120,\n                    threads: 10,\n                    userAgent: \"Mozilla/5.0 (compatible; KodeXGuard/1.0)\",\n                    excludePaths: [\n                        \"/admin/logout\",\n                        \"/user/delete\"\n                    ]\n                },\n                isDefault: true,\n                createdAt: new Date(Date.now() - 86400000 * 45).toISOString(),\n                updatedAt: new Date(Date.now() - 86400000 * 2).toISOString(),\n                usageCount: 89\n            },\n            {\n                id: \"template_003\",\n                name: \"API Security Scan\",\n                description: \"Specialized scan for REST APIs and GraphQL endpoints\",\n                category: \"api\",\n                scanTypes: [\n                    \"api_security\",\n                    \"auth_bypass\",\n                    \"rate_limiting\",\n                    \"input_validation\",\n                    \"data_exposure\"\n                ],\n                settings: {\n                    timeout: 60,\n                    threads: 8,\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Accept\": \"application/json\"\n                    }\n                },\n                isDefault: true,\n                createdAt: new Date(Date.now() - 86400000 * 20).toISOString(),\n                updatedAt: new Date(Date.now() - 86400000 * 1).toISOString(),\n                usageCount: 67\n            },\n            {\n                id: \"template_004\",\n                name: \"Mobile App Security\",\n                description: \"Security assessment for mobile applications and APIs\",\n                category: \"mobile\",\n                scanTypes: [\n                    \"mobile_security\",\n                    \"ssl_pinning\",\n                    \"data_storage\",\n                    \"crypto_implementation\"\n                ],\n                settings: {\n                    timeout: 90,\n                    threads: 6,\n                    userAgent: \"KodeXGuard Mobile Scanner\"\n                },\n                isDefault: true,\n                createdAt: new Date(Date.now() - 86400000 * 15).toISOString(),\n                updatedAt: new Date(Date.now() - 86400000 * 3).toISOString(),\n                usageCount: 34\n            },\n            {\n                id: \"template_005\",\n                name: \"Network Infrastructure\",\n                description: \"Network security scan including port scanning and service enumeration\",\n                category: \"network\",\n                scanTypes: [\n                    \"port_scan\",\n                    \"service_enum\",\n                    \"ssl_check\",\n                    \"dns_enum\"\n                ],\n                settings: {\n                    timeout: 300,\n                    threads: 20\n                },\n                isDefault: true,\n                createdAt: new Date(Date.now() - 86400000 * 60).toISOString(),\n                updatedAt: new Date(Date.now() - 86400000 * 7).toISOString(),\n                usageCount: 123\n            },\n            {\n                id: \"template_006\",\n                name: \"E-commerce Security\",\n                description: \"Custom template for e-commerce platforms with payment security focus\",\n                category: \"custom\",\n                scanTypes: [\n                    \"sqli\",\n                    \"xss\",\n                    \"payment_security\",\n                    \"session_management\",\n                    \"pci_compliance\"\n                ],\n                settings: {\n                    maxDepth: 5,\n                    timeout: 60,\n                    threads: 8,\n                    excludePaths: [\n                        \"/payment/process\",\n                        \"/checkout/complete\"\n                    ],\n                    includePaths: [\n                        \"/shop\",\n                        \"/cart\",\n                        \"/checkout\",\n                        \"/payment\"\n                    ]\n                },\n                isDefault: false,\n                createdAt: new Date(Date.now() - 86400000 * 10).toISOString(),\n                updatedAt: new Date(Date.now() - 86400000 * 1).toISOString(),\n                usageCount: 45\n            }\n        ];\n        // Sort by usage count (most used first)\n        templates.sort((a, b)=>b.usageCount - a.usageCount);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: templates,\n            meta: {\n                total: templates.length,\n                default: templates.filter((t)=>t.isDefault).length,\n                custom: templates.filter((t)=>!t.isDefault).length,\n                categories: {\n                    web: templates.filter((t)=>t.category === \"web\").length,\n                    api: templates.filter((t)=>t.category === \"api\").length,\n                    mobile: templates.filter((t)=>t.category === \"mobile\").length,\n                    network: templates.filter((t)=>t.category === \"network\").length,\n                    custom: templates.filter((t)=>t.category === \"custom\").length\n                }\n            },\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"Scanner templates API error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        // Get auth token\n        const authHeader = request.headers.get(\"authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        // Simple token validation for testing\n        if (!token.startsWith(\"kxg_\")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid token\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { name, description, category, scanTypes, settings } = body;\n        // Validate input\n        if (!name || !description || !category || !scanTypes || !Array.isArray(scanTypes)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Name, description, category, and scanTypes are required\"\n            }, {\n                status: 400\n            });\n        }\n        // Create new template\n        const newTemplate = {\n            id: `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            name,\n            description,\n            category,\n            scanTypes,\n            settings: settings || {},\n            isDefault: false,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString(),\n            usageCount: 0\n        };\n        // In a real app, save to database\n        // For demo, just return the created template\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: newTemplate,\n            message: \"Template created successfully\",\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"Create template error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/scanner/templates/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fscanner%2Ftemplates%2Froute&page=%2Fapi%2Fscanner%2Ftemplates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscanner%2Ftemplates%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();