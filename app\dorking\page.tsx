'use client'

import { useState, useEffect } from 'react'
import Navbar from '@/components/Navbar'
import { Card, StatCard, AlertCard } from '@/components/Card'
import {
  Search,
  Globe,
  Save,
  Play,
  Copy,
  ExternalLink,
  Star,
  Clock,
  TrendingUp,
  Shield,
  Database,
  FileText,
  Eye,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Filter,
  Calendar,
  Target,
  Bookmark,
  Bell
} from 'lucide-react'

interface DorkPreset {
  id: string
  name: string
  category: string
  description: string
  query: string
  risk: 'low' | 'medium' | 'high' | 'critical'
  tags: string[]
  usage: number
  favorite?: boolean
}

interface DorkResult {
  id: string
  title: string
  url: string
  snippet: string
  domain: string
  risk: 'low' | 'medium' | 'high' | 'critical'
  timestamp: string
}

interface CVEDaily {
  cveId: string
  title: string
  severity: 'critical' | 'high' | 'medium' | 'low'
  cvssScore: number
  publishedDate: string
  affectedProducts: string[]
  description: string
  trending: boolean
}

export default function DorkingPage() {
  const [user] = useState({
    username: 'admin',
    avatar: '',
    role: 'super_admin',
    plan: 'cybersecurity'
  })

  const [activeTab, setActiveTab] = useState('dorking')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [customQuery, setCustomQuery] = useState('')
  const [searchResults, setSearchResults] = useState<DorkResult[]>([])
  const [loading, setLoading] = useState(false)
  const [savedQueries, setSavedQueries] = useState<string[]>([])

  const dorkPresets: DorkPreset[] = [
    {
      id: '1',
      name: 'Admin Login Pages',
      category: 'Authentication',
      description: 'Find admin login pages and panels',
      query: 'inurl:admin inurl:login',
      risk: 'medium',
      tags: ['admin', 'login', 'panel'],
      usage: 1247
    },
    {
      id: '2',
      name: 'Database Files',
      category: 'Database',
      description: 'Exposed database files and backups',
      query: 'filetype:sql "INSERT INTO" site:*.com',
      risk: 'critical',
      tags: ['database', 'sql', 'backup'],
      usage: 892
    },
    {
      id: '3',
      name: 'Config Files',
      category: 'Configuration',
      description: 'Configuration files with sensitive data',
      query: 'filetype:conf OR filetype:config "password"',
      risk: 'high',
      tags: ['config', 'password', 'sensitive'],
      usage: 756
    },
    {
      id: '4',
      name: 'Directory Listing',
      category: 'Directory',
      description: 'Open directory listings',
      query: 'intitle:"Index of /" "Parent Directory"',
      risk: 'medium',
      tags: ['directory', 'listing', 'open'],
      usage: 634
    },
    {
      id: '5',
      name: 'Log Files',
      category: 'Logs',
      description: 'Exposed log files with sensitive information',
      query: 'filetype:log "password" OR "username"',
      risk: 'high',
      tags: ['logs', 'password', 'username'],
      usage: 523
    },
    {
      id: '6',
      name: 'PHP Info Pages',
      category: 'Information',
      description: 'PHP info pages revealing server information',
      query: 'inurl:phpinfo.php "PHP Version"',
      risk: 'medium',
      tags: ['php', 'info', 'server'],
      usage: 445
    },
    {
      id: '7',
      name: 'Backup Files',
      category: 'Backup',
      description: 'Database and website backup files',
      query: 'filetype:bak OR filetype:backup "database"',
      risk: 'critical',
      tags: ['backup', 'database', 'files'],
      usage: 389
    },
    {
      id: '8',
      name: 'Error Messages',
      category: 'Errors',
      description: 'Error messages revealing system information',
      query: '"Warning: mysql_connect()" "Access denied"',
      risk: 'low',
      tags: ['error', 'mysql', 'warning'],
      usage: 312
    }
  ]

  const cveDaily: CVEDaily[] = [
    {
      cveId: 'CVE-2024-0001',
      title: 'Critical SQL Injection in WebApp Framework',
      severity: 'critical',
      cvssScore: 9.8,
      publishedDate: '2024-01-15',
      affectedProducts: ['WebApp Framework 2.x', 'WebApp Framework 3.x'],
      description: 'A critical SQL injection vulnerability allows remote attackers to execute arbitrary SQL commands.',
      trending: true
    },
    {
      cveId: 'CVE-2024-0002',
      title: 'Remote Code Execution in CMS Platform',
      severity: 'critical',
      cvssScore: 9.5,
      publishedDate: '2024-01-14',
      affectedProducts: ['CMS Platform 1.0-4.2'],
      description: 'Unauthenticated remote code execution via file upload functionality.',
      trending: true
    },
    {
      cveId: 'CVE-2024-0003',
      title: 'Cross-Site Scripting in E-commerce Plugin',
      severity: 'high',
      cvssScore: 7.2,
      publishedDate: '2024-01-13',
      affectedProducts: ['E-commerce Plugin 2.1', 'E-commerce Plugin 2.2'],
      description: 'Stored XSS vulnerability in product review functionality.',
      trending: false
    },
    {
      cveId: 'CVE-2024-0004',
      title: 'Authentication Bypass in API Gateway',
      severity: 'high',
      cvssScore: 8.1,
      publishedDate: '2024-01-12',
      affectedProducts: ['API Gateway 1.x'],
      description: 'Authentication bypass allows unauthorized access to protected endpoints.',
      trending: false
    },
    {
      cveId: 'CVE-2024-0005',
      title: 'Path Traversal in File Manager',
      severity: 'medium',
      cvssScore: 6.5,
      publishedDate: '2024-01-11',
      affectedProducts: ['File Manager Pro 3.0'],
      description: 'Directory traversal vulnerability allows access to arbitrary files.',
      trending: false
    }
  ]

  const categories = [...new Set(dorkPresets.map(preset => preset.category))]

  const filteredPresets = selectedCategory === 'all'
    ? dorkPresets
    : dorkPresets.filter(preset => preset.category === selectedCategory)

  const executeSearch = async (query: string) => {
    setLoading(true)
    setSearchResults([])

    try {
      // Simulate search
      await new Promise(resolve => setTimeout(resolve, 2000))

      const mockResults: DorkResult[] = [
        {
          id: '1',
          title: 'Admin Login Panel - Company XYZ',
          url: 'https://example.com/admin/login.php',
          snippet: 'Admin login panel for Company XYZ management system. Please enter your credentials to access the admin area.',
          domain: 'example.com',
          risk: 'medium',
          timestamp: '2 hours ago'
        },
        {
          id: '2',
          title: 'Database Backup File - backup.sql',
          url: 'https://test-site.com/backup/backup.sql',
          snippet: 'Database backup file containing user credentials and sensitive information. File size: 2.3MB',
          domain: 'test-site.com',
          risk: 'critical',
          timestamp: '5 hours ago'
        },
        {
          id: '3',
          title: 'Configuration File - config.php',
          url: 'https://demo.org/config/config.php',
          snippet: 'Configuration file with database credentials and API keys exposed.',
          domain: 'demo.org',
          risk: 'high',
          timestamp: '1 day ago'
        }
      ]

      setSearchResults(mockResults)
    } catch (error) {
      console.error('Search error:', error)
    } finally {
      setLoading(false)
    }
  }

  const saveQuery = (query: string) => {
    if (!savedQueries.includes(query)) {
      setSavedQueries(prev => [...prev, query])
    }
  }

  const copyQuery = (query: string) => {
    navigator.clipboard.writeText(query)
  }

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'critical': return 'text-red-400 bg-red-900/20'
      case 'high': return 'text-orange-400 bg-orange-900/20'
      case 'medium': return 'text-yellow-400 bg-yellow-900/20'
      case 'low': return 'text-blue-400 bg-blue-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-400 bg-red-900/20'
      case 'high': return 'text-orange-400 bg-orange-900/20'
      case 'medium': return 'text-yellow-400 bg-yellow-900/20'
      case 'low': return 'text-blue-400 bg-blue-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-cyber">
      <Navbar user={user} />

      <div className="pt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <Search className="h-8 w-8 text-cyber-green" />
              <h1 className="text-3xl font-bold font-cyber text-white">
                Google <span className="cyber-text">Dorking</span> & CVE Harian
              </h1>
            </div>
            <p className="text-gray-400 max-w-3xl">
              Advanced Google dorking dengan preset queries untuk security research dan CVE intelligence harian.
              Temukan exposed files, admin panels, dan vulnerability terbaru.
            </p>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <StatCard
              title="Dork Presets"
              value={dorkPresets.length}
              icon={Target}
              color="green"
            />
            <StatCard
              title="Saved Queries"
              value={savedQueries.length}
              icon={Bookmark}
              color="blue"
            />
            <StatCard
              title="CVE Today"
              value={cveDaily.length}
              icon={Shield}
              color="red"
            />
            <StatCard
              title="Trending CVE"
              value={cveDaily.filter(cve => cve.trending).length}
              icon={TrendingUp}
              color="gold"
            />
          </div>

          {/* Tab Navigation */}
          <div className="mb-8">
            <div className="flex space-x-1 bg-gray-800/50 p-1 rounded-lg">
              {[
                { id: 'dorking', name: 'Google Dorking', icon: Search },
                { id: 'cve', name: 'CVE Harian', icon: Shield }
              ].map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                      activeTab === tab.id
                        ? 'bg-cyber-green text-black'
                        : 'text-gray-300 hover:text-white hover:bg-gray-700/50'
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    <span>{tab.name}</span>
                  </button>
                )
              })}
            </div>
          </div>

          {/* Google Dorking Tab */}
          {activeTab === 'dorking' && (
            <div className="grid lg:grid-cols-4 gap-8">
              <div className="lg:col-span-3">
                {/* Custom Search */}
                <Card border="green" glow className="mb-6">
                  <div className="p-6">
                    <h2 className="text-xl font-bold text-white mb-4">Custom Google Dork</h2>
                    <div className="space-y-4">
                      <div className="flex space-x-3">
                        <input
                          type="text"
                          value={customQuery}
                          onChange={(e) => setCustomQuery(e.target.value)}
                          placeholder='Enter custom dork query (e.g., filetype:sql "INSERT INTO")'
                          className="cyber-input flex-1"
                        />
                        <button
                          onClick={() => executeSearch(customQuery)}
                          disabled={loading || !customQuery.trim()}
                          className="cyber-btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {loading ? (
                            <>
                              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                              Searching...
                            </>
                          ) : (
                            <>
                              <Play className="h-4 w-4 mr-2" />
                              Search
                            </>
                          )}
                        </button>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => saveQuery(customQuery)}
                          disabled={!customQuery.trim()}
                          className="cyber-btn text-sm disabled:opacity-50"
                        >
                          <Save className="h-4 w-4 mr-2" />
                          Save Query
                        </button>
                        <button
                          onClick={() => copyQuery(customQuery)}
                          disabled={!customQuery.trim()}
                          className="cyber-btn text-sm disabled:opacity-50"
                        >
                          <Copy className="h-4 w-4 mr-2" />
                          Copy
                        </button>
                      </div>
                    </div>
                  </div>
                </Card>

                {/* Search Results */}
                {searchResults.length > 0 && (
                  <Card className="mb-6">
                    <div className="p-6">
                      <h3 className="text-lg font-bold text-white mb-4">Search Results</h3>
                      <div className="space-y-4">
                        {searchResults.map((result) => (
                          <div
                            key={result.id}
                            className="bg-gray-800/50 rounded-lg p-4 border border-gray-700"
                          >
                            <div className="flex items-start justify-between mb-2">
                              <div className="flex-1">
                                <div className="flex items-center space-x-2 mb-1">
                                  <h4 className="font-semibold text-white">{result.title}</h4>
                                  <span className={`px-2 py-1 rounded-full text-xs font-semibold ${getRiskColor(result.risk)}`}>
                                    {result.risk.toUpperCase()}
                                  </span>
                                </div>
                                <a
                                  href={result.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-cyber-green hover:text-cyber-blue transition-colors text-sm"
                                >
                                  {result.url}
                                  <ExternalLink className="h-3 w-3 ml-1 inline" />
                                </a>
                              </div>
                              <div className="text-xs text-gray-400">{result.timestamp}</div>
                            </div>
                            <p className="text-gray-300 text-sm mb-2">{result.snippet}</p>
                            <div className="flex items-center justify-between text-xs text-gray-400">
                              <span>Domain: {result.domain}</span>
                              <button className="text-cyber-green hover:text-white transition-colors">
                                <Eye className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </Card>
                )}

                {/* Dork Presets */}
                <Card>
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-6">
                      <h2 className="text-xl font-bold text-white">Dork Presets</h2>
                      <select
                        value={selectedCategory}
                        onChange={(e) => setSelectedCategory(e.target.value)}
                        className="cyber-input text-sm"
                      >
                        <option value="all">All Categories</option>
                        {categories.map(category => (
                          <option key={category} value={category}>{category}</option>
                        ))}
                      </select>
                    </div>

                    <div className="grid md:grid-cols-2 gap-4">
                      {filteredPresets.map((preset) => (
                        <div
                          key={preset.id}
                          className="bg-gray-800/50 rounded-lg p-4 border border-gray-700 hover:border-gray-600 transition-colors"
                        >
                          <div className="flex items-start justify-between mb-3">
                            <div>
                              <div className="flex items-center space-x-2 mb-1">
                                <h3 className="font-semibold text-white">{preset.name}</h3>
                                <span className={`px-2 py-1 rounded-full text-xs font-semibold ${getRiskColor(preset.risk)}`}>
                                  {preset.risk.toUpperCase()}
                                </span>
                              </div>
                              <p className="text-sm text-gray-400">{preset.description}</p>
                            </div>
                            <div className="text-xs text-gray-400">{preset.usage} uses</div>
                          </div>

                          <div className="bg-gray-900/50 rounded p-3 mb-3">
                            <code className="text-sm text-cyber-green">{preset.query}</code>
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="flex flex-wrap gap-1">
                              {preset.tags.map((tag, idx) => (
                                <span
                                  key={idx}
                                  className="px-2 py-1 bg-gray-700 text-gray-300 rounded text-xs"
                                >
                                  {tag}
                                </span>
                              ))}
                            </div>
                            <div className="flex space-x-2">
                              <button
                                onClick={() => copyQuery(preset.query)}
                                className="text-gray-400 hover:text-white transition-colors"
                              >
                                <Copy className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => {
                                  setCustomQuery(preset.query)
                                  executeSearch(preset.query)
                                }}
                                className="text-gray-400 hover:text-cyber-green transition-colors"
                              >
                                <Play className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </Card>
              </div>

              {/* Sidebar */}
              <div>
                <Card>
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-white mb-4">Saved Queries</h3>
                    {savedQueries.length === 0 ? (
                      <p className="text-gray-400 text-sm">No saved queries yet</p>
                    ) : (
                      <div className="space-y-2">
                        {savedQueries.map((query, idx) => (
                          <div
                            key={idx}
                            className="bg-gray-800/50 rounded p-3 text-sm"
                          >
                            <code className="text-cyber-green">{query}</code>
                            <div className="flex space-x-2 mt-2">
                              <button
                                onClick={() => copyQuery(query)}
                                className="text-gray-400 hover:text-white transition-colors"
                              >
                                <Copy className="h-3 w-3" />
                              </button>
                              <button
                                onClick={() => {
                                  setCustomQuery(query)
                                  executeSearch(query)
                                }}
                                className="text-gray-400 hover:text-cyber-green transition-colors"
                              >
                                <Play className="h-3 w-3" />
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </Card>

                <Card className="mt-6">
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-white mb-4">Dorking Tips</h3>
                    <div className="space-y-2 text-sm text-gray-300">
                      <p>• Use <code className="text-cyber-green">site:</code> to target specific domains</p>
                      <p>• <code className="text-cyber-green">filetype:</code> searches for specific file types</p>
                      <p>• <code className="text-cyber-green">inurl:</code> finds text in URLs</p>
                      <p>• <code className="text-cyber-green">intitle:</code> searches page titles</p>
                      <p>• Use quotes for exact phrases</p>
                      <p>• Combine operators with AND/OR</p>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          )}

          {/* CVE Harian Tab */}
          {activeTab === 'cve' && (
            <div className="grid lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <Card border="red" glow>
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-6">
                      <h2 className="text-xl font-bold text-white">CVE Intelligence Harian</h2>
                      <div className="flex items-center space-x-2 text-sm text-gray-400">
                        <Calendar className="h-4 w-4" />
                        <span>Updated: {new Date().toLocaleDateString()}</span>
                      </div>
                    </div>

                    <div className="space-y-4">
                      {cveDaily.map((cve) => (
                        <div
                          key={cve.cveId}
                          className="bg-gray-800/50 rounded-lg p-4 border border-gray-700"
                        >
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-2">
                                <h3 className="font-bold text-white">{cve.cveId}</h3>
                                <span className={`px-2 py-1 rounded-full text-xs font-semibold ${getSeverityColor(cve.severity)}`}>
                                  {cve.severity.toUpperCase()}
                                </span>
                                <span className="px-2 py-1 bg-gray-700 text-gray-300 rounded-full text-xs font-semibold">
                                  CVSS {cve.cvssScore}
                                </span>
                                {cve.trending && (
                                  <span className="px-2 py-1 bg-red-900/50 text-red-400 rounded-full text-xs font-semibold flex items-center">
                                    <TrendingUp className="h-3 w-3 mr-1" />
                                    Trending
                                  </span>
                                )}
                              </div>
                              <h4 className="font-semibold text-white mb-2">{cve.title}</h4>
                              <p className="text-gray-300 text-sm mb-3">{cve.description}</p>
                            </div>
                            <div className="text-xs text-gray-400">
                              {new Date(cve.publishedDate).toLocaleDateString()}
                            </div>
                          </div>

                          <div className="mb-3">
                            <h5 className="text-sm font-semibold text-gray-300 mb-2">Affected Products:</h5>
                            <div className="flex flex-wrap gap-2">
                              {cve.affectedProducts.map((product, idx) => (
                                <span
                                  key={idx}
                                  className="px-2 py-1 bg-blue-900/50 text-blue-400 rounded text-xs"
                                >
                                  {product}
                                </span>
                              ))}
                            </div>
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="flex space-x-2">
                              <button className="cyber-btn text-xs">
                                <ExternalLink className="h-3 w-3 mr-1" />
                                View Details
                              </button>
                              <button className="cyber-btn text-xs">
                                <Search className="h-3 w-3 mr-1" />
                                Search Exploits
                              </button>
                            </div>
                            <div className="flex space-x-2">
                              <button className="text-gray-400 hover:text-yellow-400 transition-colors">
                                <Star className="h-4 w-4" />
                              </button>
                              <button className="text-gray-400 hover:text-white transition-colors">
                                <Copy className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </Card>
              </div>

              {/* CVE Stats Sidebar */}
              <div>
                <Card>
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-white mb-4">CVE Statistics</h3>
                    <div className="space-y-4">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Total CVE Today</span>
                        <span className="text-white font-semibold">{cveDaily.length}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Critical</span>
                        <span className="text-red-400 font-semibold">
                          {cveDaily.filter(cve => cve.severity === 'critical').length}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">High</span>
                        <span className="text-orange-400 font-semibold">
                          {cveDaily.filter(cve => cve.severity === 'high').length}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Medium</span>
                        <span className="text-yellow-400 font-semibold">
                          {cveDaily.filter(cve => cve.severity === 'medium').length}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Trending</span>
                        <span className="text-cyber-green font-semibold">
                          {cveDaily.filter(cve => cve.trending).length}
                        </span>
                      </div>
                    </div>
                  </div>
                </Card>

                <Card className="mt-6" border="gold">
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-white mb-4">Quick Actions</h3>
                    <div className="space-y-3">
                      <button className="cyber-btn w-full text-sm">
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Refresh CVE Feed
                      </button>
                      <button className="cyber-btn w-full text-sm">
                        <Download className="h-4 w-4 mr-2" />
                        Export CVE List
                      </button>
                      <button className="cyber-btn w-full text-sm">
                        <Bell className="h-4 w-4 mr-2" />
                        Setup Alerts
                      </button>
                    </div>
                  </div>
                </Card>

                <Card className="mt-6">
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-white mb-4">CVE Sources</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-400" />
                        <span className="text-gray-300">NIST NVD</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-400" />
                        <span className="text-gray-300">MITRE CVE</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-400" />
                        <span className="text-gray-300">Exploit-DB</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-400" />
                        <span className="text-gray-300">Security Advisories</span>
                      </div>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}