'use client'

import { ReactNode } from 'react'
import Sidebar from './Sidebar'
import Navbar from './Navbar'

interface DashboardLayoutProps {
  children: ReactNode
  user: {
    username: string
    avatar?: string
    role: string
    plan: string
  }
  title?: string
  showSearch?: boolean
}

export default function DashboardLayout({
  children,
  user,
  title,
  showSearch = true
}: DashboardLayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-cyber flex">
      {/* Sidebar */}
      <Sidebar user={user} />

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Top Navbar */}
        <Navbar user={user} title={title} showSearch={showSearch} />

        {/* Page Content */}
        <main className="flex-1 pt-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}
