'use client'

import { useState, useEffect } from 'react'
import Navbar from '@/components/Navbar'
import { Card, StatCard, AlertCard } from '@/components/Card'
import { 
  Bot, 
  MessageCircle, 
  Send, 
  QrCode,
  Settings,
  Users,
  Activity,
  Smartphone,
  Globe,
  Shield,
  CheckCircle,
  XCircle,
  Clock,
  RefreshCw,
  Plus,
  Trash2,
  Eye,
  Copy
} from 'lucide-react'

interface BotConfig {
  id: string
  type: 'whatsapp' | 'telegram'
  name: string
  status: 'connected' | 'disconnected' | 'connecting'
  lastSeen: string
  messagesSent: number
  usersServed: number
  qrCode?: string
}

interface BotMessage {
  id: string
  botId: string
  from: string
  message: string
  response: string
  timestamp: string
  type: 'scan' | 'status' | 'help' | 'error'
}

export default function BotPage() {
  const [user] = useState({
    username: 'admin',
    avatar: '',
    role: 'super_admin',
    plan: 'cybersecurity'
  })

  const [bots, setBots] = useState<BotConfig[]>([
    {
      id: '1',
      type: 'whatsapp',
      name: 'KodeXGuard WhatsApp Bot',
      status: 'connected',
      lastSeen: '2 minutes ago',
      messagesSent: 1247,
      usersServed: 89
    },
    {
      id: '2',
      type: 'telegram',
      name: 'KodeXGuard Telegram Bot',
      status: 'disconnected',
      lastSeen: '1 hour ago',
      messagesSent: 856,
      usersServed: 156
    }
  ])

  const [messages, setMessages] = useState<BotMessage[]>([
    {
      id: '1',
      botId: '1',
      from: '+62812345678',
      message: '/scan https://example.com',
      response: 'Starting vulnerability scan for https://example.com...',
      timestamp: '2024-01-15T10:30:00Z',
      type: 'scan'
    },
    {
      id: '2',
      botId: '1',
      from: '+62812345679',
      message: '/status',
      response: 'Your plan: Bug Hunter\nDaily scans: Unlimited\nAPI calls: 8,456/10,000',
      timestamp: '2024-01-15T10:25:00Z',
      type: 'status'
    },
    {
      id: '3',
      botId: '2',
      from: '@username123',
      message: '/help',
      response: 'Available commands:\n/scan <url> - Start vulnerability scan\n/status - Check your plan status\n/help - Show this help',
      timestamp: '2024-01-15T10:20:00Z',
      type: 'help'
    }
  ])

  const [showQR, setShowQR] = useState(false)
  const [selectedBot, setSelectedBot] = useState<string | null>(null)
  const [newBotName, setNewBotName] = useState('')
  const [newBotType, setNewBotType] = useState<'whatsapp' | 'telegram'>('whatsapp')

  const stats = {
    totalBots: bots.length,
    activeBots: bots.filter(b => b.status === 'connected').length,
    totalMessages: messages.length,
    totalUsers: bots.reduce((sum, bot) => sum + bot.usersServed, 0)
  }

  const connectBot = (botId: string) => {
    setBots(prev => prev.map(bot => 
      bot.id === botId 
        ? { ...bot, status: 'connecting' as const }
        : bot
    ))

    // Simulate connection process
    setTimeout(() => {
      setBots(prev => prev.map(bot => 
        bot.id === botId 
          ? { 
              ...bot, 
              status: 'connected' as const, 
              lastSeen: 'Just now',
              qrCode: bot.type === 'whatsapp' ? generateQRCode() : undefined
            }
          : bot
      ))
      if (bots.find(b => b.id === botId)?.type === 'whatsapp') {
        setShowQR(true)
        setSelectedBot(botId)
      }
    }, 2000)
  }

  const disconnectBot = (botId: string) => {
    setBots(prev => prev.map(bot => 
      bot.id === botId 
        ? { ...bot, status: 'disconnected' as const }
        : bot
    ))
  }

  const generateQRCode = () => {
    // In real implementation, this would generate actual QR code
    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2ZmZiIvPgogIDx0ZXh0IHg9IjEwMCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LXNpemU9IjE0Ij5RUiBDb2RlPC90ZXh0Pgo8L3N2Zz4K'
  }

  const addBot = () => {
    if (!newBotName.trim()) return

    const newBot: BotConfig = {
      id: Date.now().toString(),
      type: newBotType,
      name: newBotName,
      status: 'disconnected',
      lastSeen: 'Never',
      messagesSent: 0,
      usersServed: 0
    }

    setBots(prev => [...prev, newBot])
    setNewBotName('')
  }

  const removeBot = (botId: string) => {
    setBots(prev => prev.filter(bot => bot.id !== botId))
    setMessages(prev => prev.filter(msg => msg.botId !== botId))
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected': return CheckCircle
      case 'connecting': return RefreshCw
      default: return XCircle
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'text-green-400'
      case 'connecting': return 'text-yellow-400'
      default: return 'text-red-400'
    }
  }

  const getMessageTypeIcon = (type: string) => {
    switch (type) {
      case 'scan': return Shield
      case 'status': return Activity
      case 'help': return MessageCircle
      default: return XCircle
    }
  }

  return (
    <div className="min-h-screen bg-gradient-cyber">
      <Navbar user={user} />
      
      <div className="pt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <Bot className="h-8 w-8 text-cyber-green" />
              <h1 className="text-3xl font-bold font-cyber text-white">
                Bot <span className="cyber-text">Center</span>
              </h1>
            </div>
            <p className="text-gray-400 max-w-3xl">
              Kelola bot WhatsApp dan Telegram untuk eksekusi scan, monitoring, dan notifikasi real-time. 
              Hanya Super Admin yang dapat menghubungkan bot, namun semua user dapat menggunakan sesuai plan.
            </p>
          </div>

          {/* Permission Check */}
          {user.role !== 'super_admin' && (
            <div className="mb-8">
              <AlertCard
                type="warning"
                title="Limited Access"
                message="Only Super Admins can manage bot connections. You can view bot status and message history."
              />
            </div>
          )}

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <StatCard
              title="Total Bots"
              value={stats.totalBots}
              icon={Bot}
              color="green"
            />
            <StatCard
              title="Active Bots"
              value={stats.activeBots}
              icon={CheckCircle}
              color="blue"
            />
            <StatCard
              title="Messages Sent"
              value={stats.totalMessages}
              icon={Send}
              color="purple"
            />
            <StatCard
              title="Users Served"
              value={stats.totalUsers}
              icon={Users}
              color="gold"
            />
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Bot Management */}
            <div className="lg:col-span-2">
              {/* Add New Bot */}
              {user.role === 'super_admin' && (
                <Card border="green" glow className="mb-6">
                  <div className="p-6">
                    <h2 className="text-xl font-bold text-white mb-4">Add New Bot</h2>
                    <div className="grid md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          Bot Name
                        </label>
                        <input
                          type="text"
                          value={newBotName}
                          onChange={(e) => setNewBotName(e.target.value)}
                          placeholder="My Bot"
                          className="cyber-input w-full"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          Bot Type
                        </label>
                        <select
                          value={newBotType}
                          onChange={(e) => setNewBotType(e.target.value as 'whatsapp' | 'telegram')}
                          className="cyber-input w-full"
                        >
                          <option value="whatsapp">WhatsApp</option>
                          <option value="telegram">Telegram</option>
                        </select>
                      </div>
                      <div className="flex items-end">
                        <button
                          onClick={addBot}
                          disabled={!newBotName.trim()}
                          className="cyber-btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add Bot
                        </button>
                      </div>
                    </div>
                  </div>
                </Card>
              )}

              {/* Bot List */}
              <Card>
                <div className="p-6">
                  <h2 className="text-xl font-bold text-white mb-6">Bot Configurations</h2>
                  <div className="space-y-4">
                    {bots.map((bot) => {
                      const StatusIcon = getStatusIcon(bot.status)
                      return (
                        <div
                          key={bot.id}
                          className="bg-gray-800/50 rounded-lg p-4 border border-gray-700"
                        >
                          <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center space-x-3">
                              {bot.type === 'whatsapp' ? (
                                <MessageCircle className="h-6 w-6 text-green-400" />
                              ) : (
                                <Send className="h-6 w-6 text-blue-400" />
                              )}
                              <div>
                                <h3 className="font-semibold text-white">{bot.name}</h3>
                                <p className="text-sm text-gray-400 capitalize">{bot.type} Bot</p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-3">
                              <div className="flex items-center space-x-2">
                                <StatusIcon className={`h-5 w-5 ${getStatusColor(bot.status)} ${
                                  bot.status === 'connecting' ? 'animate-spin' : ''
                                }`} />
                                <span className={`text-sm font-medium ${getStatusColor(bot.status)}`}>
                                  {bot.status.charAt(0).toUpperCase() + bot.status.slice(1)}
                                </span>
                              </div>
                              {user.role === 'super_admin' && (
                                <div className="flex space-x-2">
                                  {bot.status === 'connected' ? (
                                    <button
                                      onClick={() => disconnectBot(bot.id)}
                                      className="text-red-400 hover:text-red-300 transition-colors"
                                    >
                                      <XCircle className="h-5 w-5" />
                                    </button>
                                  ) : (
                                    <button
                                      onClick={() => connectBot(bot.id)}
                                      disabled={bot.status === 'connecting'}
                                      className="text-green-400 hover:text-green-300 transition-colors disabled:opacity-50"
                                    >
                                      <CheckCircle className="h-5 w-5" />
                                    </button>
                                  )}
                                  <button
                                    onClick={() => removeBot(bot.id)}
                                    className="text-red-400 hover:text-red-300 transition-colors"
                                  >
                                    <Trash2 className="h-5 w-5" />
                                  </button>
                                </div>
                              )}
                            </div>
                          </div>

                          <div className="grid md:grid-cols-3 gap-4 text-sm">
                            <div>
                              <span className="text-gray-400">Last Seen:</span>
                              <span className="text-white ml-2">{bot.lastSeen}</span>
                            </div>
                            <div>
                              <span className="text-gray-400">Messages:</span>
                              <span className="text-white ml-2">{bot.messagesSent.toLocaleString()}</span>
                            </div>
                            <div>
                              <span className="text-gray-400">Users:</span>
                              <span className="text-white ml-2">{bot.usersServed.toLocaleString()}</span>
                            </div>
                          </div>

                          {bot.status === 'connected' && (
                            <div className="mt-4 p-3 bg-green-900/20 rounded-lg border border-green-700">
                              <div className="flex items-center space-x-2">
                                <CheckCircle className="h-4 w-4 text-green-400" />
                                <span className="text-sm text-green-400 font-medium">
                                  Bot is online and ready to receive commands
                                </span>
                              </div>
                            </div>
                          )}
                        </div>
                      )
                    })}
                  </div>
                </div>
              </Card>

              {/* Recent Messages */}
              <Card className="mt-6">
                <div className="p-6">
                  <h3 className="text-lg font-bold text-white mb-6">Recent Bot Messages</h3>
                  <div className="space-y-4">
                    {messages.map((message) => {
                      const TypeIcon = getMessageTypeIcon(message.type)
                      const bot = bots.find(b => b.id === message.botId)
                      return (
                        <div
                          key={message.id}
                          className="bg-gray-800/50 rounded-lg p-4 border border-gray-700"
                        >
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex items-center space-x-3">
                              <TypeIcon className="h-5 w-5 text-cyber-green" />
                              <div>
                                <div className="font-medium text-white">{message.from}</div>
                                <div className="text-sm text-gray-400">
                                  via {bot?.name} • {new Date(message.timestamp).toLocaleString()}
                                </div>
                              </div>
                            </div>
                            <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                              message.type === 'scan' ? 'bg-blue-900/50 text-blue-400' :
                              message.type === 'status' ? 'bg-green-900/50 text-green-400' :
                              message.type === 'help' ? 'bg-purple-900/50 text-purple-400' :
                              'bg-red-900/50 text-red-400'
                            }`}>
                              {message.type}
                            </span>
                          </div>

                          <div className="space-y-2">
                            <div>
                              <span className="text-sm text-gray-400">Message:</span>
                              <div className="text-sm text-gray-300 bg-gray-900/50 rounded p-2 mt-1">
                                {message.message}
                              </div>
                            </div>
                            <div>
                              <span className="text-sm text-gray-400">Response:</span>
                              <div className="text-sm text-cyber-green bg-gray-900/50 rounded p-2 mt-1">
                                {message.response}
                              </div>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </Card>
            </div>

            {/* Bot Commands & QR */}
            <div>
              <Card>
                <div className="p-6">
                  <h3 className="text-lg font-bold text-white mb-4">Available Commands</h3>
                  <div className="space-y-3">
                    <div className="p-3 bg-gray-800/50 rounded-lg">
                      <div className="font-medium text-white">/scan &lt;url&gt;</div>
                      <div className="text-sm text-gray-400">Start vulnerability scan</div>
                    </div>
                    <div className="p-3 bg-gray-800/50 rounded-lg">
                      <div className="font-medium text-white">/status</div>
                      <div className="text-sm text-gray-400">Check plan status and usage</div>
                    </div>
                    <div className="p-3 bg-gray-800/50 rounded-lg">
                      <div className="font-medium text-white">/help</div>
                      <div className="text-sm text-gray-400">Show available commands</div>
                    </div>
                    <div className="p-3 bg-gray-800/50 rounded-lg">
                      <div className="font-medium text-white">/results &lt;id&gt;</div>
                      <div className="text-sm text-gray-400">Get scan results</div>
                    </div>
                  </div>
                </div>
              </Card>

              {/* QR Code Modal */}
              {showQR && selectedBot && (
                <Card className="mt-6" border="green" glow>
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-bold text-white">WhatsApp QR Code</h3>
                      <button
                        onClick={() => setShowQR(false)}
                        className="text-gray-400 hover:text-white"
                      >
                        ×
                      </button>
                    </div>
                    <div className="text-center">
                      <div className="bg-white p-4 rounded-lg inline-block mb-4">
                        <QrCode className="h-32 w-32 text-black" />
                      </div>
                      <p className="text-sm text-gray-400 mb-4">
                        Scan this QR code with WhatsApp to connect the bot
                      </p>
                      <button className="cyber-btn text-sm w-full">
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Regenerate QR
                      </button>
                    </div>
                  </div>
                </Card>
              )}

              <Card className="mt-6" border="gold">
                <div className="p-6">
                  <h3 className="text-lg font-bold text-white mb-4">Bot Usage</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Plan Access</span>
                      <span className="text-cyber-green">✓ Enabled</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Daily Commands</span>
                      <span className="text-white">Unlimited</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Scan via Bot</span>
                      <span className="text-cyber-green">✓ Enabled</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Real-time Notifications</span>
                      <span className="text-cyber-green">✓ Enabled</span>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
