"use strict";(()=>{var e={};e.id=465,e.ids=[465],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},26185:(e,s,t)=>{t.r(s),t.d(s,{originalPathname:()=>g,patchFetch:()=>I,requestAsyncStorage:()=>l,routeModule:()=>d,serverHooks:()=>m,staticGenerationAsyncStorage:()=>p});var r={};t.r(r),t.d(r,{GET:()=>c,OPTIONS:()=>i});var a=t(49303),n=t(88716),o=t(60670),u=t(87070);async function c(e,{params:s}){try{let{scanId:e}=s;if(!e)return u.NextResponse.json({success:!1,error:"Scan ID is required",code:"MISSING_SCAN_ID"},{status:400});let t=global.activeScan;if(!t||t.scanId!==e)return u.NextResponse.json({success:!1,error:"Scan not found",code:"SCAN_NOT_FOUND"},{status:404});if("started"===t.status||"running"===t.status)return u.NextResponse.json({success:!0,data:{scanId:t.scanId,status:t.status,target:t.target,progress:t.progress||0,startTime:t.startTime,scanTypes:t.scanTypes},message:"Scan is still in progress",timestamp:new Date().toISOString()});if("completed"===t.status)return u.NextResponse.json({success:!0,data:{scanId:t.scanId,status:t.status,target:t.target,startTime:t.startTime,endTime:t.endTime,scanTypes:t.scanTypes,vulnerabilities:t.vulnerabilities||[],summary:t.summary||{total:0,critical:0,high:0,medium:0,low:0}},message:"Scan completed successfully",timestamp:new Date().toISOString()});if("failed"===t.status)return u.NextResponse.json({success:!0,data:{scanId:t.scanId,status:t.status,target:t.target,startTime:t.startTime,endTime:t.endTime,error:t.error||"Scan failed due to unknown error"},message:"Scan failed",timestamp:new Date().toISOString()});return u.NextResponse.json({success:!1,error:"Invalid scan status",code:"INVALID_STATUS"},{status:500})}catch(e){return console.error("Get scan results error:",e),u.NextResponse.json({success:!1,error:"Internal server error",code:"INTERNAL_ERROR"},{status:500})}}async function i(){return new u.NextResponse(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}let d=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/scan/[scanId]/results/route",pathname:"/api/scan/[scanId]/results",filename:"route",bundlePath:"app/api/scan/[scanId]/results/route"},resolvedPagePath:"D:\\Users\\Downloads\\kodeXGuard\\app\\api\\scan\\[scanId]\\results\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:l,staticGenerationAsyncStorage:p,serverHooks:m}=d,g="/api/scan/[scanId]/results/route";function I(){return(0,o.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:p})}}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[216,592],()=>t(26185));module.exports=r})();