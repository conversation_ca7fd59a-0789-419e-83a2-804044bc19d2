(()=>{var e={};e.id=938,e.ids=[938],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},74134:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>l.a,__next_app__:()=>h,originalPathname:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>o}),a(57322),a(30829),a(35866);var s=a(23191),r=a(88716),i=a(37922),l=a.n(i),n=a(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(t,c);let o=["",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,57322)),"D:\\Users\\Downloads\\kodeXGuard\\app\\settings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,35866,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\Users\\Downloads\\kodeXGuard\\app\\settings\\page.tsx"],x="/settings/page",h={require:a,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},952:(e,t,a)=>{Promise.resolve().then(a.bind(a,80942))},80942:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m});var s=a(10326),r=a(17577),i=a(60463),l=a(2262),n=a(88378),c=a(6507),o=a(58038),d=a(9015),x=a(21405),h=a(31215);function m(){let[e]=(0,r.useState)({username:"admin",avatar:"",role:"super_admin",plan:"cybersecurity"}),[t,a]=(0,r.useState)("general"),[m,p]=(0,r.useState)({general:{language:"id",timezone:"Asia/Jakarta",theme:"dark",autoSave:!0},notifications:{emailNotifications:!0,pushNotifications:!0,scanAlerts:!0,securityAlerts:!0,weeklyReport:!1,marketingEmails:!1},security:{twoFactorAuth:!1,sessionTimeout:30,loginAlerts:!0,apiKeyExpiry:365,passwordExpiry:90},privacy:{profileVisibility:"private",activityTracking:!0,dataSharing:!1,analyticsOptOut:!1}}),[u,f]=(0,r.useState)(!1),[g,y]=(0,r.useState)(!1),b=(e,t,a)=>{p(s=>({...s,[e]:{...s[e],[t]:a}}))},j=async()=>{y(!0),await new Promise(e=>setTimeout(e,1e3)),y(!1)},v=[{id:"general",name:"General",icon:n.Z},{id:"notifications",name:"Notifications",icon:c.Z},{id:"security",name:"Security",icon:o.Z},{id:"privacy",name:"Privacy",icon:d.Z}];return s.jsx(i.Z,{user:e,title:"Settings",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[s.jsx(n.Z,{className:"h-8 w-8 text-cyber-green"}),s.jsx("h1",{className:"text-3xl font-bold font-cyber text-white",children:s.jsx("span",{className:"cyber-text",children:"Settings"})})]}),s.jsx("p",{className:"text-gray-400 max-w-3xl",children:"Kelola pengaturan akun, notifikasi, keamanan, dan privasi untuk pengalaman KodeXGuard yang optimal."})]}),s.jsx("div",{className:"mb-8",children:s.jsx("div",{className:"flex space-x-1 bg-gray-800/50 p-1 rounded-lg",children:v.map(e=>{let r=e.icon;return(0,s.jsxs)("button",{onClick:()=>a(e.id),className:`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${t===e.id?"bg-cyber-green text-black":"text-gray-300 hover:text-white hover:bg-gray-700/50"}`,children:[s.jsx(r,{className:"h-4 w-4"}),s.jsx("span",{children:e.name})]},e.id)})})}),(0,s.jsxs)("div",{className:"grid lg:grid-cols-4 gap-8",children:[(0,s.jsxs)("div",{className:"lg:col-span-3",children:["general"===t&&s.jsx(l.Zb,{border:"green",glow:!0,children:(0,s.jsxs)("div",{className:"p-6",children:[s.jsx("h2",{className:"text-xl font-bold text-white mb-6",children:"General Settings"}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Language"}),(0,s.jsxs)("select",{value:m.general.language,onChange:e=>b("general","language",e.target.value),className:"cyber-input w-full",children:[s.jsx("option",{value:"id",children:"Bahasa Indonesia"}),s.jsx("option",{value:"en",children:"English"})]})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Timezone"}),(0,s.jsxs)("select",{value:m.general.timezone,onChange:e=>b("general","timezone",e.target.value),className:"cyber-input w-full",children:[s.jsx("option",{value:"Asia/Jakarta",children:"Asia/Jakarta (WIB)"}),s.jsx("option",{value:"Asia/Makassar",children:"Asia/Makassar (WITA)"}),s.jsx("option",{value:"Asia/Jayapura",children:"Asia/Jayapura (WIT)"}),s.jsx("option",{value:"UTC",children:"UTC"})]})]})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Theme"}),s.jsx("div",{className:"flex space-x-4",children:["dark","light","auto"].map(e=>(0,s.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[s.jsx("input",{type:"radio",name:"theme",value:e,checked:m.general.theme===e,onChange:e=>b("general","theme",e.target.value),className:"text-cyber-green"}),s.jsx("span",{className:"text-gray-300 capitalize",children:e})]},e))})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"text-sm font-medium text-white",children:"Auto Save"}),s.jsx("p",{className:"text-xs text-gray-400",children:"Automatically save your work"})]}),(0,s.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[s.jsx("input",{type:"checkbox",checked:m.general.autoSave,onChange:e=>b("general","autoSave",e.target.checked),className:"sr-only peer"}),s.jsx("div",{className:"w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyber-green"})]})]})]})]})}),"notifications"===t&&s.jsx(l.Zb,{border:"blue",glow:!0,children:(0,s.jsxs)("div",{className:"p-6",children:[s.jsx("h2",{className:"text-xl font-bold text-white mb-6",children:"Notification Settings"}),s.jsx("div",{className:"space-y-6",children:Object.entries(m.notifications).map(([e,t])=>(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"text-sm font-medium text-white capitalize",children:e.replace(/([A-Z])/g," $1").trim()}),(0,s.jsxs)("p",{className:"text-xs text-gray-400",children:["emailNotifications"===e&&"Receive notifications via email","pushNotifications"===e&&"Receive push notifications in browser","scanAlerts"===e&&"Get notified when scans complete","securityAlerts"===e&&"Important security notifications","weeklyReport"===e&&"Weekly activity summary","marketingEmails"===e&&"Product updates and promotions"]})]}),(0,s.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[s.jsx("input",{type:"checkbox",checked:t,onChange:t=>b("notifications",e,t.target.checked),className:"sr-only peer"}),s.jsx("div",{className:"w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyber-green"})]})]},e))})]})}),"security"===t&&s.jsx(l.Zb,{border:"red",glow:!0,children:(0,s.jsxs)("div",{className:"p-6",children:[s.jsx("h2",{className:"text-xl font-bold text-white mb-6",children:"Security Settings"}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"text-sm font-medium text-white",children:"Two-Factor Authentication"}),s.jsx("p",{className:"text-xs text-gray-400",children:"Add an extra layer of security"})]}),(0,s.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[s.jsx("input",{type:"checkbox",checked:m.security.twoFactorAuth,onChange:e=>b("security","twoFactorAuth",e.target.checked),className:"sr-only peer"}),s.jsx("div",{className:"w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyber-green"})]})]}),(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Session Timeout (minutes)"}),s.jsx("input",{type:"number",value:m.security.sessionTimeout,onChange:e=>b("security","sessionTimeout",parseInt(e.target.value)),className:"cyber-input w-full",min:"5",max:"480"})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"API Key Expiry (days)"}),s.jsx("input",{type:"number",value:m.security.apiKeyExpiry,onChange:e=>b("security","apiKeyExpiry",parseInt(e.target.value)),className:"cyber-input w-full",min:"30",max:"365"})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"text-sm font-medium text-white",children:"Login Alerts"}),s.jsx("p",{className:"text-xs text-gray-400",children:"Get notified of new login attempts"})]}),(0,s.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[s.jsx("input",{type:"checkbox",checked:m.security.loginAlerts,onChange:e=>b("security","loginAlerts",e.target.checked),className:"sr-only peer"}),s.jsx("div",{className:"w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyber-green"})]})]})]})]})}),"privacy"===t&&s.jsx(l.Zb,{border:"purple",glow:!0,children:(0,s.jsxs)("div",{className:"p-6",children:[s.jsx("h2",{className:"text-xl font-bold text-white mb-6",children:"Privacy Settings"}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Profile Visibility"}),(0,s.jsxs)("select",{value:m.privacy.profileVisibility,onChange:e=>b("privacy","profileVisibility",e.target.value),className:"cyber-input w-full",children:[s.jsx("option",{value:"public",children:"Public"}),s.jsx("option",{value:"private",children:"Private"}),s.jsx("option",{value:"friends",children:"Friends Only"})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"text-sm font-medium text-white",children:"Activity Tracking"}),s.jsx("p",{className:"text-xs text-gray-400",children:"Allow tracking for analytics"})]}),(0,s.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[s.jsx("input",{type:"checkbox",checked:m.privacy.activityTracking,onChange:e=>b("privacy","activityTracking",e.target.checked),className:"sr-only peer"}),s.jsx("div",{className:"w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyber-green"})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"text-sm font-medium text-white",children:"Data Sharing"}),s.jsx("p",{className:"text-xs text-gray-400",children:"Share anonymized data for research"})]}),(0,s.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[s.jsx("input",{type:"checkbox",checked:m.privacy.dataSharing,onChange:e=>b("privacy","dataSharing",e.target.checked),className:"sr-only peer"}),s.jsx("div",{className:"w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyber-green"})]})]})]})]})})]}),(0,s.jsxs)("div",{children:[s.jsx(l.Zb,{children:(0,s.jsxs)("div",{className:"p-6",children:[s.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Quick Actions"}),(0,s.jsxs)("div",{className:"space-y-3",children:[s.jsx("button",{onClick:j,disabled:g,className:"cyber-btn-primary w-full disabled:opacity-50",children:g?(0,s.jsxs)(s.Fragment,{children:[s.jsx(x.Z,{className:"h-4 w-4 mr-2 animate-spin"}),"Saving..."]}):(0,s.jsxs)(s.Fragment,{children:[s.jsx(h.Z,{className:"h-4 w-4 mr-2"}),"Save Settings"]})}),(0,s.jsxs)("button",{onClick:()=>{p({general:{language:"id",timezone:"Asia/Jakarta",theme:"dark",autoSave:!0},notifications:{emailNotifications:!0,pushNotifications:!0,scanAlerts:!0,securityAlerts:!0,weeklyReport:!1,marketingEmails:!1},security:{twoFactorAuth:!1,sessionTimeout:30,loginAlerts:!0,apiKeyExpiry:365,passwordExpiry:90},privacy:{profileVisibility:"private",activityTracking:!0,dataSharing:!1,analyticsOptOut:!1}})},className:"cyber-btn w-full",children:[s.jsx(x.Z,{className:"h-4 w-4 mr-2"}),"Reset to Default"]})]})]})}),s.jsx(l.Zb,{className:"mt-6",children:(0,s.jsxs)("div",{className:"p-6",children:[s.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Account Info"}),(0,s.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-400",children:"Plan:"}),s.jsx("span",{className:"text-cyber-green font-semibold",children:e.plan.toUpperCase()})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-400",children:"Role:"}),s.jsx("span",{className:"text-white",children:e.role.replace("_"," ").toUpperCase()})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-400",children:"Status:"}),s.jsx("span",{className:"text-green-400",children:"Active"})]})]})]})})]})]})]})})}},57322:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(68570).createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\settings\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[216,592],()=>a(74134));module.exports=s})();