"use strict";(()=>{var e={};e.id=324,e.ids=[324],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3062:(e,t,s)=>{s.r(t),s.d(t,{originalPathname:()=>g,patchFetch:()=>h,requestAsyncStorage:()=>p,routeModule:()=>l,serverHooks:()=>m,staticGenerationAsyncStorage:()=>d});var r={};s.r(r),s.d(r,{DELETE:()=>c,GET:()=>u});var n=s(49303),a=s(88716),i=s(60670),o=s(87070);async function u(e){try{let t=e.headers.get("authorization"),s=t?.replace("Bearer ","");if(!s)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!s.startsWith("kxg_"))return o.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let r=[{id:"scan_001",target:"https://example.com",scanType:["sqli","xss","csrf"],status:"completed",vulnerabilities:{critical:2,high:5,medium:8,low:12,info:3},startTime:new Date(Date.now()-36e5).toISOString(),endTime:new Date(Date.now()-33e5).toISOString(),duration:300,progress:100},{id:"scan_002",target:"https://testsite.com",scanType:["port_scan","ssl_check"],status:"completed",vulnerabilities:{critical:0,high:1,medium:3,low:5,info:2},startTime:new Date(Date.now()-72e5).toISOString(),endTime:new Date(Date.now()-69e5).toISOString(),duration:180,progress:100},{id:"scan_003",target:"https://webapp.local",scanType:["sqli","xss","lfi","rfi"],status:"running",vulnerabilities:{critical:1,high:2,medium:4,low:6,info:1},startTime:new Date(Date.now()-18e5).toISOString(),duration:1800,progress:65},{id:"scan_004",target:"https://api.example.com",scanType:["api_security","auth_bypass"],status:"failed",vulnerabilities:{critical:0,high:0,medium:0,low:0,info:0},startTime:new Date(Date.now()-108e5).toISOString(),endTime:new Date(Date.now()-1074e4).toISOString(),duration:60,progress:15},{id:"scan_005",target:"https://mobile-app.com",scanType:["mobile_security","ssl_check"],status:"queued",vulnerabilities:{critical:0,high:0,medium:0,low:0,info:0},startTime:new Date().toISOString(),progress:0}];return r.sort((e,t)=>new Date(t.startTime).getTime()-new Date(e.startTime).getTime()),o.NextResponse.json({success:!0,data:r,meta:{total:r.length,completed:r.filter(e=>"completed"===e.status).length,running:r.filter(e=>"running"===e.status).length,failed:r.filter(e=>"failed"===e.status).length,queued:r.filter(e=>"queued"===e.status).length},timestamp:new Date().toISOString()})}catch(e){return console.error("Scanner history API error:",e),o.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}async function c(e){try{let t=e.headers.get("authorization"),s=t?.replace("Bearer ","");if(!s)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!s.startsWith("kxg_"))return o.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let{scanId:r}=await e.json();if(!r)return o.NextResponse.json({success:!1,error:"Scan ID is required"},{status:400});return o.NextResponse.json({success:!0,message:`Scan ${r} deleted successfully`,timestamp:new Date().toISOString()})}catch(e){return console.error("Delete scan error:",e),o.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let l=new n.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/scanner/history/route",pathname:"/api/scanner/history",filename:"route",bundlePath:"app/api/scanner/history/route"},resolvedPagePath:"D:\\Users\\Downloads\\kodeXGuard\\app\\api\\scanner\\history\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:p,staticGenerationAsyncStorage:d,serverHooks:m}=l,g="/api/scanner/history/route";function h(){return(0,i.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:d})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[216,592],()=>s(3062));module.exports=r})();