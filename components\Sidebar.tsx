'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { 
  LayoutDashboard,
  Search,
  Shield,
  FileSearch,
  Database,
  Wrench,
  Bot,
  Code,
  Trophy,
  User,
  CreditCard,
  BookOpen,
  Target,
  ChevronLeft,
  ChevronRight,
  Menu,
  X,
  Crown,
  Zap,
  Settings,
  LogOut,
  Bell,
  Star
} from 'lucide-react'

interface SidebarProps {
  user: {
    username: string
    avatar?: string
    role: string
    plan: string
  }
}

interface MenuItem {
  id: string
  name: string
  href: string
  icon: any
  badge?: string
  premium?: boolean
  adminOnly?: boolean
}

export default function Sidebar({ user }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isMobileOpen, setIsMobileOpen] = useState(false)
  const pathname = usePathname()

  const menuItems: MenuItem[] = [
    {
      id: 'dashboard',
      name: 'Dashboard',
      href: '/dashboard',
      icon: LayoutDashboard
    },
    {
      id: 'osint',
      name: 'OSINT Investigator',
      href: '/osint',
      icon: Search,
      badge: 'HOT'
    },
    {
      id: 'scanner',
      name: 'Vulnerability Scanner',
      href: '/scanner',
      icon: Shield,
      premium: true
    },
    {
      id: 'file-analyzer',
      name: 'File Analyzer',
      href: '/file-analyzer',
      icon: FileSearch
    },
    {
      id: 'cve',
      name: 'CVE Intelligence',
      href: '/cve',
      icon: Database,
      badge: 'NEW'
    },
    {
      id: 'dorking',
      name: 'Google Dorking',
      href: '/dorking',
      icon: Target
    },
    {
      id: 'tools',
      name: 'Security Tools',
      href: '/tools',
      icon: Wrench
    },
    {
      id: 'bot',
      name: 'Bot Center',
      href: '/bot',
      icon: Bot,
      premium: true,
      adminOnly: true
    },
    {
      id: 'playground',
      name: 'API Playground',
      href: '/playground',
      icon: Code,
      premium: true
    },
    {
      id: 'leaderboard',
      name: 'Leaderboard',
      href: '/leaderboard',
      icon: Trophy
    },
    {
      id: 'profile',
      name: 'Profile & API Keys',
      href: '/profile',
      icon: User
    },
    {
      id: 'plan',
      name: 'Plans & Billing',
      href: '/plan',
      icon: CreditCard
    },
    {
      id: 'docs',
      name: 'Documentation',
      href: '/docs',
      icon: BookOpen
    }
  ]

  // Filter menu items based on user role and plan
  const filteredMenuItems = menuItems.filter(item => {
    if (item.adminOnly && !['super_admin', 'admin'].includes(user.role)) {
      return false
    }
    if (item.premium && user.plan === 'gratis') {
      return false
    }
    return true
  })

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'cybersecurity': return 'text-red-400 bg-red-900/20'
      case 'bughunter': return 'text-purple-400 bg-purple-900/20'
      case 'hobby': return 'text-green-400 bg-green-900/20'
      case 'pelajar': return 'text-blue-400 bg-blue-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  const getPlanIcon = (plan: string) => {
    switch (plan) {
      case 'cybersecurity': return Crown
      case 'bughunter': return Zap
      case 'hobby': return Star
      default: return User
    }
  }

  // Close mobile sidebar when route changes
  useEffect(() => {
    setIsMobileOpen(false)
  }, [pathname])

  return (
    <>
      {/* Mobile Overlay */}
      {isMobileOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={() => setIsMobileOpen(false)}
        />
      )}

      {/* Mobile Menu Button */}
      <button
        onClick={() => setIsMobileOpen(true)}
        className="fixed top-4 left-4 z-50 lg:hidden bg-gray-800 text-white p-2 rounded-lg border border-gray-700 hover:bg-gray-700 transition-colors"
      >
        <Menu className="h-5 w-5" />
      </button>

      {/* Sidebar */}
      <div className={`
        fixed lg:relative top-0 left-0 h-full lg:h-screen bg-gray-900/95 backdrop-blur-sm border-r border-gray-800 z-50 lg:z-auto
        transition-all duration-300 ease-in-out flex-shrink-0
        ${isCollapsed ? 'w-16' : 'w-64'}
        ${isMobileOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-800">
          {!isCollapsed && (
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-cyber-green rounded-lg flex items-center justify-center">
                <Shield className="h-5 w-5 text-black" />
              </div>
              <span className="font-bold text-white font-cyber">KodeXGuard</span>
            </div>
          )}
          
          <div className="flex items-center space-x-2">
            {/* Mobile Close Button */}
            <button
              onClick={() => setIsMobileOpen(false)}
              className="lg:hidden text-gray-400 hover:text-white transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
            
            {/* Desktop Collapse Button */}
            <button
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="hidden lg:block text-gray-400 hover:text-white transition-colors"
            >
              {isCollapsed ? (
                <ChevronRight className="h-5 w-5" />
              ) : (
                <ChevronLeft className="h-5 w-5" />
              )}
            </button>
          </div>
        </div>

        {/* User Profile */}
        <div className="p-4 border-b border-gray-800">
          <div className="flex items-center space-x-3">
            {user.avatar ? (
              <img src={user.avatar} alt={user.username} className="w-10 h-10 rounded-full" />
            ) : (
              <div className="w-10 h-10 rounded-full bg-cyber-green/20 flex items-center justify-center">
                <span className="text-cyber-green font-bold">
                  {user.username.charAt(0).toUpperCase()}
                </span>
              </div>
            )}
            
            {!isCollapsed && (
              <div className="flex-1 min-w-0">
                <div className="font-medium text-white truncate">{user.username}</div>
                <div className="flex items-center space-x-1">
                  {(() => {
                    const PlanIcon = getPlanIcon(user.plan)
                    return <PlanIcon className="h-3 w-3 text-nusantara-gold" />
                  })()}
                  <span className={`text-xs px-2 py-1 rounded-full font-semibold ${getPlanColor(user.plan)}`}>
                    {user.plan.toUpperCase()}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Navigation Menu */}
        <nav className="flex-1 overflow-y-auto py-4">
          <div className="space-y-1 px-2">
            {filteredMenuItems.map((item) => {
              const Icon = item.icon
              const isActive = pathname === item.href
              
              return (
                <Link
                  key={item.id}
                  href={item.href}
                  className={`
                    group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200
                    ${isActive 
                      ? 'bg-cyber-green text-black' 
                      : 'text-gray-300 hover:text-white hover:bg-gray-800/50'
                    }
                    ${isCollapsed ? 'justify-center' : 'justify-start'}
                  `}
                >
                  <Icon className={`h-5 w-5 flex-shrink-0 ${isCollapsed ? '' : 'mr-3'}`} />
                  
                  {!isCollapsed && (
                    <>
                      <span className="flex-1">{item.name}</span>
                      
                      {/* Badges */}
                      <div className="flex items-center space-x-1">
                        {item.badge && (
                          <span className={`
                            px-2 py-1 text-xs font-bold rounded-full
                            ${item.badge === 'HOT' ? 'bg-red-500 text-white' : 'bg-blue-500 text-white'}
                          `}>
                            {item.badge}
                          </span>
                        )}
                        
                        {item.premium && (
                          <Crown className="h-3 w-3 text-nusantara-gold" />
                        )}
                        
                        {item.adminOnly && (
                          <Settings className="h-3 w-3 text-orange-400" />
                        )}
                      </div>
                    </>
                  )}
                  
                  {/* Tooltip for collapsed state */}
                  {isCollapsed && (
                    <div className="absolute left-16 bg-gray-800 text-white px-2 py-1 rounded-lg text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                      {item.name}
                      {item.badge && (
                        <span className={`ml-2 px-1 py-0.5 text-xs rounded ${
                          item.badge === 'HOT' ? 'bg-red-500' : 'bg-blue-500'
                        }`}>
                          {item.badge}
                        </span>
                      )}
                    </div>
                  )}
                </Link>
              )
            })}
          </div>
        </nav>

        {/* Footer Actions */}
        <div className="border-t border-gray-800 p-4">
          <div className="space-y-2">
            {/* Notifications */}
            <button className={`
              w-full flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors
              ${isCollapsed ? 'justify-center' : 'justify-start'}
            `}>
              <Bell className={`h-5 w-5 ${isCollapsed ? '' : 'mr-3'}`} />
              {!isCollapsed && <span>Notifications</span>}
              {!isCollapsed && (
                <span className="ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full">3</span>
              )}
            </button>

            {/* Settings */}
            <button className={`
              w-full flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors
              ${isCollapsed ? 'justify-center' : 'justify-start'}
            `}>
              <Settings className={`h-5 w-5 ${isCollapsed ? '' : 'mr-3'}`} />
              {!isCollapsed && <span>Settings</span>}
            </button>

            {/* Logout */}
            <button className={`
              w-full flex items-center px-3 py-2 text-sm font-medium text-red-400 hover:text-red-300 hover:bg-red-900/20 rounded-lg transition-colors
              ${isCollapsed ? 'justify-center' : 'justify-start'}
            `}>
              <LogOut className={`h-5 w-5 ${isCollapsed ? '' : 'mr-3'}`} />
              {!isCollapsed && <span>Logout</span>}
            </button>
          </div>
        </div>
      </div>
    </>
  )
}
