(()=>{var e={};e.id=3,e.ids=[3],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},6522:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>p,tree:()=>c}),t(66136),t(30829),t(35866);var a=t(23191),r=t(88716),n=t(37922),i=t.n(n),l=t(95231),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let c=["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,66136)),"D:\\Users\\Downloads\\kodeXGuard\\app\\admin\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\Users\\Downloads\\kodeXGuard\\app\\admin\\page.tsx"],x="/admin/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},95990:(e,s,t)=>{Promise.resolve().then(t.bind(t,88381))},88381:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var a=t(10326),r=t(17577),n=t(60463),i=t(2262),l=t(66697),d=t(24061),c=t(93892),o=t(88319),x=t(58038),m=t(59819),p=t(26092);let h=function(){let[e,s]=(0,r.useState)("overview"),t=[{id:"overview",name:"Overview",icon:l.Z},{id:"users",name:"User Management",icon:d.Z},{id:"system",name:"System Health",icon:c.Z},{id:"logs",name:"System Logs",icon:o.Z}];return a.jsx(n.Z,{user:{username:"admin",avatar:"",role:"super_admin",plan:"cybersecurity"},title:"Admin Panel",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[a.jsx(x.Z,{className:"h-8 w-8 text-cyber-green"}),a.jsx("h1",{className:"text-3xl font-bold font-cyber text-white",children:a.jsx("span",{className:"cyber-text",children:"Admin Panel"})})]}),a.jsx("p",{className:"text-gray-400 max-w-3xl",children:"Kelola pengguna, monitor sistem, dan administrasi platform KodeXGuard."})]}),a.jsx("div",{className:"mb-8",children:a.jsx("div",{className:"flex space-x-1 bg-gray-800/50 p-1 rounded-lg",children:t.map(t=>{let r=t.icon;return(0,a.jsxs)("button",{onClick:()=>s(t.id),className:`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${e===t.id?"bg-cyber-green text-black":"text-gray-300 hover:text-white hover:bg-gray-700/50"}`,children:[a.jsx(r,{className:"h-4 w-4"}),a.jsx("span",{children:t.name})]},t.id)})})}),"overview"===e&&a.jsx("div",{className:"space-y-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[a.jsx(i.Zb,{border:"blue",glow:!0,children:a.jsx("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-gray-400 text-sm",children:"Total Users"}),a.jsx("p",{className:"text-2xl font-bold text-white",children:"15,847"})]}),a.jsx(d.Z,{className:"h-8 w-8 text-blue-400"})]})})}),a.jsx(i.Zb,{border:"green",glow:!0,children:a.jsx("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-gray-400 text-sm",children:"Active Users"}),a.jsx("p",{className:"text-2xl font-bold text-white",children:"12,456"})]}),a.jsx(m.Z,{className:"h-8 w-8 text-green-400"})]})})}),a.jsx(i.Zb,{border:"gold",glow:!0,children:a.jsx("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-gray-400 text-sm",children:"Premium Users"}),a.jsx("p",{className:"text-2xl font-bold text-white",children:"3,421"})]}),a.jsx(p.Z,{className:"h-8 w-8 text-nusantara-gold"})]})})}),a.jsx(i.Zb,{border:"blue",glow:!0,children:a.jsx("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-gray-400 text-sm",children:"Today Scans"}),a.jsx("p",{className:"text-2xl font-bold text-white",children:"1,234"})]}),a.jsx(l.Z,{className:"h-8 w-8 text-purple-400"})]})})})]})}),"overview"!==e&&a.jsx(i.Zb,{children:(0,a.jsxs)("div",{className:"p-8 text-center",children:[a.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:t.find(s=>s.id===e)?.name}),a.jsx("p",{className:"text-gray-400",children:"Content for this tab is coming soon..."})]})})]})})}},66136:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\admin\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[216,592],()=>t(6522));module.exports=a})();