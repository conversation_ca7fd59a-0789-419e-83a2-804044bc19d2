/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(rsc)/./app/dashboard/page.tsx\")), \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(ssr)/./app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNVc2VycyU1QyU1Q0Rvd25sb2FkcyU1QyU1Q2tvZGVYR3VhcmQlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNEpBQStGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va29kZXhndWFyZC8/NWE1NyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFVzZXJzXFxcXERvd25sb2Fkc1xcXFxrb2RlWEd1YXJkXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNVc2VycyU1QyU1Q0Rvd25sb2FkcyU1QyU1Q2tvZGVYR3VhcmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNhcHAtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNVc2VycyU1QyU1Q0Rvd25sb2FkcyU1QyU1Q2tvZGVYR3VhcmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDVXNlcnMlNUMlNUNEb3dubG9hZHMlNUMlNUNrb2RlWEd1YXJkJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1VzZXJzJTVDJTVDRG93bmxvYWRzJTVDJTVDa29kZVhHdWFyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2xheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1VzZXJzJTVDJTVDRG93bmxvYWRzJTVDJTVDa29kZVhHdWFyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q25vdC1mb3VuZC1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDVXNlcnMlNUMlNUNEb3dubG9hZHMlNUMlNUNrb2RlWEd1YXJkJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQWtJO0FBQ2xJO0FBQ0Esb09BQW1JO0FBQ25JO0FBQ0EsME9BQXNJO0FBQ3RJO0FBQ0Esd09BQXFJO0FBQ3JJO0FBQ0Esa1BBQTBJO0FBQzFJO0FBQ0Esc1FBQW9KIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va29kZXhndWFyZC8/ODk4YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFVzZXJzXFxcXERvd25sb2Fkc1xcXFxrb2RlWEd1YXJkXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcYXBwLXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcVXNlcnNcXFxcRG93bmxvYWRzXFxcXGtvZGVYR3VhcmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcVXNlcnNcXFxcRG93bmxvYWRzXFxcXGtvZGVYR3VhcmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcVXNlcnNcXFxcRG93bmxvYWRzXFxcXGtvZGVYR3VhcmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxVc2Vyc1xcXFxEb3dubG9hZHNcXFxca29kZVhHdWFyZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG5vdC1mb3VuZC1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcVXNlcnNcXFxcRG93bmxvYWRzXFxcXGtvZGVYR3VhcmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(ssr)/./components/DashboardLayout.tsx\");\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Card */ \"(ssr)/./components/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Bug,CheckCircle,Clock,Code,FileSearch,Search,Shield,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Bug,CheckCircle,Clock,Code,FileSearch,Search,Shield,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Bug,CheckCircle,Clock,Code,FileSearch,Search,Shield,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Bug,CheckCircle,Clock,Code,FileSearch,Search,Shield,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Bug,CheckCircle,Clock,Code,FileSearch,Search,Shield,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Bug,CheckCircle,Clock,Code,FileSearch,Search,Shield,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Bug,CheckCircle,Clock,Code,FileSearch,Search,Shield,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Bug,CheckCircle,Clock,Code,FileSearch,Search,Shield,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Bug,CheckCircle,Clock,Code,FileSearch,Search,Shield,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Bug,CheckCircle,Clock,Code,FileSearch,Search,Shield,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Bug,CheckCircle,Clock,Code,FileSearch,Search,Shield,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DashboardPage() {\n    const [user] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: \"admin\",\n        avatar: \"\",\n        role: \"super_admin\",\n        plan: \"cybersecurity\"\n    });\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalScans: 0,\n        vulnerabilitiesFound: 0,\n        filesAnalyzed: 0,\n        apiCalls: 0,\n        loading: true\n    });\n    const [recentActivity, setRecentActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            type: \"scan\",\n            title: \"Vulnerability scan completed\",\n            target: \"example.com\",\n            severity: \"high\",\n            time: \"2 minutes ago\"\n        },\n        {\n            id: 2,\n            type: \"osint\",\n            title: \"OSINT search completed\",\n            target: \"<EMAIL>\",\n            severity: \"medium\",\n            time: \"15 minutes ago\"\n        },\n        {\n            id: 3,\n            type: \"file\",\n            title: \"File analysis completed\",\n            target: \"suspicious.php\",\n            severity: \"critical\",\n            time: \"1 hour ago\"\n        }\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simulate loading stats\n        setTimeout(()=>{\n            setStats({\n                totalScans: 1247,\n                vulnerabilitiesFound: 89,\n                filesAnalyzed: 456,\n                apiCalls: 12890,\n                loading: false\n            });\n        }, 1000);\n    }, []);\n    const features = [\n        {\n            title: \"OSINT Investigator\",\n            description: \"Investigasi mendalam dengan pencarian NIK, NPWP, nomor HP, email, dan domain\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: \"green\",\n            href: \"/osint\"\n        },\n        {\n            title: \"Vulnerability Scanner\",\n            description: \"Deteksi SQLi, XSS, LFI, RCE dengan scoring CVSS dan mapping CVE\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: \"red\",\n            href: \"/scanner\"\n        },\n        {\n            title: \"File Analyzer\",\n            description: \"Analisis webshell, malware, dan deteksi secret dalam berbagai format file\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: \"blue\",\n            href: \"/file-analyzer\"\n        },\n        {\n            title: \"CVE Intelligence\",\n            description: \"Database CVE terupdate dengan Google dorking dan payload generator\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            color: \"purple\",\n            href: \"/cve\"\n        },\n        {\n            title: \"Bot Integration\",\n            description: \"WhatsApp & Telegram bot untuk monitoring dan notifikasi real-time\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            color: \"gold\",\n            href: \"/bot\"\n        },\n        {\n            title: \"API Playground\",\n            description: \"Testing environment dengan Swagger docs dan request builder\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            color: \"green\",\n            href: \"/playground\"\n        }\n    ];\n    const getSeverityColor = (severity)=>{\n        switch(severity){\n            case \"critical\":\n                return \"text-red-400\";\n            case \"high\":\n                return \"text-orange-400\";\n            case \"medium\":\n                return \"text-yellow-400\";\n            case \"low\":\n                return \"text-blue-400\";\n            default:\n                return \"text-gray-400\";\n        }\n    };\n    const getSeverityIcon = (severity)=>{\n        switch(severity){\n            case \"critical\":\n                return _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\n            case \"high\":\n                return _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\n            case \"medium\":\n                return _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"];\n            default:\n                return _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"];\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        user: user,\n        title: \"Dashboard\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold font-cyber text-white mb-2\",\n                            children: [\n                                \"Welcome back, \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"cyber-text\",\n                                    children: user.username\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400\",\n                            children: \"Monitor your cybersecurity activities and manage your tools from here.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.AlertCard, {\n                        type: \"success\",\n                        title: \"System Status: Online\",\n                        message: \"All systems are operational. Last updated: 2 minutes ago\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.StatCard, {\n                            title: \"Total Scans\",\n                            value: stats.totalScans,\n                            icon: _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                            color: \"green\",\n                            trend: {\n                                value: 12,\n                                isPositive: true\n                            },\n                            loading: stats.loading\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.StatCard, {\n                            title: \"Vulnerabilities Found\",\n                            value: stats.vulnerabilitiesFound,\n                            icon: _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                            color: \"red\",\n                            trend: {\n                                value: 8,\n                                isPositive: true\n                            },\n                            loading: stats.loading\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.StatCard, {\n                            title: \"Files Analyzed\",\n                            value: stats.filesAnalyzed,\n                            icon: _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                            color: \"blue\",\n                            trend: {\n                                value: 15,\n                                isPositive: true\n                            },\n                            loading: stats.loading\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.StatCard, {\n                            title: \"API Calls\",\n                            value: stats.apiCalls,\n                            icon: _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                            color: \"purple\",\n                            trend: {\n                                value: 23,\n                                isPositive: true\n                            },\n                            loading: stats.loading\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold text-white mb-6\",\n                                    children: \"Quick Access\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-2 gap-6\",\n                                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.FeatureCard, {\n                                            title: feature.title,\n                                            description: feature.description,\n                                            icon: feature.icon,\n                                            color: feature.color,\n                                            onClick: ()=>window.location.href = feature.href\n                                        }, index, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold text-white mb-6\",\n                                    children: \"Recent Activity\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: recentActivity.map((activity)=>{\n                                                    const SeverityIcon = getSeverityIcon(activity.severity);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-3 p-3 rounded-lg bg-gray-800/50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SeverityIcon, {\n                                                                className: `h-5 w-5 mt-0.5 ${getSeverityColor(activity.severity)}`\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium text-white\",\n                                                                        children: activity.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 229,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-400 truncate\",\n                                                                        children: activity.target\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 230,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: activity.time\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 231,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `px-2 py-1 text-xs font-semibold rounded-full ${activity.severity === \"critical\" ? \"bg-red-900/50 text-red-400\" : activity.severity === \"high\" ? \"bg-orange-900/50 text-orange-400\" : activity.severity === \"medium\" ? \"bg-yellow-900/50 text-yellow-400\" : \"bg-blue-900/50 text-blue-400\"}`,\n                                                                children: activity.severity\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, activity.id, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 25\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 pt-4 border-t border-gray-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"w-full text-sm text-cyber-green hover:text-cyber-blue transition-colors\",\n                                                    children: \"View All Activity →\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"mt-6\",\n                                    border: \"gold\",\n                                    glow: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white mb-2\",\n                                                children: \"Current Plan\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl font-bold nusantara-gold\",\n                                                        children: user.plan.charAt(0).toUpperCase() + user.plan.slice(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-3 py-1 bg-nusantara-gold/20 text-nusantara-gold rounded-full text-sm font-semibold\",\n                                                        children: \"Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 text-sm text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Daily Scans\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white\",\n                                                                children: \"Unlimited\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"API Calls\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white\",\n                                                                children: \"100,000/day\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"File Upload\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white\",\n                                                                children: \"1GB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-full mt-4 cyber-btn text-sm\",\n                                                children: \"Manage Plan\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 143,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Card.tsx":
/*!*****************************!*\
  !*** ./components/Card.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlertCard: () => (/* binding */ AlertCard),\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   FeatureCard: () => (/* binding */ FeatureCard),\n/* harmony export */   LoadingCard: () => (/* binding */ LoadingCard),\n/* harmony export */   StatCard: () => (/* binding */ StatCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ Card,StatCard,FeatureCard,LoadingCard,AlertCard auto */ \nfunction Card({ children, className = \"\", hover = false, glow = false, border = \"default\" }) {\n    const borderColors = {\n        default: \"border-gray-700\",\n        green: \"border-cyber-green\",\n        blue: \"border-cyber-blue\",\n        red: \"border-cyber-red\",\n        gold: \"border-nusantara-gold\"\n    };\n    const glowColors = {\n        default: \"\",\n        green: \"shadow-lg shadow-cyber-green/20\",\n        blue: \"shadow-lg shadow-cyber-blue/20\",\n        red: \"shadow-lg shadow-cyber-red/20\",\n        gold: \"shadow-lg shadow-nusantara-gold/20\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `\n        bg-gray-900/50 backdrop-blur-sm rounded-lg border ${borderColors[border]}\n        ${hover ? \"hover:border-cyber-green hover:shadow-lg hover:shadow-cyber-green/20 transition-all duration-300 cursor-pointer\" : \"\"}\n        ${glow ? glowColors[border] : \"\"}\n        ${className}\n      `,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\nfunction StatCard({ title, value, icon: Icon, color = \"green\", trend, loading = false }) {\n    const colors = {\n        green: \"text-cyber-green\",\n        blue: \"text-cyber-blue\",\n        red: \"text-cyber-red\",\n        purple: \"text-cyber-purple\",\n        gold: \"text-nusantara-gold\"\n    };\n    const bgColors = {\n        green: \"bg-cyber-green/10\",\n        blue: \"bg-cyber-blue/10\",\n        red: \"bg-cyber-red/10\",\n        purple: \"bg-cyber-purple/10\",\n        gold: \"bg-nusantara-gold/10\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        border: color,\n        glow: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-400\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-baseline space-x-2\",\n                                children: [\n                                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 w-20 bg-gray-700 animate-pulse rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: `text-2xl font-bold ${colors[color]}`,\n                                        children: typeof value === \"number\" ? value.toLocaleString() : value\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, this),\n                                    trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `text-sm font-medium ${trend.isPositive ? \"text-green-400\" : \"text-red-400\"}`,\n                                        children: [\n                                            trend.isPositive ? \"+\" : \"\",\n                                            trend.value,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `p-3 rounded-lg ${bgColors[color]}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            className: `h-6 w-6 ${colors[color]}`\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\nfunction FeatureCard({ title, description, icon: Icon, color = \"green\", onClick, disabled = false, badge }) {\n    const colors = {\n        green: \"text-cyber-green\",\n        blue: \"text-cyber-blue\",\n        red: \"text-cyber-red\",\n        purple: \"text-cyber-purple\",\n        gold: \"text-nusantara-gold\"\n    };\n    const bgColors = {\n        green: \"bg-cyber-green/10\",\n        blue: \"bg-cyber-blue/10\",\n        red: \"bg-cyber-red/10\",\n        purple: \"bg-cyber-purple/10\",\n        gold: \"bg-nusantara-gold/10\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        hover: !disabled && !!onClick,\n        border: color,\n        className: `relative ${disabled ? \"opacity-50 cursor-not-allowed\" : \"\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 h-full\",\n            onClick: disabled ? undefined : onClick,\n            children: [\n                badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-3 right-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `px-2 py-1 text-xs font-semibold rounded-full ${bgColors[color]} ${colors[color]}`,\n                        children: badge\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `inline-flex p-3 rounded-lg ${bgColors[color]} mb-4`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: `h-6 w-6 ${colors[color]}`\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-white mb-2\",\n                    children: title\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-400 text-sm leading-relaxed\",\n                    children: description\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this),\n                onClick && !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 pt-4 border-t border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-medium ${colors[color]} hover:underline`,\n                        children: \"Mulai Sekarang →\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\nfunction LoadingCard({ className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 animate-pulse\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 bg-gray-700 rounded-lg\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-gray-700 rounded w-3/4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-3 bg-gray-700 rounded w-1/2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                lineNumber: 184,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 183,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 182,\n        columnNumber: 5\n    }, this);\n}\nfunction AlertCard({ type = \"info\", title, message, onClose }) {\n    const styles = {\n        info: {\n            border: \"border-cyber-blue\",\n            bg: \"bg-cyber-blue/10\",\n            text: \"text-cyber-blue\",\n            icon: \"\\uD83D\\uDCA1\"\n        },\n        success: {\n            border: \"border-cyber-green\",\n            bg: \"bg-cyber-green/10\",\n            text: \"text-cyber-green\",\n            icon: \"✅\"\n        },\n        warning: {\n            border: \"border-nusantara-gold\",\n            bg: \"bg-nusantara-gold/10\",\n            text: \"text-nusantara-gold\",\n            icon: \"⚠️\"\n        },\n        error: {\n            border: \"border-cyber-red\",\n            bg: \"bg-cyber-red/10\",\n            text: \"text-cyber-red\",\n            icon: \"❌\"\n        }\n    };\n    const style = styles[type];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `border ${style.border} ${style.bg} rounded-lg p-4`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-lg\",\n                    children: style.icon\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: `font-semibold ${style.text}`,\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300 text-sm mt-1\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, this),\n                onClose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onClose,\n                    className: \"text-gray-400 hover:text-white transition-colors\",\n                    children: \"\\xd7\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 238,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 237,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/DashboardLayout.tsx":
/*!****************************************!*\
  !*** ./components/DashboardLayout.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./components/Sidebar.tsx\");\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Navbar */ \"(ssr)/./components/Navbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction DashboardLayout({ children, user, title, showSearch = true }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-cyber\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                user: user,\n                title: title,\n                showSearch: showSearch,\n                isLandingPage: false\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex pt-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        user: user\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 min-w-0 lg:ml-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Navbar.tsx":
/*!*******************************!*\
  !*** ./components/Navbar.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Crown_Globe_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Crown,Globe,LogOut,Menu,Search,Settings,Shield,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Crown_Globe_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Crown,Globe,LogOut,Menu,Search,Settings,Shield,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Crown_Globe_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Crown,Globe,LogOut,Menu,Search,Settings,Shield,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Crown_Globe_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Crown,Globe,LogOut,Menu,Search,Settings,Shield,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Crown_Globe_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Crown,Globe,LogOut,Menu,Search,Settings,Shield,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Crown_Globe_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Crown,Globe,LogOut,Menu,Search,Settings,Shield,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Crown_Globe_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Crown,Globe,LogOut,Menu,Search,Settings,Shield,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Crown_Globe_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Crown,Globe,LogOut,Menu,Search,Settings,Shield,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Crown_Globe_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Crown,Globe,LogOut,Menu,Search,Settings,Shield,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Crown_Globe_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Crown,Globe,LogOut,Menu,Search,Settings,Shield,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Navbar({ user, showSearch = true, title, isLandingPage = false }) {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isProfileOpen, setIsProfileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    // Main navigation items for landing page only\n    const mainNavItems = [\n        {\n            name: \"Home\",\n            href: \"/\"\n        },\n        {\n            name: \"Features\",\n            href: \"/#features\"\n        },\n        {\n            name: \"Pricing\",\n            href: \"/#pricing\"\n        },\n        {\n            name: \"About\",\n            href: \"/#about\"\n        },\n        {\n            name: \"Contact\",\n            href: \"/#contact\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: `fixed top-0 left-0 right-0 z-40 bg-gray-900/95 backdrop-blur-sm border-b border-gray-800 ${isLandingPage ? \"lg:px-8\" : \"\"}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `flex justify-between items-center h-16 ${isLandingPage ? \"max-w-7xl mx-auto px-4 sm:px-6\" : \"px-4 lg:pl-0\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-8\",\n                        children: [\n                            !isLandingPage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 lg:hidden\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 30\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-cyber-green rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Crown_Globe_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-5 w-5 text-black\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-bold text-white text-xl font-cyber\",\n                                        children: \"KodeXGuard\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, this),\n                            isLandingPage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:flex items-center space-x-8\",\n                                children: mainNavItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: item.href,\n                                        className: \"text-gray-300 hover:text-cyber-green transition-colors duration-200 font-medium\",\n                                        children: item.name\n                                    }, item.name, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this),\n                            !isLandingPage && title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold text-white font-cyber hidden lg:block\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    !isLandingPage && showSearch && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden lg:flex flex-1 max-w-lg mx-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Crown_Globe_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search across all tools...\",\n                                    className: \"w-full pl-10 pr-4 py-2 bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyber-green focus:border-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            isLandingPage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"hidden lg:flex items-center space-x-1 text-gray-300 hover:text-white transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Crown_Globe_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"ID\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this),\n                            !isLandingPage && showSearch && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"lg:hidden text-gray-400 hover:text-white transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Crown_Globe_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this),\n                            !isLandingPage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"relative text-gray-400 hover:text-white transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Crown_Globe_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center\",\n                                        children: \"3\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this),\n                            user ? /* User Profile Dropdown */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsProfileOpen(!isProfileOpen),\n                                        className: \"flex items-center space-x-2 text-gray-300 hover:text-white transition-colors\",\n                                        children: [\n                                            user.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: user.avatar,\n                                                alt: user.username,\n                                                className: \"h-8 w-8 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-8 w-8 bg-cyber-green rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-black font-semibold text-sm\",\n                                                    children: user.username.charAt(0).toUpperCase()\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:block font-medium\",\n                                                children: user.username\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 17\n                                            }, this),\n                                            (user.plan === \"cybersecurity\" || user.plan === \"bughunter\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Crown_Globe_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 text-nusantara-gold\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this),\n                                    isProfileOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute right-0 mt-2 w-48 bg-gray-800 rounded-lg shadow-lg border border-gray-700 py-1 z-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-4 py-2 border-b border-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-white\",\n                                                        children: user.username\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: user.role?.replace(\"_\", \" \").toUpperCase() || \"User\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-cyber-green\",\n                                                        children: [\n                                                            user.plan?.toUpperCase(),\n                                                            \" Plan\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/profile\",\n                                                className: \"flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white\",\n                                                onClick: ()=>setIsProfileOpen(false),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Crown_Globe_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Profile & API Keys\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/settings\",\n                                                className: \"flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white\",\n                                                onClick: ()=>setIsProfileOpen(false),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Crown_Globe_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Settings\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 19\n                                            }, this),\n                                            (user.role === \"super_admin\" || user.role === \"admin\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/admin\",\n                                                className: \"flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white\",\n                                                onClick: ()=>setIsProfileOpen(false),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Crown_Globe_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Admin Panel\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                className: \"my-1 border-gray-700\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center w-full px-4 py-2 text-sm text-red-400 hover:bg-gray-700 text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Crown_Globe_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Logout\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this) : /* Login/Register Buttons */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/login\",\n                                        className: \"cyber-btn\",\n                                        children: \"Login\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/register\",\n                                        className: \"cyber-btn-primary\",\n                                        children: \"Register\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, this),\n                            isLandingPage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsOpen(!isOpen),\n                                className: \"lg:hidden text-gray-400 hover:text-white transition-colors\",\n                                children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Crown_Globe_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 25\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Crown_Globe_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 53\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            isLandingPage && isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden border-t border-gray-800 bg-gray-900/98\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 pt-2 pb-3 space-y-1\",\n                    children: [\n                        mainNavItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: item.href,\n                                className: \"block px-3 py-2 text-gray-300 hover:text-cyber-green hover:bg-gray-800 rounded-md transition-colors\",\n                                onClick: ()=>setIsOpen(false),\n                                children: item.name\n                            }, item.name, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 15\n                            }, this)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-700 pt-2 mt-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"flex items-center px-3 py-2 text-gray-300 hover:text-white transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Crown_Globe_LogOut_Menu_Search_Settings_Shield_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Language: Indonesia\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 13\n                        }, this),\n                        !user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-700 pt-2 mt-2 space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/login\",\n                                    className: \"block px-3 py-2 text-center cyber-btn\",\n                                    onClick: ()=>setIsOpen(false),\n                                    children: \"Login\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/register\",\n                                    className: \"block px-3 py-2 text-center cyber-btn-primary\",\n                                    onClick: ()=>setIsOpen(false),\n                                    children: \"Register\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                lineNumber: 211,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Sidebar.tsx":
/*!********************************!*\
  !*** ./components/Sidebar.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-search.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,BookOpen,Bot,ChevronLeft,ChevronRight,Code,CreditCard,Crown,Database,FileSearch,LayoutDashboard,LogOut,Menu,Search,Settings,Shield,Star,Target,Trophy,User,Wrench,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Sidebar({ user }) {\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobileOpen, setIsMobileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const menuItems = [\n        {\n            id: \"dashboard\",\n            name: \"Dashboard\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            id: \"osint\",\n            name: \"OSINT Investigator\",\n            href: \"/osint\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            badge: \"HOT\"\n        },\n        {\n            id: \"scanner\",\n            name: \"Vulnerability Scanner\",\n            href: \"/scanner\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            premium: true\n        },\n        {\n            id: \"file-analyzer\",\n            name: \"File Analyzer\",\n            href: \"/file-analyzer\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            id: \"cve\",\n            name: \"CVE Intelligence\",\n            href: \"/cve\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            badge: \"NEW\"\n        },\n        {\n            id: \"dorking\",\n            name: \"Google Dorking\",\n            href: \"/dorking\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            id: \"tools\",\n            name: \"Security Tools\",\n            href: \"/tools\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            id: \"bot\",\n            name: \"Bot Center\",\n            href: \"/bot\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            premium: true,\n            adminOnly: true\n        },\n        {\n            id: \"playground\",\n            name: \"API Playground\",\n            href: \"/playground\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            premium: true\n        },\n        {\n            id: \"leaderboard\",\n            name: \"Leaderboard\",\n            href: \"/leaderboard\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            id: \"profile\",\n            name: \"Profile & API Keys\",\n            href: \"/profile\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n        },\n        {\n            id: \"plan\",\n            name: \"Plans & Billing\",\n            href: \"/plan\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n        },\n        {\n            id: \"docs\",\n            name: \"Documentation\",\n            href: \"/docs\",\n            icon: _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n        }\n    ];\n    // Filter menu items based on user role and plan\n    const filteredMenuItems = menuItems.filter((item)=>{\n        if (item.adminOnly && ![\n            \"super_admin\",\n            \"admin\"\n        ].includes(user.role)) {\n            return false;\n        }\n        if (item.premium && user.plan === \"gratis\") {\n            return false;\n        }\n        return true;\n    });\n    const getPlanColor = (plan)=>{\n        switch(plan){\n            case \"cybersecurity\":\n                return \"text-red-400 bg-red-900/20\";\n            case \"bughunter\":\n                return \"text-purple-400 bg-purple-900/20\";\n            case \"hobby\":\n                return \"text-green-400 bg-green-900/20\";\n            case \"pelajar\":\n                return \"text-blue-400 bg-blue-900/20\";\n            default:\n                return \"text-gray-400 bg-gray-900/20\";\n        }\n    };\n    const getPlanIcon = (plan)=>{\n        switch(plan){\n            case \"cybersecurity\":\n                return _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"];\n            case \"bughunter\":\n                return _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"];\n            case \"hobby\":\n                return _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"];\n            default:\n                return _barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"];\n        }\n    };\n    // Close mobile sidebar when route changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsMobileOpen(false);\n    }, [\n        pathname\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isMobileOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 z-40 lg:hidden\",\n                onClick: ()=>setIsMobileOpen(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 182,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsMobileOpen(true),\n                className: \"fixed top-4 left-4 z-50 lg:hidden bg-gray-800 text-white p-2 rounded-lg border border-gray-700 hover:bg-gray-700 transition-colors\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `\n        fixed lg:sticky top-0 left-0 h-screen bg-gray-900/95 backdrop-blur-sm border-r border-gray-800 z-50 lg:z-auto\n        transition-all duration-300 ease-in-out flex-shrink-0\n        ${isCollapsed ? \"w-16\" : \"w-64\"}\n        ${isMobileOpen ? \"translate-x-0\" : \"-translate-x-full lg:translate-x-0\"}\n        lg:h-[calc(100vh-4rem)]\n      `,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 border-b border-gray-800\",\n                        children: [\n                            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-cyber-green rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-5 w-5 text-black\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-bold text-white font-cyber\",\n                                        children: \"KodeXGuard\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsMobileOpen(false),\n                                        className: \"lg:hidden text-gray-400 hover:text-white transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsCollapsed(!isCollapsed),\n                                        className: \"hidden lg:block text-gray-400 hover:text-white transition-colors\",\n                                        children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-800\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                user.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: user.avatar,\n                                    alt: user.username,\n                                    className: \"w-10 h-10 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 rounded-full bg-cyber-green/20 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-cyber-green font-bold\",\n                                        children: user.username.charAt(0).toUpperCase()\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 15\n                                }, this),\n                                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium text-white truncate\",\n                                            children: user.username\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1\",\n                                            children: [\n                                                (()=>{\n                                                    const PlanIcon = getPlanIcon(user.plan);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlanIcon, {\n                                                        className: \"h-3 w-3 text-nusantara-gold\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 28\n                                                    }, this);\n                                                })(),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `text-xs px-2 py-1 rounded-full font-semibold ${getPlanColor(user.plan)}`,\n                                                    children: user.plan.toUpperCase()\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex-1 overflow-y-auto py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1 px-2\",\n                            children: filteredMenuItems.map((item)=>{\n                                const Icon = item.icon;\n                                const isActive = pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: item.href,\n                                    className: `\n                    group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200\n                    ${isActive ? \"bg-cyber-green text-black\" : \"text-gray-300 hover:text-white hover:bg-gray-800/50\"}\n                    ${isCollapsed ? \"justify-center\" : \"justify-start\"}\n                  `,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: `h-5 w-5 flex-shrink-0 ${isCollapsed ? \"\" : \"mr-3\"}`\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 19\n                                        }, this),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex-1\",\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1\",\n                                                    children: [\n                                                        item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: `\n                            px-2 py-1 text-xs font-bold rounded-full\n                            ${item.badge === \"HOT\" ? \"bg-red-500 text-white\" : \"bg-blue-500 text-white\"}\n                          `,\n                                                            children: item.badge\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        item.premium && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-3 w-3 text-nusantara-gold\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        item.adminOnly && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"h-3 w-3 text-orange-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true),\n                                        isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute left-16 bg-gray-800 text-white px-2 py-1 rounded-lg text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50\",\n                                            children: [\n                                                item.name,\n                                                item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `ml-2 px-1 py-0.5 text-xs rounded ${item.badge === \"HOT\" ? \"bg-red-500\" : \"bg-blue-500\"}`,\n                                                    children: item.badge\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, item.id, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-gray-800 p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: `\n              w-full flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors\n              ${isCollapsed ? \"justify-center\" : \"justify-start\"}\n            `,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: `h-5 w-5 ${isCollapsed ? \"\" : \"mr-3\"}`\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Notifications\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 32\n                                        }, this),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full\",\n                                            children: \"3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: `\n              w-full flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors\n              ${isCollapsed ? \"justify-center\" : \"justify-start\"}\n            `,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: `h-5 w-5 ${isCollapsed ? \"\" : \"mr-3\"}`\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Settings\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 32\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: `\n              w-full flex items-center px-3 py-2 text-sm font-medium text-red-400 hover:text-red-300 hover:bg-red-900/20 rounded-lg transition-colors\n              ${isCollapsed ? \"justify-center\" : \"justify-start\"}\n            `,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_BookOpen_Bot_ChevronLeft_ChevronRight_Code_CreditCard_Crown_Database_FileSearch_LayoutDashboard_LogOut_Menu_Search_Settings_Shield_Star_Target_Trophy_User_Wrench_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            className: `h-5 w-5 ${isCollapsed ? \"\" : \"mr-3\"}`\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Logout\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 32\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Sidebar.tsx\n");

/***/ }),

/***/ "(rsc)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7cffc49d1683\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb2RleGd1YXJkLy4vc3R5bGVzL2dsb2JhbHMuY3NzPzZkZDQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3Y2ZmYzQ5ZDE2ODNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\dashboard\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(rsc)/./styles/globals.css\");\n\n\n\nconst metadata = {\n    title: \"KodeXGuard - Platform Cybersecurity & Bug Hunting\",\n    description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting dengan dukungan Bot WhatsApp/Telegram\",\n    keywords: \"cybersecurity, bug hunting, OSINT, vulnerability scanner, CVE, penetration testing, security tools\",\n    authors: [\n        {\n            name: \"KodeXGuard Team\"\n        }\n    ],\n    creator: \"KodeXGuard\",\n    publisher: \"KodeXGuard\",\n    robots: \"index, follow\",\n    openGraph: {\n        type: \"website\",\n        locale: \"id_ID\",\n        url: \"https://kodexguard.com\",\n        title: \"KodeXGuard - Platform Cybersecurity & Bug Hunting\",\n        description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting\",\n        siteName: \"KodeXGuard\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"KodeXGuard - Platform Cybersecurity & Bug Hunting\",\n        description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting\"\n    },\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#00ff41\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"id\",\n        className: \"dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} min-h-screen bg-gradient-cyber text-white antialiased`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"matrix-bg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-b from-transparent via-cyber-dark/20 to-cyber-dark\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n              // Matrix Rain Effect\n              function createMatrixRain() {\n                const canvas = document.createElement('canvas');\n                const ctx = canvas.getContext('2d');\n                canvas.style.position = 'fixed';\n                canvas.style.top = '0';\n                canvas.style.left = '0';\n                canvas.style.width = '100%';\n                canvas.style.height = '100%';\n                canvas.style.pointerEvents = 'none';\n                canvas.style.zIndex = '-1';\n                canvas.style.opacity = '0.1';\n                document.body.appendChild(canvas);\n                \n                canvas.width = window.innerWidth;\n                canvas.height = window.innerHeight;\n                \n                const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';\n                const charArray = chars.split('');\n                const fontSize = 14;\n                const columns = canvas.width / fontSize;\n                const drops = [];\n                \n                for (let i = 0; i < columns; i++) {\n                  drops[i] = 1;\n                }\n                \n                function draw() {\n                  ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';\n                  ctx.fillRect(0, 0, canvas.width, canvas.height);\n                  \n                  ctx.fillStyle = '#00ff41';\n                  ctx.font = fontSize + 'px monospace';\n                  \n                  for (let i = 0; i < drops.length; i++) {\n                    const text = charArray[Math.floor(Math.random() * charArray.length)];\n                    ctx.fillText(text, i * fontSize, drops[i] * fontSize);\n                    \n                    if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {\n                      drops[i] = 0;\n                    }\n                    drops[i]++;\n                  }\n                }\n                \n                setInterval(draw, 50);\n                \n                window.addEventListener('resize', () => {\n                  canvas.width = window.innerWidth;\n                  canvas.height = window.innerHeight;\n                });\n              }\n              \n              // Initialize matrix effect after page load\n              if (document.readyState === 'loading') {\n                document.addEventListener('DOMContentLoaded', createMatrixRain);\n              } else {\n                createMatrixRain();\n              }\n            `\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();