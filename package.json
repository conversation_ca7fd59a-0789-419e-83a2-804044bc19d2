{"name": "kodexguard", "version": "1.0.0", "private": true, "description": "KodeXGuard - Platform Cybersecurity & Bug Hunting", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "db:init": "bun run lib/init-database.ts", "db:reset": "bun run lib/init-database.ts", "db:seed": "bun run lib/init-database.ts"}, "dependencies": {"@elastic/elasticsearch": "^9.0.3", "@hookform/resolvers": "^3.3.2", "@types/node": "^24.0.14", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.0", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "cheerio": "^1.1.0", "clsx": "^2.0.0", "crypto-js": "^4.2.0", "date-fns": "^2.30.0", "idb": "^8.0.3", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.294.0", "multer": "^2.0.1", "mysql2": "^3.14.2", "next": "^14.0.0", "node-telegram-bot-api": "^0.66.0", "postcss": "^8.4.0", "qrcode": "^1.5.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.47.0", "redis": "^4.6.10", "sharp": "^0.34.3", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "uuid": "^11.1.0", "validator": "^13.15.15", "venom-bot": "^5.3.0", "workbox-sw": "^7.3.0", "workbox-webpack-plugin": "^7.3.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/crypto-js": "^4.2.1", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^2.0.0", "@types/node-telegram-bot-api": "^0.64.7", "@types/uuid": "^10.0.0", "@types/validator": "^13.15.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0"}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}}