import { NextRequest, NextResponse } from 'next/server'

interface UserPreferences {
  general: {
    language: string
    timezone: string
    theme: 'dark' | 'light' | 'auto'
    dateFormat: string
    timeFormat: '12h' | '24h'
    currency: string
    autoSave: boolean
    compactMode: boolean
  }
  dashboard: {
    defaultView: 'overview' | 'scans' | 'osint' | 'reports'
    showWelcomeMessage: boolean
    refreshInterval: number // in seconds
    chartsType: 'line' | 'bar' | 'area'
    showQuickActions: boolean
    recentItemsCount: number
  }
  notifications: {
    desktop: boolean
    sound: boolean
    vibration: boolean
    emailDigest: 'none' | 'daily' | 'weekly'
    pushFrequency: 'immediate' | 'hourly' | 'daily'
  }
  scanning: {
    defaultScanType: string[]
    autoStartScans: boolean
    saveReports: boolean
    reportFormat: 'pdf' | 'html' | 'json' | 'xml'
    maxConcurrentScans: number
    defaultTimeout: number
  }
  osint: {
    defaultSearchTypes: string[]
    autoSaveSearches: boolean
    confidenceThreshold: number
    maxResults: number
    includeBreaches: boolean
    includeSocialMedia: boolean
  }
  privacy: {
    shareUsageData: boolean
    allowCookies: boolean
    trackingOptOut: boolean
    dataRetentionDays: number
  }
  accessibility: {
    highContrast: boolean
    largeText: boolean
    reducedMotion: boolean
    screenReader: boolean
    keyboardNavigation: boolean
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Simple token validation for testing
    if (!token.startsWith('kxg_')) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    // Mock user preferences data
    const userPreferences: UserPreferences = {
      general: {
        language: 'id',
        timezone: 'Asia/Jakarta',
        theme: 'dark',
        dateFormat: 'DD/MM/YYYY',
        timeFormat: '24h',
        currency: 'IDR',
        autoSave: true,
        compactMode: false
      },
      dashboard: {
        defaultView: 'overview',
        showWelcomeMessage: true,
        refreshInterval: 30,
        chartsType: 'line',
        showQuickActions: true,
        recentItemsCount: 10
      },
      notifications: {
        desktop: true,
        sound: true,
        vibration: false,
        emailDigest: 'daily',
        pushFrequency: 'immediate'
      },
      scanning: {
        defaultScanType: ['sqli', 'xss', 'csrf'],
        autoStartScans: false,
        saveReports: true,
        reportFormat: 'pdf',
        maxConcurrentScans: 3,
        defaultTimeout: 300
      },
      osint: {
        defaultSearchTypes: ['email', 'phone', 'name'],
        autoSaveSearches: true,
        confidenceThreshold: 70,
        maxResults: 50,
        includeBreaches: true,
        includeSocialMedia: true
      },
      privacy: {
        shareUsageData: false,
        allowCookies: true,
        trackingOptOut: false,
        dataRetentionDays: 365
      },
      accessibility: {
        highContrast: false,
        largeText: false,
        reducedMotion: false,
        screenReader: false,
        keyboardNavigation: true
      }
    }

    return NextResponse.json({
      success: true,
      data: userPreferences,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('User preferences API error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Simple token validation for testing
    if (!token.startsWith('kxg_')) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    const body = await request.json()
    const { general, dashboard, notifications, scanning, osint, privacy, accessibility } = body

    // Validate input
    if (!general && !dashboard && !notifications && !scanning && !osint && !privacy && !accessibility) {
      return NextResponse.json({
        success: false,
        error: 'At least one preference category is required'
      }, { status: 400 })
    }

    // In a real app, update the user preferences in database
    // For demo, just return success with updated data
    
    const updatedPreferences = {
      general: general || {
        language: 'id',
        timezone: 'Asia/Jakarta',
        theme: 'dark',
        dateFormat: 'DD/MM/YYYY',
        timeFormat: '24h',
        currency: 'IDR',
        autoSave: true,
        compactMode: false
      },
      dashboard: dashboard || {
        defaultView: 'overview',
        showWelcomeMessage: true,
        refreshInterval: 30,
        chartsType: 'line',
        showQuickActions: true,
        recentItemsCount: 10
      },
      notifications: notifications || {
        desktop: true,
        sound: true,
        vibration: false,
        emailDigest: 'daily',
        pushFrequency: 'immediate'
      },
      scanning: scanning || {
        defaultScanType: ['sqli', 'xss', 'csrf'],
        autoStartScans: false,
        saveReports: true,
        reportFormat: 'pdf',
        maxConcurrentScans: 3,
        defaultTimeout: 300
      },
      osint: osint || {
        defaultSearchTypes: ['email', 'phone', 'name'],
        autoSaveSearches: true,
        confidenceThreshold: 70,
        maxResults: 50,
        includeBreaches: true,
        includeSocialMedia: true
      },
      privacy: privacy || {
        shareUsageData: false,
        allowCookies: true,
        trackingOptOut: false,
        dataRetentionDays: 365
      },
      accessibility: accessibility || {
        highContrast: false,
        largeText: false,
        reducedMotion: false,
        screenReader: false,
        keyboardNavigation: true
      },
      updatedAt: new Date().toISOString()
    }

    return NextResponse.json({
      success: true,
      data: updatedPreferences,
      message: 'Preferences updated successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Update preferences error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}
