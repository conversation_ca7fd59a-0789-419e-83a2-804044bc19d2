/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/lru-cache";
exports.ids = ["vendor-chunks/lru-cache"];
exports.modules = {

/***/ "(rsc)/./node_modules/lru-cache/index.js":
/*!*****************************************!*\
  !*** ./node_modules/lru-cache/index.js ***!
  \*****************************************/
/***/ ((module) => {

eval("const perf =\n  typeof performance === 'object' &&\n  performance &&\n  typeof performance.now === 'function'\n    ? performance\n    : Date\n\nconst hasAbortController = typeof AbortController === 'function'\n\n// minimal backwards-compatibility polyfill\n// this doesn't have nearly all the checks and whatnot that\n// actual AbortController/Signal has, but it's enough for\n// our purposes, and if used properly, behaves the same.\nconst AC = hasAbortController\n  ? AbortController\n  : class AbortController {\n      constructor() {\n        this.signal = new AS()\n      }\n      abort(reason = new Error('This operation was aborted')) {\n        this.signal.reason = this.signal.reason || reason\n        this.signal.aborted = true\n        this.signal.dispatchEvent({\n          type: 'abort',\n          target: this.signal,\n        })\n      }\n    }\n\nconst hasAbortSignal = typeof AbortSignal === 'function'\n// Some polyfills put this on the AC class, not global\nconst hasACAbortSignal = typeof AC.AbortSignal === 'function'\nconst AS = hasAbortSignal\n  ? AbortSignal\n  : hasACAbortSignal\n  ? AC.AbortController\n  : class AbortSignal {\n      constructor() {\n        this.reason = undefined\n        this.aborted = false\n        this._listeners = []\n      }\n      dispatchEvent(e) {\n        if (e.type === 'abort') {\n          this.aborted = true\n          this.onabort(e)\n          this._listeners.forEach(f => f(e), this)\n        }\n      }\n      onabort() {}\n      addEventListener(ev, fn) {\n        if (ev === 'abort') {\n          this._listeners.push(fn)\n        }\n      }\n      removeEventListener(ev, fn) {\n        if (ev === 'abort') {\n          this._listeners = this._listeners.filter(f => f !== fn)\n        }\n      }\n    }\n\nconst warned = new Set()\nconst deprecatedOption = (opt, instead) => {\n  const code = `LRU_CACHE_OPTION_${opt}`\n  if (shouldWarn(code)) {\n    warn(code, `${opt} option`, `options.${instead}`, LRUCache)\n  }\n}\nconst deprecatedMethod = (method, instead) => {\n  const code = `LRU_CACHE_METHOD_${method}`\n  if (shouldWarn(code)) {\n    const { prototype } = LRUCache\n    const { get } = Object.getOwnPropertyDescriptor(prototype, method)\n    warn(code, `${method} method`, `cache.${instead}()`, get)\n  }\n}\nconst deprecatedProperty = (field, instead) => {\n  const code = `LRU_CACHE_PROPERTY_${field}`\n  if (shouldWarn(code)) {\n    const { prototype } = LRUCache\n    const { get } = Object.getOwnPropertyDescriptor(prototype, field)\n    warn(code, `${field} property`, `cache.${instead}`, get)\n  }\n}\n\nconst emitWarning = (...a) => {\n  typeof process === 'object' &&\n  process &&\n  typeof process.emitWarning === 'function'\n    ? process.emitWarning(...a)\n    : console.error(...a)\n}\n\nconst shouldWarn = code => !warned.has(code)\n\nconst warn = (code, what, instead, fn) => {\n  warned.add(code)\n  const msg = `The ${what} is deprecated. Please use ${instead} instead.`\n  emitWarning(msg, 'DeprecationWarning', code, fn)\n}\n\nconst isPosInt = n => n && n === Math.floor(n) && n > 0 && isFinite(n)\n\n/* istanbul ignore next - This is a little bit ridiculous, tbh.\n * The maximum array length is 2^32-1 or thereabouts on most JS impls.\n * And well before that point, you're caching the entire world, I mean,\n * that's ~32GB of just integers for the next/prev links, plus whatever\n * else to hold that many keys and values.  Just filling the memory with\n * zeroes at init time is brutal when you get that big.\n * But why not be complete?\n * Maybe in the future, these limits will have expanded. */\nconst getUintArray = max =>\n  !isPosInt(max)\n    ? null\n    : max <= Math.pow(2, 8)\n    ? Uint8Array\n    : max <= Math.pow(2, 16)\n    ? Uint16Array\n    : max <= Math.pow(2, 32)\n    ? Uint32Array\n    : max <= Number.MAX_SAFE_INTEGER\n    ? ZeroArray\n    : null\n\nclass ZeroArray extends Array {\n  constructor(size) {\n    super(size)\n    this.fill(0)\n  }\n}\n\nclass Stack {\n  constructor(max) {\n    if (max === 0) {\n      return []\n    }\n    const UintArray = getUintArray(max)\n    this.heap = new UintArray(max)\n    this.length = 0\n  }\n  push(n) {\n    this.heap[this.length++] = n\n  }\n  pop() {\n    return this.heap[--this.length]\n  }\n}\n\nclass LRUCache {\n  constructor(options = {}) {\n    const {\n      max = 0,\n      ttl,\n      ttlResolution = 1,\n      ttlAutopurge,\n      updateAgeOnGet,\n      updateAgeOnHas,\n      allowStale,\n      dispose,\n      disposeAfter,\n      noDisposeOnSet,\n      noUpdateTTL,\n      maxSize = 0,\n      maxEntrySize = 0,\n      sizeCalculation,\n      fetchMethod,\n      fetchContext,\n      noDeleteOnFetchRejection,\n      noDeleteOnStaleGet,\n      allowStaleOnFetchRejection,\n      allowStaleOnFetchAbort,\n      ignoreFetchAbort,\n    } = options\n\n    // deprecated options, don't trigger a warning for getting them if\n    // the thing being passed in is another LRUCache we're copying.\n    const { length, maxAge, stale } =\n      options instanceof LRUCache ? {} : options\n\n    if (max !== 0 && !isPosInt(max)) {\n      throw new TypeError('max option must be a nonnegative integer')\n    }\n\n    const UintArray = max ? getUintArray(max) : Array\n    if (!UintArray) {\n      throw new Error('invalid max value: ' + max)\n    }\n\n    this.max = max\n    this.maxSize = maxSize\n    this.maxEntrySize = maxEntrySize || this.maxSize\n    this.sizeCalculation = sizeCalculation || length\n    if (this.sizeCalculation) {\n      if (!this.maxSize && !this.maxEntrySize) {\n        throw new TypeError(\n          'cannot set sizeCalculation without setting maxSize or maxEntrySize'\n        )\n      }\n      if (typeof this.sizeCalculation !== 'function') {\n        throw new TypeError('sizeCalculation set to non-function')\n      }\n    }\n\n    this.fetchMethod = fetchMethod || null\n    if (this.fetchMethod && typeof this.fetchMethod !== 'function') {\n      throw new TypeError(\n        'fetchMethod must be a function if specified'\n      )\n    }\n\n    this.fetchContext = fetchContext\n    if (!this.fetchMethod && fetchContext !== undefined) {\n      throw new TypeError(\n        'cannot set fetchContext without fetchMethod'\n      )\n    }\n\n    this.keyMap = new Map()\n    this.keyList = new Array(max).fill(null)\n    this.valList = new Array(max).fill(null)\n    this.next = new UintArray(max)\n    this.prev = new UintArray(max)\n    this.head = 0\n    this.tail = 0\n    this.free = new Stack(max)\n    this.initialFill = 1\n    this.size = 0\n\n    if (typeof dispose === 'function') {\n      this.dispose = dispose\n    }\n    if (typeof disposeAfter === 'function') {\n      this.disposeAfter = disposeAfter\n      this.disposed = []\n    } else {\n      this.disposeAfter = null\n      this.disposed = null\n    }\n    this.noDisposeOnSet = !!noDisposeOnSet\n    this.noUpdateTTL = !!noUpdateTTL\n    this.noDeleteOnFetchRejection = !!noDeleteOnFetchRejection\n    this.allowStaleOnFetchRejection = !!allowStaleOnFetchRejection\n    this.allowStaleOnFetchAbort = !!allowStaleOnFetchAbort\n    this.ignoreFetchAbort = !!ignoreFetchAbort\n\n    // NB: maxEntrySize is set to maxSize if it's set\n    if (this.maxEntrySize !== 0) {\n      if (this.maxSize !== 0) {\n        if (!isPosInt(this.maxSize)) {\n          throw new TypeError(\n            'maxSize must be a positive integer if specified'\n          )\n        }\n      }\n      if (!isPosInt(this.maxEntrySize)) {\n        throw new TypeError(\n          'maxEntrySize must be a positive integer if specified'\n        )\n      }\n      this.initializeSizeTracking()\n    }\n\n    this.allowStale = !!allowStale || !!stale\n    this.noDeleteOnStaleGet = !!noDeleteOnStaleGet\n    this.updateAgeOnGet = !!updateAgeOnGet\n    this.updateAgeOnHas = !!updateAgeOnHas\n    this.ttlResolution =\n      isPosInt(ttlResolution) || ttlResolution === 0\n        ? ttlResolution\n        : 1\n    this.ttlAutopurge = !!ttlAutopurge\n    this.ttl = ttl || maxAge || 0\n    if (this.ttl) {\n      if (!isPosInt(this.ttl)) {\n        throw new TypeError(\n          'ttl must be a positive integer if specified'\n        )\n      }\n      this.initializeTTLTracking()\n    }\n\n    // do not allow completely unbounded caches\n    if (this.max === 0 && this.ttl === 0 && this.maxSize === 0) {\n      throw new TypeError(\n        'At least one of max, maxSize, or ttl is required'\n      )\n    }\n    if (!this.ttlAutopurge && !this.max && !this.maxSize) {\n      const code = 'LRU_CACHE_UNBOUNDED'\n      if (shouldWarn(code)) {\n        warned.add(code)\n        const msg =\n          'TTL caching without ttlAutopurge, max, or maxSize can ' +\n          'result in unbounded memory consumption.'\n        emitWarning(msg, 'UnboundedCacheWarning', code, LRUCache)\n      }\n    }\n\n    if (stale) {\n      deprecatedOption('stale', 'allowStale')\n    }\n    if (maxAge) {\n      deprecatedOption('maxAge', 'ttl')\n    }\n    if (length) {\n      deprecatedOption('length', 'sizeCalculation')\n    }\n  }\n\n  getRemainingTTL(key) {\n    return this.has(key, { updateAgeOnHas: false }) ? Infinity : 0\n  }\n\n  initializeTTLTracking() {\n    this.ttls = new ZeroArray(this.max)\n    this.starts = new ZeroArray(this.max)\n\n    this.setItemTTL = (index, ttl, start = perf.now()) => {\n      this.starts[index] = ttl !== 0 ? start : 0\n      this.ttls[index] = ttl\n      if (ttl !== 0 && this.ttlAutopurge) {\n        const t = setTimeout(() => {\n          if (this.isStale(index)) {\n            this.delete(this.keyList[index])\n          }\n        }, ttl + 1)\n        /* istanbul ignore else - unref() not supported on all platforms */\n        if (t.unref) {\n          t.unref()\n        }\n      }\n    }\n\n    this.updateItemAge = index => {\n      this.starts[index] = this.ttls[index] !== 0 ? perf.now() : 0\n    }\n\n    this.statusTTL = (status, index) => {\n      if (status) {\n        status.ttl = this.ttls[index]\n        status.start = this.starts[index]\n        status.now = cachedNow || getNow()\n        status.remainingTTL = status.now + status.ttl - status.start\n      }\n    }\n\n    // debounce calls to perf.now() to 1s so we're not hitting\n    // that costly call repeatedly.\n    let cachedNow = 0\n    const getNow = () => {\n      const n = perf.now()\n      if (this.ttlResolution > 0) {\n        cachedNow = n\n        const t = setTimeout(\n          () => (cachedNow = 0),\n          this.ttlResolution\n        )\n        /* istanbul ignore else - not available on all platforms */\n        if (t.unref) {\n          t.unref()\n        }\n      }\n      return n\n    }\n\n    this.getRemainingTTL = key => {\n      const index = this.keyMap.get(key)\n      if (index === undefined) {\n        return 0\n      }\n      return this.ttls[index] === 0 || this.starts[index] === 0\n        ? Infinity\n        : this.starts[index] +\n            this.ttls[index] -\n            (cachedNow || getNow())\n    }\n\n    this.isStale = index => {\n      return (\n        this.ttls[index] !== 0 &&\n        this.starts[index] !== 0 &&\n        (cachedNow || getNow()) - this.starts[index] >\n          this.ttls[index]\n      )\n    }\n  }\n  updateItemAge(_index) {}\n  statusTTL(_status, _index) {}\n  setItemTTL(_index, _ttl, _start) {}\n  isStale(_index) {\n    return false\n  }\n\n  initializeSizeTracking() {\n    this.calculatedSize = 0\n    this.sizes = new ZeroArray(this.max)\n    this.removeItemSize = index => {\n      this.calculatedSize -= this.sizes[index]\n      this.sizes[index] = 0\n    }\n    this.requireSize = (k, v, size, sizeCalculation) => {\n      // provisionally accept background fetches.\n      // actual value size will be checked when they return.\n      if (this.isBackgroundFetch(v)) {\n        return 0\n      }\n      if (!isPosInt(size)) {\n        if (sizeCalculation) {\n          if (typeof sizeCalculation !== 'function') {\n            throw new TypeError('sizeCalculation must be a function')\n          }\n          size = sizeCalculation(v, k)\n          if (!isPosInt(size)) {\n            throw new TypeError(\n              'sizeCalculation return invalid (expect positive integer)'\n            )\n          }\n        } else {\n          throw new TypeError(\n            'invalid size value (must be positive integer). ' +\n              'When maxSize or maxEntrySize is used, sizeCalculation or size ' +\n              'must be set.'\n          )\n        }\n      }\n      return size\n    }\n    this.addItemSize = (index, size, status) => {\n      this.sizes[index] = size\n      if (this.maxSize) {\n        const maxSize = this.maxSize - this.sizes[index]\n        while (this.calculatedSize > maxSize) {\n          this.evict(true)\n        }\n      }\n      this.calculatedSize += this.sizes[index]\n      if (status) {\n        status.entrySize = size\n        status.totalCalculatedSize = this.calculatedSize\n      }\n    }\n  }\n  removeItemSize(_index) {}\n  addItemSize(_index, _size) {}\n  requireSize(_k, _v, size, sizeCalculation) {\n    if (size || sizeCalculation) {\n      throw new TypeError(\n        'cannot set size without setting maxSize or maxEntrySize on cache'\n      )\n    }\n  }\n\n  *indexes({ allowStale = this.allowStale } = {}) {\n    if (this.size) {\n      for (let i = this.tail; true; ) {\n        if (!this.isValidIndex(i)) {\n          break\n        }\n        if (allowStale || !this.isStale(i)) {\n          yield i\n        }\n        if (i === this.head) {\n          break\n        } else {\n          i = this.prev[i]\n        }\n      }\n    }\n  }\n\n  *rindexes({ allowStale = this.allowStale } = {}) {\n    if (this.size) {\n      for (let i = this.head; true; ) {\n        if (!this.isValidIndex(i)) {\n          break\n        }\n        if (allowStale || !this.isStale(i)) {\n          yield i\n        }\n        if (i === this.tail) {\n          break\n        } else {\n          i = this.next[i]\n        }\n      }\n    }\n  }\n\n  isValidIndex(index) {\n    return (\n      index !== undefined &&\n      this.keyMap.get(this.keyList[index]) === index\n    )\n  }\n\n  *entries() {\n    for (const i of this.indexes()) {\n      if (\n        this.valList[i] !== undefined &&\n        this.keyList[i] !== undefined &&\n        !this.isBackgroundFetch(this.valList[i])\n      ) {\n        yield [this.keyList[i], this.valList[i]]\n      }\n    }\n  }\n  *rentries() {\n    for (const i of this.rindexes()) {\n      if (\n        this.valList[i] !== undefined &&\n        this.keyList[i] !== undefined &&\n        !this.isBackgroundFetch(this.valList[i])\n      ) {\n        yield [this.keyList[i], this.valList[i]]\n      }\n    }\n  }\n\n  *keys() {\n    for (const i of this.indexes()) {\n      if (\n        this.keyList[i] !== undefined &&\n        !this.isBackgroundFetch(this.valList[i])\n      ) {\n        yield this.keyList[i]\n      }\n    }\n  }\n  *rkeys() {\n    for (const i of this.rindexes()) {\n      if (\n        this.keyList[i] !== undefined &&\n        !this.isBackgroundFetch(this.valList[i])\n      ) {\n        yield this.keyList[i]\n      }\n    }\n  }\n\n  *values() {\n    for (const i of this.indexes()) {\n      if (\n        this.valList[i] !== undefined &&\n        !this.isBackgroundFetch(this.valList[i])\n      ) {\n        yield this.valList[i]\n      }\n    }\n  }\n  *rvalues() {\n    for (const i of this.rindexes()) {\n      if (\n        this.valList[i] !== undefined &&\n        !this.isBackgroundFetch(this.valList[i])\n      ) {\n        yield this.valList[i]\n      }\n    }\n  }\n\n  [Symbol.iterator]() {\n    return this.entries()\n  }\n\n  find(fn, getOptions) {\n    for (const i of this.indexes()) {\n      const v = this.valList[i]\n      const value = this.isBackgroundFetch(v)\n        ? v.__staleWhileFetching\n        : v\n      if (value === undefined) continue\n      if (fn(value, this.keyList[i], this)) {\n        return this.get(this.keyList[i], getOptions)\n      }\n    }\n  }\n\n  forEach(fn, thisp = this) {\n    for (const i of this.indexes()) {\n      const v = this.valList[i]\n      const value = this.isBackgroundFetch(v)\n        ? v.__staleWhileFetching\n        : v\n      if (value === undefined) continue\n      fn.call(thisp, value, this.keyList[i], this)\n    }\n  }\n\n  rforEach(fn, thisp = this) {\n    for (const i of this.rindexes()) {\n      const v = this.valList[i]\n      const value = this.isBackgroundFetch(v)\n        ? v.__staleWhileFetching\n        : v\n      if (value === undefined) continue\n      fn.call(thisp, value, this.keyList[i], this)\n    }\n  }\n\n  get prune() {\n    deprecatedMethod('prune', 'purgeStale')\n    return this.purgeStale\n  }\n\n  purgeStale() {\n    let deleted = false\n    for (const i of this.rindexes({ allowStale: true })) {\n      if (this.isStale(i)) {\n        this.delete(this.keyList[i])\n        deleted = true\n      }\n    }\n    return deleted\n  }\n\n  dump() {\n    const arr = []\n    for (const i of this.indexes({ allowStale: true })) {\n      const key = this.keyList[i]\n      const v = this.valList[i]\n      const value = this.isBackgroundFetch(v)\n        ? v.__staleWhileFetching\n        : v\n      if (value === undefined) continue\n      const entry = { value }\n      if (this.ttls) {\n        entry.ttl = this.ttls[i]\n        // always dump the start relative to a portable timestamp\n        // it's ok for this to be a bit slow, it's a rare operation.\n        const age = perf.now() - this.starts[i]\n        entry.start = Math.floor(Date.now() - age)\n      }\n      if (this.sizes) {\n        entry.size = this.sizes[i]\n      }\n      arr.unshift([key, entry])\n    }\n    return arr\n  }\n\n  load(arr) {\n    this.clear()\n    for (const [key, entry] of arr) {\n      if (entry.start) {\n        // entry.start is a portable timestamp, but we may be using\n        // node's performance.now(), so calculate the offset.\n        // it's ok for this to be a bit slow, it's a rare operation.\n        const age = Date.now() - entry.start\n        entry.start = perf.now() - age\n      }\n      this.set(key, entry.value, entry)\n    }\n  }\n\n  dispose(_v, _k, _reason) {}\n\n  set(\n    k,\n    v,\n    {\n      ttl = this.ttl,\n      start,\n      noDisposeOnSet = this.noDisposeOnSet,\n      size = 0,\n      sizeCalculation = this.sizeCalculation,\n      noUpdateTTL = this.noUpdateTTL,\n      status,\n    } = {}\n  ) {\n    size = this.requireSize(k, v, size, sizeCalculation)\n    // if the item doesn't fit, don't do anything\n    // NB: maxEntrySize set to maxSize by default\n    if (this.maxEntrySize && size > this.maxEntrySize) {\n      if (status) {\n        status.set = 'miss'\n        status.maxEntrySizeExceeded = true\n      }\n      // have to delete, in case a background fetch is there already.\n      // in non-async cases, this is a no-op\n      this.delete(k)\n      return this\n    }\n    let index = this.size === 0 ? undefined : this.keyMap.get(k)\n    if (index === undefined) {\n      // addition\n      index = this.newIndex()\n      this.keyList[index] = k\n      this.valList[index] = v\n      this.keyMap.set(k, index)\n      this.next[this.tail] = index\n      this.prev[index] = this.tail\n      this.tail = index\n      this.size++\n      this.addItemSize(index, size, status)\n      if (status) {\n        status.set = 'add'\n      }\n      noUpdateTTL = false\n    } else {\n      // update\n      this.moveToTail(index)\n      const oldVal = this.valList[index]\n      if (v !== oldVal) {\n        if (this.isBackgroundFetch(oldVal)) {\n          oldVal.__abortController.abort(new Error('replaced'))\n        } else {\n          if (!noDisposeOnSet) {\n            this.dispose(oldVal, k, 'set')\n            if (this.disposeAfter) {\n              this.disposed.push([oldVal, k, 'set'])\n            }\n          }\n        }\n        this.removeItemSize(index)\n        this.valList[index] = v\n        this.addItemSize(index, size, status)\n        if (status) {\n          status.set = 'replace'\n          const oldValue =\n            oldVal && this.isBackgroundFetch(oldVal)\n              ? oldVal.__staleWhileFetching\n              : oldVal\n          if (oldValue !== undefined) status.oldValue = oldValue\n        }\n      } else if (status) {\n        status.set = 'update'\n      }\n    }\n    if (ttl !== 0 && this.ttl === 0 && !this.ttls) {\n      this.initializeTTLTracking()\n    }\n    if (!noUpdateTTL) {\n      this.setItemTTL(index, ttl, start)\n    }\n    this.statusTTL(status, index)\n    if (this.disposeAfter) {\n      while (this.disposed.length) {\n        this.disposeAfter(...this.disposed.shift())\n      }\n    }\n    return this\n  }\n\n  newIndex() {\n    if (this.size === 0) {\n      return this.tail\n    }\n    if (this.size === this.max && this.max !== 0) {\n      return this.evict(false)\n    }\n    if (this.free.length !== 0) {\n      return this.free.pop()\n    }\n    // initial fill, just keep writing down the list\n    return this.initialFill++\n  }\n\n  pop() {\n    if (this.size) {\n      const val = this.valList[this.head]\n      this.evict(true)\n      return val\n    }\n  }\n\n  evict(free) {\n    const head = this.head\n    const k = this.keyList[head]\n    const v = this.valList[head]\n    if (this.isBackgroundFetch(v)) {\n      v.__abortController.abort(new Error('evicted'))\n    } else {\n      this.dispose(v, k, 'evict')\n      if (this.disposeAfter) {\n        this.disposed.push([v, k, 'evict'])\n      }\n    }\n    this.removeItemSize(head)\n    // if we aren't about to use the index, then null these out\n    if (free) {\n      this.keyList[head] = null\n      this.valList[head] = null\n      this.free.push(head)\n    }\n    this.head = this.next[head]\n    this.keyMap.delete(k)\n    this.size--\n    return head\n  }\n\n  has(k, { updateAgeOnHas = this.updateAgeOnHas, status } = {}) {\n    const index = this.keyMap.get(k)\n    if (index !== undefined) {\n      if (!this.isStale(index)) {\n        if (updateAgeOnHas) {\n          this.updateItemAge(index)\n        }\n        if (status) status.has = 'hit'\n        this.statusTTL(status, index)\n        return true\n      } else if (status) {\n        status.has = 'stale'\n        this.statusTTL(status, index)\n      }\n    } else if (status) {\n      status.has = 'miss'\n    }\n    return false\n  }\n\n  // like get(), but without any LRU updating or TTL expiration\n  peek(k, { allowStale = this.allowStale } = {}) {\n    const index = this.keyMap.get(k)\n    if (index !== undefined && (allowStale || !this.isStale(index))) {\n      const v = this.valList[index]\n      // either stale and allowed, or forcing a refresh of non-stale value\n      return this.isBackgroundFetch(v) ? v.__staleWhileFetching : v\n    }\n  }\n\n  backgroundFetch(k, index, options, context) {\n    const v = index === undefined ? undefined : this.valList[index]\n    if (this.isBackgroundFetch(v)) {\n      return v\n    }\n    const ac = new AC()\n    if (options.signal) {\n      options.signal.addEventListener('abort', () =>\n        ac.abort(options.signal.reason)\n      )\n    }\n    const fetchOpts = {\n      signal: ac.signal,\n      options,\n      context,\n    }\n    const cb = (v, updateCache = false) => {\n      const { aborted } = ac.signal\n      const ignoreAbort = options.ignoreFetchAbort && v !== undefined\n      if (options.status) {\n        if (aborted && !updateCache) {\n          options.status.fetchAborted = true\n          options.status.fetchError = ac.signal.reason\n          if (ignoreAbort) options.status.fetchAbortIgnored = true\n        } else {\n          options.status.fetchResolved = true\n        }\n      }\n      if (aborted && !ignoreAbort && !updateCache) {\n        return fetchFail(ac.signal.reason)\n      }\n      // either we didn't abort, and are still here, or we did, and ignored\n      if (this.valList[index] === p) {\n        if (v === undefined) {\n          if (p.__staleWhileFetching) {\n            this.valList[index] = p.__staleWhileFetching\n          } else {\n            this.delete(k)\n          }\n        } else {\n          if (options.status) options.status.fetchUpdated = true\n          this.set(k, v, fetchOpts.options)\n        }\n      }\n      return v\n    }\n    const eb = er => {\n      if (options.status) {\n        options.status.fetchRejected = true\n        options.status.fetchError = er\n      }\n      return fetchFail(er)\n    }\n    const fetchFail = er => {\n      const { aborted } = ac.signal\n      const allowStaleAborted =\n        aborted && options.allowStaleOnFetchAbort\n      const allowStale =\n        allowStaleAborted || options.allowStaleOnFetchRejection\n      const noDelete = allowStale || options.noDeleteOnFetchRejection\n      if (this.valList[index] === p) {\n        // if we allow stale on fetch rejections, then we need to ensure that\n        // the stale value is not removed from the cache when the fetch fails.\n        const del = !noDelete || p.__staleWhileFetching === undefined\n        if (del) {\n          this.delete(k)\n        } else if (!allowStaleAborted) {\n          // still replace the *promise* with the stale value,\n          // since we are done with the promise at this point.\n          // leave it untouched if we're still waiting for an\n          // aborted background fetch that hasn't yet returned.\n          this.valList[index] = p.__staleWhileFetching\n        }\n      }\n      if (allowStale) {\n        if (options.status && p.__staleWhileFetching !== undefined) {\n          options.status.returnedStale = true\n        }\n        return p.__staleWhileFetching\n      } else if (p.__returned === p) {\n        throw er\n      }\n    }\n    const pcall = (res, rej) => {\n      this.fetchMethod(k, v, fetchOpts).then(v => res(v), rej)\n      // ignored, we go until we finish, regardless.\n      // defer check until we are actually aborting,\n      // so fetchMethod can override.\n      ac.signal.addEventListener('abort', () => {\n        if (\n          !options.ignoreFetchAbort ||\n          options.allowStaleOnFetchAbort\n        ) {\n          res()\n          // when it eventually resolves, update the cache.\n          if (options.allowStaleOnFetchAbort) {\n            res = v => cb(v, true)\n          }\n        }\n      })\n    }\n    if (options.status) options.status.fetchDispatched = true\n    const p = new Promise(pcall).then(cb, eb)\n    p.__abortController = ac\n    p.__staleWhileFetching = v\n    p.__returned = null\n    if (index === undefined) {\n      // internal, don't expose status.\n      this.set(k, p, { ...fetchOpts.options, status: undefined })\n      index = this.keyMap.get(k)\n    } else {\n      this.valList[index] = p\n    }\n    return p\n  }\n\n  isBackgroundFetch(p) {\n    return (\n      p &&\n      typeof p === 'object' &&\n      typeof p.then === 'function' &&\n      Object.prototype.hasOwnProperty.call(\n        p,\n        '__staleWhileFetching'\n      ) &&\n      Object.prototype.hasOwnProperty.call(p, '__returned') &&\n      (p.__returned === p || p.__returned === null)\n    )\n  }\n\n  // this takes the union of get() and set() opts, because it does both\n  async fetch(\n    k,\n    {\n      // get options\n      allowStale = this.allowStale,\n      updateAgeOnGet = this.updateAgeOnGet,\n      noDeleteOnStaleGet = this.noDeleteOnStaleGet,\n      // set options\n      ttl = this.ttl,\n      noDisposeOnSet = this.noDisposeOnSet,\n      size = 0,\n      sizeCalculation = this.sizeCalculation,\n      noUpdateTTL = this.noUpdateTTL,\n      // fetch exclusive options\n      noDeleteOnFetchRejection = this.noDeleteOnFetchRejection,\n      allowStaleOnFetchRejection = this.allowStaleOnFetchRejection,\n      ignoreFetchAbort = this.ignoreFetchAbort,\n      allowStaleOnFetchAbort = this.allowStaleOnFetchAbort,\n      fetchContext = this.fetchContext,\n      forceRefresh = false,\n      status,\n      signal,\n    } = {}\n  ) {\n    if (!this.fetchMethod) {\n      if (status) status.fetch = 'get'\n      return this.get(k, {\n        allowStale,\n        updateAgeOnGet,\n        noDeleteOnStaleGet,\n        status,\n      })\n    }\n\n    const options = {\n      allowStale,\n      updateAgeOnGet,\n      noDeleteOnStaleGet,\n      ttl,\n      noDisposeOnSet,\n      size,\n      sizeCalculation,\n      noUpdateTTL,\n      noDeleteOnFetchRejection,\n      allowStaleOnFetchRejection,\n      allowStaleOnFetchAbort,\n      ignoreFetchAbort,\n      status,\n      signal,\n    }\n\n    let index = this.keyMap.get(k)\n    if (index === undefined) {\n      if (status) status.fetch = 'miss'\n      const p = this.backgroundFetch(k, index, options, fetchContext)\n      return (p.__returned = p)\n    } else {\n      // in cache, maybe already fetching\n      const v = this.valList[index]\n      if (this.isBackgroundFetch(v)) {\n        const stale =\n          allowStale && v.__staleWhileFetching !== undefined\n        if (status) {\n          status.fetch = 'inflight'\n          if (stale) status.returnedStale = true\n        }\n        return stale ? v.__staleWhileFetching : (v.__returned = v)\n      }\n\n      // if we force a refresh, that means do NOT serve the cached value,\n      // unless we are already in the process of refreshing the cache.\n      const isStale = this.isStale(index)\n      if (!forceRefresh && !isStale) {\n        if (status) status.fetch = 'hit'\n        this.moveToTail(index)\n        if (updateAgeOnGet) {\n          this.updateItemAge(index)\n        }\n        this.statusTTL(status, index)\n        return v\n      }\n\n      // ok, it is stale or a forced refresh, and not already fetching.\n      // refresh the cache.\n      const p = this.backgroundFetch(k, index, options, fetchContext)\n      const hasStale = p.__staleWhileFetching !== undefined\n      const staleVal = hasStale && allowStale\n      if (status) {\n        status.fetch = hasStale && isStale ? 'stale' : 'refresh'\n        if (staleVal && isStale) status.returnedStale = true\n      }\n      return staleVal ? p.__staleWhileFetching : (p.__returned = p)\n    }\n  }\n\n  get(\n    k,\n    {\n      allowStale = this.allowStale,\n      updateAgeOnGet = this.updateAgeOnGet,\n      noDeleteOnStaleGet = this.noDeleteOnStaleGet,\n      status,\n    } = {}\n  ) {\n    const index = this.keyMap.get(k)\n    if (index !== undefined) {\n      const value = this.valList[index]\n      const fetching = this.isBackgroundFetch(value)\n      this.statusTTL(status, index)\n      if (this.isStale(index)) {\n        if (status) status.get = 'stale'\n        // delete only if not an in-flight background fetch\n        if (!fetching) {\n          if (!noDeleteOnStaleGet) {\n            this.delete(k)\n          }\n          if (status) status.returnedStale = allowStale\n          return allowStale ? value : undefined\n        } else {\n          if (status) {\n            status.returnedStale =\n              allowStale && value.__staleWhileFetching !== undefined\n          }\n          return allowStale ? value.__staleWhileFetching : undefined\n        }\n      } else {\n        if (status) status.get = 'hit'\n        // if we're currently fetching it, we don't actually have it yet\n        // it's not stale, which means this isn't a staleWhileRefetching.\n        // If it's not stale, and fetching, AND has a __staleWhileFetching\n        // value, then that means the user fetched with {forceRefresh:true},\n        // so it's safe to return that value.\n        if (fetching) {\n          return value.__staleWhileFetching\n        }\n        this.moveToTail(index)\n        if (updateAgeOnGet) {\n          this.updateItemAge(index)\n        }\n        return value\n      }\n    } else if (status) {\n      status.get = 'miss'\n    }\n  }\n\n  connect(p, n) {\n    this.prev[n] = p\n    this.next[p] = n\n  }\n\n  moveToTail(index) {\n    // if tail already, nothing to do\n    // if head, move head to next[index]\n    // else\n    //   move next[prev[index]] to next[index] (head has no prev)\n    //   move prev[next[index]] to prev[index]\n    // prev[index] = tail\n    // next[tail] = index\n    // tail = index\n    if (index !== this.tail) {\n      if (index === this.head) {\n        this.head = this.next[index]\n      } else {\n        this.connect(this.prev[index], this.next[index])\n      }\n      this.connect(this.tail, index)\n      this.tail = index\n    }\n  }\n\n  get del() {\n    deprecatedMethod('del', 'delete')\n    return this.delete\n  }\n\n  delete(k) {\n    let deleted = false\n    if (this.size !== 0) {\n      const index = this.keyMap.get(k)\n      if (index !== undefined) {\n        deleted = true\n        if (this.size === 1) {\n          this.clear()\n        } else {\n          this.removeItemSize(index)\n          const v = this.valList[index]\n          if (this.isBackgroundFetch(v)) {\n            v.__abortController.abort(new Error('deleted'))\n          } else {\n            this.dispose(v, k, 'delete')\n            if (this.disposeAfter) {\n              this.disposed.push([v, k, 'delete'])\n            }\n          }\n          this.keyMap.delete(k)\n          this.keyList[index] = null\n          this.valList[index] = null\n          if (index === this.tail) {\n            this.tail = this.prev[index]\n          } else if (index === this.head) {\n            this.head = this.next[index]\n          } else {\n            this.next[this.prev[index]] = this.next[index]\n            this.prev[this.next[index]] = this.prev[index]\n          }\n          this.size--\n          this.free.push(index)\n        }\n      }\n    }\n    if (this.disposed) {\n      while (this.disposed.length) {\n        this.disposeAfter(...this.disposed.shift())\n      }\n    }\n    return deleted\n  }\n\n  clear() {\n    for (const index of this.rindexes({ allowStale: true })) {\n      const v = this.valList[index]\n      if (this.isBackgroundFetch(v)) {\n        v.__abortController.abort(new Error('deleted'))\n      } else {\n        const k = this.keyList[index]\n        this.dispose(v, k, 'delete')\n        if (this.disposeAfter) {\n          this.disposed.push([v, k, 'delete'])\n        }\n      }\n    }\n\n    this.keyMap.clear()\n    this.valList.fill(null)\n    this.keyList.fill(null)\n    if (this.ttls) {\n      this.ttls.fill(0)\n      this.starts.fill(0)\n    }\n    if (this.sizes) {\n      this.sizes.fill(0)\n    }\n    this.head = 0\n    this.tail = 0\n    this.initialFill = 1\n    this.free.length = 0\n    this.calculatedSize = 0\n    this.size = 0\n    if (this.disposed) {\n      while (this.disposed.length) {\n        this.disposeAfter(...this.disposed.shift())\n      }\n    }\n  }\n\n  get reset() {\n    deprecatedMethod('reset', 'clear')\n    return this.clear\n  }\n\n  get length() {\n    deprecatedProperty('length', 'size')\n    return this.size\n  }\n\n  static get AbortController() {\n    return AC\n  }\n  static get AbortSignal() {\n    return AS\n  }\n}\n\nmodule.exports = LRUCache\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/lru-cache/index.js\n");

/***/ })

};
;