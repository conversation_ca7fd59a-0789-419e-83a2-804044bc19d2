(()=>{var e={};e.id=6388,e.ids=[6388],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},76996:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>u,tree:()=>d}),a(62503),a(30829),a(35866);var s=a(23191),r=a(88716),i=a(37922),l=a.n(i),n=a(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(t,c);let d=["",{children:["osint",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,62503)),"D:\\Users\\Downloads\\kodeXGuard\\app\\osint\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,35866,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\Users\\Downloads\\kodeXGuard\\app\\osint\\page.tsx"],m="/osint/page",x={require:a,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/osint/page",pathname:"/osint",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},62521:(e,t,a)=>{Promise.resolve().then(a.bind(a,19355))},27564:(e,t,a)=>{"use strict";a.d(t,{EZ:()=>d,Gr:()=>n,Vb:()=>c,f_:()=>l});var s=a(17577),r=a(67382);function i(e,t={immediate:!0}){let[a,i]=(0,s.useState)(null),[l,n]=(0,s.useState)(!1),[c,d]=(0,s.useState)(null),{token:o,isAuthenticated:m}=(0,r.a)();return{data:a,loading:l,error:c,refetch:async()=>{if(!m||!o){d("Authentication required");return}try{n(!0),d(null);let t=await fetch(e,{headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json"}}),a=await t.json();a.success&&a.data?i(a.data):d(a.error||"Failed to fetch data")}catch(e){d(e instanceof Error?e.message:"Network error")}finally{n(!1)}},mutate:async(t="POST",a)=>{if(!m||!o)throw Error("Authentication required");try{n(!0),d(null);let s=await fetch(e,{method:t,headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json"},body:a?JSON.stringify(a):void 0}),r=await s.json();if(r.success)return r.data&&i(r.data),r;throw Error(r.error||"Request failed")}catch(t){let e=t instanceof Error?t.message:"Network error";throw d(e),Error(e)}finally{n(!1)}}}}function l(){return i("/api/dashboard/stats")}function n(){return i("/api/user/profile")}function c(){return i("/api/osint/recent")}function d(){return i("/api/osint/templates")}},19355:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>w});var s=a(10326),r=a(17577),i=a(83061),l=a(2262),n=a(5932),c=a(42887),d=a(28916),o=a(79635),m=a(54014),x=a(77636),u=a(54659),h=a(91470),p=a(37202),y=a(48998),g=a(88307),b=a(88319),j=a(21405);let f=(0,a(76557).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);var v=a(31540),N=a(67382),k=a(27564);function w(){let{user:e,token:t,isAuthenticated:a}=(0,N.a)(),{data:w,loading:Z,refetch:S}=(0,k.Vb)(),{data:D,loading:P}=(0,k.EZ)(),[M,A]=(0,r.useState)("email"),[E,U]=(0,r.useState)(""),[I,_]=(0,r.useState)(!1),[q,C]=(0,r.useState)([]),[G,L]=(0,r.useState)(!1),[O,R]=(0,r.useState)(!0),[T,z]=(0,r.useState)({totalSearches:1247,successfulFinds:892,dataSourcesActive:15,lastUpdate:"2 minutes ago"}),H=[{id:"email",name:"Email Address",icon:n.Z,placeholder:"<EMAIL>"},{id:"phone",name:"Phone Number",icon:c.Z,placeholder:"+62812345678"},{id:"nik",name:"NIK",icon:d.Z,placeholder:"1234567890123456"},{id:"npwp",name:"NPWP",icon:d.Z,placeholder:"12.345.678.9-012.345"},{id:"name",name:"Full Name",icon:o.Z,placeholder:"John Doe"},{id:"domain",name:"Domain",icon:m.Z,placeholder:"example.com"},{id:"imei",name:"IMEI",icon:c.Z,placeholder:"123456789012345"},{id:"address",name:"Address",icon:x.Z,placeholder:"Jakarta, Indonesia"}],J=async()=>{if(E.trim()){_(!0),C([]);try{await new Promise(e=>setTimeout(e,2e3));let e=[{id:"1",type:M,source:"Dukcapil Database",data:{name:"John Doe",email:E,phone:"+62812345678",address:"Jakarta, Indonesia",verified:!0},confidence:95,timestamp:new Date().toISOString(),status:"found"},{id:"2",type:M,source:"Social Media",data:{platform:"LinkedIn",profile:"https://linkedin.com/in/johndoe",company:"Tech Corp",location:"Jakarta"},confidence:87,timestamp:new Date().toISOString(),status:"found"},{id:"3",type:M,source:"GitHub",data:{username:"johndoe",repositories:15,followers:42,email_exposed:!0},confidence:78,timestamp:new Date().toISOString(),status:"found"}];C(e)}catch(e){console.error("Search failed:",e)}finally{_(!1)}}},F=e=>e>=90?"text-green-400":e>=70?"text-yellow-400":"text-red-400",$=e=>{switch(e){case"found":return u.Z;case"not_found":return h.Z;case"error":return p.Z;default:return y.Z}};return s.jsx(i.Z,{user:e,title:"OSINT Investigator",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[s.jsx(g.Z,{className:"h-8 w-8 text-cyber-green"}),(0,s.jsxs)("h1",{className:"text-3xl font-bold font-cyber text-white",children:["OSINT ",s.jsx("span",{className:"cyber-text",children:"Investigator"})]})]}),s.jsx("p",{className:"text-gray-400 max-w-3xl",children:"Investigasi mendalam dengan pencarian NIK, NPWP, nomor HP, email, domain, dan tracking lokasi real-time. Akses database leaked Dukcapil, Kemkes, GitHub dan sumber data lainnya."})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[s.jsx(l.Rm,{title:"Total Searches",value:T.totalSearches,icon:g.Z,color:"green",trend:{value:12,isPositive:!0}}),s.jsx(l.Rm,{title:"Successful Finds",value:T.successfulFinds,icon:u.Z,color:"blue",trend:{value:8,isPositive:!0}}),s.jsx(l.Rm,{title:"Data Sources",value:T.dataSourcesActive,icon:b.Z,color:"purple"}),s.jsx(l.Rm,{title:"Last Update",value:T.lastUpdate,icon:y.Z,color:"gold"})]}),(0,s.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,s.jsxs)("div",{className:"lg:col-span-2",children:[s.jsx(l.Zb,{border:"green",glow:!0,children:(0,s.jsxs)("div",{className:"p-6",children:[s.jsx("h2",{className:"text-xl font-bold text-white mb-6",children:"Search Configuration"}),(0,s.jsxs)("div",{className:"mb-6",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Search Type"}),s.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:H.map(e=>{let t=e.icon;return(0,s.jsxs)("button",{onClick:()=>A(e.id),className:`p-3 rounded-lg border transition-all duration-200 ${M===e.id?"border-cyber-green bg-cyber-green/10 text-cyber-green":"border-gray-600 hover:border-gray-500 text-gray-300"}`,children:[s.jsx(t,{className:"h-5 w-5 mx-auto mb-2"}),s.jsx("div",{className:"text-xs font-medium",children:e.name})]},e.id)})})]}),(0,s.jsxs)("div",{className:"mb-6",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Search Query"}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[s.jsx("input",{type:"text",value:E,onChange:e=>U(e.target.value),placeholder:H.find(e=>e.id===M)?.placeholder,className:"cyber-input flex-1",onKeyPress:e=>"Enter"===e.key&&J()}),s.jsx("button",{onClick:J,disabled:I||!E.trim(),className:"cyber-btn-primary px-6 disabled:opacity-50 disabled:cursor-not-allowed",children:I?s.jsx(j.Z,{className:"h-5 w-5 animate-spin"}):s.jsx(g.Z,{className:"h-5 w-5"})})]})]}),s.jsx("div",{className:"border-t border-gray-700 pt-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[s.jsx("input",{type:"checkbox",className:"rounded bg-gray-800 border-gray-600"}),s.jsx("span",{className:"text-sm text-gray-300",children:"Deep Search"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[s.jsx("input",{type:"checkbox",className:"rounded bg-gray-800 border-gray-600"}),s.jsx("span",{className:"text-sm text-gray-300",children:"Include Social Media"})]})]}),(0,s.jsxs)("button",{className:"text-sm text-cyber-green hover:text-cyber-blue transition-colors",children:[s.jsx(f,{className:"h-4 w-4 inline mr-1"}),"More Filters"]})]})})]})}),q.length>0&&s.jsx(l.Zb,{className:"mt-6",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsxs)("h3",{className:"text-lg font-bold text-white",children:["Search Results (",q.length,")"]}),(0,s.jsxs)("button",{onClick:()=>{let e=new Blob([JSON.stringify(q,null,2)],{type:"application/json"}),t=URL.createObjectURL(e),a=document.createElement("a");a.href=t,a.download=`osint-results-${Date.now()}.json`,a.click()},className:"cyber-btn text-sm",children:[s.jsx(v.Z,{className:"h-4 w-4 mr-2"}),"Export"]})]}),s.jsx("div",{className:"space-y-4",children:q.map(e=>{let t=$(e.status);return(0,s.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-4 border border-gray-700",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[s.jsx(t,{className:`h-5 w-5 ${"found"===e.status?"text-green-400":"not_found"===e.status?"text-red-400":"text-yellow-400"}`}),(0,s.jsxs)("div",{children:[s.jsx("h4",{className:"font-semibold text-white",children:e.source}),s.jsx("p",{className:"text-sm text-gray-400",children:e.type})]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("div",{className:`text-sm font-semibold ${F(e.confidence)}`,children:[e.confidence,"% confidence"]}),s.jsx("div",{className:"text-xs text-gray-500",children:new Date(e.timestamp).toLocaleString()})]})]}),s.jsx("div",{className:"bg-gray-900/50 rounded p-3",children:s.jsx("pre",{className:"text-sm text-gray-300 whitespace-pre-wrap",children:JSON.stringify(e.data,null,2)})})]},e.id)})})]})}),I&&s.jsx(l.Zb,{className:"mt-6",children:(0,s.jsxs)("div",{className:"p-8 text-center",children:[s.jsx(j.Z,{className:"h-8 w-8 text-cyber-green animate-spin mx-auto mb-4"}),s.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"Searching..."}),s.jsx("p",{className:"text-gray-400",children:"Scanning multiple data sources for information"})]})})]}),(0,s.jsxs)("div",{children:[s.jsx(l.Zb,{children:(0,s.jsxs)("div",{className:"p-6",children:[s.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Data Sources"}),s.jsx("div",{className:"space-y-3",children:[{name:"Dukcapil Database",status:"active",lastUpdate:"1 hour ago"},{name:"Kemkes Database",status:"active",lastUpdate:"30 minutes ago"},{name:"GitHub Leaked Data",status:"active",lastUpdate:"5 minutes ago"},{name:"Social Media APIs",status:"active",lastUpdate:"2 minutes ago"},{name:"Domain WHOIS",status:"active",lastUpdate:"1 minute ago"},{name:"Phone Number DB",status:"maintenance",lastUpdate:"2 hours ago"},{name:"Email Breach DB",status:"active",lastUpdate:"10 minutes ago"},{name:"Location Services",status:"active",lastUpdate:"3 minutes ago"}].map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-800/50 rounded-lg",children:[(0,s.jsxs)("div",{children:[s.jsx("div",{className:"font-medium text-white text-sm",children:e.name}),s.jsx("div",{className:"text-xs text-gray-400",children:e.lastUpdate})]}),s.jsx("div",{className:`px-2 py-1 rounded-full text-xs font-semibold ${"active"===e.status?"bg-green-900/50 text-green-400":"bg-yellow-900/50 text-yellow-400"}`,children:e.status})]},t))})]})}),s.jsx(l.Zb,{className:"mt-6",border:"gold",children:(0,s.jsxs)("div",{className:"p-6",children:[s.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Plan Usage"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-400",children:"Daily Searches"}),s.jsx("span",{className:"text-white",children:"Unlimited"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-400",children:"Deep Search"}),s.jsx("span",{className:"text-cyber-green",children:"✓ Enabled"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-400",children:"Export Results"}),s.jsx("span",{className:"text-cyber-green",children:"✓ Enabled"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-400",children:"API Access"}),s.jsx("span",{className:"text-cyber-green",children:"✓ Enabled"})]})]})]})})]})]})]})})}},37202:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},54659:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},48998:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},28916:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},88319:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},31540:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},54014:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},5932:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},77636:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},42887:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},21405:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},88307:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},79635:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},91470:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(76557).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},62503:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(68570).createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\osint\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[9276,82,7608,3061],()=>a(76996));module.exports=s})();