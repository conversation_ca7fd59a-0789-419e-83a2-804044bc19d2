'use client'

import { useState, useEffect } from 'react'
import Navbar from '@/components/Navbar'
import { Card, StatCard, AlertCard } from '@/components/Card'
import { 
  Shield, 
  Bug, 
  AlertTriangle, 
  Target,
  Play,
  Pause,
  Square,
  Download,
  Eye,
  Clock,
  CheckCircle,
  XCircle,
  RefreshCw,
  Settings,
  BarChart3,
  Globe,
  Database
} from 'lucide-react'

interface ScanResult {
  id: string
  type: string
  severity: 'info' | 'low' | 'medium' | 'high' | 'critical'
  title: string
  description: string
  url: string
  method?: string
  parameter?: string
  payload?: string
  evidence: string
  cveId?: string
  cvssScore?: number
  recommendation: string
  timestamp: string
}

interface ScanConfig {
  target: string
  scanTypes: string[]
  maxDepth: number
  timeout: number
  userAgent: string
  followRedirects: boolean
  checkSSL: boolean
}

export default function ScannerPage() {
  const [user] = useState({
    username: 'admin',
    avatar: '',
    role: 'super_admin',
    plan: 'cybersecurity'
  })

  const [scanConfig, setScanConfig] = useState<ScanConfig>({
    target: '',
    scanTypes: ['sqli', 'xss', 'lfi'],
    maxDepth: 3,
    timeout: 30,
    userAgent: 'KodeXGuard Scanner 1.0',
    followRedirects: true,
    checkSSL: true
  })

  const [scanning, setScanning] = useState(false)
  const [scanProgress, setScanProgress] = useState(0)
  const [results, setResults] = useState<ScanResult[]>([])
  const [stats, setStats] = useState({
    totalScans: 456,
    vulnerabilitiesFound: 89,
    criticalIssues: 12,
    lastScan: '1 hour ago'
  })

  const vulnerabilityTypes = [
    { id: 'sqli', name: 'SQL Injection', description: 'Database injection attacks' },
    { id: 'xss', name: 'Cross-Site Scripting', description: 'Client-side code injection' },
    { id: 'lfi', name: 'Local File Inclusion', description: 'File system access vulnerabilities' },
    { id: 'rfi', name: 'Remote File Inclusion', description: 'Remote code execution via file inclusion' },
    { id: 'rce', name: 'Remote Code Execution', description: 'Server-side code execution' },
    { id: 'csrf', name: 'Cross-Site Request Forgery', description: 'Unauthorized request execution' },
    { id: 'ssrf', name: 'Server-Side Request Forgery', description: 'Internal network access' },
    { id: 'xxe', name: 'XML External Entity', description: 'XML parser vulnerabilities' },
    { id: 'idor', name: 'Insecure Direct Object Reference', description: 'Access control bypass' },
    { id: 'path_traversal', name: 'Path Traversal', description: 'Directory traversal attacks' }
  ]

  const severityColors = {
    info: 'text-blue-400 bg-blue-900/20',
    low: 'text-green-400 bg-green-900/20',
    medium: 'text-yellow-400 bg-yellow-900/20',
    high: 'text-orange-400 bg-orange-900/20',
    critical: 'text-red-400 bg-red-900/20'
  }

  const startScan = async () => {
    if (!scanConfig.target.trim()) return

    setScanning(true)
    setScanProgress(0)
    setResults([])

    // Simulate scanning progress
    const progressInterval = setInterval(() => {
      setScanProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval)
          return 100
        }
        return prev + Math.random() * 10
      })
    }, 500)

    try {
      // Simulate scan results
      await new Promise(resolve => setTimeout(resolve, 8000))

      const mockResults: ScanResult[] = [
        {
          id: '1',
          type: 'SQL Injection',
          severity: 'critical',
          title: 'SQL Injection in login form',
          description: 'The login form is vulnerable to SQL injection attacks through the username parameter.',
          url: `${scanConfig.target}/login.php`,
          method: 'POST',
          parameter: 'username',
          payload: "admin' OR '1'='1",
          evidence: 'MySQL error: You have an error in your SQL syntax',
          cveId: 'CVE-2023-1234',
          cvssScore: 9.8,
          recommendation: 'Use parameterized queries and input validation.',
          timestamp: new Date().toISOString()
        },
        {
          id: '2',
          type: 'Cross-Site Scripting',
          severity: 'high',
          title: 'Reflected XSS in search parameter',
          description: 'User input is reflected in the page without proper sanitization.',
          url: `${scanConfig.target}/search.php`,
          method: 'GET',
          parameter: 'q',
          payload: '<script>alert("XSS")</script>',
          evidence: 'Script tag executed in browser',
          cvssScore: 7.5,
          recommendation: 'Implement proper input sanitization and output encoding.',
          timestamp: new Date().toISOString()
        },
        {
          id: '3',
          type: 'Local File Inclusion',
          severity: 'medium',
          title: 'LFI vulnerability in file parameter',
          description: 'The application allows reading arbitrary files from the server.',
          url: `${scanConfig.target}/view.php`,
          method: 'GET',
          parameter: 'file',
          payload: '../../../etc/passwd',
          evidence: 'root:x:0:0:root:/root:/bin/bash',
          cvssScore: 6.5,
          recommendation: 'Validate and sanitize file paths, use whitelist approach.',
          timestamp: new Date().toISOString()
        }
      ]

      setResults(mockResults)
    } catch (error) {
      console.error('Scan failed:', error)
    } finally {
      setScanning(false)
      clearInterval(progressInterval)
      setScanProgress(100)
    }
  }

  const stopScan = () => {
    setScanning(false)
    setScanProgress(0)
  }

  const exportResults = () => {
    const dataStr = JSON.stringify(results, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `vulnerability-scan-${Date.now()}.json`
    link.click()
  }

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return AlertTriangle
      case 'high': return AlertTriangle
      case 'medium': return Bug
      case 'low': return CheckCircle
      default: return CheckCircle
    }
  }

  return (
    <div className="min-h-screen bg-gradient-cyber">
      <Navbar user={user} />
      
      <div className="pt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <Shield className="h-8 w-8 text-cyber-green" />
              <h1 className="text-3xl font-bold font-cyber text-white">
                Vulnerability <span className="cyber-text">Scanner</span>
              </h1>
            </div>
            <p className="text-gray-400 max-w-3xl">
              Deteksi SQLi, XSS, LFI, RCE, Path Traversal, CSRF dengan scoring CVSS dan mapping CVE otomatis. 
              Simpan riwayat scan dengan auto versioning hasil.
            </p>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <StatCard
              title="Total Scans"
              value={stats.totalScans}
              icon={Target}
              color="green"
              trend={{ value: 15, isPositive: true }}
            />
            <StatCard
              title="Vulnerabilities Found"
              value={stats.vulnerabilitiesFound}
              icon={Bug}
              color="red"
              trend={{ value: 8, isPositive: true }}
            />
            <StatCard
              title="Critical Issues"
              value={stats.criticalIssues}
              icon={AlertTriangle}
              color="purple"
              trend={{ value: -12, isPositive: false }}
            />
            <StatCard
              title="Last Scan"
              value={stats.lastScan}
              icon={Clock}
              color="blue"
            />
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Scan Configuration */}
            <div className="lg:col-span-2">
              <Card border="green" glow>
                <div className="p-6">
                  <h2 className="text-xl font-bold text-white mb-6">Scan Configuration</h2>
                  
                  {/* Target URL */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Target URL
                    </label>
                    <div className="flex space-x-3">
                      <input
                        type="url"
                        value={scanConfig.target}
                        onChange={(e) => setScanConfig(prev => ({ ...prev, target: e.target.value }))}
                        placeholder="https://example.com"
                        className="cyber-input flex-1"
                      />
                      <button className="cyber-btn">
                        <Globe className="h-5 w-5" />
                      </button>
                    </div>
                  </div>

                  {/* Vulnerability Types */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-300 mb-3">
                      Vulnerability Types
                    </label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {vulnerabilityTypes.map((type) => (
                        <label
                          key={type.id}
                          className={`cursor-pointer border rounded-lg p-3 transition-all duration-200 ${
                            scanConfig.scanTypes.includes(type.id)
                              ? 'border-cyber-green bg-cyber-green/10'
                              : 'border-gray-600 hover:border-gray-500'
                          }`}
                        >
                          <input
                            type="checkbox"
                            checked={scanConfig.scanTypes.includes(type.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setScanConfig(prev => ({
                                  ...prev,
                                  scanTypes: [...prev.scanTypes, type.id]
                                }))
                              } else {
                                setScanConfig(prev => ({
                                  ...prev,
                                  scanTypes: prev.scanTypes.filter(t => t !== type.id)
                                }))
                              }
                            }}
                            className="sr-only"
                          />
                          <div className="text-sm font-medium text-white">{type.name}</div>
                          <div className="text-xs text-gray-400 mt-1">{type.description}</div>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Advanced Settings */}
                  <div className="border-t border-gray-700 pt-6">
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          Max Depth
                        </label>
                        <input
                          type="number"
                          value={scanConfig.maxDepth}
                          onChange={(e) => setScanConfig(prev => ({ ...prev, maxDepth: parseInt(e.target.value) }))}
                          className="cyber-input w-full"
                          min="1"
                          max="10"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          Timeout (seconds)
                        </label>
                        <input
                          type="number"
                          value={scanConfig.timeout}
                          onChange={(e) => setScanConfig(prev => ({ ...prev, timeout: parseInt(e.target.value) }))}
                          className="cyber-input w-full"
                          min="5"
                          max="300"
                        />
                      </div>
                    </div>

                    <div className="mt-4 space-y-3">
                      <label className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={scanConfig.followRedirects}
                          onChange={(e) => setScanConfig(prev => ({ ...prev, followRedirects: e.target.checked }))}
                          className="rounded bg-gray-800 border-gray-600"
                        />
                        <span className="text-sm text-gray-300">Follow Redirects</span>
                      </label>
                      <label className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={scanConfig.checkSSL}
                          onChange={(e) => setScanConfig(prev => ({ ...prev, checkSSL: e.target.checked }))}
                          className="rounded bg-gray-800 border-gray-600"
                        />
                        <span className="text-sm text-gray-300">Verify SSL Certificate</span>
                      </label>
                    </div>
                  </div>

                  {/* Scan Controls */}
                  <div className="border-t border-gray-700 pt-6 mt-6">
                    <div className="flex items-center justify-between">
                      <div className="flex space-x-3">
                        {!scanning ? (
                          <button
                            onClick={startScan}
                            disabled={!scanConfig.target.trim() || scanConfig.scanTypes.length === 0}
                            className="cyber-btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            <Play className="h-5 w-5 mr-2" />
                            Start Scan
                          </button>
                        ) : (
                          <button
                            onClick={stopScan}
                            className="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-semibold transition-colors"
                          >
                            <Square className="h-5 w-5 mr-2" />
                            Stop Scan
                          </button>
                        )}
                        <button className="cyber-btn">
                          <Settings className="h-5 w-5 mr-2" />
                          Advanced
                        </button>
                      </div>
                      {results.length > 0 && (
                        <button
                          onClick={exportResults}
                          className="cyber-btn"
                        >
                          <Download className="h-5 w-5 mr-2" />
                          Export
                        </button>
                      )}
                    </div>

                    {/* Progress Bar */}
                    {scanning && (
                      <div className="mt-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-gray-300">Scanning Progress</span>
                          <span className="text-sm text-cyber-green">{Math.round(scanProgress)}%</span>
                        </div>
                        <div className="w-full bg-gray-700 rounded-full h-2">
                          <div
                            className="bg-cyber-green h-2 rounded-full transition-all duration-300"
                            style={{ width: `${scanProgress}%` }}
                          ></div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </Card>

              {/* Results */}
              {results.length > 0 && (
                <Card className="mt-6">
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-white mb-6">
                      Scan Results ({results.length} vulnerabilities found)
                    </h3>

                    <div className="space-y-4">
                      {results.map((result) => {
                        const SeverityIcon = getSeverityIcon(result.severity)
                        return (
                          <div
                            key={result.id}
                            className="bg-gray-800/50 rounded-lg p-4 border border-gray-700"
                          >
                            <div className="flex items-start justify-between mb-3">
                              <div className="flex items-center space-x-3">
                                <SeverityIcon className={`h-5 w-5 ${severityColors[result.severity].split(' ')[0]}`} />
                                <div>
                                  <h4 className="font-semibold text-white">{result.title}</h4>
                                  <p className="text-sm text-gray-400">{result.type}</p>
                                </div>
                              </div>
                              <div className="text-right">
                                <span className={`px-2 py-1 rounded-full text-xs font-semibold ${severityColors[result.severity]}`}>
                                  {result.severity.toUpperCase()}
                                </span>
                                {result.cvssScore && (
                                  <div className="text-sm text-gray-400 mt-1">
                                    CVSS: {result.cvssScore}
                                  </div>
                                )}
                              </div>
                            </div>

                            <p className="text-gray-300 mb-3">{result.description}</p>

                            <div className="grid md:grid-cols-2 gap-4 text-sm">
                              <div>
                                <span className="text-gray-400">URL:</span>
                                <span className="text-white ml-2">{result.url}</span>
                              </div>
                              {result.parameter && (
                                <div>
                                  <span className="text-gray-400">Parameter:</span>
                                  <span className="text-white ml-2">{result.parameter}</span>
                                </div>
                              )}
                              {result.payload && (
                                <div className="md:col-span-2">
                                  <span className="text-gray-400">Payload:</span>
                                  <code className="text-cyber-green ml-2 bg-gray-900 px-2 py-1 rounded">
                                    {result.payload}
                                  </code>
                                </div>
                              )}
                            </div>

                            <div className="mt-4 p-3 bg-gray-900/50 rounded">
                              <div className="text-sm text-gray-400 mb-1">Recommendation:</div>
                              <div className="text-sm text-gray-300">{result.recommendation}</div>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                </Card>
              )}
            </div>

            {/* Scan History & Info */}
            <div>
              <Card>
                <div className="p-6">
                  <h3 className="text-lg font-bold text-white mb-4">Recent Scans</h3>
                  <div className="space-y-3">
                    {[
                      { target: 'example.com', vulnerabilities: 3, severity: 'high', time: '2 hours ago' },
                      { target: 'test.local', vulnerabilities: 1, severity: 'medium', time: '1 day ago' },
                      { target: 'demo.site', vulnerabilities: 0, severity: 'info', time: '2 days ago' }
                    ].map((scan, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                        <div>
                          <div className="font-medium text-white text-sm">{scan.target}</div>
                          <div className="text-xs text-gray-400">{scan.time}</div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-white">{scan.vulnerabilities} issues</div>
                          <div className={`text-xs ${
                            scan.severity === 'high' ? 'text-red-400' :
                            scan.severity === 'medium' ? 'text-yellow-400' :
                            'text-green-400'
                          }`}>
                            {scan.severity}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </Card>

              <Card className="mt-6" border="gold">
                <div className="p-6">
                  <h3 className="text-lg font-bold text-white mb-4">Plan Limits</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Daily Scans</span>
                      <span className="text-white">Unlimited</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Concurrent Scans</span>
                      <span className="text-white">10</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Max Depth</span>
                      <span className="text-white">10 levels</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Export Results</span>
                      <span className="text-cyber-green">✓ Enabled</span>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
