"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/dashboard/stats/route";
exports.ids = ["app/api/dashboard/stats/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Users_Downloads_kodeXGuard_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/dashboard/stats/route.ts */ \"(rsc)/./app/api/dashboard/stats/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/dashboard/stats/route\",\n        pathname: \"/api/dashboard/stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/dashboard/stats/route\"\n    },\n    resolvedPagePath: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\api\\\\dashboard\\\\stats\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Users_Downloads_kodeXGuard_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/dashboard/stats/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/dashboard/stats/route.ts":
/*!******************************************!*\
  !*** ./app/api/dashboard/stats/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function GET(request) {\n    try {\n        // Get auth token\n        const authHeader = request.headers.get(\"authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        // Simple token validation for testing\n        if (!token.startsWith(\"kxg_\")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid token\"\n            }, {\n                status: 401\n            });\n        }\n        // Mock dashboard statistics\n        const stats = {\n            overview: {\n                totalScans: 1247,\n                activeScans: 3,\n                vulnerabilitiesFound: 156,\n                osintSearches: 892\n            },\n            vulnerabilities: {\n                critical: 12,\n                high: 34,\n                medium: 67,\n                low: 89,\n                info: 23\n            },\n            scanActivity: {\n                today: 15,\n                thisWeek: 89,\n                thisMonth: 234,\n                lastMonth: 198\n            },\n            osintActivity: {\n                today: 23,\n                thisWeek: 156,\n                thisMonth: 445,\n                lastMonth: 378\n            },\n            topTargets: [\n                {\n                    target: \"https://example.com\",\n                    scans: 45,\n                    lastScan: new Date(Date.now() - 3600000).toISOString(),\n                    status: \"vulnerable\"\n                },\n                {\n                    target: \"https://testsite.com\",\n                    scans: 32,\n                    lastScan: new Date(Date.now() - 7200000).toISOString(),\n                    status: \"secure\"\n                },\n                {\n                    target: \"https://webapp.local\",\n                    scans: 28,\n                    lastScan: new Date(Date.now() - 1800000).toISOString(),\n                    status: \"critical\"\n                },\n                {\n                    target: \"https://api.example.com\",\n                    scans: 21,\n                    lastScan: new Date(Date.now() - 10800000).toISOString(),\n                    status: \"vulnerable\"\n                },\n                {\n                    target: \"https://mobile-app.com\",\n                    scans: 18,\n                    lastScan: new Date(Date.now() - 14400000).toISOString(),\n                    status: \"secure\"\n                }\n            ],\n            recentActivity: [\n                {\n                    id: \"activity_001\",\n                    type: \"scan\",\n                    title: \"Vulnerability Scan Completed\",\n                    description: \"Scan of https://example.com found 5 vulnerabilities\",\n                    timestamp: new Date(Date.now() - 1800000).toISOString(),\n                    status: \"warning\"\n                },\n                {\n                    id: \"activity_002\",\n                    type: \"osint\",\n                    title: \"OSINT Search Completed\",\n                    description: \"Email <NAME_EMAIL> returned 3 results\",\n                    timestamp: new Date(Date.now() - 3600000).toISOString(),\n                    status: \"success\"\n                },\n                {\n                    id: \"activity_003\",\n                    type: \"alert\",\n                    title: \"Critical Vulnerability Detected\",\n                    description: \"SQL injection vulnerability found in login form\",\n                    timestamp: new Date(Date.now() - 5400000).toISOString(),\n                    status: \"error\"\n                },\n                {\n                    id: \"activity_004\",\n                    type: \"scan\",\n                    title: \"Scan Started\",\n                    description: \"Comprehensive security scan initiated for https://webapp.local\",\n                    timestamp: new Date(Date.now() - 7200000).toISOString(),\n                    status: \"info\"\n                },\n                {\n                    id: \"activity_005\",\n                    type: \"osint\",\n                    title: \"Phone Number Investigation\",\n                    description: \"Phone lookup for +628123456789 completed successfully\",\n                    timestamp: new Date(Date.now() - 9000000).toISOString(),\n                    status: \"success\"\n                },\n                {\n                    id: \"activity_006\",\n                    type: \"alert\",\n                    title: \"Quota Warning\",\n                    description: \"Daily OSINT quota 80% used (40/50 searches)\",\n                    timestamp: new Date(Date.now() - 10800000).toISOString(),\n                    status: \"warning\"\n                }\n            ],\n            systemHealth: {\n                scannerStatus: \"online\",\n                osintStatus: \"online\",\n                databaseStatus: \"online\",\n                apiStatus: \"online\"\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: stats,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"Dashboard stats API error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/dashboard/stats/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();