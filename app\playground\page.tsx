'use client'

import { useState, useEffect } from 'react'
import DashboardLayout from '@/components/DashboardLayout'
import { Card, StatCard, AlertCard } from '@/components/Card'
import { 
  Code, 
  Play, 
  Save, 
  Copy,
  Download,
  BookOpen,
  Settings,
  Key,
  Globe,
  Database,
  Zap,
  RefreshCw,
  CheckCircle,
  XCircle,
  Clock,
  Star,
  Trash2,
  Edit
} from 'lucide-react'

interface APIEndpoint {
  id: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE'
  path: string
  description: string
  category: string
  requiresAuth: boolean
  parameters: Array<{
    name: string
    type: string
    required: boolean
    description: string
  }>
  example: {
    request: any
    response: any
  }
}

interface SavedRequest {
  id: string
  name: string
  method: string
  url: string
  headers: Record<string, string>
  body: string
  createdAt: string
  favorite: boolean
}

export default function PlaygroundPage() {
  const [user] = useState({
    username: 'admin',
    avatar: '',
    role: 'super_admin',
    plan: 'cybersecurity'
  })

  const [selectedEndpoint, setSelectedEndpoint] = useState<APIEndpoint | null>(null)
  const [requestMethod, setRequestMethod] = useState('GET')
  const [requestUrl, setRequestUrl] = useState('')
  const [requestHeaders, setRequestHeaders] = useState('{\n  "Authorization": "Bearer your-api-key",\n  "Content-Type": "application/json"\n}')
  const [requestBody, setRequestBody] = useState('{}')
  const [response, setResponse] = useState('')
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('playground')
  const [savedRequests, setSavedRequests] = useState<SavedRequest[]>([])

  const apiEndpoints: APIEndpoint[] = [
    {
      id: '1',
      method: 'POST',
      path: '/api/auth/login',
      description: 'Authenticate user and get access token',
      category: 'Authentication',
      requiresAuth: false,
      parameters: [
        { name: 'email', type: 'string', required: true, description: 'User email address' },
        { name: 'password', type: 'string', required: true, description: 'User password' }
      ],
      example: {
        request: { email: '<EMAIL>', password: 'admin123' },
        response: { token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...', user: { id: '1', username: 'admin', role: 'super_admin' } }
      }
    },
    {
      id: '2',
      method: 'GET',
      path: '/api/user/profile',
      description: 'Get current user profile information',
      category: 'User',
      requiresAuth: true,
      parameters: [],
      example: {
        request: {},
        response: { id: '1', username: 'admin', email: '<EMAIL>', plan: 'cybersecurity', stats: { totalScans: 1247 } }
      }
    },
    {
      id: '3',
      method: 'POST',
      path: '/api/scan/vulnerability',
      description: 'Start a new vulnerability scan',
      category: 'Scanning',
      requiresAuth: true,
      parameters: [
        { name: 'target', type: 'string', required: true, description: 'Target URL to scan' },
        { name: 'scanTypes', type: 'array', required: true, description: 'Types of vulnerabilities to scan for' },
        { name: 'maxDepth', type: 'number', required: false, description: 'Maximum crawl depth (default: 3)' }
      ],
      example: {
        request: { target: 'https://example.com', scanTypes: ['sqli', 'xss', 'lfi'], maxDepth: 3 },
        response: { scanId: 'scan_123456', status: 'started', estimatedTime: '5-10 minutes' }
      }
    },
    {
      id: '4',
      method: 'GET',
      path: '/api/scan/{scanId}/results',
      description: 'Get vulnerability scan results',
      category: 'Scanning',
      requiresAuth: true,
      parameters: [
        { name: 'scanId', type: 'string', required: true, description: 'Scan ID from scan initiation' }
      ],
      example: {
        request: {},
        response: { scanId: 'scan_123456', status: 'completed', vulnerabilities: [{ type: 'sqli', severity: 'high', url: 'https://example.com/login' }] }
      }
    },
    {
      id: '5',
      method: 'POST',
      path: '/api/osint/search',
      description: 'Perform OSINT investigation',
      category: 'OSINT',
      requiresAuth: true,
      parameters: [
        { name: 'query', type: 'string', required: true, description: 'Search query (email, phone, domain, etc.)' },
        { name: 'type', type: 'string', required: true, description: 'Search type (email, phone, nik, domain, etc.)' },
        { name: 'deepSearch', type: 'boolean', required: false, description: 'Enable deep search (default: false)' }
      ],
      example: {
        request: { query: '<EMAIL>', type: 'email', deepSearch: true },
        response: { results: [{ source: 'Dukcapil', data: { name: 'John Doe', verified: true }, confidence: 95 }] }
      }
    },
    {
      id: '6',
      method: 'POST',
      path: '/api/file/analyze',
      description: 'Analyze uploaded file for threats',
      category: 'File Analysis',
      requiresAuth: true,
      parameters: [
        { name: 'file', type: 'file', required: true, description: 'File to analyze (multipart/form-data)' },
        { name: 'analysisType', type: 'string', required: false, description: 'Type of analysis (malware, webshell, secret, general)' }
      ],
      example: {
        request: { file: 'suspicious.php', analysisType: 'webshell' },
        response: { analysisId: 'analysis_789', threatLevel: 'suspicious', results: { webshellDetected: true, type: 'PHP Webshell' } }
      }
    },
    {
      id: '7',
      method: 'GET',
      path: '/api/cve/search',
      description: 'Search CVE database',
      category: 'CVE Intelligence',
      requiresAuth: true,
      parameters: [
        { name: 'query', type: 'string', required: false, description: 'Search query' },
        { name: 'severity', type: 'string', required: false, description: 'Filter by severity (critical, high, medium, low)' },
        { name: 'year', type: 'string', required: false, description: 'Filter by year' },
        { name: 'limit', type: 'number', required: false, description: 'Number of results (default: 20)' }
      ],
      example: {
        request: { query: 'SQL injection', severity: 'critical', limit: 10 },
        response: { total: 156, cves: [{ cveId: 'CVE-2024-0001', severity: 'critical', cvssScore: 9.8, description: 'SQL injection vulnerability...' }] }
      }
    }
  ]

  const executeRequest = async () => {
    setLoading(true)
    setResponse('')

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))

      const mockResponse = {
        status: 200,
        statusText: 'OK',
        headers: {
          'Content-Type': 'application/json',
          'X-RateLimit-Remaining': '99',
          'X-Response-Time': '142ms'
        },
        data: selectedEndpoint?.example.response || { message: 'Success', timestamp: new Date().toISOString() }
      }

      setResponse(JSON.stringify(mockResponse, null, 2))
    } catch (error) {
      setResponse(JSON.stringify({
        status: 500,
        error: 'Internal Server Error',
        message: 'An error occurred while processing your request'
      }, null, 2))
    } finally {
      setLoading(false)
    }
  }

  const saveRequest = () => {
    const newRequest: SavedRequest = {
      id: Date.now().toString(),
      name: `${requestMethod} ${requestUrl}`,
      method: requestMethod,
      url: requestUrl,
      headers: JSON.parse(requestHeaders),
      body: requestBody,
      createdAt: new Date().toISOString(),
      favorite: false
    }

    setSavedRequests(prev => [...prev, newRequest])
  }

  const loadRequest = (request: SavedRequest) => {
    setRequestMethod(request.method)
    setRequestUrl(request.url)
    setRequestHeaders(JSON.stringify(request.headers, null, 2))
    setRequestBody(request.body)
    setActiveTab('playground')
  }

  const toggleFavorite = (requestId: string) => {
    setSavedRequests(prev => prev.map(req => 
      req.id === requestId ? { ...req, favorite: !req.favorite } : req
    ))
  }

  const deleteRequest = (requestId: string) => {
    setSavedRequests(prev => prev.filter(req => req.id !== requestId))
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  const categories = [...new Set(apiEndpoints.map(ep => ep.category))]

  return (
    <DashboardLayout user={user} title="API Playground">
      <div>
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <Code className="h-8 w-8 text-cyber-green" />
              <h1 className="text-3xl font-bold font-cyber text-white">
                API <span className="cyber-text">Playground</span>
              </h1>
            </div>
            <p className="text-gray-400 max-w-3xl">
              Interactive API testing environment dengan Swagger documentation, request builder, dan endpoint management. 
              Test semua endpoint KodeXGuard dengan real-time response.
            </p>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <StatCard
              title="API Endpoints"
              value={apiEndpoints.length}
              icon={Globe}
              color="green"
            />
            <StatCard
              title="Categories"
              value={categories.length}
              icon={Database}
              color="blue"
            />
            <StatCard
              title="Saved Requests"
              value={savedRequests.length}
              icon={Save}
              color="purple"
            />
            <StatCard
              title="API Calls Today"
              value="1,247"
              icon={Zap}
              color="gold"
            />
          </div>

          {/* Tab Navigation */}
          <div className="mb-8">
            <div className="flex space-x-1 bg-gray-800/50 p-1 rounded-lg">
              {[
                { id: 'playground', name: 'API Playground', icon: Play },
                { id: 'docs', name: 'Documentation', icon: BookOpen },
                { id: 'saved', name: 'Saved Requests', icon: Save }
              ].map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                      activeTab === tab.id
                        ? 'bg-cyber-green text-black'
                        : 'text-gray-300 hover:text-white hover:bg-gray-700/50'
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    <span>{tab.name}</span>
                  </button>
                )
              })}
            </div>
          </div>

          {/* API Playground Tab */}
          {activeTab === 'playground' && (
            <div className="grid lg:grid-cols-4 gap-8">
              {/* Request Builder */}
              <div className="lg:col-span-3">
                <Card border="green" glow>
                  <div className="p-6">
                    <h2 className="text-xl font-bold text-white mb-6">Request Builder</h2>
                    
                    {/* Method and URL */}
                    <div className="grid md:grid-cols-4 gap-4 mb-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">Method</label>
                        <select
                          value={requestMethod}
                          onChange={(e) => setRequestMethod(e.target.value)}
                          className="cyber-input w-full"
                        >
                          <option value="GET">GET</option>
                          <option value="POST">POST</option>
                          <option value="PUT">PUT</option>
                          <option value="DELETE">DELETE</option>
                        </select>
                      </div>
                      <div className="md:col-span-3">
                        <label className="block text-sm font-medium text-gray-300 mb-2">URL</label>
                        <input
                          type="text"
                          value={requestUrl}
                          onChange={(e) => setRequestUrl(e.target.value)}
                          placeholder="https://api.kodexguard.com/v1/endpoint"
                          className="cyber-input w-full"
                        />
                      </div>
                    </div>

                    {/* Headers */}
                    <div className="mb-6">
                      <label className="block text-sm font-medium text-gray-300 mb-2">Headers</label>
                      <textarea
                        value={requestHeaders}
                        onChange={(e) => setRequestHeaders(e.target.value)}
                        className="cyber-input w-full h-24 font-mono text-sm"
                        placeholder='{"Authorization": "Bearer your-api-key"}'
                      />
                    </div>

                    {/* Request Body */}
                    {(requestMethod === 'POST' || requestMethod === 'PUT') && (
                      <div className="mb-6">
                        <label className="block text-sm font-medium text-gray-300 mb-2">Request Body</label>
                        <textarea
                          value={requestBody}
                          onChange={(e) => setRequestBody(e.target.value)}
                          className="cyber-input w-full h-32 font-mono text-sm"
                          placeholder='{"key": "value"}'
                        />
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex space-x-3">
                      <button
                        onClick={executeRequest}
                        disabled={loading || !requestUrl}
                        className="cyber-btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {loading ? (
                          <>
                            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                            Executing...
                          </>
                        ) : (
                          <>
                            <Play className="h-4 w-4 mr-2" />
                            Send Request
                          </>
                        )}
                      </button>
                      <button
                        onClick={saveRequest}
                        disabled={!requestUrl}
                        className="cyber-btn disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <Save className="h-4 w-4 mr-2" />
                        Save
                      </button>
                      <button
                        onClick={() => copyToClipboard(requestUrl)}
                        className="cyber-btn"
                      >
                        <Copy className="h-4 w-4 mr-2" />
                        Copy URL
                      </button>
                    </div>
                  </div>
                </Card>

                {/* Response */}
                {response && (
                  <Card className="mt-6">
                    <div className="p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-bold text-white">Response</h3>
                        <button
                          onClick={() => copyToClipboard(response)}
                          className="cyber-btn text-sm"
                        >
                          <Copy className="h-4 w-4 mr-2" />
                          Copy
                        </button>
                      </div>
                      <div className="bg-gray-900/50 rounded-lg p-4">
                        <pre className="text-sm text-cyber-green whitespace-pre-wrap overflow-x-auto">
                          {response}
                        </pre>
                      </div>
                    </div>
                  </Card>
                )}
              </div>

              {/* API Endpoints List */}
              <div>
                <Card>
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-white mb-4">API Endpoints</h3>
                    <div className="space-y-2">
                      {categories.map(category => (
                        <div key={category}>
                          <h4 className="text-sm font-semibold text-gray-400 mb-2">{category}</h4>
                          {apiEndpoints.filter(ep => ep.category === category).map(endpoint => (
                            <button
                              key={endpoint.id}
                              onClick={() => {
                                setSelectedEndpoint(endpoint)
                                setRequestMethod(endpoint.method)
                                setRequestUrl(`https://api.kodexguard.com/v1${endpoint.path}`)
                                setRequestBody(JSON.stringify(endpoint.example.request, null, 2))
                              }}
                              className={`w-full text-left p-3 rounded-lg border transition-all duration-200 ${
                                selectedEndpoint?.id === endpoint.id
                                  ? 'border-cyber-green bg-cyber-green/10'
                                  : 'border-gray-700 hover:border-gray-600'
                              }`}
                            >
                              <div className="flex items-center justify-between mb-1">
                                <span className={`px-2 py-1 rounded text-xs font-semibold ${
                                  endpoint.method === 'GET' ? 'bg-blue-900/50 text-blue-400' :
                                  endpoint.method === 'POST' ? 'bg-green-900/50 text-green-400' :
                                  endpoint.method === 'PUT' ? 'bg-yellow-900/50 text-yellow-400' :
                                  'bg-red-900/50 text-red-400'
                                }`}>
                                  {endpoint.method}
                                </span>
                                {endpoint.requiresAuth && (
                                  <Key className="h-3 w-3 text-gray-400" />
                                )}
                              </div>
                              <div className="text-sm font-medium text-white">{endpoint.path}</div>
                              <div className="text-xs text-gray-400 mt-1">{endpoint.description}</div>
                            </button>
                          ))}
                        </div>
                      ))}
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          )}

          {/* Documentation Tab */}
          {activeTab === 'docs' && selectedEndpoint && (
            <Card>
              <div className="p-6">
                <div className="flex items-center space-x-3 mb-6">
                  <span className={`px-3 py-1 rounded font-semibold ${
                    selectedEndpoint.method === 'GET' ? 'bg-blue-900/50 text-blue-400' :
                    selectedEndpoint.method === 'POST' ? 'bg-green-900/50 text-green-400' :
                    selectedEndpoint.method === 'PUT' ? 'bg-yellow-900/50 text-yellow-400' :
                    'bg-red-900/50 text-red-400'
                  }`}>
                    {selectedEndpoint.method}
                  </span>
                  <h2 className="text-xl font-bold text-white">{selectedEndpoint.path}</h2>
                  {selectedEndpoint.requiresAuth && (
                    <span className="px-2 py-1 bg-orange-900/50 text-orange-400 rounded text-sm">
                      🔒 Auth Required
                    </span>
                  )}
                </div>

                <p className="text-gray-300 mb-6">{selectedEndpoint.description}</p>

                {/* Parameters */}
                {selectedEndpoint.parameters.length > 0 && (
                  <div className="mb-6">
                    <h3 className="text-lg font-semibold text-white mb-4">Parameters</h3>
                    <div className="overflow-x-auto">
                      <table className="w-full text-sm">
                        <thead>
                          <tr className="border-b border-gray-700">
                            <th className="text-left py-2 text-gray-400">Name</th>
                            <th className="text-left py-2 text-gray-400">Type</th>
                            <th className="text-left py-2 text-gray-400">Required</th>
                            <th className="text-left py-2 text-gray-400">Description</th>
                          </tr>
                        </thead>
                        <tbody>
                          {selectedEndpoint.parameters.map((param, idx) => (
                            <tr key={idx} className="border-b border-gray-800">
                              <td className="py-2 text-white font-mono">{param.name}</td>
                              <td className="py-2 text-cyber-green">{param.type}</td>
                              <td className="py-2">
                                {param.required ? (
                                  <span className="text-red-400">Yes</span>
                                ) : (
                                  <span className="text-gray-400">No</span>
                                )}
                              </td>
                              <td className="py-2 text-gray-300">{param.description}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}

                {/* Example */}
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-4">Example Request</h3>
                    <div className="bg-gray-900/50 rounded-lg p-4">
                      <pre className="text-sm text-gray-300 whitespace-pre-wrap">
                        {JSON.stringify(selectedEndpoint.example.request, null, 2)}
                      </pre>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-4">Example Response</h3>
                    <div className="bg-gray-900/50 rounded-lg p-4">
                      <pre className="text-sm text-cyber-green whitespace-pre-wrap">
                        {JSON.stringify(selectedEndpoint.example.response, null, 2)}
                      </pre>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          )}

          {/* Saved Requests Tab */}
          {activeTab === 'saved' && (
            <Card>
              <div className="p-6">
                <h2 className="text-xl font-bold text-white mb-6">Saved Requests</h2>
                {savedRequests.length === 0 ? (
                  <div className="text-center py-8">
                    <Save className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-white mb-2">No Saved Requests</h3>
                    <p className="text-gray-400">Save your API requests to access them later</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {savedRequests.map((request) => (
                      <div
                        key={request.id}
                        className="bg-gray-800/50 rounded-lg p-4 border border-gray-700"
                      >
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <span className={`px-2 py-1 rounded text-xs font-semibold ${
                              request.method === 'GET' ? 'bg-blue-900/50 text-blue-400' :
                              request.method === 'POST' ? 'bg-green-900/50 text-green-400' :
                              request.method === 'PUT' ? 'bg-yellow-900/50 text-yellow-400' :
                              'bg-red-900/50 text-red-400'
                            }`}>
                              {request.method}
                            </span>
                            <h3 className="font-semibold text-white">{request.name}</h3>
                            {request.favorite && (
                              <Star className="h-4 w-4 text-yellow-400 fill-current" />
                            )}
                          </div>
                          <div className="flex space-x-2">
                            <button
                              onClick={() => toggleFavorite(request.id)}
                              className={`text-gray-400 hover:text-yellow-400 transition-colors ${
                                request.favorite ? 'text-yellow-400' : ''
                              }`}
                            >
                              <Star className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => loadRequest(request)}
                              className="text-gray-400 hover:text-cyber-green transition-colors"
                            >
                              <Edit className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => deleteRequest(request.id)}
                              className="text-gray-400 hover:text-red-400 transition-colors"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                        <div className="text-sm text-gray-300 mb-2">{request.url}</div>
                        <div className="text-xs text-gray-400">
                          Saved: {new Date(request.createdAt).toLocaleString()}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </Card>
          )}
      </div>
    </DashboardLayout>
  )
}
