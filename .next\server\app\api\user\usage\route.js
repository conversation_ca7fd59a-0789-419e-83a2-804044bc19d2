"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/user/usage/route";
exports.ids = ["app/api/user/usage/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fusage%2Froute&page=%2Fapi%2Fuser%2Fusage%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fusage%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fusage%2Froute&page=%2Fapi%2Fuser%2Fusage%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fusage%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Users_Downloads_kodeXGuard_app_api_user_usage_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/user/usage/route.ts */ \"(rsc)/./app/api/user/usage/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/user/usage/route\",\n        pathname: \"/api/user/usage\",\n        filename: \"route\",\n        bundlePath: \"app/api/user/usage/route\"\n    },\n    resolvedPagePath: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\api\\\\user\\\\usage\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Users_Downloads_kodeXGuard_app_api_user_usage_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/user/usage/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZ1c2VyJTJGdXNhZ2UlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRnVzZXIlMkZ1c2FnZSUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRnVzZXIlMkZ1c2FnZSUyRnJvdXRlLnRzJmFwcERpcj1EJTNBJTVDVXNlcnMlNUNEb3dubG9hZHMlNUNrb2RlWEd1YXJkJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1EJTNBJTVDVXNlcnMlNUNEb3dubG9hZHMlNUNrb2RlWEd1YXJkJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBc0c7QUFDdkM7QUFDYztBQUNpQjtBQUM5RjtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsZ0hBQW1CO0FBQzNDO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLGlFQUFpRTtBQUN6RTtBQUNBO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ3VIOztBQUV2SCIsInNvdXJjZXMiOlsid2VicGFjazovL2tvZGV4Z3VhcmQvP2VkMDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiRDpcXFxcVXNlcnNcXFxcRG93bmxvYWRzXFxcXGtvZGVYR3VhcmRcXFxcYXBwXFxcXGFwaVxcXFx1c2VyXFxcXHVzYWdlXFxcXHJvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS91c2VyL3VzYWdlL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvdXNlci91c2FnZVwiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvdXNlci91c2FnZS9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkQ6XFxcXFVzZXJzXFxcXERvd25sb2Fkc1xcXFxrb2RlWEd1YXJkXFxcXGFwcFxcXFxhcGlcXFxcdXNlclxcXFx1c2FnZVxcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9hcGkvdXNlci91c2FnZS9yb3V0ZVwiO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICBzZXJ2ZXJIb29rcyxcbiAgICAgICAgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBvcmlnaW5hbFBhdGhuYW1lLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fusage%2Froute&page=%2Fapi%2Fuser%2Fusage%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fusage%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/user/usage/route.ts":
/*!*************************************!*\
  !*** ./app/api/user/usage/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function GET(request) {\n    try {\n        // Get auth token\n        const authHeader = request.headers.get(\"authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        // Simple token validation for testing\n        if (!token.startsWith(\"kxg_\")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid token\"\n            }, {\n                status: 401\n            });\n        }\n        // Get query parameters\n        const url = new URL(request.url);\n        const period = url.searchParams.get(\"period\") || \"current\";\n        const granularity = url.searchParams.get(\"granularity\") || \"daily\";\n        // Mock usage data\n        const usageData = {\n            current: {\n                scans: {\n                    total: 45,\n                    limit: null,\n                    percentage: 0,\n                    resetDate: new Date(Date.now() + 86400000 * 5).toISOString()\n                },\n                osintSearches: {\n                    total: 123,\n                    limit: null,\n                    percentage: 0,\n                    resetDate: new Date(Date.now() + 86400000 * 5).toISOString()\n                },\n                apiCalls: {\n                    total: 28560,\n                    limit: 100000,\n                    percentage: 28.56,\n                    resetDate: new Date(Date.now() + 86400000 * 5).toISOString()\n                },\n                storage: {\n                    used: 156.7,\n                    limit: 1000,\n                    percentage: 15.67\n                }\n            },\n            history: {\n                daily: Array.from({\n                    length: 30\n                }, (_, i)=>({\n                        date: new Date(Date.now() - 86400000 * (29 - i)).toISOString().split(\"T\")[0],\n                        scans: Math.floor(Math.random() * 5) + 1,\n                        osintSearches: Math.floor(Math.random() * 10) + 2,\n                        apiCalls: Math.floor(Math.random() * 2000) + 500\n                    })),\n                weekly: Array.from({\n                    length: 12\n                }, (_, i)=>({\n                        week: `Week ${i + 1}`,\n                        scans: Math.floor(Math.random() * 20) + 5,\n                        osintSearches: Math.floor(Math.random() * 50) + 10,\n                        apiCalls: Math.floor(Math.random() * 10000) + 2000\n                    })),\n                monthly: Array.from({\n                    length: 6\n                }, (_, i)=>({\n                        month: new Date(Date.now() - 86400000 * 30 * (5 - i)).toISOString().slice(0, 7),\n                        scans: Math.floor(Math.random() * 80) + 20,\n                        osintSearches: Math.floor(Math.random() * 200) + 50,\n                        apiCalls: Math.floor(Math.random() * 40000) + 10000,\n                        storageUsed: Math.floor(Math.random() * 50) + 10\n                    }))\n            },\n            breakdown: {\n                scanTypes: [\n                    {\n                        type: \"SQL Injection\",\n                        count: 18,\n                        percentage: 40\n                    },\n                    {\n                        type: \"XSS\",\n                        count: 12,\n                        percentage: 26.7\n                    },\n                    {\n                        type: \"CSRF\",\n                        count: 8,\n                        percentage: 17.8\n                    },\n                    {\n                        type: \"Directory Traversal\",\n                        count: 4,\n                        percentage: 8.9\n                    },\n                    {\n                        type: \"Others\",\n                        count: 3,\n                        percentage: 6.7\n                    }\n                ],\n                osintTypes: [\n                    {\n                        type: \"Email\",\n                        count: 45,\n                        percentage: 36.6\n                    },\n                    {\n                        type: \"Phone\",\n                        count: 32,\n                        percentage: 26.0\n                    },\n                    {\n                        type: \"Name\",\n                        count: 28,\n                        percentage: 22.8\n                    },\n                    {\n                        type: \"Domain\",\n                        count: 12,\n                        percentage: 9.8\n                    },\n                    {\n                        type: \"Others\",\n                        count: 6,\n                        percentage: 4.9\n                    }\n                ],\n                apiEndpoints: [\n                    {\n                        endpoint: \"/api/scan/vulnerability\",\n                        calls: 8520,\n                        percentage: 29.8\n                    },\n                    {\n                        endpoint: \"/api/osint/search\",\n                        calls: 7340,\n                        percentage: 25.7\n                    },\n                    {\n                        endpoint: \"/api/notifications\",\n                        calls: 4680,\n                        percentage: 16.4\n                    },\n                    {\n                        endpoint: \"/api/dashboard/stats\",\n                        calls: 3920,\n                        percentage: 13.7\n                    },\n                    {\n                        endpoint: \"Others\",\n                        calls: 4100,\n                        percentage: 14.4\n                    }\n                ]\n            },\n            predictions: {\n                monthlyProjection: {\n                    scans: 67,\n                    osintSearches: 184,\n                    apiCalls: 42840\n                },\n                quotaExhaustion: {\n                    // No exhaustion for unlimited features\n                    apiCalls: new Date(Date.now() + 86400000 * 45).toISOString() // 45 days\n                }\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: usageData,\n            meta: {\n                period,\n                granularity,\n                generatedAt: new Date().toISOString()\n            },\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"User usage API error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/user/usage/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fuser%2Fusage%2Froute&page=%2Fapi%2Fuser%2Fusage%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fusage%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();