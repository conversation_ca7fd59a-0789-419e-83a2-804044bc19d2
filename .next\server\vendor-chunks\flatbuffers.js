"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/flatbuffers";
exports.ids = ["vendor-chunks/flatbuffers"];
exports.modules = {

/***/ "(rsc)/./node_modules/flatbuffers/mjs/builder.js":
/*!*************************************************!*\
  !*** ./node_modules/flatbuffers/mjs/builder.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Builder: () => (/* binding */ Builder)\n/* harmony export */ });\n/* harmony import */ var _byte_buffer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./byte-buffer.js */ \"(rsc)/./node_modules/flatbuffers/mjs/byte-buffer.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants.js */ \"(rsc)/./node_modules/flatbuffers/mjs/constants.js\");\n\n\nclass Builder {\n    /**\n     * Create a FlatBufferBuilder.\n     */\n    constructor(opt_initial_size) {\n        /** Minimum alignment encountered so far. */\n        this.minalign = 1;\n        /** The vtable for the current table. */\n        this.vtable = null;\n        /** The amount of fields we're actually using. */\n        this.vtable_in_use = 0;\n        /** Whether we are currently serializing a table. */\n        this.isNested = false;\n        /** Starting offset of the current struct/table. */\n        this.object_start = 0;\n        /** List of offsets of all vtables. */\n        this.vtables = [];\n        /** For the current vector being built. */\n        this.vector_num_elems = 0;\n        /** False omits default values from the serialized data */\n        this.force_defaults = false;\n        this.string_maps = null;\n        this.text_encoder = new TextEncoder();\n        let initial_size;\n        if (!opt_initial_size) {\n            initial_size = 1024;\n        }\n        else {\n            initial_size = opt_initial_size;\n        }\n        /**\n         * @type {ByteBuffer}\n         * @private\n         */\n        this.bb = _byte_buffer_js__WEBPACK_IMPORTED_MODULE_0__.ByteBuffer.allocate(initial_size);\n        this.space = initial_size;\n    }\n    clear() {\n        this.bb.clear();\n        this.space = this.bb.capacity();\n        this.minalign = 1;\n        this.vtable = null;\n        this.vtable_in_use = 0;\n        this.isNested = false;\n        this.object_start = 0;\n        this.vtables = [];\n        this.vector_num_elems = 0;\n        this.force_defaults = false;\n        this.string_maps = null;\n    }\n    /**\n     * In order to save space, fields that are set to their default value\n     * don't get serialized into the buffer. Forcing defaults provides a\n     * way to manually disable this optimization.\n     *\n     * @param forceDefaults true always serializes default values\n     */\n    forceDefaults(forceDefaults) {\n        this.force_defaults = forceDefaults;\n    }\n    /**\n     * Get the ByteBuffer representing the FlatBuffer. Only call this after you've\n     * called finish(). The actual data starts at the ByteBuffer's current position,\n     * not necessarily at 0.\n     */\n    dataBuffer() {\n        return this.bb;\n    }\n    /**\n     * Get the bytes representing the FlatBuffer. Only call this after you've\n     * called finish().\n     */\n    asUint8Array() {\n        return this.bb.bytes().subarray(this.bb.position(), this.bb.position() + this.offset());\n    }\n    /**\n     * Prepare to write an element of `size` after `additional_bytes` have been\n     * written, e.g. if you write a string, you need to align such the int length\n     * field is aligned to 4 bytes, and the string data follows it directly. If all\n     * you need to do is alignment, `additional_bytes` will be 0.\n     *\n     * @param size This is the of the new element to write\n     * @param additional_bytes The padding size\n     */\n    prep(size, additional_bytes) {\n        // Track the biggest thing we've ever aligned to.\n        if (size > this.minalign) {\n            this.minalign = size;\n        }\n        // Find the amount of alignment needed such that `size` is properly\n        // aligned after `additional_bytes`\n        const align_size = ((~(this.bb.capacity() - this.space + additional_bytes)) + 1) & (size - 1);\n        // Reallocate the buffer if needed.\n        while (this.space < align_size + size + additional_bytes) {\n            const old_buf_size = this.bb.capacity();\n            this.bb = Builder.growByteBuffer(this.bb);\n            this.space += this.bb.capacity() - old_buf_size;\n        }\n        this.pad(align_size);\n    }\n    pad(byte_size) {\n        for (let i = 0; i < byte_size; i++) {\n            this.bb.writeInt8(--this.space, 0);\n        }\n    }\n    writeInt8(value) {\n        this.bb.writeInt8(this.space -= 1, value);\n    }\n    writeInt16(value) {\n        this.bb.writeInt16(this.space -= 2, value);\n    }\n    writeInt32(value) {\n        this.bb.writeInt32(this.space -= 4, value);\n    }\n    writeInt64(value) {\n        this.bb.writeInt64(this.space -= 8, value);\n    }\n    writeFloat32(value) {\n        this.bb.writeFloat32(this.space -= 4, value);\n    }\n    writeFloat64(value) {\n        this.bb.writeFloat64(this.space -= 8, value);\n    }\n    /**\n     * Add an `int8` to the buffer, properly aligned, and grows the buffer (if necessary).\n     * @param value The `int8` to add the buffer.\n     */\n    addInt8(value) {\n        this.prep(1, 0);\n        this.writeInt8(value);\n    }\n    /**\n     * Add an `int16` to the buffer, properly aligned, and grows the buffer (if necessary).\n     * @param value The `int16` to add the buffer.\n     */\n    addInt16(value) {\n        this.prep(2, 0);\n        this.writeInt16(value);\n    }\n    /**\n     * Add an `int32` to the buffer, properly aligned, and grows the buffer (if necessary).\n     * @param value The `int32` to add the buffer.\n     */\n    addInt32(value) {\n        this.prep(4, 0);\n        this.writeInt32(value);\n    }\n    /**\n     * Add an `int64` to the buffer, properly aligned, and grows the buffer (if necessary).\n     * @param value The `int64` to add the buffer.\n     */\n    addInt64(value) {\n        this.prep(8, 0);\n        this.writeInt64(value);\n    }\n    /**\n     * Add a `float32` to the buffer, properly aligned, and grows the buffer (if necessary).\n     * @param value The `float32` to add the buffer.\n     */\n    addFloat32(value) {\n        this.prep(4, 0);\n        this.writeFloat32(value);\n    }\n    /**\n     * Add a `float64` to the buffer, properly aligned, and grows the buffer (if necessary).\n     * @param value The `float64` to add the buffer.\n     */\n    addFloat64(value) {\n        this.prep(8, 0);\n        this.writeFloat64(value);\n    }\n    addFieldInt8(voffset, value, defaultValue) {\n        if (this.force_defaults || value != defaultValue) {\n            this.addInt8(value);\n            this.slot(voffset);\n        }\n    }\n    addFieldInt16(voffset, value, defaultValue) {\n        if (this.force_defaults || value != defaultValue) {\n            this.addInt16(value);\n            this.slot(voffset);\n        }\n    }\n    addFieldInt32(voffset, value, defaultValue) {\n        if (this.force_defaults || value != defaultValue) {\n            this.addInt32(value);\n            this.slot(voffset);\n        }\n    }\n    addFieldInt64(voffset, value, defaultValue) {\n        if (this.force_defaults || value !== defaultValue) {\n            this.addInt64(value);\n            this.slot(voffset);\n        }\n    }\n    addFieldFloat32(voffset, value, defaultValue) {\n        if (this.force_defaults || value != defaultValue) {\n            this.addFloat32(value);\n            this.slot(voffset);\n        }\n    }\n    addFieldFloat64(voffset, value, defaultValue) {\n        if (this.force_defaults || value != defaultValue) {\n            this.addFloat64(value);\n            this.slot(voffset);\n        }\n    }\n    addFieldOffset(voffset, value, defaultValue) {\n        if (this.force_defaults || value != defaultValue) {\n            this.addOffset(value);\n            this.slot(voffset);\n        }\n    }\n    /**\n     * Structs are stored inline, so nothing additional is being added. `d` is always 0.\n     */\n    addFieldStruct(voffset, value, defaultValue) {\n        if (value != defaultValue) {\n            this.nested(value);\n            this.slot(voffset);\n        }\n    }\n    /**\n     * Structures are always stored inline, they need to be created right\n     * where they're used.  You'll get this assertion failure if you\n     * created it elsewhere.\n     */\n    nested(obj) {\n        if (obj != this.offset()) {\n            throw new TypeError('FlatBuffers: struct must be serialized inline.');\n        }\n    }\n    /**\n     * Should not be creating any other object, string or vector\n     * while an object is being constructed\n     */\n    notNested() {\n        if (this.isNested) {\n            throw new TypeError('FlatBuffers: object serialization must not be nested.');\n        }\n    }\n    /**\n     * Set the current vtable at `voffset` to the current location in the buffer.\n     */\n    slot(voffset) {\n        if (this.vtable !== null)\n            this.vtable[voffset] = this.offset();\n    }\n    /**\n     * @returns Offset relative to the end of the buffer.\n     */\n    offset() {\n        return this.bb.capacity() - this.space;\n    }\n    /**\n     * Doubles the size of the backing ByteBuffer and copies the old data towards\n     * the end of the new buffer (since we build the buffer backwards).\n     *\n     * @param bb The current buffer with the existing data\n     * @returns A new byte buffer with the old data copied\n     * to it. The data is located at the end of the buffer.\n     *\n     * uint8Array.set() formally takes {Array<number>|ArrayBufferView}, so to pass\n     * it a uint8Array we need to suppress the type check:\n     * @suppress {checkTypes}\n     */\n    static growByteBuffer(bb) {\n        const old_buf_size = bb.capacity();\n        // Ensure we don't grow beyond what fits in an int.\n        if (old_buf_size & 0xC0000000) {\n            throw new Error('FlatBuffers: cannot grow buffer beyond 2 gigabytes.');\n        }\n        const new_buf_size = old_buf_size << 1;\n        const nbb = _byte_buffer_js__WEBPACK_IMPORTED_MODULE_0__.ByteBuffer.allocate(new_buf_size);\n        nbb.setPosition(new_buf_size - old_buf_size);\n        nbb.bytes().set(bb.bytes(), new_buf_size - old_buf_size);\n        return nbb;\n    }\n    /**\n     * Adds on offset, relative to where it will be written.\n     *\n     * @param offset The offset to add.\n     */\n    addOffset(offset) {\n        this.prep(_constants_js__WEBPACK_IMPORTED_MODULE_1__.SIZEOF_INT, 0); // Ensure alignment is already done.\n        this.writeInt32(this.offset() - offset + _constants_js__WEBPACK_IMPORTED_MODULE_1__.SIZEOF_INT);\n    }\n    /**\n     * Start encoding a new object in the buffer.  Users will not usually need to\n     * call this directly. The FlatBuffers compiler will generate helper methods\n     * that call this method internally.\n     */\n    startObject(numfields) {\n        this.notNested();\n        if (this.vtable == null) {\n            this.vtable = [];\n        }\n        this.vtable_in_use = numfields;\n        for (let i = 0; i < numfields; i++) {\n            this.vtable[i] = 0; // This will push additional elements as needed\n        }\n        this.isNested = true;\n        this.object_start = this.offset();\n    }\n    /**\n     * Finish off writing the object that is under construction.\n     *\n     * @returns The offset to the object inside `dataBuffer`\n     */\n    endObject() {\n        if (this.vtable == null || !this.isNested) {\n            throw new Error('FlatBuffers: endObject called without startObject');\n        }\n        this.addInt32(0);\n        const vtableloc = this.offset();\n        // Trim trailing zeroes.\n        let i = this.vtable_in_use - 1;\n        // eslint-disable-next-line no-empty\n        for (; i >= 0 && this.vtable[i] == 0; i--) { }\n        const trimmed_size = i + 1;\n        // Write out the current vtable.\n        for (; i >= 0; i--) {\n            // Offset relative to the start of the table.\n            this.addInt16(this.vtable[i] != 0 ? vtableloc - this.vtable[i] : 0);\n        }\n        const standard_fields = 2; // The fields below:\n        this.addInt16(vtableloc - this.object_start);\n        const len = (trimmed_size + standard_fields) * _constants_js__WEBPACK_IMPORTED_MODULE_1__.SIZEOF_SHORT;\n        this.addInt16(len);\n        // Search for an existing vtable that matches the current one.\n        let existing_vtable = 0;\n        const vt1 = this.space;\n        outer_loop: for (i = 0; i < this.vtables.length; i++) {\n            const vt2 = this.bb.capacity() - this.vtables[i];\n            if (len == this.bb.readInt16(vt2)) {\n                for (let j = _constants_js__WEBPACK_IMPORTED_MODULE_1__.SIZEOF_SHORT; j < len; j += _constants_js__WEBPACK_IMPORTED_MODULE_1__.SIZEOF_SHORT) {\n                    if (this.bb.readInt16(vt1 + j) != this.bb.readInt16(vt2 + j)) {\n                        continue outer_loop;\n                    }\n                }\n                existing_vtable = this.vtables[i];\n                break;\n            }\n        }\n        if (existing_vtable) {\n            // Found a match:\n            // Remove the current vtable.\n            this.space = this.bb.capacity() - vtableloc;\n            // Point table to existing vtable.\n            this.bb.writeInt32(this.space, existing_vtable - vtableloc);\n        }\n        else {\n            // No match:\n            // Add the location of the current vtable to the list of vtables.\n            this.vtables.push(this.offset());\n            // Point table to current vtable.\n            this.bb.writeInt32(this.bb.capacity() - vtableloc, this.offset() - vtableloc);\n        }\n        this.isNested = false;\n        return vtableloc;\n    }\n    /**\n     * Finalize a buffer, poiting to the given `root_table`.\n     */\n    finish(root_table, opt_file_identifier, opt_size_prefix) {\n        const size_prefix = opt_size_prefix ? _constants_js__WEBPACK_IMPORTED_MODULE_1__.SIZE_PREFIX_LENGTH : 0;\n        if (opt_file_identifier) {\n            const file_identifier = opt_file_identifier;\n            this.prep(this.minalign, _constants_js__WEBPACK_IMPORTED_MODULE_1__.SIZEOF_INT +\n                _constants_js__WEBPACK_IMPORTED_MODULE_1__.FILE_IDENTIFIER_LENGTH + size_prefix);\n            if (file_identifier.length != _constants_js__WEBPACK_IMPORTED_MODULE_1__.FILE_IDENTIFIER_LENGTH) {\n                throw new TypeError('FlatBuffers: file identifier must be length ' +\n                    _constants_js__WEBPACK_IMPORTED_MODULE_1__.FILE_IDENTIFIER_LENGTH);\n            }\n            for (let i = _constants_js__WEBPACK_IMPORTED_MODULE_1__.FILE_IDENTIFIER_LENGTH - 1; i >= 0; i--) {\n                this.writeInt8(file_identifier.charCodeAt(i));\n            }\n        }\n        this.prep(this.minalign, _constants_js__WEBPACK_IMPORTED_MODULE_1__.SIZEOF_INT + size_prefix);\n        this.addOffset(root_table);\n        if (size_prefix) {\n            this.addInt32(this.bb.capacity() - this.space);\n        }\n        this.bb.setPosition(this.space);\n    }\n    /**\n     * Finalize a size prefixed buffer, pointing to the given `root_table`.\n     */\n    finishSizePrefixed(root_table, opt_file_identifier) {\n        this.finish(root_table, opt_file_identifier, true);\n    }\n    /**\n     * This checks a required field has been set in a given table that has\n     * just been constructed.\n     */\n    requiredField(table, field) {\n        const table_start = this.bb.capacity() - table;\n        const vtable_start = table_start - this.bb.readInt32(table_start);\n        const ok = field < this.bb.readInt16(vtable_start) &&\n            this.bb.readInt16(vtable_start + field) != 0;\n        // If this fails, the caller will show what field needs to be set.\n        if (!ok) {\n            throw new TypeError('FlatBuffers: field ' + field + ' must be set');\n        }\n    }\n    /**\n     * Start a new array/vector of objects.  Users usually will not call\n     * this directly. The FlatBuffers compiler will create a start/end\n     * method for vector types in generated code.\n     *\n     * @param elem_size The size of each element in the array\n     * @param num_elems The number of elements in the array\n     * @param alignment The alignment of the array\n     */\n    startVector(elem_size, num_elems, alignment) {\n        this.notNested();\n        this.vector_num_elems = num_elems;\n        this.prep(_constants_js__WEBPACK_IMPORTED_MODULE_1__.SIZEOF_INT, elem_size * num_elems);\n        this.prep(alignment, elem_size * num_elems); // Just in case alignment > int.\n    }\n    /**\n     * Finish off the creation of an array and all its elements. The array must be\n     * created with `startVector`.\n     *\n     * @returns The offset at which the newly created array\n     * starts.\n     */\n    endVector() {\n        this.writeInt32(this.vector_num_elems);\n        return this.offset();\n    }\n    /**\n     * Encode the string `s` in the buffer using UTF-8. If the string passed has\n     * already been seen, we return the offset of the already written string\n     *\n     * @param s The string to encode\n     * @return The offset in the buffer where the encoded string starts\n     */\n    createSharedString(s) {\n        if (!s) {\n            return 0;\n        }\n        if (!this.string_maps) {\n            this.string_maps = new Map();\n        }\n        if (this.string_maps.has(s)) {\n            return this.string_maps.get(s);\n        }\n        const offset = this.createString(s);\n        this.string_maps.set(s, offset);\n        return offset;\n    }\n    /**\n     * Encode the string `s` in the buffer using UTF-8. If a Uint8Array is passed\n     * instead of a string, it is assumed to contain valid UTF-8 encoded data.\n     *\n     * @param s The string to encode\n     * @return The offset in the buffer where the encoded string starts\n     */\n    createString(s) {\n        if (s === null || s === undefined) {\n            return 0;\n        }\n        let utf8;\n        if (s instanceof Uint8Array) {\n            utf8 = s;\n        }\n        else {\n            utf8 = this.text_encoder.encode(s);\n        }\n        this.addInt8(0);\n        this.startVector(1, utf8.length, 1);\n        this.bb.setPosition(this.space -= utf8.length);\n        this.bb.bytes().set(utf8, this.space);\n        return this.endVector();\n    }\n    /**\n     * Create a byte vector.\n     *\n     * @param v The bytes to add\n     * @returns The offset in the buffer where the byte vector starts\n     */\n    createByteVector(v) {\n        if (v === null || v === undefined) {\n            return 0;\n        }\n        this.startVector(1, v.length, 1);\n        this.bb.setPosition(this.space -= v.length);\n        this.bb.bytes().set(v, this.space);\n        return this.endVector();\n    }\n    /**\n     * A helper function to pack an object\n     *\n     * @returns offset of obj\n     */\n    createObjectOffset(obj) {\n        if (obj === null) {\n            return 0;\n        }\n        if (typeof obj === 'string') {\n            return this.createString(obj);\n        }\n        else {\n            return obj.pack(this);\n        }\n    }\n    /**\n     * A helper function to pack a list of object\n     *\n     * @returns list of offsets of each non null object\n     */\n    createObjectOffsetList(list) {\n        const ret = [];\n        for (let i = 0; i < list.length; ++i) {\n            const val = list[i];\n            if (val !== null) {\n                ret.push(this.createObjectOffset(val));\n            }\n            else {\n                throw new TypeError('FlatBuffers: Argument for createObjectOffsetList cannot contain null.');\n            }\n        }\n        return ret;\n    }\n    createStructOffsetList(list, startFunc) {\n        startFunc(this, list.length);\n        this.createObjectOffsetList(list.slice().reverse());\n        return this.endVector();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/flatbuffers/mjs/builder.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/flatbuffers/mjs/byte-buffer.js":
/*!*****************************************************!*\
  !*** ./node_modules/flatbuffers/mjs/byte-buffer.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ByteBuffer: () => (/* binding */ ByteBuffer)\n/* harmony export */ });\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants.js */ \"(rsc)/./node_modules/flatbuffers/mjs/constants.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/flatbuffers/mjs/utils.js\");\n/* harmony import */ var _encoding_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./encoding.js */ \"(rsc)/./node_modules/flatbuffers/mjs/encoding.js\");\n\n\n\nclass ByteBuffer {\n    /**\n     * Create a new ByteBuffer with a given array of bytes (`Uint8Array`)\n     */\n    constructor(bytes_) {\n        this.bytes_ = bytes_;\n        this.position_ = 0;\n        this.text_decoder_ = new TextDecoder();\n    }\n    /**\n     * Create and allocate a new ByteBuffer with a given size.\n     */\n    static allocate(byte_size) {\n        return new ByteBuffer(new Uint8Array(byte_size));\n    }\n    clear() {\n        this.position_ = 0;\n    }\n    /**\n     * Get the underlying `Uint8Array`.\n     */\n    bytes() {\n        return this.bytes_;\n    }\n    /**\n     * Get the buffer's position.\n     */\n    position() {\n        return this.position_;\n    }\n    /**\n     * Set the buffer's position.\n     */\n    setPosition(position) {\n        this.position_ = position;\n    }\n    /**\n     * Get the buffer's capacity.\n     */\n    capacity() {\n        return this.bytes_.length;\n    }\n    readInt8(offset) {\n        return this.readUint8(offset) << 24 >> 24;\n    }\n    readUint8(offset) {\n        return this.bytes_[offset];\n    }\n    readInt16(offset) {\n        return this.readUint16(offset) << 16 >> 16;\n    }\n    readUint16(offset) {\n        return this.bytes_[offset] | this.bytes_[offset + 1] << 8;\n    }\n    readInt32(offset) {\n        return this.bytes_[offset] | this.bytes_[offset + 1] << 8 | this.bytes_[offset + 2] << 16 | this.bytes_[offset + 3] << 24;\n    }\n    readUint32(offset) {\n        return this.readInt32(offset) >>> 0;\n    }\n    readInt64(offset) {\n        return BigInt.asIntN(64, BigInt(this.readUint32(offset)) + (BigInt(this.readUint32(offset + 4)) << BigInt(32)));\n    }\n    readUint64(offset) {\n        return BigInt.asUintN(64, BigInt(this.readUint32(offset)) + (BigInt(this.readUint32(offset + 4)) << BigInt(32)));\n    }\n    readFloat32(offset) {\n        _utils_js__WEBPACK_IMPORTED_MODULE_1__.int32[0] = this.readInt32(offset);\n        return _utils_js__WEBPACK_IMPORTED_MODULE_1__.float32[0];\n    }\n    readFloat64(offset) {\n        _utils_js__WEBPACK_IMPORTED_MODULE_1__.int32[_utils_js__WEBPACK_IMPORTED_MODULE_1__.isLittleEndian ? 0 : 1] = this.readInt32(offset);\n        _utils_js__WEBPACK_IMPORTED_MODULE_1__.int32[_utils_js__WEBPACK_IMPORTED_MODULE_1__.isLittleEndian ? 1 : 0] = this.readInt32(offset + 4);\n        return _utils_js__WEBPACK_IMPORTED_MODULE_1__.float64[0];\n    }\n    writeInt8(offset, value) {\n        this.bytes_[offset] = value;\n    }\n    writeUint8(offset, value) {\n        this.bytes_[offset] = value;\n    }\n    writeInt16(offset, value) {\n        this.bytes_[offset] = value;\n        this.bytes_[offset + 1] = value >> 8;\n    }\n    writeUint16(offset, value) {\n        this.bytes_[offset] = value;\n        this.bytes_[offset + 1] = value >> 8;\n    }\n    writeInt32(offset, value) {\n        this.bytes_[offset] = value;\n        this.bytes_[offset + 1] = value >> 8;\n        this.bytes_[offset + 2] = value >> 16;\n        this.bytes_[offset + 3] = value >> 24;\n    }\n    writeUint32(offset, value) {\n        this.bytes_[offset] = value;\n        this.bytes_[offset + 1] = value >> 8;\n        this.bytes_[offset + 2] = value >> 16;\n        this.bytes_[offset + 3] = value >> 24;\n    }\n    writeInt64(offset, value) {\n        this.writeInt32(offset, Number(BigInt.asIntN(32, value)));\n        this.writeInt32(offset + 4, Number(BigInt.asIntN(32, value >> BigInt(32))));\n    }\n    writeUint64(offset, value) {\n        this.writeUint32(offset, Number(BigInt.asUintN(32, value)));\n        this.writeUint32(offset + 4, Number(BigInt.asUintN(32, value >> BigInt(32))));\n    }\n    writeFloat32(offset, value) {\n        _utils_js__WEBPACK_IMPORTED_MODULE_1__.float32[0] = value;\n        this.writeInt32(offset, _utils_js__WEBPACK_IMPORTED_MODULE_1__.int32[0]);\n    }\n    writeFloat64(offset, value) {\n        _utils_js__WEBPACK_IMPORTED_MODULE_1__.float64[0] = value;\n        this.writeInt32(offset, _utils_js__WEBPACK_IMPORTED_MODULE_1__.int32[_utils_js__WEBPACK_IMPORTED_MODULE_1__.isLittleEndian ? 0 : 1]);\n        this.writeInt32(offset + 4, _utils_js__WEBPACK_IMPORTED_MODULE_1__.int32[_utils_js__WEBPACK_IMPORTED_MODULE_1__.isLittleEndian ? 1 : 0]);\n    }\n    /**\n     * Return the file identifier.   Behavior is undefined for FlatBuffers whose\n     * schema does not include a file_identifier (likely points at padding or the\n     * start of a the root vtable).\n     */\n    getBufferIdentifier() {\n        if (this.bytes_.length < this.position_ + _constants_js__WEBPACK_IMPORTED_MODULE_0__.SIZEOF_INT +\n            _constants_js__WEBPACK_IMPORTED_MODULE_0__.FILE_IDENTIFIER_LENGTH) {\n            throw new Error('FlatBuffers: ByteBuffer is too short to contain an identifier.');\n        }\n        let result = \"\";\n        for (let i = 0; i < _constants_js__WEBPACK_IMPORTED_MODULE_0__.FILE_IDENTIFIER_LENGTH; i++) {\n            result += String.fromCharCode(this.readInt8(this.position_ + _constants_js__WEBPACK_IMPORTED_MODULE_0__.SIZEOF_INT + i));\n        }\n        return result;\n    }\n    /**\n     * Look up a field in the vtable, return an offset into the object, or 0 if the\n     * field is not present.\n     */\n    __offset(bb_pos, vtable_offset) {\n        const vtable = bb_pos - this.readInt32(bb_pos);\n        return vtable_offset < this.readInt16(vtable) ? this.readInt16(vtable + vtable_offset) : 0;\n    }\n    /**\n     * Initialize any Table-derived type to point to the union at the given offset.\n     */\n    __union(t, offset) {\n        t.bb_pos = offset + this.readInt32(offset);\n        t.bb = this;\n        return t;\n    }\n    /**\n     * Create a JavaScript string from UTF-8 data stored inside the FlatBuffer.\n     * This allocates a new string and converts to wide chars upon each access.\n     *\n     * To avoid the conversion to string, pass Encoding.UTF8_BYTES as the\n     * \"optionalEncoding\" argument. This is useful for avoiding conversion when\n     * the data will just be packaged back up in another FlatBuffer later on.\n     *\n     * @param offset\n     * @param opt_encoding Defaults to UTF16_STRING\n     */\n    __string(offset, opt_encoding) {\n        offset += this.readInt32(offset);\n        const length = this.readInt32(offset);\n        offset += _constants_js__WEBPACK_IMPORTED_MODULE_0__.SIZEOF_INT;\n        const utf8bytes = this.bytes_.subarray(offset, offset + length);\n        if (opt_encoding === _encoding_js__WEBPACK_IMPORTED_MODULE_2__.Encoding.UTF8_BYTES)\n            return utf8bytes;\n        else\n            return this.text_decoder_.decode(utf8bytes);\n    }\n    /**\n     * Handle unions that can contain string as its member, if a Table-derived type then initialize it,\n     * if a string then return a new one\n     *\n     * WARNING: strings are immutable in JS so we can't change the string that the user gave us, this\n     * makes the behaviour of __union_with_string different compared to __union\n     */\n    __union_with_string(o, offset) {\n        if (typeof o === 'string') {\n            return this.__string(offset);\n        }\n        return this.__union(o, offset);\n    }\n    /**\n     * Retrieve the relative offset stored at \"offset\"\n     */\n    __indirect(offset) {\n        return offset + this.readInt32(offset);\n    }\n    /**\n     * Get the start of data of a vector whose offset is stored at \"offset\" in this object.\n     */\n    __vector(offset) {\n        return offset + this.readInt32(offset) + _constants_js__WEBPACK_IMPORTED_MODULE_0__.SIZEOF_INT; // data starts after the length\n    }\n    /**\n     * Get the length of a vector whose offset is stored at \"offset\" in this object.\n     */\n    __vector_len(offset) {\n        return this.readInt32(offset + this.readInt32(offset));\n    }\n    __has_identifier(ident) {\n        if (ident.length != _constants_js__WEBPACK_IMPORTED_MODULE_0__.FILE_IDENTIFIER_LENGTH) {\n            throw new Error('FlatBuffers: file identifier must be length ' +\n                _constants_js__WEBPACK_IMPORTED_MODULE_0__.FILE_IDENTIFIER_LENGTH);\n        }\n        for (let i = 0; i < _constants_js__WEBPACK_IMPORTED_MODULE_0__.FILE_IDENTIFIER_LENGTH; i++) {\n            if (ident.charCodeAt(i) != this.readInt8(this.position() + _constants_js__WEBPACK_IMPORTED_MODULE_0__.SIZEOF_INT + i)) {\n                return false;\n            }\n        }\n        return true;\n    }\n    /**\n     * A helper function for generating list for obj api\n     */\n    createScalarList(listAccessor, listLength) {\n        const ret = [];\n        for (let i = 0; i < listLength; ++i) {\n            const val = listAccessor(i);\n            if (val !== null) {\n                ret.push(val);\n            }\n        }\n        return ret;\n    }\n    /**\n     * A helper function for generating list for obj api\n     * @param listAccessor function that accepts an index and return data at that index\n     * @param listLength listLength\n     * @param res result list\n     */\n    createObjList(listAccessor, listLength) {\n        const ret = [];\n        for (let i = 0; i < listLength; ++i) {\n            const val = listAccessor(i);\n            if (val !== null) {\n                ret.push(val.unpack());\n            }\n        }\n        return ret;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/flatbuffers/mjs/byte-buffer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/flatbuffers/mjs/constants.js":
/*!***************************************************!*\
  !*** ./node_modules/flatbuffers/mjs/constants.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FILE_IDENTIFIER_LENGTH: () => (/* binding */ FILE_IDENTIFIER_LENGTH),\n/* harmony export */   SIZEOF_INT: () => (/* binding */ SIZEOF_INT),\n/* harmony export */   SIZEOF_SHORT: () => (/* binding */ SIZEOF_SHORT),\n/* harmony export */   SIZE_PREFIX_LENGTH: () => (/* binding */ SIZE_PREFIX_LENGTH)\n/* harmony export */ });\nconst SIZEOF_SHORT = 2;\nconst SIZEOF_INT = 4;\nconst FILE_IDENTIFIER_LENGTH = 4;\nconst SIZE_PREFIX_LENGTH = 4;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZmxhdGJ1ZmZlcnMvbWpzL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQU87QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb2RleGd1YXJkLy4vbm9kZV9tb2R1bGVzL2ZsYXRidWZmZXJzL21qcy9jb25zdGFudHMuanM/OTZkZiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgU0laRU9GX1NIT1JUID0gMjtcbmV4cG9ydCBjb25zdCBTSVpFT0ZfSU5UID0gNDtcbmV4cG9ydCBjb25zdCBGSUxFX0lERU5USUZJRVJfTEVOR1RIID0gNDtcbmV4cG9ydCBjb25zdCBTSVpFX1BSRUZJWF9MRU5HVEggPSA0O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/flatbuffers/mjs/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/flatbuffers/mjs/encoding.js":
/*!**************************************************!*\
  !*** ./node_modules/flatbuffers/mjs/encoding.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Encoding: () => (/* binding */ Encoding)\n/* harmony export */ });\nvar Encoding;\n(function (Encoding) {\n    Encoding[Encoding[\"UTF8_BYTES\"] = 1] = \"UTF8_BYTES\";\n    Encoding[Encoding[\"UTF16_STRING\"] = 2] = \"UTF16_STRING\";\n})(Encoding || (Encoding = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZmxhdGJ1ZmZlcnMvbWpzL2VuY29kaW5nLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLENBQUMsNEJBQTRCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va29kZXhndWFyZC8uL25vZGVfbW9kdWxlcy9mbGF0YnVmZmVycy9tanMvZW5jb2RpbmcuanM/NTA5YyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgdmFyIEVuY29kaW5nO1xuKGZ1bmN0aW9uIChFbmNvZGluZykge1xuICAgIEVuY29kaW5nW0VuY29kaW5nW1wiVVRGOF9CWVRFU1wiXSA9IDFdID0gXCJVVEY4X0JZVEVTXCI7XG4gICAgRW5jb2RpbmdbRW5jb2RpbmdbXCJVVEYxNl9TVFJJTkdcIl0gPSAyXSA9IFwiVVRGMTZfU1RSSU5HXCI7XG59KShFbmNvZGluZyB8fCAoRW5jb2RpbmcgPSB7fSkpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/flatbuffers/mjs/encoding.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/flatbuffers/mjs/flatbuffers.js":
/*!*****************************************************!*\
  !*** ./node_modules/flatbuffers/mjs/flatbuffers.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Builder: () => (/* reexport safe */ _builder_js__WEBPACK_IMPORTED_MODULE_3__.Builder),\n/* harmony export */   ByteBuffer: () => (/* reexport safe */ _byte_buffer_js__WEBPACK_IMPORTED_MODULE_4__.ByteBuffer),\n/* harmony export */   Encoding: () => (/* reexport safe */ _encoding_js__WEBPACK_IMPORTED_MODULE_2__.Encoding),\n/* harmony export */   FILE_IDENTIFIER_LENGTH: () => (/* reexport safe */ _constants_js__WEBPACK_IMPORTED_MODULE_0__.FILE_IDENTIFIER_LENGTH),\n/* harmony export */   SIZEOF_INT: () => (/* reexport safe */ _constants_js__WEBPACK_IMPORTED_MODULE_0__.SIZEOF_INT),\n/* harmony export */   SIZEOF_SHORT: () => (/* reexport safe */ _constants_js__WEBPACK_IMPORTED_MODULE_0__.SIZEOF_SHORT),\n/* harmony export */   SIZE_PREFIX_LENGTH: () => (/* reexport safe */ _constants_js__WEBPACK_IMPORTED_MODULE_0__.SIZE_PREFIX_LENGTH),\n/* harmony export */   float32: () => (/* reexport safe */ _utils_js__WEBPACK_IMPORTED_MODULE_1__.float32),\n/* harmony export */   float64: () => (/* reexport safe */ _utils_js__WEBPACK_IMPORTED_MODULE_1__.float64),\n/* harmony export */   int32: () => (/* reexport safe */ _utils_js__WEBPACK_IMPORTED_MODULE_1__.int32),\n/* harmony export */   isLittleEndian: () => (/* reexport safe */ _utils_js__WEBPACK_IMPORTED_MODULE_1__.isLittleEndian)\n/* harmony export */ });\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants.js */ \"(rsc)/./node_modules/flatbuffers/mjs/constants.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/flatbuffers/mjs/utils.js\");\n/* harmony import */ var _encoding_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./encoding.js */ \"(rsc)/./node_modules/flatbuffers/mjs/encoding.js\");\n/* harmony import */ var _builder_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./builder.js */ \"(rsc)/./node_modules/flatbuffers/mjs/builder.js\");\n/* harmony import */ var _byte_buffer_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./byte-buffer.js */ \"(rsc)/./node_modules/flatbuffers/mjs/byte-buffer.js\");\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZmxhdGJ1ZmZlcnMvbWpzL2ZsYXRidWZmZXJzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBOEM7QUFDRjtBQUNZO0FBQ0o7QUFDaUI7QUFDNUI7QUFDRjtBQUNPIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va29kZXhndWFyZC8uL25vZGVfbW9kdWxlcy9mbGF0YnVmZmVycy9tanMvZmxhdGJ1ZmZlcnMuanM/N2U4NyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBTSVpFT0ZfU0hPUlQgfSBmcm9tICcuL2NvbnN0YW50cy5qcyc7XG5leHBvcnQgeyBTSVpFT0ZfSU5UIH0gZnJvbSAnLi9jb25zdGFudHMuanMnO1xuZXhwb3J0IHsgRklMRV9JREVOVElGSUVSX0xFTkdUSCB9IGZyb20gJy4vY29uc3RhbnRzLmpzJztcbmV4cG9ydCB7IFNJWkVfUFJFRklYX0xFTkdUSCB9IGZyb20gJy4vY29uc3RhbnRzLmpzJztcbmV4cG9ydCB7IGludDMyLCBmbG9hdDMyLCBmbG9hdDY0LCBpc0xpdHRsZUVuZGlhbiB9IGZyb20gJy4vdXRpbHMuanMnO1xuZXhwb3J0IHsgRW5jb2RpbmcgfSBmcm9tICcuL2VuY29kaW5nLmpzJztcbmV4cG9ydCB7IEJ1aWxkZXIgfSBmcm9tICcuL2J1aWxkZXIuanMnO1xuZXhwb3J0IHsgQnl0ZUJ1ZmZlciB9IGZyb20gJy4vYnl0ZS1idWZmZXIuanMnO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/flatbuffers/mjs/flatbuffers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/flatbuffers/mjs/utils.js":
/*!***********************************************!*\
  !*** ./node_modules/flatbuffers/mjs/utils.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   float32: () => (/* binding */ float32),\n/* harmony export */   float64: () => (/* binding */ float64),\n/* harmony export */   int32: () => (/* binding */ int32),\n/* harmony export */   isLittleEndian: () => (/* binding */ isLittleEndian)\n/* harmony export */ });\nconst int32 = new Int32Array(2);\nconst float32 = new Float32Array(int32.buffer);\nconst float64 = new Float64Array(int32.buffer);\nconst isLittleEndian = new Uint16Array(new Uint8Array([1, 0]).buffer)[0] === 1;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZmxhdGJ1ZmZlcnMvbWpzL3V0aWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBTztBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2tvZGV4Z3VhcmQvLi9ub2RlX21vZHVsZXMvZmxhdGJ1ZmZlcnMvbWpzL3V0aWxzLmpzPzkxODUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGludDMyID0gbmV3IEludDMyQXJyYXkoMik7XG5leHBvcnQgY29uc3QgZmxvYXQzMiA9IG5ldyBGbG9hdDMyQXJyYXkoaW50MzIuYnVmZmVyKTtcbmV4cG9ydCBjb25zdCBmbG9hdDY0ID0gbmV3IEZsb2F0NjRBcnJheShpbnQzMi5idWZmZXIpO1xuZXhwb3J0IGNvbnN0IGlzTGl0dGxlRW5kaWFuID0gbmV3IFVpbnQxNkFycmF5KG5ldyBVaW50OEFycmF5KFsxLCAwXSkuYnVmZmVyKVswXSA9PT0gMTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/flatbuffers/mjs/utils.js\n");

/***/ })

};
;