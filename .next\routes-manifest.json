{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-DNS-Prefetch-Control", "value": "on"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}], "regex": "^(?:/(.*))(?:/)?$"}, {"source": "/api/:path*", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}, {"key": "Cache-Control", "value": "public, s-maxage=60, stale-while-revalidate=300"}], "regex": "^/api(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/_next/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}], "regex": "^/_next/static(?:/(.*))(?:/)?$"}, {"source": "/images/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}], "regex": "^/images(?:/(.*))(?:/)?$"}], "dynamicRoutes": [{"page": "/api/notifications/[id]/read", "regex": "^/api/notifications/([^/]+?)/read(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/notifications/(?<nxtPid>[^/]+?)/read(?:/)?$"}, {"page": "/api/scan/[scanId]/results", "regex": "^/api/scan/([^/]+?)/results(?:/)?$", "routeKeys": {"nxtPscanId": "nxtPscanId"}, "namedRegex": "^/api/scan/(?<nxtPscanId>[^/]+?)/results(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/admin", "regex": "^/admin(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin(?:/)?$"}, {"page": "/bot", "regex": "^/bot(?:/)?$", "routeKeys": {}, "namedRegex": "^/bot(?:/)?$"}, {"page": "/cve", "regex": "^/cve(?:/)?$", "routeKeys": {}, "namedRegex": "^/cve(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/docs", "regex": "^/docs(?:/)?$", "routeKeys": {}, "namedRegex": "^/docs(?:/)?$"}, {"page": "/dorking", "regex": "^/dorking(?:/)?$", "routeKeys": {}, "namedRegex": "^/dorking(?:/)?$"}, {"page": "/file-analyzer", "regex": "^/file\\-analyzer(?:/)?$", "routeKeys": {}, "namedRegex": "^/file\\-analyzer(?:/)?$"}, {"page": "/leaderboard", "regex": "^/leaderboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/leaderboard(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/notifications", "regex": "^/notifications(?:/)?$", "routeKeys": {}, "namedRegex": "^/notifications(?:/)?$"}, {"page": "/osint", "regex": "^/osint(?:/)?$", "routeKeys": {}, "namedRegex": "^/osint(?:/)?$"}, {"page": "/plan", "regex": "^/plan(?:/)?$", "routeKeys": {}, "namedRegex": "^/plan(?:/)?$"}, {"page": "/playground", "regex": "^/playground(?:/)?$", "routeKeys": {}, "namedRegex": "^/playground(?:/)?$"}, {"page": "/profile", "regex": "^/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile(?:/)?$"}, {"page": "/register", "regex": "^/register(?:/)?$", "routeKeys": {}, "namedRegex": "^/register(?:/)?$"}, {"page": "/scanner", "regex": "^/scanner(?:/)?$", "routeKeys": {}, "namedRegex": "^/scanner(?:/)?$"}, {"page": "/settings", "regex": "^/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings(?:/)?$"}, {"page": "/tools", "regex": "^/tools(?:/)?$", "routeKeys": {}, "namedRegex": "^/tools(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}