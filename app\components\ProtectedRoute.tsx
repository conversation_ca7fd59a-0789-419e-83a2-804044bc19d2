'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/app/contexts/AuthContext'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredRole?: string[]
  requiredPlan?: string[]
  fallbackPath?: string
}

export default function ProtectedRoute({
  children,
  requiredRole = [],
  requiredPlan = [],
  fallbackPath = '/login'
}: ProtectedRouteProps) {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        router.push(fallbackPath)
        return
      }

      // Check role requirements
      if (requiredRole.length > 0 && user) {
        if (!requiredRole.includes(user.role)) {
          router.push('/unauthorized')
          return
        }
      }

      // Check plan requirements
      if (requiredPlan.length > 0 && user) {
        if (!requiredPlan.includes(user.plan)) {
          router.push('/upgrade')
          return
        }
      }
    }
  }, [isAuthenticated, isLoading, user, router, requiredRole, requiredPlan, fallbackPath])

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-cyber flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-cyber-green mx-auto"></div>
          <p className="mt-4 text-gray-400">Authenticating...</p>
        </div>
      </div>
    )
  }

  // Show unauthorized state
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-cyber flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">🔒</div>
          <h1 className="text-2xl font-bold text-white mb-2">Access Denied</h1>
          <p className="text-gray-400 mb-4">Please log in to access this page</p>
          <button
            onClick={() => router.push('/login')}
            className="bg-cyber-green text-black px-6 py-2 rounded-lg font-semibold hover:bg-green-400 transition-colors"
          >
            Go to Login
          </button>
        </div>
      </div>
    )
  }

  // Show role/plan restriction
  if (user) {
    if (requiredRole.length > 0 && !requiredRole.includes(user.role)) {
      return (
        <div className="min-h-screen bg-gradient-cyber flex items-center justify-center">
          <div className="text-center">
            <div className="text-yellow-500 text-6xl mb-4">⚠️</div>
            <h1 className="text-2xl font-bold text-white mb-2">Insufficient Permissions</h1>
            <p className="text-gray-400 mb-4">
              This feature requires {requiredRole.join(' or ')} role
            </p>
            <button
              onClick={() => router.push('/dashboard')}
              className="bg-cyber-green text-black px-6 py-2 rounded-lg font-semibold hover:bg-green-400 transition-colors"
            >
              Back to Dashboard
            </button>
          </div>
        </div>
      )
    }

    if (requiredPlan.length > 0 && !requiredPlan.includes(user.plan)) {
      return (
        <div className="min-h-screen bg-gradient-cyber flex items-center justify-center">
          <div className="text-center">
            <div className="text-purple-500 text-6xl mb-4">💎</div>
            <h1 className="text-2xl font-bold text-white mb-2">Upgrade Required</h1>
            <p className="text-gray-400 mb-4">
              This feature requires {requiredPlan.join(' or ')} plan
            </p>
            <div className="space-x-4">
              <button
                onClick={() => router.push('/upgrade')}
                className="bg-cyber-green text-black px-6 py-2 rounded-lg font-semibold hover:bg-green-400 transition-colors"
              >
                Upgrade Plan
              </button>
              <button
                onClick={() => router.push('/dashboard')}
                className="bg-gray-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-gray-500 transition-colors"
              >
                Back to Dashboard
              </button>
            </div>
          </div>
        </div>
      )
    }
  }

  return <>{children}</>
}
