"use strict";(()=>{var e={};e.id=3744,e.ids=[3744],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},97918:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>g,patchFetch:()=>A,requestAsyncStorage:()=>p,routeModule:()=>c,serverHooks:()=>f,staticGenerationAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>u,OPTIONS:()=>d,PUT:()=>l});var o=t(49303),a=t(88716),n=t(60670),i=t(87070);async function u(e){try{let r=e.headers.get("authorization");if(!r||!r.startsWith("Bearer "))return i.NextResponse.json({success:!1,error:"Authorization token required",code:"MISSING_TOKEN"},{status:401});let t={id:"user_123456789",username:"admin",email:"<EMAIL>",fullName:"Super Administrator",avatar:null,bio:"Platform creator and cybersecurity enthusiast. Building tools to make the internet safer.",role:"super_admin",plan:"cybersecurity",planExpiry:"2024-12-31",isActive:!0,emailVerified:!0,createdAt:"2023-01-01T00:00:00Z",lastLogin:new Date().toISOString(),stats:{totalScans:1247,vulnerabilitiesFound:89,filesAnalyzed:456,apiCalls:28560,score:9870,rank:4},apiKeys:[{id:"key_1",name:"Production API",keyPrefix:"kxg_prod_",isActive:!0,lastUsed:"2 hours ago",usageCount:15420,createdAt:"2023-06-15"},{id:"key_2",name:"Development API",keyPrefix:"kxg_dev_",isActive:!0,lastUsed:"1 day ago",usageCount:2847,createdAt:"2023-08-20"}]};return i.NextResponse.json({success:!0,data:t,message:"Profile retrieved successfully",timestamp:new Date().toISOString()})}catch(e){return console.error("Get profile error:",e),i.NextResponse.json({success:!1,error:"Internal server error",code:"INTERNAL_ERROR"},{status:500})}}async function l(e){try{let r=e.headers.get("authorization");if(!r||!r.startsWith("Bearer "))return i.NextResponse.json({success:!1,error:"Authorization token required",code:"MISSING_TOKEN"},{status:401});let{fullName:t,username:s,bio:o}=await e.json();if(!t||!s)return i.NextResponse.json({success:!1,error:"Full name and username are required",code:"MISSING_FIELDS"},{status:400});let a={id:"user_123456789",username:s,email:"<EMAIL>",fullName:t,bio:o||"",role:"super_admin",plan:"cybersecurity",updatedAt:new Date().toISOString()};return i.NextResponse.json({success:!0,data:a,message:"Profile updated successfully",timestamp:new Date().toISOString()})}catch(e){return console.error("Update profile error:",e),i.NextResponse.json({success:!1,error:"Internal server error",code:"INTERNAL_ERROR"},{status:500})}}async function d(){return new i.NextResponse(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, PUT, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}let c=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/user/profile/route",pathname:"/api/user/profile",filename:"route",bundlePath:"app/api/user/profile/route"},resolvedPagePath:"D:\\Users\\Downloads\\kodeXGuard\\app\\api\\user\\profile\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:p,staticGenerationAsyncStorage:m,serverHooks:f}=c,g="/api/user/profile/route";function A(){return(0,n.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:m})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9276,5972],()=>t(97918));module.exports=s})();