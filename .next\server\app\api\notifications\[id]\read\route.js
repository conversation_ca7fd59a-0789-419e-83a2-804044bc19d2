"use strict";(()=>{var e={};e.id=812,e.ids=[812],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},70995:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>f,patchFetch:()=>x,requestAsyncStorage:()=>c,routeModule:()=>d,serverHooks:()=>l,staticGenerationAsyncStorage:()=>p});var s={};t.r(s),t.d(s,{POST:()=>u});var i=t(49303),o=t(88716),a=t(60670),n=t(87070);async function u(e,{params:r}){try{let t=e.headers.get("authorization"),s=t?.replace("Bearer ","");if(!s)return n.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!s.startsWith("kxg_"))return n.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let i=r.id;if(!i)return n.NextResponse.json({success:!1,error:"Notification ID is required"},{status:400});return n.NextResponse.json({success:!0,message:`Notification ${i} marked as read`,timestamp:new Date().toISOString()})}catch(e){return console.error("Mark notification as read error:",e),n.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/notifications/[id]/read/route",pathname:"/api/notifications/[id]/read",filename:"route",bundlePath:"app/api/notifications/[id]/read/route"},resolvedPagePath:"D:\\Users\\Downloads\\kodeXGuard\\app\\api\\notifications\\[id]\\read\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:c,staticGenerationAsyncStorage:p,serverHooks:l}=d,f="/api/notifications/[id]/read/route";function x(){return(0,a.patchFetch)({serverHooks:l,staticGenerationAsyncStorage:p})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[216,592],()=>t(70995));module.exports=s})();