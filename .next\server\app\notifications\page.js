(()=>{var e={};e.id=193,e.ids=[193],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},54717:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>c}),r(90834),r(30829),r(35866);var a=r(23191),s=r(88716),n=r(37922),i=r.n(n),l=r(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let c=["",{children:["notifications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,90834)),"D:\\Users\\Downloads\\kodeXGuard\\app\\notifications\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\Users\\Downloads\\kodeXGuard\\app\\notifications\\page.tsx"],x="/notifications/page",u={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/notifications/page",pathname:"/notifications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},28931:(e,t,r)=>{Promise.resolve().then(r.bind(r,68636))},68636:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var a=r(10326),s=r(17577),n=r(54659),i=r(37202),l=r(91470),o=r(18019),c=r(6507),d=r(98091),x=r(12714),u=r(48998),m=r(90434);function g(){let[e,t]=(0,s.useState)([]),[r,g]=(0,s.useState)("all"),[h,p]=(0,s.useState)("all"),[f,b]=(0,s.useState)(!0),[y,j]=(0,s.useState)([]),v=async e=>{try{let r=localStorage.getItem("auth-token");if(!r)return;await fetch(`/api/notifications/${e}/read`,{method:"POST",headers:{Authorization:`Bearer ${r}`}}),t(t=>t.map(t=>t.id===e?{...t,read:!0}:t))}catch(r){console.error("Failed to mark notification as read:",r),t(t=>t.map(t=>t.id===e?{...t,read:!0}:t))}},N=async()=>{try{let e=localStorage.getItem("auth-token");if(!e)return;await fetch("/api/notifications/mark-all-read",{method:"POST",headers:{Authorization:`Bearer ${e}`}}),t(e=>e.map(e=>({...e,read:!0})))}catch(e){console.error("Failed to mark all notifications as read:",e),t(e=>e.map(e=>({...e,read:!0})))}},w=async e=>{try{if(!localStorage.getItem("auth-token"))return;t(t=>t.filter(t=>t.id!==e))}catch(e){console.error("Failed to delete notification:",e)}},k=async()=>{try{if(!localStorage.getItem("auth-token"))return;t(e=>e.filter(e=>!y.includes(e.id))),j([])}catch(e){console.error("Failed to delete selected notifications:",e)}},P=e=>{switch(e){case"success":return n.Z;case"warning":return i.Z;case"error":return l.Z;default:return o.Z}},S=e=>{switch(e){case"success":return"text-green-500";case"warning":return"text-yellow-500";case"error":return"text-red-500";default:return"text-blue-500"}},_=e=>{switch(e){case"success":return"bg-green-500/10 border-green-500/20";case"warning":return"bg-yellow-500/10 border-yellow-500/20";case"error":return"bg-red-500/10 border-red-500/20";default:return"bg-blue-500/10 border-blue-500/20"}},D=e.filter(e=>("read"!==r||!!e.read)&&("unread"!==r||!e.read)&&("all"===h||e.type===h)),Z=e.filter(e=>!e.read).length,A=e=>{let t=new Date(e),r=(new Date().getTime()-t.getTime())/36e5;return r<1?`${Math.floor(60*r)} minutes ago`:r<24?`${Math.floor(r)} hours ago`:t.toLocaleDateString("id-ID",{day:"numeric",month:"short",year:"numeric"})};return a.jsx("div",{className:"min-h-screen bg-black text-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[a.jsx("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[a.jsx("div",{className:"w-12 h-12 bg-cyber-green rounded-lg flex items-center justify-center",children:a.jsx(c.Z,{className:"h-6 w-6 text-black"})}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold font-cyber",children:"Notifications"}),a.jsx("p",{className:"text-gray-400",children:Z>0?`${Z} unread notifications`:"All notifications read"})]})]})}),a.jsx("div",{className:"mb-6 bg-gray-900 rounded-lg border border-gray-800 p-4",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex flex-wrap gap-3",children:[(0,a.jsxs)("select",{value:r,onChange:e=>g(e.target.value),className:"bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-cyber-green",children:[a.jsx("option",{value:"all",children:"All Notifications"}),a.jsx("option",{value:"unread",children:"Unread Only"}),a.jsx("option",{value:"read",children:"Read Only"})]}),(0,a.jsxs)("select",{value:h,onChange:e=>p(e.target.value),className:"bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-cyber-green",children:[a.jsx("option",{value:"all",children:"All Types"}),a.jsx("option",{value:"success",children:"Success"}),a.jsx("option",{value:"warning",children:"Warning"}),a.jsx("option",{value:"error",children:"Error"}),a.jsx("option",{value:"info",children:"Info"})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[y.length>0&&(0,a.jsxs)("button",{onClick:k,className:"flex items-center gap-2 px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm",children:[a.jsx(d.Z,{className:"h-4 w-4"}),"Delete Selected (",y.length,")"]}),Z>0&&(0,a.jsxs)("button",{onClick:N,className:"flex items-center gap-2 px-3 py-2 bg-cyber-green text-black rounded-lg hover:bg-cyber-green/90 transition-colors text-sm",children:[a.jsx(x.Z,{className:"h-4 w-4"}),"Mark All Read"]})]})]})}),a.jsx("div",{className:"space-y-4",children:f?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("div",{className:"w-8 h-8 border-2 border-cyber-green border-t-transparent rounded-full animate-spin mx-auto mb-4"}),a.jsx("p",{className:"text-gray-400",children:"Loading notifications..."})]}):0===D.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx(c.Z,{className:"h-12 w-12 text-gray-600 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-400",children:"No notifications found"})]}):D.map(e=>{let t=P(e.type);return a.jsx("div",{className:`bg-gray-900 border rounded-lg p-4 transition-all hover:bg-gray-800/50 ${e.read?"border-gray-800":`${_(e.type)} border-l-4`}`,children:(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[a.jsx("input",{type:"checkbox",checked:y.includes(e.id),onChange:t=>{t.target.checked?j(t=>[...t,e.id]):j(t=>t.filter(t=>t!==e.id))},className:"mt-1 w-4 h-4 text-cyber-green bg-gray-800 border-gray-600 rounded focus:ring-cyber-green"}),a.jsx(t,{className:`flex-shrink-0 h-5 w-5 mt-0.5 ${S(e.type)}`}),a.jsx("div",{className:"flex-1 min-w-0",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("h3",{className:`text-sm font-medium ${e.read?"text-gray-300":"text-white"}`,children:e.title}),a.jsx("p",{className:"text-sm text-gray-400 mt-1",children:e.message}),(0,a.jsxs)("div",{className:"flex items-center gap-4 mt-3",children:[(0,a.jsxs)("span",{className:"text-xs text-gray-500 flex items-center gap-1",children:[a.jsx(u.Z,{className:"h-3 w-3"}),A(e.timestamp)]}),e.action&&(0,a.jsxs)(m.default,{href:e.action.url,className:"text-xs text-cyber-green hover:text-cyber-green/80 transition-colors",onClick:()=>!e.read&&v(e.id),children:[e.action.label," →"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 ml-4",children:[!e.read&&a.jsx("button",{onClick:()=>v(e.id),className:"text-gray-400 hover:text-cyber-green transition-colors",title:"Mark as read",children:a.jsx(x.Z,{className:"h-4 w-4"})}),a.jsx("button",{onClick:()=>w(e.id),className:"text-gray-400 hover:text-red-400 transition-colors",title:"Delete notification",children:a.jsx(d.Z,{className:"h-4 w-4"})})]})]})})]})},e.id)})})]})})}},90834:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(68570).createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\notifications\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[216,592],()=>r(54717));module.exports=a})();