(()=>{var e={};e.id=931,e.ids=[931],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},35434:(e,a,s)=>{"use strict";s.r(a),s.d(a,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d}),s(90908),s(30829),s(35866);var r=s(23191),t=s(88716),l=s(37922),n=s.n(l),i=s(95231),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);s.d(a,c);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,90908)),"D:\\Users\\Downloads\\kodeXGuard\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\Users\\Downloads\\kodeXGuard\\app\\page.tsx"],x="/page",m={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},87443:(e,a,s)=>{Promise.resolve().then(s.bind(s,3767))},3767:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>I});var r=s(10326),t=s(17577),l=s(90434),n=s(16361),i=s(12893),c=s(74857),d=s(32019),o=s(34738),x=s(58038),m=s(5932),h=s(42887),u=s(77636),p=s(7027),g=s(24230),b=s(67427),y=s(54014);function f(){let e=new Date().getFullYear(),a=[{name:"GitHub",icon:i.Z,href:"https://github.com/kodexguard"},{name:"Twitter",icon:c.Z,href:"https://twitter.com/kodexguard"},{name:"LinkedIn",icon:d.Z,href:"https://linkedin.com/company/kodexguard"},{name:"Instagram",icon:o.Z,href:"https://instagram.com/kodexguard"}];return(0,r.jsxs)("footer",{className:"bg-gray-900 border-t border-gray-800",children:[r.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[r.jsx("div",{className:"w-8 h-8 bg-cyber-green rounded-lg flex items-center justify-center",children:r.jsx(x.Z,{className:"h-5 w-5 text-black"})}),r.jsx("span",{className:"font-bold text-white text-xl font-cyber",children:"KodeXGuard"})]}),r.jsx("p",{className:"text-gray-400 mb-6 max-w-sm",children:"Platform cybersecurity terdepan di Indonesia untuk OSINT, vulnerability scanning, dan analisis keamanan yang komprehensif."}),(0,r.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 text-gray-400",children:[r.jsx(m.Z,{className:"h-4 w-4 text-cyber-green"}),r.jsx("span",{className:"text-sm",children:"<EMAIL>"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3 text-gray-400",children:[r.jsx(h.Z,{className:"h-4 w-4 text-cyber-green"}),r.jsx("span",{className:"text-sm",children:"+62 21 1234 5678"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3 text-gray-400",children:[r.jsx(u.Z,{className:"h-4 w-4 text-cyber-green"}),r.jsx("span",{className:"text-sm",children:"Jakarta, Indonesia"})]})]}),r.jsx("div",{className:"flex space-x-4",children:a.map(e=>{let a=e.icon;return r.jsx("a",{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center text-gray-400 hover:text-cyber-green hover:bg-gray-700 transition-all duration-200","aria-label":e.name,children:r.jsx(a,{className:"h-5 w-5"})},e.name)})})]}),[{title:"Platform",links:[{name:"Dashboard",href:"/dashboard"},{name:"OSINT Tools",href:"/osint"},{name:"Vulnerability Scanner",href:"/scanner"},{name:"File Analyzer",href:"/file-analyzer"},{name:"CVE Intelligence",href:"/cve"},{name:"API Playground",href:"/playground"}]},{title:"Resources",links:[{name:"Documentation",href:"/docs"},{name:"API Reference",href:"/docs/api"},{name:"Tutorials",href:"/docs/tutorials"},{name:"Blog",href:"/blog"},{name:"Community",href:"/community"},{name:"Status Page",href:"/status",external:!0}]},{title:"Company",links:[{name:"About Us",href:"/about"},{name:"Careers",href:"/careers"},{name:"Press Kit",href:"/press"},{name:"Partners",href:"/partners"},{name:"Contact",href:"/contact"},{name:"Support",href:"/support"}]},{title:"Legal",links:[{name:"Privacy Policy",href:"/privacy"},{name:"Terms of Service",href:"/terms"},{name:"Cookie Policy",href:"/cookies"},{name:"Security",href:"/security"},{name:"Compliance",href:"/compliance"},{name:"Bug Bounty",href:"/bug-bounty"}]}].map(e=>(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-white font-semibold mb-4",children:e.title}),r.jsx("ul",{className:"space-y-3",children:e.links.map(e=>r.jsx("li",{children:e.external?(0,r.jsxs)("a",{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-cyber-green transition-colors duration-200 text-sm flex items-center space-x-1",children:[r.jsx("span",{children:e.name}),r.jsx(p.Z,{className:"h-3 w-3"})]}):r.jsx(l.default,{href:e.href,className:"text-gray-400 hover:text-cyber-green transition-colors duration-200 text-sm",children:e.name})},e.name))})]},e.title))]})}),r.jsx("div",{className:"border-t border-gray-800",children:r.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between",children:[(0,r.jsxs)("div",{className:"mb-4 lg:mb-0",children:[r.jsx("h3",{className:"text-white font-semibold mb-2",children:"Stay Updated"}),r.jsx("p",{className:"text-gray-400 text-sm",children:"Dapatkan update terbaru tentang threat intelligence dan cybersecurity."})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3",children:[r.jsx("input",{type:"email",placeholder:"Enter your email",className:"px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyber-green focus:border-transparent"}),(0,r.jsxs)("button",{className:"cyber-btn-primary flex items-center justify-center space-x-2",children:[r.jsx("span",{children:"Subscribe"}),r.jsx(g.Z,{className:"h-4 w-4"})]})]})]})})}),r.jsx("div",{className:"border-t border-gray-800",children:r.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-6 mb-4 md:mb-0",children:[(0,r.jsxs)("p",{className:"text-gray-400 text-sm",children:["\xa9 ",e," KodeXGuard. All rights reserved."]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-gray-400 text-sm",children:[r.jsx("span",{children:"Made with"}),r.jsx(b.Z,{className:"h-4 w-4 text-red-500"}),r.jsx("span",{children:"in Indonesia"}),r.jsx(y.Z,{className:"h-4 w-4 text-cyber-green ml-2"})]})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-400",children:[r.jsx(l.default,{href:"/privacy",className:"hover:text-cyber-green transition-colors",children:"Privacy"}),r.jsx(l.default,{href:"/terms",className:"hover:text-cyber-green transition-colors",children:"Terms"}),r.jsx(l.default,{href:"/cookies",className:"hover:text-cyber-green transition-colors",children:"Cookies"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-400",children:[r.jsx("span",{children:"\uD83C\uDDEE\uD83C\uDDE9"}),r.jsx("span",{children:"Indonesia"})]})]})]})})})]})}var j=s(88307),N=s(6530),v=s(94244),k=s(50732),w=s(92498),P=s(39183),S=s(33734);function I(){let[e,a]=(0,t.useState)(0),[s,i]=(0,t.useState)({users:0,scans:0,vulnerabilities:0,cveDatabase:0}),c=[{icon:j.Z,title:"OSINT Investigator",description:"Investigasi mendalam dengan pencarian NIK, NPWP, nomor HP, email, domain, dan tracking lokasi real-time",color:"text-cyber-green"},{icon:x.Z,title:"Vulnerability Scanner",description:"Deteksi SQLi, XSS, LFI, RCE, Path Traversal dengan scoring CVSS dan mapping CVE otomatis",color:"text-cyber-blue"},{icon:N.Z,title:"File Analyzer",description:"Analisis webshell, malware, ransomware, dan deteksi secret/token dalam berbagai format file",color:"text-cyber-purple"},{icon:v.Z,title:"CVE Intelligence",description:"Database CVE terupdate harian dengan Google dorking preset dan payload generator",color:"text-cyber-red"},{icon:k.Z,title:"Bot Integration",description:"WhatsApp & Telegram bot untuk eksekusi scan, monitoring, dan notifikasi real-time",color:"text-nusantara-gold"},{icon:w.Z,title:"Developer Playground",description:"API testing environment dengan Swagger docs dan request builder interaktif",color:"text-nusantara-emerald"}];return(0,r.jsxs)("div",{className:"min-h-screen",children:[r.jsx(n.default,{isLandingPage:!0}),r.jsx("section",{className:"pt-32 pb-20 px-4",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto text-center",children:[(0,r.jsxs)("h1",{className:"text-5xl md:text-7xl font-bold font-cyber mb-6",children:[r.jsx("span",{className:"cyber-text",children:"Kode"}),r.jsx("span",{className:"nusantara-gold",children:"X"}),r.jsx("span",{className:"text-white",children:"Guard"})]}),r.jsx("p",{className:"text-xl md:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto",children:"Platform Cybersecurity & Bug Hunting mandiri dengan fitur OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bot Automation"}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12",children:[(0,r.jsxs)(l.default,{href:"/register",className:"cyber-btn-primary text-lg px-8 py-4",children:["Mulai Gratis ",r.jsx(P.Z,{className:"ml-2 h-5 w-5"})]}),r.jsx(l.default,{href:"/demo",className:"cyber-btn text-lg px-8 py-4",children:"Lihat Demo"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto",children:[(0,r.jsxs)("div",{className:"cyber-card p-6",children:[r.jsx("div",{className:"text-3xl font-bold cyber-text",children:s.users.toLocaleString()}),r.jsx("div",{className:"text-gray-400",children:"Active Users"})]}),(0,r.jsxs)("div",{className:"cyber-card p-6",children:[r.jsx("div",{className:"text-3xl font-bold text-cyber-blue",children:s.scans.toLocaleString()}),r.jsx("div",{className:"text-gray-400",children:"Scans Completed"})]}),(0,r.jsxs)("div",{className:"cyber-card p-6",children:[r.jsx("div",{className:"text-3xl font-bold text-cyber-red",children:s.vulnerabilities.toLocaleString()}),r.jsx("div",{className:"text-gray-400",children:"Vulnerabilities Found"})]}),(0,r.jsxs)("div",{className:"cyber-card p-6",children:[r.jsx("div",{className:"text-3xl font-bold nusantara-gold",children:s.cveDatabase.toLocaleString()}),r.jsx("div",{className:"text-gray-400",children:"CVE Database"})]})]})]})}),r.jsx("section",{id:"features",className:"py-20 px-4",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsxs)("h2",{className:"text-4xl font-bold font-cyber mb-4",children:["Fitur ",r.jsx("span",{className:"cyber-text",children:"Unggulan"})]}),r.jsx("p",{className:"text-xl text-gray-300 max-w-3xl mx-auto",children:"Solusi lengkap untuk kebutuhan cybersecurity dan bug hunting dengan teknologi terdepan"})]}),r.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:c.map((s,t)=>{let l=s.icon;return(0,r.jsxs)("div",{className:`cyber-card p-8 transition-all duration-300 cursor-pointer ${e===t?"border-cyber-green shadow-lg shadow-cyber-green/20":""}`,onClick:()=>a(t),children:[r.jsx(l,{className:`h-12 w-12 ${s.color} mb-4`}),r.jsx("h3",{className:"text-xl font-bold mb-3",children:s.title}),r.jsx("p",{className:"text-gray-400",children:s.description})]},t)})})]})}),r.jsx("section",{id:"pricing",className:"py-20 px-4 bg-cyber-darker/50",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsxs)("h2",{className:"text-4xl font-bold font-cyber mb-4",children:["Pilih ",r.jsx("span",{className:"nusantara-gold",children:"Plan"})," Anda"]}),r.jsx("p",{className:"text-xl text-gray-300",children:"Dari gratis hingga enterprise, kami punya solusi untuk semua kebutuhan"})]}),r.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:[{name:"Gratis",price:"Rp 0",period:"/bulan",features:["5 Scan/hari","Basic OSINT","File Analyzer (2MB)","Community Support"],color:"border-gray-600",popular:!1},{name:"Pelajar",price:"Rp 25.000",period:"/bulan",features:["50 Scan/hari","Full OSINT","File Analyzer (10MB)","CVE Database","Email Support"],color:"border-cyber-blue",popular:!1},{name:"Bug Hunter",price:"Rp 150.000",period:"/bulan",features:["Unlimited Scan","Advanced OSINT","File Analyzer (100MB)","Bot Integration","Priority Support","API Access"],color:"border-cyber-green",popular:!0},{name:"Cybersecurity",price:"Rp 500.000",period:"/bulan",features:["Enterprise Features","Custom Integration","Dedicated Support","White-label API","Advanced Analytics"],color:"border-nusantara-gold",popular:!1}].map((e,a)=>(0,r.jsxs)("div",{className:`cyber-card p-8 relative ${e.color} ${e.popular?"ring-2 ring-cyber-green":""}`,children:[e.popular&&r.jsx("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:r.jsx("span",{className:"bg-cyber-green text-black px-4 py-1 rounded-full text-sm font-bold",children:"POPULER"})}),(0,r.jsxs)("div",{className:"text-center mb-6",children:[r.jsx("h3",{className:"text-xl font-bold mb-2",children:e.name}),(0,r.jsxs)("div",{className:"text-3xl font-bold cyber-text",children:[e.price,r.jsx("span",{className:"text-sm text-gray-400",children:e.period})]})]}),r.jsx("ul",{className:"space-y-3 mb-8",children:e.features.map((e,a)=>(0,r.jsxs)("li",{className:"flex items-center",children:[r.jsx(S.Z,{className:"h-4 w-4 text-cyber-green mr-2"}),r.jsx("span",{className:"text-gray-300",children:e})]},a))}),r.jsx("button",{className:`w-full py-3 rounded-lg font-semibold transition-all duration-300 ${e.popular?"bg-cyber-green text-black hover:bg-transparent hover:text-cyber-green border border-cyber-green":"cyber-btn"}`,children:"Pilih Plan"})]},a))})]})}),r.jsx("footer",{className:"py-12 px-4 border-t border-gray-800",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsxs)("div",{className:"grid md:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[r.jsx(x.Z,{className:"h-6 w-6 text-cyber-green"}),r.jsx("span",{className:"text-lg font-bold font-cyber",children:"KodeXGuard"})]}),r.jsx("p",{className:"text-gray-400",children:"Platform cybersecurity terdepan untuk profesional keamanan dan bug hunter Indonesia."})]}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-semibold mb-4",children:"Produk"}),(0,r.jsxs)("ul",{className:"space-y-2 text-gray-400",children:[r.jsx("li",{children:r.jsx(l.default,{href:"/osint",className:"hover:text-cyber-green",children:"OSINT Tools"})}),r.jsx("li",{children:r.jsx(l.default,{href:"/scanner",className:"hover:text-cyber-green",children:"Vulnerability Scanner"})}),r.jsx("li",{children:r.jsx(l.default,{href:"/file-analyzer",className:"hover:text-cyber-green",children:"File Analyzer"})}),r.jsx("li",{children:r.jsx(l.default,{href:"/cve",className:"hover:text-cyber-green",children:"CVE Database"})})]})]}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-semibold mb-4",children:"Sumber Daya"}),(0,r.jsxs)("ul",{className:"space-y-2 text-gray-400",children:[r.jsx("li",{children:r.jsx(l.default,{href:"/docs",className:"hover:text-cyber-green",children:"Dokumentasi"})}),r.jsx("li",{children:r.jsx(l.default,{href:"/api",className:"hover:text-cyber-green",children:"API Reference"})}),r.jsx("li",{children:r.jsx(l.default,{href:"/blog",className:"hover:text-cyber-green",children:"Blog"})}),r.jsx("li",{children:r.jsx(l.default,{href:"/community",className:"hover:text-cyber-green",children:"Komunitas"})})]})]}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-semibold mb-4",children:"Perusahaan"}),(0,r.jsxs)("ul",{className:"space-y-2 text-gray-400",children:[r.jsx("li",{children:r.jsx(l.default,{href:"/about",className:"hover:text-cyber-green",children:"Tentang Kami"})}),r.jsx("li",{children:r.jsx(l.default,{href:"/contact",className:"hover:text-cyber-green",children:"Kontak"})}),r.jsx("li",{children:r.jsx(l.default,{href:"/privacy",className:"hover:text-cyber-green",children:"Kebijakan Privasi"})}),r.jsx("li",{children:r.jsx(l.default,{href:"/terms",className:"hover:text-cyber-green",children:"Syarat & Ketentuan"})})]})]})]}),r.jsx("div",{className:"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400",children:r.jsx("p",{children:"\xa9 2024 KodeXGuard. All rights reserved. Made with ❤️ for Indonesian Cybersecurity Community."})})]})}),r.jsx(f,{})]})}},90908:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>r});let r=(0,s(68570).createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\page.tsx#default`)}};var a=require("../webpack-runtime.js");a.C(e);var s=e=>a(a.s=e),r=a.X(0,[216,592],()=>s(35434));module.exports=r})();