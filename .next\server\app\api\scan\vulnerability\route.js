(()=>{var e={};e.id=8078,e.ids=[8078],e.modules={62849:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=62849,e.exports=t},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},96119:e=>{"use strict";e.exports=require("perf_hooks")},35816:e=>{"use strict";e.exports=require("process")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},95346:e=>{"use strict";e.exports=require("timers")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},30604:e=>{"use strict";e.exports=require("node:dns")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},22286:e=>{"use strict";e.exports=require("node:https")},87503:e=>{"use strict";e.exports=require("node:net")},70612:e=>{"use strict";e.exports=require("node:os")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},97742:e=>{"use strict";e.exports=require("node:process")},39630:e=>{"use strict";e.exports=require("node:querystring")},55467:e=>{"use strict";e.exports=require("node:sqlite")},84492:e=>{"use strict";e.exports=require("node:stream")},99397:e=>{"use strict";e.exports=require("node:timers/promises")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},93630:(e,t,s)=>{"use strict";s.r(t),s.d(t,{originalPathname:()=>g,patchFetch:()=>R,requestAsyncStorage:()=>E,routeModule:()=>w,serverHooks:()=>S,staticGenerationAsyncStorage:()=>m});var r={};s.r(r),s.d(r,{GET:()=>h,POST:()=>y});var a=s(49303),i=s(88716),n=s(60670),o=s(87070),c=s(29712),u=s(24544);class l{constructor(){this.scanTests=new Map,this.activeScan=new Map,this.initializeScanTests()}initializeScanTests(){this.scanTests.set("sqli",[{name:"Basic SQL Injection",type:"sqli",payloads:["' OR '1'='1","' OR 1=1--","' UNION SELECT NULL--","'; DROP TABLE users--","' AND (SELECT COUNT(*) FROM information_schema.tables)>0--"],patterns:[/mysql_fetch_array/i,/ORA-\d{5}/i,/Microsoft.*ODBC.*SQL Server/i,/PostgreSQL.*ERROR/i,/Warning.*mysql_/i,/valid MySQL result/i,/MySqlClient\./i],severity:"critical",description:"SQL Injection vulnerability allows attackers to manipulate database queries",recommendation:"Use parameterized queries and input validation"}]),this.scanTests.set("xss",[{name:"Cross-Site Scripting",type:"xss",payloads:['<script>alert("XSS")</script>','<img src=x onerror=alert("XSS")>','<svg onload=alert("XSS")>','javascript:alert("XSS")','"><script>alert("XSS")</script>'],patterns:[/<script[^>]*>.*?<\/script>/i,/javascript:/i,/on\w+\s*=/i],severity:"high",description:"Cross-Site Scripting allows execution of malicious scripts",recommendation:"Implement proper input sanitization and output encoding"}]),this.scanTests.set("lfi",[{name:"Local File Inclusion",type:"lfi",payloads:["../../../etc/passwd","..\\..\\..\\windows\\system32\\drivers\\etc\\hosts","/etc/passwd","C:\\windows\\system32\\drivers\\etc\\hosts","....//....//....//etc/passwd"],patterns:[/root:.*:0:0:/,/\[drivers\]/i,/# Copyright \(c\) 1993-2009 Microsoft Corp/i],severity:"high",description:"Local File Inclusion allows reading sensitive files",recommendation:"Validate and sanitize file path inputs"}]),this.scanTests.set("rce",[{name:"Remote Code Execution",type:"rce",payloads:["; ls -la","| whoami","`id`","$(whoami)","; cat /etc/passwd","& dir","| type C:\\windows\\system32\\drivers\\etc\\hosts"],patterns:[/uid=\d+\(.*?\)/,/gid=\d+\(.*?\)/,/root:.*:0:0:/,/Volume.*Serial Number/i,/Directory of/i],severity:"critical",description:"Remote Code Execution allows arbitrary command execution",recommendation:"Avoid system calls with user input, use whitelisting"}]),this.scanTests.set("path_traversal",[{name:"Path Traversal",type:"path_traversal",payloads:["../","..\\","..../","....\\","%2e%2e%2f","%2e%2e%5c","..%2f","..%5c"],patterns:[/root:.*:0:0:/,/\[boot loader\]/i,/\[operating systems\]/i],severity:"medium",description:"Path traversal allows access to files outside intended directory",recommendation:"Implement proper path validation and canonicalization"}])}async startScan(e){let t=this.generateScanId();try{return await u.db.query(`INSERT INTO scan_history (user_id, scan_type, target, status, created_at)
         VALUES (?, 'vulnerability', ?, 'pending', NOW())`,[e.userId,e.target]),this.executeScan(t,e).catch(e=>{console.error(`Scan ${t} failed:`,e),this.updateScanStatus(t,"failed")}),{scanId:t,status:"started"}}catch(e){throw console.error("Failed to start scan:",e),Error("Failed to start scan")}}async executeScan(e,t){let s=Date.now();try{await this.updateScanStatus(e,"running");let r=[],a=await this.discoverEndpoints(t.target,t.maxDepth||3);for(let e of t.scanTypes)for(let s of this.scanTests.get(e)||[])for(let e of a){let a=await this.testEndpoint(e,s,t.timeout||3e4);r.push(...a)}let i=this.calculateSummary(r),n=Date.now()-s,o={scanId:e,target:t.target,status:"completed",vulnerabilities:r,summary:i,scanDuration:n,startTime:new Date(s),endTime:new Date};await this.saveScanResults(o,t.userId),await this.updateScanStatus(e,"completed")}catch(t){console.error(`Scan execution failed for ${e}:`,t),await this.updateScanStatus(e,"failed")}}async discoverEndpoints(e,t){let s=new Set,r=new URL(e);s.add(e);try{let e=`${r.origin}/robots.txt`,t=await c.Z.get(e,{timeout:1e4});if(200===t.status){let e=t.data.match(/Disallow:\s*(.+)/gi);e&&e.forEach(e=>{let t=e.replace(/Disallow:\s*/i,"").trim();t&&"/"!==t&&s.add(`${r.origin}${t}`)})}}catch(e){}return["/admin","/login","/api","/search","/contact","/upload","/download","/user","/profile"].forEach(e=>{s.add(`${r.origin}${e}`)}),Array.from(s).slice(0,20)}async testEndpoint(e,t,s){let r=[];try{for(let a of t.payloads){let i=`${e}?test=${encodeURIComponent(a)}`,n=await this.testRequest("GET",i,null,t,s);n&&r.push(n);let o=await this.testRequest("POST",e,{test:a},t,s);o&&r.push(o)}}catch(e){}return r}async testRequest(e,t,s,r,a){try{let i={method:e,url:t,timeout:a,validateStatus:()=>!0,maxRedirects:5};s&&"POST"===e&&(i.data=s,i.headers={"Content-Type":"application/x-www-form-urlencoded"});let n=(await (0,c.Z)(i)).data;for(let e of r.patterns)if(e.test(n))return{id:this.generateVulnId(),type:r.type,severity:r.severity,url:t,parameter:s?Object.keys(s)[0]:"query parameter",payload:s?Object.values(s)[0]:new URL(t).searchParams.get("test")||"",evidence:n.substring(0,500),recommendation:r.recommendation,description:r.description};return null}catch(e){return null}}calculateSummary(e){let t={total:e.length,critical:0,high:0,medium:0,low:0,info:0};return e.forEach(e=>{t[e.severity]++}),t}async saveScanResults(e,t){try{await u.db.query(`UPDATE scan_history 
         SET results = ?, status = ?, vulnerabilities_found = ?, scan_duration = ?, completed_at = NOW()
         WHERE user_id = ? AND target = ? AND status = 'running'`,[JSON.stringify(e),e.status,e.vulnerabilities.length,e.scanDuration,t,e.target])}catch(e){console.error("Failed to save scan results:",e)}}async updateScanStatus(e,t){try{console.log(`Scan ${e} status updated to: ${t}`)}catch(e){console.error("Failed to update scan status:",e)}}async getScanResults(e,t){try{let e=await u.db.queryOne("SELECT * FROM scan_history WHERE user_id = ? ORDER BY created_at DESC LIMIT 1",[t]);if(!e||!e.results)return null;return JSON.parse(e.results)}catch(e){return console.error("Failed to get scan results:",e),null}}async getScanHistory(e,t=20){try{return await u.db.query(`SELECT * FROM scan_history 
         WHERE user_id = ? AND scan_type = 'vulnerability'
         ORDER BY created_at DESC 
         LIMIT ?`,[e,t])}catch(e){return console.error("Failed to get scan history:",e),[]}}generateScanId(){return"scan_"+Date.now()+"_"+Math.random().toString(36).substr(2,9)}generateVulnId(){return"vuln_"+Date.now()+"_"+Math.random().toString(36).substr(2,9)}}let d=new l;var p=s(90455);async function y(e){try{let t=e.headers.get("authorization"),s=t?.replace("Bearer ","");if(!s)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});let r=await p.e8.getUserByToken(s);if(!r)return o.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let{target:a,scanTypes:i=[],maxDepth:n=3,timeout:c=30,followRedirects:l=!0}=await e.json();if(!a)return o.NextResponse.json({success:!1,error:"Target URL is required",code:"MISSING_TARGET"},{status:400});if(!i||0===i.length)return o.NextResponse.json({success:!1,error:"At least one scan type is required",code:"MISSING_SCAN_TYPES"},{status:400});try{new URL(a)}catch{return o.NextResponse.json({success:!1,error:"Invalid URL format",code:"INVALID_URL"},{status:400})}let y=["sqli","xss","lfi","rfi","rce","csrf","ssrf","xxe","idor","path_traversal"],h=i.filter(e=>!y.includes(e));if(h.length>0)return o.NextResponse.json({success:!1,error:`Invalid scan types: ${h.join(", ")}`,code:"INVALID_SCAN_TYPES"},{status:400});let w=u.vo.getInstance(),E=new Date().toISOString().split("T")[0],m=await w.query(`SELECT COUNT(*) as count FROM scan_history
       WHERE user_id = ? AND scan_type = 'vulnerability' AND DATE(created_at) = ?`,[r.id,E]),S=await w.query("SELECT scan_quota_daily FROM plans WHERE id = ?",[r.planId]),g=S[0]?.scan_quota_daily||5,R=m[0]?.count||0;if(R>=g)return o.NextResponse.json({success:!1,error:`Daily scan quota exceeded. Used: ${R}/${g}`,code:"QUOTA_EXCEEDED"},{status:429});let x={target:a,scanTypes:i,maxDepth:n,timeout:1e3*c,followRedirects:l,userId:r.id},f=await d.startScan(x);return o.NextResponse.json({success:!0,data:{scanId:f.scanId,status:f.status,target:a,scanTypes:i,quotaUsed:R+1,quotaLimit:g,maxDepth:n,timeout:c},message:"Vulnerability scan started successfully",timestamp:new Date().toISOString()})}catch(e){return console.error("Vulnerability scan error:",e),o.NextResponse.json({success:!1,error:"Internal server error",code:"INTERNAL_ERROR"},{status:500})}}async function h(e){try{let t=e.headers.get("authorization"),s=t?.replace("Bearer ","");if(!s)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});let r=await p.e8.getUserByToken(s);if(!r)return o.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let{searchParams:a}=new URL(e.url),i=parseInt(a.get("page")||"1"),n=parseInt(a.get("limit")||"20"),c=await d.getScanHistory(r.id,n);return o.NextResponse.json({success:!0,data:c,pagination:{page:i,limit:n,total:c.length,totalPages:Math.ceil(c.length/n)},timestamp:new Date().toISOString()})}catch(e){return console.error("Scan history error:",e),o.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let w=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/scan/vulnerability/route",pathname:"/api/scan/vulnerability",filename:"route",bundlePath:"app/api/scan/vulnerability/route"},resolvedPagePath:"D:\\Users\\Downloads\\kodeXGuard\\app\\api\\scan\\vulnerability\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:E,staticGenerationAsyncStorage:m,serverHooks:S}=w,g="/api/scan/vulnerability/route";function R(){return(0,n.patchFetch)({serverHooks:S,staticGenerationAsyncStorage:m})}},90455:(e,t,s)=>{"use strict";s.d(t,{e8:()=>d});var r=s(98691),a=s(41482),i=s.n(a),n=s(84770),o=s.n(n),c=s(24544);let u=process.env.JWT_SECRET||"kodexguard-secret",l=process.env.JWT_EXPIRES_IN||"7d";class d{static async hashPassword(e){return await r.ZP.hash(e,12)}static async verifyPassword(e,t){return await r.ZP.compare(e,t)}static generateToken(e){return i().sign(e,u,{expiresIn:l})}static verifyToken(e){try{return i().verify(e,u)}catch(e){return null}}static generateApiKey(){return{apiKey:"kxg_"+o().randomBytes(32).toString("hex"),apiSecret:o().randomBytes(64).toString("hex")}}static async register(e){try{if((await c.db.query("SELECT id FROM users WHERE email = ? OR username = ?",[e.email,e.username])).length>0)return{success:!1,error:"User with this email or username already exists"};let t=await this.hashPassword(e.password),s=await c.db.query(`INSERT INTO users (username, email, password_hash, full_name, plan_id, created_at) 
         VALUES (?, ?, ?, ?, 1, NOW())`,[e.username,e.email,t,e.fullName||null]),r=await c.db.queryOne("SELECT * FROM users WHERE id = ?",[s.insertId]);if(!r)return{success:!1,error:"Failed to create user"};let a=this.generateToken({userId:r.id,username:r.username,email:r.email,role:r.role});return{success:!0,user:r,token:a,message:"User registered successfully"}}catch(e){return console.error("Registration error:",e),{success:!1,error:"Registration failed"}}}static async login(e,t){try{let s=await c.db.queryOne("SELECT * FROM users WHERE email = ? AND is_active = 1",[e]);if(!s||!await this.verifyPassword(t,s.password_hash))return{success:!1,error:"Invalid credentials"};await c.db.query("UPDATE users SET last_login = NOW() WHERE id = ?",[s.id]);let r=this.generateToken({userId:s.id,username:s.username,email:s.email,role:s.role});return{success:!0,user:s,token:r,message:"Login successful"}}catch(e){return console.error("Login error:",e),{success:!1,error:"Login failed"}}}static async getUserByToken(e){try{let t=this.verifyToken(e);if(!t)return null;return await c.db.queryOne("SELECT * FROM users WHERE id = ? AND is_active = 1",[t.userId])}catch(e){return console.error("Get user by token error:",e),null}}static async createApiKey(e,t,s=[]){try{let{apiKey:r,apiSecret:a}=this.generateApiKey(),i=await c.db.query(`INSERT INTO api_keys (user_id, key_name, api_key, api_secret, permissions, created_at) 
         VALUES (?, ?, ?, ?, ?, NOW())`,[e,t,r,a,JSON.stringify(s)]),n=await c.db.queryOne("SELECT * FROM api_keys WHERE id = ?",[i.insertId]);return{success:!0,apiKey:n,message:"API key created successfully"}}catch(e){return console.error("Create API key error:",e),{success:!1,error:"Failed to create API key"}}}static async verifyApiKey(e){try{let t=await c.db.queryOne("SELECT * FROM api_keys WHERE api_key = ? AND is_active = 1",[e]);if(!t)return null;let s=await c.db.queryOne("SELECT * FROM users WHERE id = ? AND is_active = 1",[t.userId]);if(!s)return null;return await c.db.query("UPDATE api_keys SET usage_count = usage_count + 1, last_used = NOW() WHERE id = ?",[t.id]),{user:s,apiKeyData:t}}catch(e){return console.error("Verify API key error:",e),null}}static async getUserApiKeys(e){try{return await c.db.query("SELECT * FROM api_keys WHERE user_id = ? ORDER BY created_at DESC",[e])}catch(e){return console.error("Get user API keys error:",e),[]}}static async deleteApiKey(e,t){try{return(await c.db.query("DELETE FROM api_keys WHERE id = ? AND user_id = ?",[t,e])).affectedRows>0}catch(e){return console.error("Delete API key error:",e),!1}}static async changePassword(e,t,s){try{let r=await c.db.queryOne("SELECT * FROM users WHERE id = ?",[e]);if(!r)return{success:!1,error:"User not found"};if(!await this.verifyPassword(t,r.password_hash))return{success:!1,error:"Current password is incorrect"};let a=await this.hashPassword(s);return await c.db.query("UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?",[a,e]),{success:!0,message:"Password changed successfully"}}catch(e){return console.error("Change password error:",e),{success:!1,error:"Failed to change password"}}}}},24544:(e,t,s)=>{"use strict";s.d(t,{db:()=>u,vo:()=>n});var r=s(73785),a=s(79984),i=s(31328);class n{constructor(){this.pool=r.createPool({host:process.env.DB_HOST||"localhost",port:parseInt(process.env.DB_PORT||"3306"),user:process.env.DB_USER||"root",password:process.env.DB_PASSWORD||"rootkan",database:process.env.DB_NAME||"db_kodexguard",waitForConnections:!0,connectionLimit:10,queueLimit:0,charset:"utf8mb4",timezone:"+00:00"})}static getInstance(){return n.instance||(n.instance=new n),n.instance}async query(e,t){try{let[s]=await this.pool.execute(e,t);return s}catch(e){throw console.error("Database query error:",e),e}}async transaction(e){let t=await this.pool.getConnection();try{await t.beginTransaction();let s=await e(t);return await t.commit(),s}catch(e){throw await t.rollback(),e}finally{t.release()}}async close(){await this.pool.end()}}class o{constructor(){this.client=(0,a.createClient)({url:process.env.REDIS_URL||"redis://localhost:6379",password:process.env.REDIS_PASSWORD||void 0,socket:{reconnectStrategy:e=>Math.min(50*e,500)}}),this.client.on("error",e=>{console.error("Redis Client Error:",e)}),this.client.on("connect",()=>{console.log("Redis Client Connected")})}static getInstance(){return o.instance||(o.instance=new o),o.instance}async connect(){this.client.isOpen||await this.client.connect()}async get(e){return await this.connect(),await this.client.get(e)}async set(e,t,s){await this.connect(),s?await this.client.setEx(e,s,t):await this.client.set(e,t)}async del(e){await this.connect(),await this.client.del(e)}async exists(e){return await this.connect(),await this.client.exists(e)===1}async incr(e){return await this.connect(),await this.client.incr(e)}async expire(e,t){await this.connect(),await this.client.expire(e,t)}async close(){this.client.isOpen&&await this.client.quit()}}class c{constructor(){this.client=new i.Client({node:process.env.ELASTICSEARCH_URL||"http://localhost:9200",auth:process.env.ELASTICSEARCH_USERNAME&&process.env.ELASTICSEARCH_PASSWORD?{username:process.env.ELASTICSEARCH_USERNAME,password:process.env.ELASTICSEARCH_PASSWORD}:void 0,requestTimeout:3e4,pingTimeout:3e3,sniffOnStart:!1})}static getInstance(){return c.instance||(c.instance=new c),c.instance}async index(e,t,s){try{await this.client.index({index:e,id:t,body:s})}catch(e){throw console.error("Elasticsearch index error:",e),e}}async search(e,t){try{return await this.client.search({index:e,body:t})}catch(e){throw console.error("Elasticsearch search error:",e),e}}async delete(e,t){try{await this.client.delete({index:e,id:t})}catch(e){throw console.error("Elasticsearch delete error:",e),e}}async createIndex(e,t){try{await this.client.indices.exists({index:e})||await this.client.indices.create({index:e,body:{mappings:t}})}catch(e){throw console.error("Elasticsearch create index error:",e),e}}async close(){await this.client.close()}}let u=n.getInstance();o.getInstance(),c.getInstance()}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[9276,5972,9309,9712,2086],()=>s(93630));module.exports=r})();