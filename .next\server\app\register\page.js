(()=>{var e={};e.id=4011,e.ids=[4011],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},57226:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>u,tree:()=>d}),t(7203),t(30829),t(35866);var a=t(23191),r=t(88716),l=t(37922),n=t.n(l),i=t(95231),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);t.d(s,c);let d=["",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,7203)),"D:\\Users\\Downloads\\kodeXGuard\\app\\register\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\Users\\Downloads\\kodeXGuard\\app\\register\\page.tsx"],m="/register/page",x={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},43495:(e,s,t)=>{Promise.resolve().then(t.bind(t,99869))},99869:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var a=t(10326),r=t(17577),l=t(90434),n=t(35047),i=t(58038),c=t(79635),d=t(5932),o=t(9015),m=t(91216),x=t(12714);let u=(0,t(76557).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);var p=t(2262);function h(){let e=(0,n.useRouter)(),[s,t]=(0,r.useState)({username:"",email:"",fullName:"",password:"",confirmPassword:"",plan:"free",agreeToTerms:!1}),[h,g]=(0,r.useState)(!1),[y,j]=(0,r.useState)(!1),[f,b]=(0,r.useState)(!1),[N,v]=(0,r.useState)(""),[w,k]=(0,r.useState)(""),P=(e=>{let s=e.length>=8,t=/[A-Z]/.test(e),a=/[a-z]/.test(e),r=/\d/.test(e),l=/[!@#$%^&*(),.?":{}|<>]/.test(e);return{minLength:s,hasUpper:t,hasLower:a,hasNumber:r,hasSpecial:l,isValid:s&&t&&a&&r&&l}})(s.password),Z=async t=>{if(t.preventDefault(),b(!0),v(""),k(""),!P.isValid){v("Password does not meet requirements"),b(!1);return}if(s.password!==s.confirmPassword){v("Passwords do not match"),b(!1);return}if(!s.agreeToTerms){v("You must agree to the terms and conditions"),b(!1);return}try{let t=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:s.username,email:s.email,fullName:s.fullName,password:s.password,plan:s.plan})});if(t.ok)k("Account created successfully! Please check your email for verification."),setTimeout(()=>{e.push("/login")},3e3);else{let e=await t.json();v(e.message||"Registration failed")}}catch(e){v("Network error. Please try again.")}finally{b(!1)}},S=e=>{let{name:s,value:a,type:r}=e.target,l=e.target.checked;t(e=>({...e,[s]:"checkbox"===r?l:a}))};return(0,a.jsxs)("div",{className:"min-h-screen flex items-center justify-center px-4 py-12",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-cyber opacity-50"}),a.jsx("div",{className:"absolute inset-0 nusantara-pattern opacity-5"}),(0,a.jsxs)("div",{className:"relative z-10 w-full max-w-2xl",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsxs)(l.default,{href:"/",className:"inline-flex items-center space-x-2",children:[a.jsx(i.Z,{className:"h-12 w-12 text-cyber-green animate-pulse"}),(0,a.jsxs)("span",{className:"text-3xl font-bold font-cyber",children:[a.jsx("span",{className:"cyber-text",children:"Kode"}),a.jsx("span",{className:"nusantara-gold",children:"X"}),a.jsx("span",{className:"text-white",children:"Guard"})]})]}),a.jsx("p",{className:"mt-2 text-gray-400",children:"Bergabung dengan komunitas cybersecurity Indonesia"})]}),a.jsx(p.Zb,{border:"green",glow:!0,children:(0,a.jsxs)("div",{className:"p-8",children:[N&&a.jsx("div",{className:"mb-6",children:a.jsx(p.XO,{type:"error",title:"Registration Failed",message:N,onClose:()=>v("")})}),w&&a.jsx("div",{className:"mb-6",children:a.jsx(p.XO,{type:"success",title:"Registration Successful",message:w})}),(0,a.jsxs)("form",{onSubmit:Z,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-300 mb-2",children:"Username"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(c.Z,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{id:"username",name:"username",type:"text",required:!0,value:s.username,onChange:S,className:"cyber-input pl-10 w-full",placeholder:"johndoe"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"fullName",className:"block text-sm font-medium text-gray-300 mb-2",children:"Full Name"}),a.jsx("input",{id:"fullName",name:"fullName",type:"text",required:!0,value:s.fullName,onChange:S,className:"cyber-input w-full",placeholder:"John Doe"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-300 mb-2",children:"Email Address"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(d.Z,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{id:"email",name:"email",type:"email",required:!0,value:s.email,onChange:S,className:"cyber-input pl-10 w-full",placeholder:"<EMAIL>"})]})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-300 mb-2",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(o.Z,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{id:"password",name:"password",type:h?"text":"password",required:!0,value:s.password,onChange:S,className:"cyber-input pl-10 pr-10 w-full",placeholder:"••••••••"}),a.jsx("button",{type:"button",onClick:()=>g(!h),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-cyber-green",children:h?a.jsx(m.Z,{className:"h-5 w-5"}):a.jsx(x.Z,{className:"h-5 w-5"})})]}),s.password&&(0,a.jsxs)("div",{className:"mt-2 space-y-1",children:[(0,a.jsxs)("div",{className:`text-xs flex items-center space-x-2 ${P.minLength?"text-green-400":"text-gray-400"}`,children:[a.jsx(u,{className:"h-3 w-3"}),a.jsx("span",{children:"At least 8 characters"})]}),(0,a.jsxs)("div",{className:`text-xs flex items-center space-x-2 ${P.hasUpper?"text-green-400":"text-gray-400"}`,children:[a.jsx(u,{className:"h-3 w-3"}),a.jsx("span",{children:"One uppercase letter"})]}),(0,a.jsxs)("div",{className:`text-xs flex items-center space-x-2 ${P.hasLower?"text-green-400":"text-gray-400"}`,children:[a.jsx(u,{className:"h-3 w-3"}),a.jsx("span",{children:"One lowercase letter"})]}),(0,a.jsxs)("div",{className:`text-xs flex items-center space-x-2 ${P.hasNumber?"text-green-400":"text-gray-400"}`,children:[a.jsx(u,{className:"h-3 w-3"}),a.jsx("span",{children:"One number"})]}),(0,a.jsxs)("div",{className:`text-xs flex items-center space-x-2 ${P.hasSpecial?"text-green-400":"text-gray-400"}`,children:[a.jsx(u,{className:"h-3 w-3"}),a.jsx("span",{children:"One special character"})]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-300 mb-2",children:"Confirm Password"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(o.Z,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{id:"confirmPassword",name:"confirmPassword",type:y?"text":"password",required:!0,value:s.confirmPassword,onChange:S,className:"cyber-input pl-10 pr-10 w-full",placeholder:"••••••••"}),a.jsx("button",{type:"button",onClick:()=>j(!y),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-cyber-green",children:y?a.jsx(m.Z,{className:"h-5 w-5"}):a.jsx(x.Z,{className:"h-5 w-5"})})]}),s.confirmPassword&&s.password!==s.confirmPassword&&a.jsx("p",{className:"mt-1 text-xs text-red-400",children:"Passwords do not match"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Choose Your Plan"}),a.jsx("div",{className:"grid md:grid-cols-3 gap-4",children:[{id:"free",name:"Gratis",price:"Rp 0",features:["5 Scan/hari","Basic OSINT","Community Support"]},{id:"student",name:"Pelajar",price:"Rp 25.000",features:["50 Scan/hari","Full OSINT","Email Support"]},{id:"bug_hunter",name:"Bug Hunter",price:"Rp 150.000",features:["Unlimited Scan","Bot Integration","Priority Support"]}].map(e=>(0,a.jsxs)("label",{className:`cursor-pointer border rounded-lg p-4 transition-all duration-200 ${s.plan===e.id?"border-cyber-green bg-cyber-green/10":"border-gray-600 hover:border-gray-500"}`,children:[a.jsx("input",{type:"radio",name:"plan",value:e.id,checked:s.plan===e.id,onChange:S,className:"sr-only"}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("h4",{className:"font-semibold text-white",children:e.name}),a.jsx("p",{className:"text-cyber-green font-bold",children:e.price}),a.jsx("ul",{className:"mt-2 text-xs text-gray-400 space-y-1",children:e.features.map((e,s)=>(0,a.jsxs)("li",{children:["• ",e]},s))})]})]},e.id))})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx("input",{id:"agreeToTerms",name:"agreeToTerms",type:"checkbox",checked:s.agreeToTerms,onChange:S,className:"mt-1 h-4 w-4 text-cyber-green focus:ring-cyber-green border-gray-600 rounded bg-gray-800"}),(0,a.jsxs)("label",{htmlFor:"agreeToTerms",className:"text-sm text-gray-300",children:["I agree to the"," ",a.jsx(l.default,{href:"/terms",className:"text-cyber-green hover:underline",children:"Terms of Service"})," ","and"," ",a.jsx(l.default,{href:"/privacy",className:"text-cyber-green hover:underline",children:"Privacy Policy"})]})]}),a.jsx("button",{type:"submit",disabled:f||!s.agreeToTerms||!P.isValid,className:"w-full cyber-btn-primary py-3 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed",children:f?(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[a.jsx("div",{className:"w-5 h-5 border-2 border-black border-t-transparent rounded-full animate-spin"}),a.jsx("span",{children:"Creating Account..."})]}):"Create Account"})]}),a.jsx("div",{className:"mt-6 text-center",children:(0,a.jsxs)("p",{className:"text-gray-400",children:["Already have an account?"," ",a.jsx(l.default,{href:"/login",className:"text-cyber-green hover:text-cyber-blue transition-colors font-semibold",children:"Sign in here"})]})})]})})]})]})}},91216:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},12714:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},9015:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},5932:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},58038:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},79635:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},35047:(e,s,t)=>{"use strict";var a=t(77389);t.o(a,"usePathname")&&t.d(s,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}})},7203:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\register\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[9276,82,434,7608],()=>t(57226));module.exports=a})();