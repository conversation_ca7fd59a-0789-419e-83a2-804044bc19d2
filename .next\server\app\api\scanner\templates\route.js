"use strict";(()=>{var e={};e.id=472,e.ids=[472],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},57631:(e,t,a)=>{a.r(t),a.d(t,{originalPathname:()=>g,patchFetch:()=>h,requestAsyncStorage:()=>l,routeModule:()=>p,serverHooks:()=>m,staticGenerationAsyncStorage:()=>d});var s={};a.r(s),a.d(s,{GET:()=>c,POST:()=>u});var r=a(49303),n=a(88716),o=a(60670),i=a(87070);async function c(e){try{let t=e.headers.get("authorization"),a=t?.replace("Bearer ","");if(!a)return i.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!a.startsWith("kxg_"))return i.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let s=[{id:"template_001",name:"Quick Web Scan",description:"Fast vulnerability scan for web applications covering common security issues",category:"web",scanTypes:["sqli","xss","csrf","directory_traversal"],settings:{maxDepth:3,timeout:30,threads:5,userAgent:"KodeXGuard Scanner v1.0"},isDefault:!0,createdAt:new Date(Date.now()-2592e6).toISOString(),updatedAt:new Date(Date.now()-432e6).toISOString(),usageCount:156},{id:"template_002",name:"Comprehensive Web Audit",description:"Deep security audit covering all OWASP Top 10 vulnerabilities and more",category:"web",scanTypes:["sqli","xss","csrf","xxe","broken_auth","sensitive_data","xml_injection","broken_access","security_misconfig","vulnerable_components"],settings:{maxDepth:10,timeout:120,threads:10,userAgent:"Mozilla/5.0 (compatible; KodeXGuard/1.0)",excludePaths:["/admin/logout","/user/delete"]},isDefault:!0,createdAt:new Date(Date.now()-3888e6).toISOString(),updatedAt:new Date(Date.now()-1728e5).toISOString(),usageCount:89},{id:"template_003",name:"API Security Scan",description:"Specialized scan for REST APIs and GraphQL endpoints",category:"api",scanTypes:["api_security","auth_bypass","rate_limiting","input_validation","data_exposure"],settings:{timeout:60,threads:8,headers:{"Content-Type":"application/json",Accept:"application/json"}},isDefault:!0,createdAt:new Date(Date.now()-1728e6).toISOString(),updatedAt:new Date(Date.now()-864e5).toISOString(),usageCount:67},{id:"template_004",name:"Mobile App Security",description:"Security assessment for mobile applications and APIs",category:"mobile",scanTypes:["mobile_security","ssl_pinning","data_storage","crypto_implementation"],settings:{timeout:90,threads:6,userAgent:"KodeXGuard Mobile Scanner"},isDefault:!0,createdAt:new Date(Date.now()-1296e6).toISOString(),updatedAt:new Date(Date.now()-2592e5).toISOString(),usageCount:34},{id:"template_005",name:"Network Infrastructure",description:"Network security scan including port scanning and service enumeration",category:"network",scanTypes:["port_scan","service_enum","ssl_check","dns_enum"],settings:{timeout:300,threads:20},isDefault:!0,createdAt:new Date(Date.now()-5184e6).toISOString(),updatedAt:new Date(Date.now()-6048e5).toISOString(),usageCount:123},{id:"template_006",name:"E-commerce Security",description:"Custom template for e-commerce platforms with payment security focus",category:"custom",scanTypes:["sqli","xss","payment_security","session_management","pci_compliance"],settings:{maxDepth:5,timeout:60,threads:8,excludePaths:["/payment/process","/checkout/complete"],includePaths:["/shop","/cart","/checkout","/payment"]},isDefault:!1,createdAt:new Date(Date.now()-864e6).toISOString(),updatedAt:new Date(Date.now()-864e5).toISOString(),usageCount:45}];return s.sort((e,t)=>t.usageCount-e.usageCount),i.NextResponse.json({success:!0,data:s,meta:{total:s.length,default:s.filter(e=>e.isDefault).length,custom:s.filter(e=>!e.isDefault).length,categories:{web:s.filter(e=>"web"===e.category).length,api:s.filter(e=>"api"===e.category).length,mobile:s.filter(e=>"mobile"===e.category).length,network:s.filter(e=>"network"===e.category).length,custom:s.filter(e=>"custom"===e.category).length}},timestamp:new Date().toISOString()})}catch(e){return console.error("Scanner templates API error:",e),i.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}async function u(e){try{let t=e.headers.get("authorization"),a=t?.replace("Bearer ","");if(!a)return i.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!a.startsWith("kxg_"))return i.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let{name:s,description:r,category:n,scanTypes:o,settings:c}=await e.json();if(!s||!r||!n||!o||!Array.isArray(o))return i.NextResponse.json({success:!1,error:"Name, description, category, and scanTypes are required"},{status:400});let u={id:`template_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,name:s,description:r,category:n,scanTypes:o,settings:c||{},isDefault:!1,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),usageCount:0};return i.NextResponse.json({success:!0,data:u,message:"Template created successfully",timestamp:new Date().toISOString()})}catch(e){return console.error("Create template error:",e),i.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let p=new r.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/scanner/templates/route",pathname:"/api/scanner/templates",filename:"route",bundlePath:"app/api/scanner/templates/route"},resolvedPagePath:"D:\\Users\\Downloads\\kodeXGuard\\app\\api\\scanner\\templates\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:l,staticGenerationAsyncStorage:d,serverHooks:m}=p,g="/api/scanner/templates/route";function h(){return(0,o.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:d})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[216,592],()=>a(57631));module.exports=s})();