"use strict";(()=>{var e={};e.id=821,e.ids=[821],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},11367:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>f,patchFetch:()=>g,requestAsyncStorage:()=>d,routeModule:()=>h,serverHooks:()=>m,staticGenerationAsyncStorage:()=>p});var o={};r.r(o),r.d(o,{GET:()=>c,OPTIONS:()=>u});var a=r(49303),n=r(88716),s=r(60670),i=r(87070);async function c(e){try{let{searchParams:t}=new URL(e.url),r=t.get("query")||"",o=t.get("severity")||"",a=t.get("year")||"",n=parseInt(t.get("limit")||"20"),s=parseInt(t.get("offset")||"0");if(n>100)return i.NextResponse.json({success:!1,error:"Limit cannot exceed 100",code:"LIMIT_EXCEEDED"},{status:400});await new Promise(e=>setTimeout(e,500+1e3*Math.random()));let c=function(e,t,r,o,a){let n=[],s=t?[t]:["critical","high","medium","low"],i=r?[r]:["2024","2023","2022","2021"];for(let t=0;t<o;t++){let r=i[Math.floor(Math.random()*i.length)],o=s[Math.floor(Math.random()*s.length)],c=(a+t+1).toString().padStart(4,"0"),u={cveId:`CVE-${r}-${c}`,description:function(e,t){let r=["SQL injection","Cross-site scripting (XSS)","Remote code execution","Buffer overflow","Authentication bypass","Path traversal","Cross-site request forgery (CSRF)","Server-side request forgery (SSRF)","XML external entity (XXE)","Insecure direct object reference"],o=["WordPress Plugin","Apache HTTP Server","Microsoft Windows","Linux Kernel","PHP Framework","Node.js Package","Java Library","Python Package","Web Application","Mobile Application","Database System","Network Device"],a=r[Math.floor(Math.random()*r.length)],n=o[Math.floor(Math.random()*o.length)],s=`${a} vulnerability in ${n}`;switch(e&&e.length>0&&(s+=` related to ${e}`),t){case"critical":s+=" allows remote attackers to execute arbitrary code with system privileges";break;case"high":s+=" allows remote attackers to gain unauthorized access";break;case"medium":s+=" allows attackers to bypass security restrictions";break;case"low":s+=" may allow information disclosure"}return s+"."}(e,o),severity:o,cvssScore:function(e){let t={critical:[9,10],high:[7,8.9],medium:[4,6.9],low:[.1,3.9]}[e]||[.1,10];return Math.round(10*(Math.random()*(t[1]-t[0])+t[0]))/10}(o),cvssVector:function(){let e=["CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H","CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H","CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:H/I:H/A:H","CVSS:3.1/AV:L/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H","CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N"];return e[Math.floor(Math.random()*e.length)]}(),publishedDate:l(r),modifiedDate:l(r,!0),affectedProducts:function(){let e=["WebApp Framework 1.0-2.5","CMS Platform 3.x","E-commerce Plugin 2.1-2.3","API Gateway 1.x-2.x","File Manager Pro 3.0","Security Scanner 4.2","Database Connector 1.5","Authentication Service 2.0"],t=Math.floor(3*Math.random())+1,r=[];for(let o=0;o<t;o++){let t=e[Math.floor(Math.random()*e.length)];r.includes(t)||r.push(t)}return r}(),references:[`https://nvd.nist.gov/vuln/detail/CVE-${r}-${c}`,`https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-${r}-${c}`,`https://security-tracker.debian.org/tracker/CVE-${r}-${c}`,`https://access.redhat.com/security/cve/CVE-${r}-${c}`],exploits:function(e){if("low"===e||Math.random()>.4)return[];let t=[],r="critical"===e?Math.floor(3*Math.random())+1:Math.floor(2*Math.random())+1;for(let o=0;o<r;o++)t.push({title:`${e.charAt(0).toUpperCase()+e.slice(1)} Exploit ${o+1}`,url:`https://exploit-db.com/exploits/${Math.floor(5e4*Math.random())+1e4}`,type:Math.random()>.5?"Public Exploit":"Proof of Concept"});return t}(o),trending:Math.random()>.8};n.push(u)}return n}(r,o,a,n,s);return i.NextResponse.json({success:!0,data:{query:r,filters:{severity:o,year:a},total:189234,limit:n,offset:s,cves:c},message:"CVE search completed successfully",timestamp:new Date().toISOString()})}catch(e){return console.error("CVE search error:",e),i.NextResponse.json({success:!1,error:"Internal server error",code:"INTERNAL_ERROR"},{status:500})}}function l(e,t=!1){let r=new Date(`${e}-01-01`),o=t?new Date:new Date(`${e}-12-31`);return new Date(r.getTime()+Math.random()*(o.getTime()-r.getTime())).toISOString().split("T")[0]}async function u(){return new i.NextResponse(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}let h=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/cve/search/route",pathname:"/api/cve/search",filename:"route",bundlePath:"app/api/cve/search/route"},resolvedPagePath:"D:\\Users\\Downloads\\kodeXGuard\\app\\api\\cve\\search\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:d,staticGenerationAsyncStorage:p,serverHooks:m}=h,f="/api/cve/search/route";function g(){return(0,s.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:p})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[216,592],()=>r(11367));module.exports=o})();