"use strict";(()=>{var e={};e.id=2,e.ids=[2],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},93944:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>g,patchFetch:()=>h,requestAsyncStorage:()=>l,routeModule:()=>d,serverHooks:()=>m,staticGenerationAsyncStorage:()=>p});var s={};t.r(s),t.d(s,{OPTIONS:()=>c,POST:()=>u});var a=t(49303),o=t(88716),n=t(60670),i=t(87070);async function u(e){try{let{username:r,email:t,password:s,fullName:a}=await e.json();if(!r||!t||!s)return i.NextResponse.json({success:!1,error:"Username, email and password are required",code:"MISSING_FIELDS"},{status:400});if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t))return i.NextResponse.json({success:!1,error:"Invalid email format",code:"INVALID_EMAIL"},{status:400});if(s.length<6)return i.NextResponse.json({success:!1,error:"Password must be at least 6 characters long",code:"WEAK_PASSWORD"},{status:400});if(["<EMAIL>","<EMAIL>","<EMAIL>"].includes(t))return i.NextResponse.json({success:!1,error:"Email already registered",code:"EMAIL_EXISTS"},{status:409});let o=`kxg_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,n={id:Math.floor(1e3*Math.random()),username:r,email:t,fullName:a||r,role:"user",plan:"gratis",planName:"Gratis",isActive:!0,emailVerified:!1,createdAt:new Date().toISOString(),lastLogin:null},u=i.NextResponse.json({success:!0,data:{token:o,user:n,expiresIn:"7d"},message:"Registration successful",timestamp:new Date().toISOString()});return u.cookies.set("auth-token",o,{httpOnly:!0,secure:!0,sameSite:"strict",maxAge:86400}),u}catch(e){return console.error("Registration error:",e),i.NextResponse.json({success:!1,error:"Internal server error",code:"INTERNAL_ERROR"},{status:500})}}async function c(){return new i.NextResponse(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}let d=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/auth/register/route",pathname:"/api/auth/register",filename:"route",bundlePath:"app/api/auth/register/route"},resolvedPagePath:"D:\\Users\\Downloads\\kodeXGuard\\app\\api\\auth\\register\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:l,staticGenerationAsyncStorage:p,serverHooks:m}=d,g="/api/auth/register/route";function h(){return(0,n.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:p})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[216,592],()=>t(93944));module.exports=s})();