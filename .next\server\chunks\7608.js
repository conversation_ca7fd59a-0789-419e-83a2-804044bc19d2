exports.id=7608,exports.ids=[7608],exports.modules={60546:(e,r,t)=>{Promise.resolve().then(t.bind(t,67382))},27807:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,12994,23)),Promise.resolve().then(t.t.bind(t,96114,23)),Promise.resolve().then(t.t.bind(t,9727,23)),Promise.resolve().then(t.t.bind(t,79671,23)),Promise.resolve().then(t.t.bind(t,41868,23)),Promise.resolve().then(t.t.bind(t,84759,23))},67382:(e,r,t)=>{"use strict";t.d(r,{AuthProvider:()=>i,a:()=>o});var n=t(10326),a=t(17577);let s=(0,a.createContext)(void 0);function i({children:e}){let[r,t]=(0,a.useState)(null),[i,o]=(0,a.useState)(null),[l,d]=(0,a.useState)(!0),c=async(e,r)=>{try{d(!0);let n=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:r})}),a=await n.json();if(a.success&&a.token)return o(a.token),t(a.user),!0;return!1}catch(e){return console.error("Login error:",e),!1}finally{d(!1)}},u=async()=>{try{let e=await fetch("/api/auth/refresh",{method:"POST",headers:{Authorization:`Bearer ${i}`}}),r=await e.json();r.success&&r.token&&o(r.token)}catch(e){console.error("Token refresh error:",e),h()}},h=()=>{t(null),o(null)};return n.jsx(s.Provider,{value:{user:r,token:i,login:c,logout:h,isLoading:l,isAuthenticated:!!r&&!!i,refreshToken:u},children:e})}function o(){let e=(0,a.useContext)(s);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},2262:(e,r,t)=>{"use strict";t.d(r,{Rm:()=>s,TT:()=>i,XO:()=>o,Zb:()=>a});var n=t(10326);function a({children:e,className:r="",hover:t=!1,glow:a=!1,border:s="default"}){return n.jsx("div",{className:`
        bg-gray-900/50 backdrop-blur-sm rounded-lg border ${{default:"border-gray-700",green:"border-cyber-green",blue:"border-cyber-blue",red:"border-cyber-red",gold:"border-nusantara-gold"}[s]}
        ${t?"hover:border-cyber-green hover:shadow-lg hover:shadow-cyber-green/20 transition-all duration-300 cursor-pointer":""}
        ${a?({default:"",green:"shadow-lg shadow-cyber-green/20",blue:"shadow-lg shadow-cyber-blue/20",red:"shadow-lg shadow-cyber-red/20",gold:"shadow-lg shadow-nusantara-gold/20"})[s]:""}
        ${r}
      `,children:e})}function s({title:e,value:r,icon:t,color:s="green",trend:i,loading:o=!1}){let l={green:"text-cyber-green",blue:"text-cyber-blue",red:"text-cyber-red",purple:"text-cyber-purple",gold:"text-nusantara-gold"};return n.jsx(a,{border:s,glow:!0,children:n.jsx("div",{className:"p-6",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[n.jsx("p",{className:"text-sm font-medium text-gray-400",children:e}),(0,n.jsxs)("div",{className:"flex items-baseline space-x-2",children:[o?n.jsx("div",{className:"h-8 w-20 bg-gray-700 animate-pulse rounded"}):n.jsx("p",{className:`text-2xl font-bold ${l[s]}`,children:"number"==typeof r?r.toLocaleString():r}),i&&(0,n.jsxs)("span",{className:`text-sm font-medium ${i.isPositive?"text-green-400":"text-red-400"}`,children:[i.isPositive?"+":"",i.value,"%"]})]})]}),n.jsx("div",{className:`p-3 rounded-lg ${{green:"bg-cyber-green/10",blue:"bg-cyber-blue/10",red:"bg-cyber-red/10",purple:"bg-cyber-purple/10",gold:"bg-nusantara-gold/10"}[s]}`,children:n.jsx(t,{className:`h-6 w-6 ${l[s]}`})})]})})})}function i({title:e,description:r,icon:t,color:s="green",onClick:i,disabled:o=!1,badge:l}){let d={green:"text-cyber-green",blue:"text-cyber-blue",red:"text-cyber-red",purple:"text-cyber-purple",gold:"text-nusantara-gold"},c={green:"bg-cyber-green/10",blue:"bg-cyber-blue/10",red:"bg-cyber-red/10",purple:"bg-cyber-purple/10",gold:"bg-nusantara-gold/10"};return n.jsx(a,{hover:!o&&!!i,border:s,className:`relative ${o?"opacity-50 cursor-not-allowed":""}`,children:(0,n.jsxs)("div",{className:"p-6 h-full",onClick:o?void 0:i,children:[l&&n.jsx("div",{className:"absolute top-3 right-3",children:n.jsx("span",{className:`px-2 py-1 text-xs font-semibold rounded-full ${c[s]} ${d[s]}`,children:l})}),n.jsx("div",{className:`inline-flex p-3 rounded-lg ${c[s]} mb-4`,children:n.jsx(t,{className:`h-6 w-6 ${d[s]}`})}),n.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:e}),n.jsx("p",{className:"text-gray-400 text-sm leading-relaxed",children:r}),i&&!o&&n.jsx("div",{className:"mt-4 pt-4 border-t border-gray-700",children:n.jsx("span",{className:`text-sm font-medium ${d[s]} hover:underline`,children:"Mulai Sekarang →"})})]})})}function o({type:e="info",title:r,message:t,onClose:a}){let s={info:{border:"border-cyber-blue",bg:"bg-cyber-blue/10",text:"text-cyber-blue",icon:"\uD83D\uDCA1"},success:{border:"border-cyber-green",bg:"bg-cyber-green/10",text:"text-cyber-green",icon:"✅"},warning:{border:"border-nusantara-gold",bg:"bg-nusantara-gold/10",text:"text-nusantara-gold",icon:"⚠️"},error:{border:"border-cyber-red",bg:"bg-cyber-red/10",text:"text-cyber-red",icon:"❌"}}[e];return n.jsx("div",{className:`border ${s.border} ${s.bg} rounded-lg p-4`,children:(0,n.jsxs)("div",{className:"flex items-start space-x-3",children:[n.jsx("span",{className:"text-lg",children:s.icon}),(0,n.jsxs)("div",{className:"flex-1",children:[n.jsx("h4",{className:`font-semibold ${s.text}`,children:r}),n.jsx("p",{className:"text-gray-300 text-sm mt-1",children:t})]}),a&&n.jsx("button",{onClick:a,className:"text-gray-400 hover:text-white transition-colors",children:"\xd7"})]})})}},76557:(e,r,t)=>{"use strict";t.d(r,{Z:()=>i});var n=t(17577),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),i=(e,r)=>{let t=(0,n.forwardRef)(({color:t="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:l,className:d="",children:c,...u},h)=>(0,n.createElement)("svg",{ref:h,...a,width:i,height:i,stroke:t,strokeWidth:l?24*Number(o)/Number(i):o,className:["lucide",`lucide-${s(e)}`,d].join(" "),...u},[...r.map(([e,r])=>(0,n.createElement)(e,r)),...Array.isArray(c)?c:[c]]));return t.displayName=`${e}`,t}},30829:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d,metadata:()=>l});var n=t(19510),a=t(77366),s=t.n(a);t(7633);var i=t(68570);let o=(0,i.createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\contexts\AuthContext.tsx#AuthProvider`);(0,i.createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\contexts\AuthContext.tsx#useAuth`);let l={title:"KodeXGuard - Platform Cybersecurity & Bug Hunting",description:"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting dengan dukungan Bot WhatsApp/Telegram",keywords:"cybersecurity, bug hunting, OSINT, vulnerability scanner, CVE, penetration testing, security tools",authors:[{name:"KodeXGuard Team"}],creator:"KodeXGuard",publisher:"KodeXGuard",robots:"index, follow",openGraph:{type:"website",locale:"id_ID",url:"https://kodexguard.com",title:"KodeXGuard - Platform Cybersecurity & Bug Hunting",description:"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting",siteName:"KodeXGuard"},twitter:{card:"summary_large_image",title:"KodeXGuard - Platform Cybersecurity & Bug Hunting",description:"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting"},viewport:"width=device-width, initial-scale=1",themeColor:"#00ff41"};function d({children:e}){return(0,n.jsxs)("html",{lang:"id",className:"dark",children:[(0,n.jsxs)("head",{children:[n.jsx("link",{rel:"icon",href:"/favicon.ico"}),n.jsx("link",{rel:"apple-touch-icon",href:"/apple-touch-icon.png"}),n.jsx("link",{rel:"manifest",href:"/manifest.json"})]}),(0,n.jsxs)("body",{className:`${s().className} min-h-screen bg-gradient-cyber text-white antialiased`,children:[n.jsx("div",{className:"matrix-bg",children:n.jsx("div",{className:"absolute inset-0 bg-gradient-to-b from-transparent via-cyber-dark/20 to-cyber-dark"})}),n.jsx("div",{className:"relative z-10",children:n.jsx(o,{children:e})}),n.jsx("script",{dangerouslySetInnerHTML:{__html:`
              // Matrix Rain Effect
              function createMatrixRain() {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.style.position = 'fixed';
                canvas.style.top = '0';
                canvas.style.left = '0';
                canvas.style.width = '100%';
                canvas.style.height = '100%';
                canvas.style.pointerEvents = 'none';
                canvas.style.zIndex = '-1';
                canvas.style.opacity = '0.1';
                document.body.appendChild(canvas);
                
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
                
                const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';
                const charArray = chars.split('');
                const fontSize = 14;
                const columns = canvas.width / fontSize;
                const drops = [];
                
                for (let i = 0; i < columns; i++) {
                  drops[i] = 1;
                }
                
                function draw() {
                  ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
                  ctx.fillRect(0, 0, canvas.width, canvas.height);
                  
                  ctx.fillStyle = '#00ff41';
                  ctx.font = fontSize + 'px monospace';
                  
                  for (let i = 0; i < drops.length; i++) {
                    const text = charArray[Math.floor(Math.random() * charArray.length)];
                    ctx.fillText(text, i * fontSize, drops[i] * fontSize);
                    
                    if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
                      drops[i] = 0;
                    }
                    drops[i]++;
                  }
                }
                
                setInterval(draw, 50);
                
                window.addEventListener('resize', () => {
                  canvas.width = window.innerWidth;
                  canvas.height = window.innerHeight;
                });
              }
              
              // Initialize matrix effect after page load
              if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', createMatrixRain);
              } else {
                createMatrixRain();
              }
            `}})]})]})}},7633:()=>{}};