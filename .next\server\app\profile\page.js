(()=>{var e={};e.id=178,e.ids=[178],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},20820:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>r.a,__next_app__:()=>m,originalPathname:()=>o,pages:()=>x,routeModule:()=>p,tree:()=>d}),a(44591),a(30829),a(35866);var t=a(23191),i=a(88716),l=a(37922),r=a.n(l),n=a(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(s,c);let d=["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,44591)),"D:\\Users\\Downloads\\kodeXGuard\\app\\profile\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,35866,23)),"next/dist/client/components/not-found-error"]}],x=["D:\\Users\\Downloads\\kodeXGuard\\app\\profile\\page.tsx"],o="/profile/page",m={require:a,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},44493:(e,s,a)=>{Promise.resolve().then(a.bind(a,9776))},9776:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>v});var t=a(10326),i=a(17577),l=a(60463),r=a(2262),n=a(79635),c=a(58038),d=a(66697),x=a(3634),o=a(76828),m=a(70003),p=a(51896),h=a(54659),u=a(31215),g=a(83855),b=a(98091),j=a(91216),y=a(12714),N=a(43810),f=a(54014);function v(){let[e]=(0,i.useState)({username:"admin",avatar:"",role:"super_admin",plan:"cybersecurity"}),[s,a]=(0,i.useState)("profile"),[v,w]=(0,i.useState)({id:"1",username:"admin",email:"<EMAIL>",fullName:"Super Administrator",avatar:"",bio:"Platform creator and cybersecurity enthusiast. Building tools to make the internet safer.",role:"super_admin",plan:"cybersecurity",planExpiry:"2024-12-31",isActive:!0,emailVerified:!0,createdAt:"2023-01-01",lastLogin:"2024-01-15T10:30:00Z",stats:{totalScans:1247,vulnerabilitiesFound:89,filesAnalyzed:456,apiCalls:28560,score:9870,rank:4}}),[A,k]=(0,i.useState)([{id:"1",name:"Production API",keyPrefix:"kxg_prod_",permissions:["scan:read","scan:write","osint:read","file:analyze"],isActive:!0,lastUsed:"2 hours ago",usageCount:15420,rateLimit:1e4,createdAt:"2023-06-15",expiresAt:"2024-06-15"},{id:"2",name:"Development API",keyPrefix:"kxg_dev_",permissions:["scan:read","osint:read"],isActive:!0,lastUsed:"1 day ago",usageCount:2847,rateLimit:1e3,createdAt:"2023-08-20"},{id:"3",name:"Bot Integration",keyPrefix:"kxg_bot_",permissions:["scan:write","user:read"],isActive:!1,lastUsed:"1 week ago",usageCount:567,rateLimit:5e3,createdAt:"2023-10-10",expiresAt:"2024-10-10"}]),[P,C]=(0,i.useState)(!1),[S,Z]=(0,i.useState)(!1),[I,_]=(0,i.useState)(""),[D,L]=(0,i.useState)([]),[U,E]=(0,i.useState)(null),K=e=>{k(s=>s.map(s=>s.id===e?{...s,isActive:!s.isActive}:s))},R=e=>{k(s=>s.filter(s=>s.id!==e))},V=e=>{navigator.clipboard.writeText(e+"xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx")},G=e=>e.includes("admin")?"bg-red-900/50 text-red-400":e.includes("write")?"bg-orange-900/50 text-orange-400":"bg-blue-900/50 text-blue-400";return t.jsx(l.Z,{user:e,title:"Profile & API Keys",children:(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[t.jsx(n.Z,{className:"h-8 w-8 text-cyber-green"}),(0,t.jsxs)("h1",{className:"text-3xl font-bold font-cyber text-white",children:["Profile & ",t.jsx("span",{className:"cyber-text",children:"API Keys"})]})]}),t.jsx("p",{className:"text-gray-400 max-w-3xl",children:"Kelola profil pengguna, statistik aktivitas, dan API keys untuk integrasi dengan aplikasi eksternal. Monitor penggunaan API dan atur permissions sesuai kebutuhan."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[t.jsx(r.Rm,{title:"Total Scans",value:v.stats.totalScans,icon:c.Z,color:"green",trend:{value:12,isPositive:!0}}),t.jsx(r.Rm,{title:"Vulnerabilities Found",value:v.stats.vulnerabilitiesFound,icon:d.Z,color:"red"}),t.jsx(r.Rm,{title:"API Calls",value:v.stats.apiCalls,icon:x.Z,color:"purple",trend:{value:23,isPositive:!0}}),t.jsx(r.Rm,{title:"Hunter Rank",value:`#${v.stats.rank}`,icon:n.Z,color:"gold"})]}),t.jsx("div",{className:"mb-8",children:t.jsx("div",{className:"flex space-x-1 bg-gray-800/50 p-1 rounded-lg",children:[{id:"profile",name:"Profile Settings",icon:n.Z},{id:"apikeys",name:"API Keys",icon:o.Z},{id:"activity",name:"Activity Log",icon:d.Z}].map(e=>{let i=e.icon;return(0,t.jsxs)("button",{onClick:()=>a(e.id),className:`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${s===e.id?"bg-cyber-green text-black":"text-gray-300 hover:text-white hover:bg-gray-700/50"}`,children:[t.jsx(i,{className:"h-4 w-4"}),t.jsx("span",{children:e.name})]},e.id)})})}),"profile"===s&&(0,t.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[t.jsx("div",{className:"lg:col-span-2",children:t.jsx(r.Zb,{border:"green",glow:!0,children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[t.jsx("h2",{className:"text-xl font-bold text-white",children:"Profile Information"}),(0,t.jsxs)("button",{onClick:()=>C(!P),className:"cyber-btn text-sm",children:[t.jsx(m.Z,{className:"h-4 w-4 mr-2"}),P?"Cancel":"Edit Profile"]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,t.jsxs)("div",{className:"relative",children:[v.avatar?t.jsx("img",{src:v.avatar,alt:v.username,className:"h-20 w-20 rounded-full"}):t.jsx("div",{className:"h-20 w-20 rounded-full bg-cyber-green/20 flex items-center justify-center",children:t.jsx("span",{className:"text-2xl font-bold text-cyber-green",children:v.username.charAt(0).toUpperCase()})}),P&&t.jsx("button",{className:"absolute bottom-0 right-0 bg-cyber-green text-black rounded-full p-2 hover:bg-cyber-blue transition-colors",children:t.jsx(p.Z,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-xl font-bold text-white",children:v.fullName}),(0,t.jsxs)("p",{className:"text-gray-400",children:["@",v.username]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[t.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-semibold ${"super_admin"===v.role?"bg-red-900/50 text-red-400":"admin"===v.role?"bg-orange-900/50 text-orange-400":"bg-blue-900/50 text-blue-400"}`,children:v.role.replace("_"," ").toUpperCase()}),t.jsx("span",{className:"px-2 py-1 bg-nusantara-gold/20 text-nusantara-gold rounded-full text-xs font-semibold",children:v.plan.toUpperCase()})]})]})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Full Name"}),t.jsx("input",{type:"text",value:v.fullName,onChange:e=>w(s=>({...s,fullName:e.target.value})),disabled:!P,className:"cyber-input w-full disabled:opacity-50"})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Username"}),t.jsx("input",{type:"text",value:v.username,onChange:e=>w(s=>({...s,username:e.target.value})),disabled:!P,className:"cyber-input w-full disabled:opacity-50"})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Email Address"}),(0,t.jsxs)("div",{className:"relative",children:[t.jsx("input",{type:"email",value:v.email,onChange:e=>w(s=>({...s,email:e.target.value})),disabled:!P,className:"cyber-input w-full disabled:opacity-50"}),v.emailVerified&&t.jsx(h.Z,{className:"absolute right-3 top-3 h-5 w-5 text-green-400"})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Plan Expiry"}),t.jsx("input",{type:"text",value:v.planExpiry?new Date(v.planExpiry).toLocaleDateString():"Never",disabled:!0,className:"cyber-input w-full disabled:opacity-50"})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Bio"}),t.jsx("textarea",{value:v.bio||"",onChange:e=>w(s=>({...s,bio:e.target.value})),disabled:!P,className:"cyber-input w-full h-24 disabled:opacity-50",placeholder:"Tell us about yourself..."})]}),P&&(0,t.jsxs)("div",{className:"flex space-x-3",children:[(0,t.jsxs)("button",{onClick:()=>{C(!1)},className:"cyber-btn-primary",children:[t.jsx(u.Z,{className:"h-4 w-4 mr-2"}),"Save Changes"]}),t.jsx("button",{onClick:()=>C(!1),className:"cyber-btn",children:"Cancel"})]})]})]})})}),(0,t.jsxs)("div",{children:[t.jsx(r.Zb,{children:(0,t.jsxs)("div",{className:"p-6",children:[t.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Account Information"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"Member Since"}),t.jsx("span",{className:"text-white",children:new Date(v.createdAt).toLocaleDateString()})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"Last Login"}),t.jsx("span",{className:"text-white",children:new Date(v.lastLogin).toLocaleString()})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"Account Status"}),t.jsx("span",{className:`font-semibold ${v.isActive?"text-green-400":"text-red-400"}`,children:v.isActive?"Active":"Inactive"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"Email Verified"}),t.jsx("span",{className:`font-semibold ${v.emailVerified?"text-green-400":"text-red-400"}`,children:v.emailVerified?"Verified":"Unverified"})]})]})]})}),t.jsx(r.Zb,{className:"mt-6",border:"gold",children:(0,t.jsxs)("div",{className:"p-6",children:[t.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Hunter Statistics"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"Hunter Score"}),t.jsx("span",{className:"text-cyber-green font-bold",children:v.stats.score.toLocaleString()})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"Global Rank"}),(0,t.jsxs)("span",{className:"text-nusantara-gold font-bold",children:["#",v.stats.rank]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"Files Analyzed"}),t.jsx("span",{className:"text-white",children:v.stats.filesAnalyzed.toLocaleString()})]})]})]})})]})]}),"apikeys"===s&&(0,t.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[t.jsx("div",{className:"lg:col-span-2",children:t.jsx(r.Zb,{border:"green",glow:!0,children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[t.jsx("h2",{className:"text-xl font-bold text-white",children:"API Keys"}),(0,t.jsxs)("button",{onClick:()=>Z(!0),className:"cyber-btn-primary",children:[t.jsx(g.Z,{className:"h-4 w-4 mr-2"}),"Create New Key"]})]}),t.jsx("div",{className:"space-y-4",children:A.map(e=>(0,t.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-4 border border-gray-700",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx("h3",{className:"font-bold text-white",children:e.name}),t.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-semibold ${e.isActive?"bg-green-900/50 text-green-400":"bg-red-900/50 text-red-400"}`,children:e.isActive?"Active":"Inactive"})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-400 mt-1",children:["Created: ",new Date(e.createdAt).toLocaleDateString(),e.expiresAt&&(0,t.jsxs)("span",{children:[" • Expires: ",new Date(e.expiresAt).toLocaleDateString()]})]})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[t.jsx("button",{onClick:()=>K(e.id),className:`text-sm px-3 py-1 rounded transition-colors ${e.isActive?"bg-red-600 hover:bg-red-700 text-white":"bg-green-600 hover:bg-green-700 text-white"}`,children:e.isActive?"Disable":"Enable"}),t.jsx("button",{onClick:()=>R(e.id),className:"text-red-400 hover:text-red-300 transition-colors",children:t.jsx(b.Z,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"API Key"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("div",{className:"flex-1 bg-gray-900/50 rounded p-3 font-mono text-sm",children:U===e.id?e.keyPrefix+"xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx":e.keyPrefix+"••••••••••••••••••••••••••••••••"}),t.jsx("button",{onClick:()=>E(U===e.id?null:e.id),className:"cyber-btn text-sm",children:U===e.id?t.jsx(j.Z,{className:"h-4 w-4"}):t.jsx(y.Z,{className:"h-4 w-4"})}),t.jsx("button",{onClick:()=>V(e.keyPrefix),className:"cyber-btn text-sm",children:t.jsx(N.Z,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Permissions"}),t.jsx("div",{className:"flex flex-wrap gap-2",children:e.permissions.map((e,s)=>t.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-semibold ${G(e)}`,children:e},s))})]}),(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[t.jsx("span",{className:"text-gray-400",children:"Usage Count:"}),t.jsx("span",{className:"text-white ml-2 font-semibold",children:e.usageCount.toLocaleString()})]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"text-gray-400",children:"Rate Limit:"}),(0,t.jsxs)("span",{className:"text-white ml-2 font-semibold",children:[e.rateLimit.toLocaleString(),"/day"]})]}),(0,t.jsxs)("div",{children:[t.jsx("span",{className:"text-gray-400",children:"Last Used:"}),t.jsx("span",{className:"text-white ml-2 font-semibold",children:e.lastUsed||"Never"})]})]})]})]},e.id))})]})})}),(0,t.jsxs)("div",{children:[t.jsx(r.Zb,{children:(0,t.jsxs)("div",{className:"p-6",children:[t.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"API Usage"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"Total API Keys"}),t.jsx("span",{className:"text-white font-semibold",children:A.length})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"Active Keys"}),t.jsx("span",{className:"text-green-400 font-semibold",children:A.filter(e=>e.isActive).length})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"Total Calls"}),t.jsx("span",{className:"text-white font-semibold",children:A.reduce((e,s)=>e+s.usageCount,0).toLocaleString()})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"Daily Limit"}),t.jsx("span",{className:"text-cyber-green font-semibold",children:"100,000"})]})]})]})}),t.jsx(r.Zb,{className:"mt-6",children:(0,t.jsxs)("div",{className:"p-6",children:[t.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"API Documentation"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[t.jsx("p",{className:"text-gray-300",children:"Use your API keys to integrate KodeXGuard with your applications."}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("p",{className:"text-gray-400",children:["• Base URL: ",t.jsx("code",{className:"text-cyber-green",children:"https://api.kodexguard.com/v1"})]}),t.jsx("p",{className:"text-gray-400",children:"• Authentication: Bearer token"}),t.jsx("p",{className:"text-gray-400",children:"• Rate limiting: Per API key"}),t.jsx("p",{className:"text-gray-400",children:"• Response format: JSON"})]}),t.jsx("button",{className:"cyber-btn text-sm w-full mt-4",children:"View Full Documentation"})]})]})})]})]}),"activity"===s&&t.jsx(r.Zb,{children:(0,t.jsxs)("div",{className:"p-6",children:[t.jsx("h2",{className:"text-xl font-bold text-white mb-6",children:"Activity Log"}),t.jsx("div",{className:"space-y-4",children:[{action:"Vulnerability scan completed",target:"https://example.com",time:"2 hours ago",type:"scan"},{action:"API key created",target:"Production API",time:"1 day ago",type:"api"},{action:"Profile updated",target:"Bio information",time:"2 days ago",type:"profile"},{action:"OSINT search performed",target:"<EMAIL>",time:"3 days ago",type:"osint"},{action:"File analyzed",target:"suspicious.php",time:"1 week ago",type:"file"}].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-gray-800/50 rounded-lg",children:[(0,t.jsxs)("div",{className:`p-2 rounded-full ${"scan"===e.type?"bg-blue-900/50":"api"===e.type?"bg-green-900/50":"profile"===e.type?"bg-purple-900/50":"osint"===e.type?"bg-yellow-900/50":"bg-red-900/50"}`,children:["scan"===e.type&&t.jsx(c.Z,{className:"h-4 w-4 text-blue-400"}),"api"===e.type&&t.jsx(o.Z,{className:"h-4 w-4 text-green-400"}),"profile"===e.type&&t.jsx(n.Z,{className:"h-4 w-4 text-purple-400"}),"osint"===e.type&&t.jsx(f.Z,{className:"h-4 w-4 text-yellow-400"}),"file"===e.type&&t.jsx(c.Z,{className:"h-4 w-4 text-red-400"})]}),(0,t.jsxs)("div",{className:"flex-1",children:[t.jsx("div",{className:"font-medium text-white",children:e.action}),t.jsx("div",{className:"text-sm text-gray-400",children:e.target})]}),t.jsx("div",{className:"text-sm text-gray-400",children:e.time})]},s))})]})}),S&&t.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:t.jsx(r.Zb,{className:"w-full max-w-md mx-4",border:"green",children:(0,t.jsxs)("div",{className:"p-6",children:[t.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Create New API Key"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Key Name"}),t.jsx("input",{type:"text",value:I,onChange:e=>_(e.target.value),placeholder:"My API Key",className:"cyber-input w-full"})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Permissions"}),t.jsx("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:[{id:"scan:read",name:"Read Scan Results",description:"View vulnerability scan results"},{id:"scan:write",name:"Create Scans",description:"Start new vulnerability scans"},{id:"osint:read",name:"OSINT Search",description:"Perform OSINT investigations"},{id:"file:analyze",name:"File Analysis",description:"Analyze files for threats"},{id:"cve:read",name:"CVE Database",description:"Access CVE intelligence data"},{id:"user:read",name:"User Info",description:"Read user profile information"},{id:"admin:read",name:"Admin Access",description:"Administrative operations (Admin only)"}].map(e=>(0,t.jsxs)("label",{className:"flex items-start space-x-3 cursor-pointer",children:[t.jsx("input",{type:"checkbox",checked:D.includes(e.id),onChange:s=>{s.target.checked?L(s=>[...s,e.id]):L(s=>s.filter(s=>s!==e.id))},className:"mt-1 rounded bg-gray-800 border-gray-600"}),(0,t.jsxs)("div",{children:[t.jsx("div",{className:"text-sm font-medium text-white",children:e.name}),t.jsx("div",{className:"text-xs text-gray-400",children:e.description})]})]},e.id))})]})]}),(0,t.jsxs)("div",{className:"flex space-x-3 mt-6",children:[t.jsx("button",{onClick:()=>{if(!I.trim()||0===D.length)return;let s={id:Date.now().toString(),name:I,keyPrefix:"kxg_"+Math.random().toString(36).substr(2,8)+"_",permissions:D,isActive:!0,usageCount:0,rateLimit:"cybersecurity"===e.plan?1e5:"bug_hunter"===e.plan?1e4:1e3,createdAt:new Date().toISOString()};k(e=>[...e,s]),_(""),L([]),Z(!1)},disabled:!I.trim()||0===D.length,className:"cyber-btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:"Create Key"}),t.jsx("button",{onClick:()=>Z(!1),className:"cyber-btn",children:"Cancel"})]})]})})})]})})}},44591:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(68570).createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\profile\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[216,592],()=>a(20820));module.exports=t})();