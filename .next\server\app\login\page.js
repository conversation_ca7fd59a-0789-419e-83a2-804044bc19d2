(()=>{var e={};e.id=626,e.ids=[626],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10929:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o}),r(94687),r(30829),r(35866);var a=r(23191),t=r(88716),n=r(37922),l=r.n(n),i=r(95231),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(s,d);let o=["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,94687)),"D:\\Users\\Downloads\\kodeXGuard\\app\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],c=["D:\\Users\\Downloads\\kodeXGuard\\app\\login\\page.tsx"],m="/login/page",x={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},67552:(e,s,r)=>{Promise.resolve().then(r.bind(r,1442))},1442:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>p});var a=r(10326),t=r(17577),n=r(90434),l=r(35047),i=r(58038),d=r(5932),o=r(9015),c=r(91216),m=r(12714),x=r(2262),u=r(67382);function p(){let e=(0,l.useRouter)(),{login:s,isAuthenticated:r,isLoading:p}=(0,u.a)(),[h,g]=(0,t.useState)({email:"",password:"",rememberMe:!1}),[b,y]=(0,t.useState)(!1),[j,f]=(0,t.useState)(!1),[v,N]=(0,t.useState)(""),w=async r=>{r.preventDefault(),f(!0),N("");try{await s(h.email,h.password)?e.push("/dashboard"):N("Invalid email or password")}catch(e){N("Network error. Please try again.")}finally{f(!1)}},k=e=>{let{name:s,value:r,type:a,checked:t}=e.target;g(e=>({...e,[s]:"checkbox"===a?t:r}))};return(0,a.jsxs)("div",{className:"min-h-screen flex items-center justify-center px-4 py-12",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-cyber opacity-50"}),a.jsx("div",{className:"absolute inset-0 nusantara-pattern opacity-5"}),(0,a.jsxs)("div",{className:"relative z-10 w-full max-w-md",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsxs)(n.default,{href:"/",className:"inline-flex items-center space-x-2",children:[a.jsx(i.Z,{className:"h-12 w-12 text-cyber-green animate-pulse"}),(0,a.jsxs)("span",{className:"text-3xl font-bold font-cyber",children:[a.jsx("span",{className:"cyber-text",children:"Kode"}),a.jsx("span",{className:"nusantara-gold",children:"X"}),a.jsx("span",{className:"text-white",children:"Guard"})]})]}),a.jsx("p",{className:"mt-2 text-gray-400",children:"Masuk ke akun Anda"})]}),a.jsx(x.Zb,{border:"green",glow:!0,children:(0,a.jsxs)("div",{className:"p-8",children:[v&&a.jsx("div",{className:"mb-6",children:a.jsx(x.XO,{type:"error",title:"Login Gagal",message:v,onClose:()=>N("")})}),(0,a.jsxs)("form",{onSubmit:w,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-300 mb-2",children:"Email Address"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(d.Z,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{id:"email",name:"email",type:"email",required:!0,value:h.email,onChange:k,className:"cyber-input pl-10 w-full",placeholder:"<EMAIL>"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-300 mb-2",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(o.Z,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{id:"password",name:"password",type:b?"text":"password",required:!0,value:h.password,onChange:k,className:"cyber-input pl-10 pr-10 w-full",placeholder:"••••••••"}),a.jsx("button",{type:"button",onClick:()=>y(!b),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-cyber-green",children:b?a.jsx(c.Z,{className:"h-5 w-5"}):a.jsx(m.Z,{className:"h-5 w-5"})})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("input",{id:"rememberMe",name:"rememberMe",type:"checkbox",checked:h.rememberMe,onChange:k,className:"h-4 w-4 text-cyber-green focus:ring-cyber-green border-gray-600 rounded bg-gray-800"}),a.jsx("label",{htmlFor:"rememberMe",className:"ml-2 block text-sm text-gray-300",children:"Remember me"})]}),a.jsx(n.default,{href:"/forgot-password",className:"text-sm text-cyber-green hover:text-cyber-blue transition-colors",children:"Forgot password?"})]}),a.jsx("button",{type:"submit",disabled:j,className:"w-full cyber-btn-primary py-3 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed",children:j?(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[a.jsx("div",{className:"w-5 h-5 border-2 border-black border-t-transparent rounded-full animate-spin"}),a.jsx("span",{children:"Signing in..."})]}):"Sign In"})]}),(0,a.jsxs)("div",{className:"mt-6 p-4 bg-gray-800/50 rounded-lg border border-gray-700",children:[a.jsx("h4",{className:"text-sm font-semibold text-cyber-green mb-2",children:"Demo Credentials:"}),(0,a.jsxs)("div",{className:"text-xs text-gray-400 space-y-1",children:[(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"Admin:"})," <EMAIL> / admin123"]}),(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"User:"})," <EMAIL> / admin123"]})]})]}),a.jsx("div",{className:"mt-6 text-center",children:(0,a.jsxs)("p",{className:"text-gray-400",children:["Don't have an account?"," ",a.jsx(n.default,{href:"/register",className:"text-cyber-green hover:text-cyber-blue transition-colors font-semibold",children:"Sign up here"})]})})]})}),a.jsx("div",{className:"mt-8 text-center",children:(0,a.jsxs)("p",{className:"text-gray-500 text-sm",children:["By signing in, you agree to our"," ",a.jsx(n.default,{href:"/terms",className:"text-cyber-green hover:underline",children:"Terms of Service"})," ","and"," ",a.jsx(n.default,{href:"/privacy",className:"text-cyber-green hover:underline",children:"Privacy Policy"})]})})]})]})}},94687:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});let a=(0,r(68570).createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\login\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[216,592],()=>r(10929));module.exports=a})();