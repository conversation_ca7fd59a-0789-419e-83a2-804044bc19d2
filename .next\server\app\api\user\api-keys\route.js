"use strict";(()=>{var e={};e.id=874,e.ids=[874],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},59045:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>m,patchFetch:()=>k,requestAsyncStorage:()=>p,routeModule:()=>l,serverHooks:()=>w,staticGenerationAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{DELETE:()=>d,GET:()=>u,POST:()=>c});var a=r(49303),n=r(88716),o=r(60670),i=r(87070);async function u(e){try{let t=e.headers.get("authorization"),r=t?.replace("Bearer ","");if(!r)return i.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!r.startsWith("kxg_"))return i.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let s=[{id:"key_001",name:"Production API",keyPrefix:"kxg_prod_",isActive:!0,permissions:["scan:read","scan:write","osint:read","osint:write","notifications:read"],lastUsed:new Date(Date.now()-36e5).toISOString(),usageCount:15420,rateLimit:1e3,allowedIPs:["*************","*********"],createdAt:new Date(Date.now()-15552e6).toISOString(),expiresAt:new Date(Date.now()+15984e6).toISOString()},{id:"key_002",name:"Development API",keyPrefix:"kxg_dev_",isActive:!0,permissions:["scan:read","osint:read","notifications:read"],lastUsed:new Date(Date.now()-864e5).toISOString(),usageCount:2340,rateLimit:100,allowedIPs:["127.0.0.1","***********/24"],createdAt:new Date(Date.now()-2592e6).toISOString()},{id:"key_003",name:"Mobile App API",keyPrefix:"kxg_mobile_",isActive:!1,permissions:["scan:read","osint:read"],lastUsed:new Date(Date.now()-6048e5).toISOString(),usageCount:890,rateLimit:500,allowedIPs:[],createdAt:new Date(Date.now()-5184e6).toISOString(),expiresAt:new Date(Date.now()+26352e6).toISOString()},{id:"key_004",name:"Webhook Integration",keyPrefix:"kxg_webhook_",isActive:!0,permissions:["notifications:write","scan:read"],lastUsed:new Date(Date.now()-18e5).toISOString(),usageCount:5670,rateLimit:200,allowedIPs:["webhook.example.com"],createdAt:new Date(Date.now()-3888e6).toISOString()}];return i.NextResponse.json({success:!0,data:s,meta:{total:s.length,active:s.filter(e=>e.isActive).length,inactive:s.filter(e=>!e.isActive).length,totalUsage:s.reduce((e,t)=>e+t.usageCount,0)},timestamp:new Date().toISOString()})}catch(e){return console.error("API keys API error:",e),i.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}async function c(e){try{let t=e.headers.get("authorization"),r=t?.replace("Bearer ","");if(!r)return i.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!r.startsWith("kxg_"))return i.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let{name:s,permissions:a,rateLimit:n,allowedIPs:o,expiresAt:u}=await e.json();if(!s||!a||!Array.isArray(a))return i.NextResponse.json({success:!1,error:"Name and permissions array are required"},{status:400});let c=`key_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,d=`kxg_${s.toLowerCase().replace(/\s+/g,"_")}_`,l=`${d}${Math.random().toString(36).substr(2,32)}`,p={id:c,name:s,keyPrefix:d,fullKey:l,isActive:!0,permissions:a,usageCount:0,rateLimit:n||1e3,allowedIPs:o||[],createdAt:new Date().toISOString(),expiresAt:u};return i.NextResponse.json({success:!0,data:p,message:"API key created successfully. Please save the full key as it will not be shown again.",timestamp:new Date().toISOString()})}catch(e){return console.error("Create API key error:",e),i.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}async function d(e){try{let t=e.headers.get("authorization"),r=t?.replace("Bearer ","");if(!r)return i.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!r.startsWith("kxg_"))return i.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let{keyId:s}=await e.json();if(!s)return i.NextResponse.json({success:!1,error:"Key ID is required"},{status:400});return i.NextResponse.json({success:!0,message:`API key ${s} deleted successfully`,timestamp:new Date().toISOString()})}catch(e){return console.error("Delete API key error:",e),i.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/user/api-keys/route",pathname:"/api/user/api-keys",filename:"route",bundlePath:"app/api/user/api-keys/route"},resolvedPagePath:"D:\\Users\\Downloads\\kodeXGuard\\app\\api\\user\\api-keys\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:p,staticGenerationAsyncStorage:g,serverHooks:w}=l,m="/api/user/api-keys/route";function k(){return(0,o.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:g})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[216,592],()=>r(59045));module.exports=s})();