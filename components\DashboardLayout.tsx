'use client'

import { ReactNode, useEffect, Suspense } from 'react'
import dynamic from 'next/dynamic'
import { preloader, observeLinks } from '@/lib/preloader'
import { performanceMonitor, markRouteChangeComplete } from '@/lib/performance'
import { cache } from '@/lib/cache'

// Dynamic imports for better code splitting
const Sidebar = dynamic(() => import('./Sidebar'), {
  loading: () => <div className="w-64 bg-gray-900 animate-pulse" />,
  ssr: true
})

const Navbar = dynamic(() => import('./Navbar'), {
  loading: () => <div className="h-16 bg-gray-900 animate-pulse" />,
  ssr: true
})

interface DashboardLayoutProps {
  children: ReactNode
  user?: {
    username: string
    avatar?: string
    role: string
    plan: string
  }
  title?: string
  showSearch?: boolean
}

export default function DashboardLayout({
  children,
  user,
  title,
  showSearch = true
}: DashboardLayoutProps) {
  useEffect(() => {
    // Performance optimizations on mount
    const initOptimizations = async () => {
      try {
        // Mark route change complete for performance tracking
        markRouteChangeComplete()

        // Preload user journey based on role and plan
        if (user) {
          await preloader.preloadUserJourney(user.role, user.plan)
          // Preload critical data for current user
          await cache.preloadCriticalData(user.username)
        }

        // Observe links for intersection-based preloading
        setTimeout(() => {
          observeLinks()
        }, 1000)

        // Preload critical assets
        await preloader.preloadCriticalAssets()

      } catch (error) {
        console.warn('Optimization initialization failed:', error)
      }
    }

    initOptimizations()

    // Cleanup on unmount
    return () => {
      performanceMonitor.cleanup()
    }
  }, [user.role, user.plan, user.username])

  return (
    <div className="min-h-screen bg-gradient-cyber">
      {/* Top Navbar with Suspense */}
      <Suspense fallback={<div className="h-16 bg-gray-900 animate-pulse" />}>
        <Navbar user={user} title={title} showSearch={showSearch} isLandingPage={false} />
      </Suspense>

      <div className="flex pt-16">
        {/* Sidebar with Suspense */}
        <Suspense fallback={<div className="w-64 bg-gray-900 animate-pulse" />}>
          <Sidebar user={user} />
        </Suspense>

        {/* Main Content Area */}
        <main className="flex-1 min-w-0 lg:ml-0">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="w-full">
              <Suspense fallback={
                <div className="space-y-4">
                  <div className="h-8 bg-gray-800 rounded animate-pulse" />
                  <div className="h-64 bg-gray-800 rounded animate-pulse" />
                </div>
              }>
                {children}
              </Suspense>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
