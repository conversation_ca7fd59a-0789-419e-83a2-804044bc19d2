const mysql = require('mysql2/promise')
const bcrypt = require('bcryptjs')

async function setupDatabase() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'rootkan'
  })

  try {
    console.log('🚀 Setting up KodeXGuard database...')

    // Create database
    await connection.execute('CREATE DATABASE IF NOT EXISTS db_kodexguard')
    await connection.execute('USE db_kodexguard')

    console.log('✅ Database created successfully')

    // Create plans table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS plans (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL,
        slug VARCHAR(50) UNIQUE NOT NULL,
        price DECIMAL(10,2) NOT NULL DEFAULT 0,
        currency VARCHAR(3) DEFAULT 'IDR',
        billing_cycle ENUM('monthly', 'yearly') DEFAULT 'monthly',
        scan_quota_daily INT DEFAULT 5,
        osint_quota_daily INT DEFAULT 10,
        file_quota_daily INT DEFAULT 2,
        api_quota_daily INT DEFAULT 100,
        features <PERSON><PERSON><PERSON>,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `)

    // Insert default plans
    await connection.execute(`
      INSERT IGNORE INTO plans (name, slug, price, scan_quota_daily, osint_quota_daily, file_quota_daily, api_quota_daily, features) VALUES
      ('Gratis', 'gratis', 0, 5, 10, 2, 100, '["basic_scan", "basic_osint"]'),
      ('Hobby', 'hobby', 99000, 20, 50, 10, 500, '["advanced_scan", "osint_pro", "file_analysis"]'),
      ('Bug Hunter', 'bughunter', 299000, 100, 200, 50, 2000, '["premium_scan", "osint_premium", "advanced_file", "cve_access"]'),
      ('Professional', 'professional', 599000, 500, 1000, 200, 10000, '["enterprise_scan", "osint_enterprise", "malware_analysis", "api_access"]'),
      ('Cybersecurity', 'cybersecurity', 1299000, -1, -1, -1, -1, '["unlimited_access", "priority_support", "custom_integrations", "white_label"]')
    `)

    // Create users table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id INT PRIMARY KEY AUTO_INCREMENT,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        full_name VARCHAR(255),
        role ENUM('super_admin', 'admin', 'user') DEFAULT 'user',
        plan_id INT DEFAULT 1,
        is_active BOOLEAN DEFAULT TRUE,
        email_verified BOOLEAN DEFAULT FALSE,
        email_verification_token VARCHAR(255),
        password_reset_token VARCHAR(255),
        password_reset_expires TIMESTAMP NULL,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (plan_id) REFERENCES plans(id)
      )
    `)

    // Create api_keys table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS api_keys (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        key_name VARCHAR(100) NOT NULL,
        api_key VARCHAR(255) UNIQUE NOT NULL,
        permissions JSON,
        is_active BOOLEAN DEFAULT TRUE,
        last_used TIMESTAMP NULL,
        expires_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `)

    // Create scan_history table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS scan_history (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        scan_type ENUM('vulnerability', 'osint', 'file_analysis') NOT NULL,
        target VARCHAR(500) NOT NULL,
        status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending',
        results JSON,
        vulnerabilities_found INT DEFAULT 0,
        scan_duration INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP NULL,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `)

    // Create osint_results table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS osint_results (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        search_type ENUM('email', 'phone', 'nik', 'npwp', 'name', 'domain', 'imei', 'address') NOT NULL,
        search_query VARCHAR(500) NOT NULL,
        results JSON,
        sources JSON,
        confidence_score INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `)

    // Create file_analysis table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS file_analysis (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        filename VARCHAR(255) NOT NULL,
        file_hash VARCHAR(64) NOT NULL,
        file_size BIGINT NOT NULL,
        file_type VARCHAR(100),
        analysis_results JSON,
        threat_level ENUM('safe', 'suspicious', 'malicious') DEFAULT 'safe',
        detected_threats JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `)

    // Create cve_database table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS cve_database (
        id INT PRIMARY KEY AUTO_INCREMENT,
        cve_id VARCHAR(20) UNIQUE NOT NULL,
        description TEXT,
        severity ENUM('low', 'medium', 'high', 'critical') NOT NULL,
        cvss_score DECIMAL(3,1),
        published_date DATE,
        modified_date DATE,
        affected_products JSON,
        references JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `)

    // Create bot_configs table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS bot_configs (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        bot_type ENUM('whatsapp', 'telegram') NOT NULL,
        bot_name VARCHAR(100) NOT NULL,
        config JSON,
        is_active BOOLEAN DEFAULT FALSE,
        last_activity TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `)

    // Create leaderboard table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS leaderboard (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        total_scans INT DEFAULT 0,
        vulnerabilities_found INT DEFAULT 0,
        files_analyzed INT DEFAULT 0,
        osint_searches INT DEFAULT 0,
        score INT DEFAULT 0,
        rank_position INT DEFAULT 0,
        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user (user_id)
      )
    `)

    // Create audit_logs table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS audit_logs (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT,
        action VARCHAR(100) NOT NULL,
        resource VARCHAR(100),
        resource_id INT,
        details JSON,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
      )
    `)

    // Create system_settings table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS system_settings (
        id INT PRIMARY KEY AUTO_INCREMENT,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value JSON,
        description TEXT,
        is_public BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `)

    console.log('✅ All tables created successfully')

    // Create default admin user
    const adminPassword = await bcrypt.hash('admin123', 12)
    await connection.execute(`
      INSERT IGNORE INTO users (username, email, password_hash, full_name, role, plan_id, is_active, email_verified) 
      VALUES ('admin', '<EMAIL>', ?, 'System Administrator', 'super_admin', 5, TRUE, TRUE)
    `, [adminPassword])

    // Create default user
    const userPassword = await bcrypt.hash('user123', 12)
    await connection.execute(`
      INSERT IGNORE INTO users (username, email, password_hash, full_name, role, plan_id, is_active, email_verified) 
      VALUES ('user', '<EMAIL>', ?, 'Demo User', 'user', 1, TRUE, TRUE)
    `, [userPassword])

    console.log('✅ Default users created successfully')

    // Insert system settings
    await connection.execute(`
      INSERT IGNORE INTO system_settings (setting_key, setting_value, description, is_public) VALUES
      ('app_name', '"KodeXGuard"', 'Application name', TRUE),
      ('app_version', '"1.0.0"', 'Application version', TRUE),
      ('maintenance_mode', 'false', 'Maintenance mode status', FALSE),
      ('max_file_size', '********', 'Maximum file upload size in bytes (50MB)', FALSE),
      ('rate_limit_enabled', 'true', 'Enable API rate limiting', FALSE),
      ('email_verification_required', 'false', 'Require email verification for new users', FALSE)
    `)

    console.log('✅ System settings configured successfully')

    console.log('\n🎉 KodeXGuard database setup completed!')
    console.log('\n📋 Default Accounts:')
    console.log('   Admin: <EMAIL> / admin123')
    console.log('   User:  <EMAIL> / user123')
    console.log('\n🔗 Database: db_kodexguard')
    console.log('   Host: localhost:3306')
    console.log('   User: root')

  } catch (error) {
    console.error('❌ Database setup failed:', error)
    throw error
  } finally {
    await connection.end()
  }
}

// Run setup
setupDatabase().catch(console.error)
