import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { query, type, deepSearch = false, sources = [] } = body

    // Validate input
    if (!query || !type) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Query and type are required',
          code: 'MISSING_PARAMETERS'
        },
        { status: 400 }
      )
    }

    // Validate search type
    const validTypes = ['email', 'phone', 'nik', 'npwp', 'name', 'domain', 'imei', 'address']
    if (!validTypes.includes(type)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid search type',
          code: 'INVALID_TYPE'
        },
        { status: 400 }
      )
    }

    // Simulate search delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000))

    // Generate mock results based on search type
    const mockResults = generateMockResults(query, type, deepSearch)

    return NextResponse.json({
      success: true,
      data: {
        query,
        type,
        deepSearch,
        results: mockResults,
        totalResults: mockResults.length,
        searchTime: `${(1 + Math.random() * 2).toFixed(1)}s`,
        sources: sources.length > 0 ? sources : ['Dukcapil', 'Kemkes', 'GitHub Leaked', 'Social Media']
      },
      message: 'Search completed successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('OSINT search error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    )
  }
}

function generateMockResults(query: string, type: string, deepSearch: boolean) {
  const baseResults = []

  switch (type) {
    case 'email':
      baseResults.push(
        {
          source: 'Dukcapil Database',
          data: {
            name: 'John Doe',
            email: query,
            nik: '3201234567890123',
            address: 'Jakarta Selatan',
            verified: true
          },
          confidence: 95,
          timestamp: new Date().toISOString()
        },
        {
          source: 'GitHub Leaked',
          data: {
            username: query.split('@')[0],
            email: query,
            repositories: 12,
            lastActivity: '2024-01-10'
          },
          confidence: 87,
          timestamp: new Date().toISOString()
        }
      )
      break

    case 'phone':
      baseResults.push(
        {
          source: 'Telecom Database',
          data: {
            phone: query,
            operator: 'Telkomsel',
            location: 'Jakarta',
            registered: true
          },
          confidence: 92,
          timestamp: new Date().toISOString()
        },
        {
          source: 'Social Media',
          data: {
            phone: query,
            platform: 'WhatsApp',
            lastSeen: '2024-01-14',
            profilePic: true
          },
          confidence: 78,
          timestamp: new Date().toISOString()
        }
      )
      break

    case 'nik':
      baseResults.push(
        {
          source: 'Dukcapil Database',
          data: {
            nik: query,
            name: 'Jane Smith',
            birthDate: '1990-05-15',
            birthPlace: 'Jakarta',
            address: 'Jl. Sudirman No. 123',
            verified: true
          },
          confidence: 98,
          timestamp: new Date().toISOString()
        }
      )
      break

    case 'domain':
      baseResults.push(
        {
          source: 'WHOIS Database',
          data: {
            domain: query,
            registrar: 'GoDaddy',
            createdDate: '2020-03-15',
            expiryDate: '2025-03-15',
            nameServers: ['ns1.example.com', 'ns2.example.com']
          },
          confidence: 100,
          timestamp: new Date().toISOString()
        },
        {
          source: 'DNS Records',
          data: {
            domain: query,
            ipAddress: '***********',
            mxRecords: ['mail.example.com'],
            txtRecords: ['v=spf1 include:_spf.google.com ~all']
          },
          confidence: 100,
          timestamp: new Date().toISOString()
        }
      )
      break

    default:
      baseResults.push(
        {
          source: 'General Database',
          data: {
            query: query,
            type: type,
            found: true,
            details: 'Information found in general database'
          },
          confidence: 75,
          timestamp: new Date().toISOString()
        }
      )
  }

  // Add more results if deep search is enabled
  if (deepSearch) {
    baseResults.push(
      {
        source: 'Deep Web Search',
        data: {
          query: query,
          additionalInfo: 'Additional information from deep web sources',
          relatedEntities: ['Entity 1', 'Entity 2'],
          crossReferences: 3
        },
        confidence: 65,
        timestamp: new Date().toISOString()
      }
    )
  }

  return baseResults
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
