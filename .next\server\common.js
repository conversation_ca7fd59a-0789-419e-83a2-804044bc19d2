exports.id=592,exports.ids=[592],exports.modules={62849:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=62849,e.exports=t},60546:(e,t,s)=>{Promise.resolve().then(s.bind(s,67382))},27807:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,12994,23)),Promise.resolve().then(s.t.bind(s,96114,23)),Promise.resolve().then(s.t.bind(s,9727,23)),Promise.resolve().then(s.t.bind(s,79671,23)),Promise.resolve().then(s.t.bind(s,41868,23)),Promise.resolve().then(s.t.bind(s,84759,23))},35303:()=>{},67382:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>n,a:()=>o});var r=s(10326),a=s(17577);let i=(0,a.createContext)(void 0);function n({children:e}){let[t,s]=(0,a.useState)(null),[n,o]=(0,a.useState)(null),[c,l]=(0,a.useState)(!0),d=async(e,t)=>{try{l(!0);let r=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})}),a=await r.json();if(a.success&&a.token)return o(a.token),s(a.user),!0;return!1}catch(e){return console.error("Login error:",e),!1}finally{l(!1)}},h=async()=>{try{let e=await fetch("/api/auth/refresh",{method:"POST",headers:{Authorization:`Bearer ${n}`}}),t=await e.json();t.success&&t.token&&o(t.token)}catch(e){console.error("Token refresh error:",e),u()}},u=()=>{s(null),o(null)};return r.jsx(i.Provider,{value:{user:t,token:n,login:d,logout:u,isLoading:c,isAuthenticated:!!t&&!!n,refreshToken:h},children:e})}function o(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},2262:(e,t,s)=>{"use strict";s.d(t,{Rm:()=>i,TT:()=>n,XO:()=>o,Zb:()=>a});var r=s(10326);function a({children:e,className:t="",hover:s=!1,glow:a=!1,border:i="default"}){return r.jsx("div",{className:`
        bg-gray-900/50 backdrop-blur-sm rounded-lg border ${{default:"border-gray-700",green:"border-cyber-green",blue:"border-cyber-blue",red:"border-cyber-red",gold:"border-nusantara-gold"}[i]}
        ${s?"hover:border-cyber-green hover:shadow-lg hover:shadow-cyber-green/20 transition-all duration-300 cursor-pointer":""}
        ${a?({default:"",green:"shadow-lg shadow-cyber-green/20",blue:"shadow-lg shadow-cyber-blue/20",red:"shadow-lg shadow-cyber-red/20",gold:"shadow-lg shadow-nusantara-gold/20"})[i]:""}
        ${t}
      `,children:e})}function i({title:e,value:t,icon:s,color:i="green",trend:n,loading:o=!1}){let c={green:"text-cyber-green",blue:"text-cyber-blue",red:"text-cyber-red",purple:"text-cyber-purple",gold:"text-nusantara-gold"};return r.jsx(a,{border:i,glow:!0,children:r.jsx("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm font-medium text-gray-400",children:e}),(0,r.jsxs)("div",{className:"flex items-baseline space-x-2",children:[o?r.jsx("div",{className:"h-8 w-20 bg-gray-700 animate-pulse rounded"}):r.jsx("p",{className:`text-2xl font-bold ${c[i]}`,children:"number"==typeof t?t.toLocaleString():t}),n&&(0,r.jsxs)("span",{className:`text-sm font-medium ${n.isPositive?"text-green-400":"text-red-400"}`,children:[n.isPositive?"+":"",n.value,"%"]})]})]}),r.jsx("div",{className:`p-3 rounded-lg ${{green:"bg-cyber-green/10",blue:"bg-cyber-blue/10",red:"bg-cyber-red/10",purple:"bg-cyber-purple/10",gold:"bg-nusantara-gold/10"}[i]}`,children:r.jsx(s,{className:`h-6 w-6 ${c[i]}`})})]})})})}function n({title:e,description:t,icon:s,color:i="green",onClick:n,disabled:o=!1,badge:c}){let l={green:"text-cyber-green",blue:"text-cyber-blue",red:"text-cyber-red",purple:"text-cyber-purple",gold:"text-nusantara-gold"},d={green:"bg-cyber-green/10",blue:"bg-cyber-blue/10",red:"bg-cyber-red/10",purple:"bg-cyber-purple/10",gold:"bg-nusantara-gold/10"};return r.jsx(a,{hover:!o&&!!n,border:i,className:`relative ${o?"opacity-50 cursor-not-allowed":""}`,children:(0,r.jsxs)("div",{className:"p-6 h-full",onClick:o?void 0:n,children:[c&&r.jsx("div",{className:"absolute top-3 right-3",children:r.jsx("span",{className:`px-2 py-1 text-xs font-semibold rounded-full ${d[i]} ${l[i]}`,children:c})}),r.jsx("div",{className:`inline-flex p-3 rounded-lg ${d[i]} mb-4`,children:r.jsx(s,{className:`h-6 w-6 ${l[i]}`})}),r.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:e}),r.jsx("p",{className:"text-gray-400 text-sm leading-relaxed",children:t}),n&&!o&&r.jsx("div",{className:"mt-4 pt-4 border-t border-gray-700",children:r.jsx("span",{className:`text-sm font-medium ${l[i]} hover:underline`,children:"Mulai Sekarang →"})})]})})}function o({type:e="info",title:t,message:s,onClose:a}){let i={info:{border:"border-cyber-blue",bg:"bg-cyber-blue/10",text:"text-cyber-blue",icon:"\uD83D\uDCA1"},success:{border:"border-cyber-green",bg:"bg-cyber-green/10",text:"text-cyber-green",icon:"✅"},warning:{border:"border-nusantara-gold",bg:"bg-nusantara-gold/10",text:"text-nusantara-gold",icon:"⚠️"},error:{border:"border-cyber-red",bg:"bg-cyber-red/10",text:"text-cyber-red",icon:"❌"}}[e];return r.jsx("div",{className:`border ${i.border} ${i.bg} rounded-lg p-4`,children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[r.jsx("span",{className:"text-lg",children:i.icon}),(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("h4",{className:`font-semibold ${i.text}`,children:t}),r.jsx("p",{className:"text-gray-300 text-sm mt-1",children:s})]}),a&&r.jsx("button",{onClick:a,className:"text-gray-400 hover:text-white transition-colors",children:"\xd7"})]})})}},60463:(e,t,s)=>{"use strict";s.d(t,{Z:()=>u});var r=s(10326),a=s(17577),i=s(33265);class n{constructor(){this.memoryCache=new Map,this.diskCache=null,this.stats={hits:0,misses:0,sets:0,deletes:0,size:0},this.TTL={STATIC:864e5,API:3e5,USER_DATA:9e5,SEARCH:6e5,DASHBOARD:12e4,REALTIME:3e4},this.initDiskCache(),this.startCleanupInterval()}async initDiskCache(){}isExpired(e){return Date.now()-e.timestamp>e.ttl}generateKey(e,t){return`${e}:${t}`}async get(e,t,s){let r=this.memoryCache.get(e);if(r&&!this.isExpired(r))return this.stats.hits++,r.data;if(this.diskCache)try{let t=await this.diskCache.get("cache",e);if(t&&!this.isExpired(t))return this.memoryCache.set(e,t),this.stats.hits++,t.data}catch(e){console.warn("Disk cache read error:",e)}if(t)try{let r=await t();return null!=r&&await this.set(e,r,s||this.TTL.API),this.stats.misses++,r}catch(e){return console.error("Cache fallback error:",e),this.stats.misses++,null}return this.stats.misses++,null}async set(e,t,s=this.TTL.API){let r={data:t,timestamp:Date.now(),ttl:s,version:"1.0"};if(this.memoryCache.set(e,r),this.stats.sets++,this.stats.size=this.memoryCache.size,this.diskCache&&s>this.TTL.REALTIME)try{await this.diskCache.put("cache",{key:e,...r})}catch(e){console.warn("Disk cache write error:",e)}this.memoryCache.size>1e3&&this.evictOldest()}async delete(e){if(this.memoryCache.delete(e),this.stats.deletes++,this.stats.size=this.memoryCache.size,this.diskCache)try{await this.diskCache.delete("cache",e)}catch(e){console.warn("Disk cache delete error:",e)}}async clear(){if(this.memoryCache.clear(),this.stats={hits:0,misses:0,sets:0,deletes:0,size:0},this.diskCache)try{await this.diskCache.clear("cache")}catch(e){console.warn("Disk cache clear error:",e)}}evictOldest(){let e=Array.from(this.memoryCache.entries());e.sort((e,t)=>e[1].timestamp-t[1].timestamp);let t=Math.floor(.1*e.length);for(let s=0;s<t;s++)this.memoryCache.delete(e[s][0])}startCleanupInterval(){}cleanup(){for(let[e,t]of(Date.now(),this.memoryCache.entries()))this.isExpired(t)&&this.memoryCache.delete(e);this.stats.size=this.memoryCache.size}async cacheUserData(e,t){await this.set(`user:${e}`,t,this.TTL.USER_DATA)}async getUserData(e){return await this.get(`user:${e}`)}async cacheSearchResults(e,t){let s=`search:${btoa(e)}`;await this.set(s,t,this.TTL.SEARCH)}async getSearchResults(e){let t=`search:${btoa(e)}`;return await this.get(t)}async cacheDashboardData(e,t){await this.set(`dashboard:${e}`,t,this.TTL.DASHBOARD)}async getDashboardData(e){return await this.get(`dashboard:${e}`)}async cacheApiResponse(e,t,s){let r=`api:${e}:${JSON.stringify(t)}`;await this.set(r,s,this.TTL.API)}async getApiResponse(e,t){let s=`api:${e}:${JSON.stringify(t)}`;return await this.get(s)}getStats(){return{...this.stats}}getHitRate(){let e=this.stats.hits+this.stats.misses;return e>0?this.stats.hits/e*100:0}async preloadCriticalData(e){let t=["/api/user/profile","/api/dashboard/stats","/api/notifications","/api/user/plan"].map(async t=>{try{let s=await fetch(t);if(s.ok){let r=await s.json();await this.cacheApiResponse(t,{userId:e},r)}}catch(e){console.warn(`Preload failed for ${t}:`,e)}});await Promise.allSettled(t)}}let o=new n;class c{constructor(){this.preloadQueue=new Map,this.preloadedRoutes=new Set,this.isPreloading=!1,this.observer=null,this.ROUTE_STRATEGIES=[{route:"/dashboard",dependencies:["/osint","/scanner","/profile"],dataEndpoints:["/api/dashboard/stats","/api/user/profile","/api/notifications"],assets:[],priority:1},{route:"/osint",dependencies:["/file-analyzer","/cve"],dataEndpoints:["/api/osint/recent","/api/osint/templates"],assets:[],priority:2},{route:"/scanner",dependencies:["/cve","/tools"],dataEndpoints:["/api/scanner/history","/api/scanner/templates"],assets:[],priority:2},{route:"/profile",dependencies:["/settings","/plan"],dataEndpoints:["/api/user/profile","/api/user/api-keys","/api/user/usage"],assets:[],priority:3},{route:"/settings",dependencies:[],dataEndpoints:["/api/user/settings","/api/user/preferences"],assets:[],priority:4}],this.initIntersectionObserver(),this.initRoutePreloading()}initIntersectionObserver(){}initRoutePreloading(){}async preloadCriticalRoutes(){let e=window.location.pathname,t=this.ROUTE_STRATEGIES.find(t=>t.route===e);if(t){for(let e of t.dependencies)await this.preloadRoute(e,"high");for(let e of t.dataEndpoints)await this.preloadData(e)}"/dashboard"!==e&&await this.preloadRoute("/dashboard","medium")}setupHoverPreloading(){}setupBehaviorBasedPreloading(){}predictNextRoutes(e,t){return({"/dashboard":["/osint","/scanner","/profile"],"/osint":["/file-analyzer","/cve","/dashboard"],"/scanner":["/cve","/tools","/dashboard"],"/profile":["/settings","/plan","/dashboard"],"/":["/dashboard","/login","/register"]})[e]||[]}getUserBehaviorPattern(){return{}}async preloadRoute(e,t="medium"){if(!this.preloadedRoutes.has(e))try{let t=this.ROUTE_STRATEGIES.find(t=>t.route===e);if(t){let e=t.dataEndpoints.map(e=>this.preloadData(e));await Promise.allSettled(e)}this.preloadedRoutes.add(e),console.log(`✅ Preloaded route: ${e}`)}catch(t){console.warn(`❌ Failed to preload route ${e}:`,t)}}async preloadData(e){try{if(await o.get(e))return;let t=await fetch(e,{method:"GET",headers:{"Content-Type":"application/json"}});if(t.ok){let s=await t.json();await o.set(e,s),console.log(`📦 Preloaded data: ${e}`)}}catch(t){console.warn(`❌ Failed to preload data ${e}:`,t)}}async preloadCriticalAssets(){let e=["/images/logo.svg","/images/cyber-bg.jpg"].map(e=>new Promise(t=>{let s=document.createElement("link");s.rel="preload",s.href=e,s.as=(e.endsWith(".svg"),"image"),s.onload=()=>t(),s.onerror=()=>t(),document.head.appendChild(s)}));await Promise.allSettled(e)}observeLinks(){this.observer&&document.querySelectorAll('a[href^="/"]').forEach(e=>{this.observer.observe(e)})}async preloadUserJourney(e,t){for(let[s,r]of this.getUserJourneyRoutes(e,t).entries())setTimeout(()=>{this.preloadRoute(r,s<3?"high":"low")},500*s)}getUserJourneyRoutes(e,t){let s=["/dashboard","/profile","/settings"];return"cybersecurity"===t||"bughunter"===t?[...s,"/osint","/scanner","/file-analyzer","/cve","/playground","/bot"]:[...s,"/osint","/file-analyzer","/cve","/tools"]}getStats(){return{preloadedRoutes:Array.from(this.preloadedRoutes),queueSize:this.preloadQueue.size,isPreloading:this.isPreloading,cacheStats:o.getStats()}}clearPreloaded(){this.preloadedRoutes.clear(),this.preloadQueue.clear()}}new c;class l{constructor(){this.metrics={},this.budget={pageLoadTime:2e3,firstContentfulPaint:1e3,largestContentfulPaint:2500,firstInputDelay:100,cumulativeLayoutShift:.1,timeToInteractive:3e3},this.observers=new Map,this.routeStartTime=0,this.initPerformanceObservers(),this.trackPageLoad(),this.trackRouteChanges()}initPerformanceObservers(){}initWebVitalsObserver(){if(!("PerformanceObserver"in window))return;let e=new PerformanceObserver(e=>{let t=e.getEntries(),s=t[t.length-1];this.metrics.firstContentfulPaint=s.startTime,this.checkBudget("firstContentfulPaint",s.startTime)});e.observe({entryTypes:["paint"]}),this.observers.set("fcp",e);let t=new PerformanceObserver(e=>{let t=e.getEntries(),s=t[t.length-1];this.metrics.largestContentfulPaint=s.startTime,this.checkBudget("largestContentfulPaint",s.startTime)});t.observe({entryTypes:["largest-contentful-paint"]}),this.observers.set("lcp",t);let s=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{this.metrics.firstInputDelay=e.processingStart-e.startTime,this.checkBudget("firstInputDelay",this.metrics.firstInputDelay)})});s.observe({entryTypes:["first-input"]}),this.observers.set("fid",s);let r=0,a=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{e.hadRecentInput||(r+=e.value)}),this.metrics.cumulativeLayoutShift=r,this.checkBudget("cumulativeLayoutShift",r)});a.observe({entryTypes:["layout-shift"]}),this.observers.set("cls",a)}initNavigationObserver(){if(!("PerformanceObserver"in window))return;let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{this.metrics.pageLoadTime=e.loadEventEnd-e.fetchStart,this.metrics.timeToInteractive=e.domInteractive-e.fetchStart,this.checkBudget("pageLoadTime",this.metrics.pageLoadTime),this.checkBudget("timeToInteractive",this.metrics.timeToInteractive)})});e.observe({entryTypes:["navigation"]}),this.observers.set("navigation",e)}initResourceObserver(){if(!("PerformanceObserver"in window))return;let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{e.duration>1e3&&console.warn(`Slow resource detected: ${e.name} (${e.duration}ms)`),e.name.includes("/api/")&&(this.metrics.apiResponseTime=e.duration)})});e.observe({entryTypes:["resource"]}),this.observers.set("resource",e)}initLongTaskObserver(){if(!("PerformanceObserver"in window))return;let e=0,t=new PerformanceObserver(t=>{t.getEntries().forEach(t=>{t.duration>50&&(e+=t.duration-50)}),this.metrics.totalBlockingTime=e});t.observe({entryTypes:["longtask"]}),this.observers.set("longtask",t)}trackPageLoad(){}trackRouteChanges(){}markRouteChangeComplete(){this.routeStartTime>0&&(this.metrics.routeChangeTime=performance.now()-this.routeStartTime,this.routeStartTime=0,this.metrics.routeChangeTime>500&&console.warn(`Slow route change: ${this.metrics.routeChangeTime}ms`))}checkBudget(e,t){let s=this.budget[e];t>s&&(console.warn(`Performance budget exceeded for ${e}: ${t}ms > ${s}ms`),this.suggestOptimizations(e,t,s))}suggestOptimizations(e,t,s){console.group(`🚀 Performance Optimization Suggestions for ${e}`),console.log(`Current: ${t}ms, Budget: ${s}ms`),(({pageLoadTime:["Enable compression (gzip/brotli)","Optimize images and use WebP format","Implement code splitting","Use CDN for static assets"],firstContentfulPaint:["Optimize critical CSS","Preload key resources","Reduce server response time","Eliminate render-blocking resources"],largestContentfulPaint:["Optimize images above the fold","Preload LCP element","Reduce server response time","Remove unused CSS"],firstInputDelay:["Reduce JavaScript execution time","Break up long tasks","Use web workers for heavy computations","Optimize third-party scripts"],cumulativeLayoutShift:["Set size attributes on images and videos","Reserve space for ads and embeds","Avoid inserting content above existing content","Use CSS aspect-ratio for responsive images"]})[e]||[]).forEach(e=>console.log(`• ${e}`)),console.groupEnd()}calculatePerformanceScore(){let e=100;return Object.entries({firstContentfulPaint:.15,largestContentfulPaint:.25,firstInputDelay:.25,cumulativeLayoutShift:.25,totalBlockingTime:.1}).forEach(([t,s])=>{let r=this.metrics[t],a=this.budget[t];if(r&&a){let t=r/a;t>1&&(e-=(t-1)*s*100)}}),Math.max(0,Math.min(100,e))}reportMetrics(){let e=this.calculatePerformanceScore();console.group("\uD83D\uDCCA Performance Metrics Report"),console.log("Overall Score:",`${e.toFixed(1)}/100`),console.log("Metrics:",this.metrics),console.log("Cache Hit Rate:",this.getCacheHitRate()),console.groupEnd(),this.sendToAnalytics({...this.metrics,performanceScore:e,timestamp:Date.now(),userAgent:navigator.userAgent,url:window.location.href})}getCacheHitRate(){return 0}sendToAnalytics(e){}getMetrics(){return{...this.metrics}}getPerformanceScore(){return this.calculatePerformanceScore()}updateBudget(e){this.budget={...this.budget,...e}}cleanup(){this.observers.forEach(e=>e.disconnect()),this.observers.clear()}}new l;let d=(0,i.default)(()=>Promise.all([s.e(216),s.e(633)]).then(s.bind(s,8633)),{loadableGenerated:{modules:["components\\DashboardLayout.tsx -> ./Sidebar"]},loading:()=>r.jsx("div",{className:"w-64 bg-gray-900 animate-pulse"}),ssr:!0}),h=(0,i.default)(()=>Promise.all([s.e(216),s.e(592)]).then(s.bind(s,16361)),{loadableGenerated:{modules:["components\\DashboardLayout.tsx -> ./Navbar"]},loading:()=>r.jsx("div",{className:"h-16 bg-gray-900 animate-pulse"}),ssr:!0});function u({children:e,user:t,title:s,showSearch:i=!0}){return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-cyber",children:[r.jsx(a.Suspense,{fallback:r.jsx("div",{className:"h-16 bg-gray-900 animate-pulse"}),children:r.jsx(h,{user:t,title:s,showSearch:i,isLandingPage:!1})}),(0,r.jsxs)("div",{className:"flex pt-16",children:[r.jsx(a.Suspense,{fallback:r.jsx("div",{className:"w-64 bg-gray-900 animate-pulse"}),children:r.jsx(d,{user:t})}),r.jsx("main",{className:"flex-1 min-w-0 lg:ml-0",children:r.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:r.jsx("div",{className:"w-full",children:r.jsx(a.Suspense,{fallback:(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx("div",{className:"h-8 bg-gray-800 rounded animate-pulse"}),r.jsx("div",{className:"h-64 bg-gray-800 rounded animate-pulse"})]}),children:e})})})})]})]})}},16361:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v});var r=s(10326),a=s(17577),i=s(90434),n=s(35047),o=s(54659),c=s(37202),l=s(91470),d=s(18019),h=s(58038),u=s(88307),m=s(54014),g=s(6507),p=s(26092),y=s(79635),x=s(88378),b=s(71810),f=s(94019),w=s(90748);function v({user:e,showSearch:t=!0,title:s,isLandingPage:v=!1}){let[N,E]=(0,a.useState)(!1),[j,S]=(0,a.useState)(!1),[C,T]=(0,a.useState)(!1),[k,P]=(0,a.useState)([]),[R,A]=(0,a.useState)(0);(0,n.usePathname)();let O=(0,n.useRouter)(),I=async e=>{try{let t=localStorage.getItem("auth-token");if(!t)return;await fetch(`/api/notifications/${e}/read`,{method:"POST",headers:{Authorization:`Bearer ${t}`}}),P(t=>t.map(t=>t.id===e?{...t,read:!0}:t))}catch(t){console.error("Failed to mark notification as read:",t),P(t=>t.map(t=>t.id===e?{...t,read:!0}:t))}},D=e=>{switch(e){case"success":return o.Z;case"warning":return c.Z;case"error":return l.Z;default:return d.Z}},L=e=>{switch(e){case"success":return"text-green-500";case"warning":return"text-yellow-500";case"error":return"text-red-500";default:return"text-blue-500"}},_=[{name:"Home",href:"/"},{name:"Features",href:"/#features"},{name:"Pricing",href:"/#pricing"},{name:"About",href:"/#about"},{name:"Contact",href:"/#contact"}];return(0,r.jsxs)("nav",{className:`fixed top-0 left-0 right-0 z-40 bg-gray-900/95 backdrop-blur-sm border-b border-gray-800 ${v?"lg:px-8":""}`,children:[(0,r.jsxs)("div",{className:`flex justify-between items-center h-16 ${v?"max-w-7xl mx-auto px-4 sm:px-6":"px-4 lg:pl-0"}`,children:[(0,r.jsxs)("div",{className:"flex items-center space-x-8",children:[!v&&r.jsx("div",{className:"w-12 lg:hidden"}),(0,r.jsxs)(i.default,{href:"/",className:"flex items-center space-x-2",children:[r.jsx("div",{className:"w-8 h-8 bg-cyber-green rounded-lg flex items-center justify-center",children:r.jsx(h.Z,{className:"h-5 w-5 text-black"})}),r.jsx("span",{className:"font-bold text-white text-xl font-cyber",children:"KodeXGuard"})]}),v&&r.jsx("div",{className:"hidden lg:flex items-center space-x-8",children:_.map(e=>r.jsx(i.default,{href:e.href,className:"text-gray-300 hover:text-cyber-green transition-colors duration-200 font-medium",children:e.name},e.name))}),!v&&s&&r.jsx("h1",{className:"text-xl font-bold text-white font-cyber hidden lg:block",children:s})]}),!v&&t&&r.jsx("div",{className:"hidden lg:flex flex-1 max-w-lg mx-8",children:(0,r.jsxs)("div",{className:"relative w-full",children:[r.jsx(u.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),r.jsx("input",{type:"text",placeholder:"Search across all tools...",className:"w-full pl-10 pr-4 py-2 bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyber-green focus:border-transparent"})]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[v&&(0,r.jsxs)("button",{className:"hidden lg:flex items-center space-x-1 text-gray-300 hover:text-white transition-colors",children:[r.jsx(m.Z,{className:"h-4 w-4"}),r.jsx("span",{className:"text-sm",children:"ID"})]}),!v&&t&&r.jsx("button",{className:"lg:hidden text-gray-400 hover:text-white transition-colors",children:r.jsx(u.Z,{className:"h-5 w-5"})}),!v&&(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{onClick:()=>T(!C),className:"relative text-gray-400 hover:text-white transition-colors",children:[r.jsx(g.Z,{className:"h-5 w-5"}),R>0&&r.jsx("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center",children:R>9?"9+":R})]}),C&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-80 bg-gray-800 border border-gray-700 rounded-lg shadow-xl z-50 max-h-96 overflow-hidden",children:[(0,r.jsxs)("div",{className:"p-3 border-b border-gray-700 flex items-center justify-between",children:[r.jsx("h3",{className:"text-sm font-semibold text-white",children:"Notifications"}),r.jsx(i.default,{href:"/notifications",className:"text-xs text-cyber-green hover:text-cyber-green/80 transition-colors",onClick:()=>T(!1),children:"View All"})]}),r.jsx("div",{className:"max-h-80 overflow-y-auto",children:0===k.length?r.jsx("div",{className:"p-4 text-center text-gray-400 text-sm",children:"No notifications"}):k.map(e=>{let t=D(e.type);return r.jsx("div",{className:`p-3 border-b border-gray-700 last:border-b-0 hover:bg-gray-700/50 transition-colors cursor-pointer ${e.read?"":"bg-gray-700/20"}`,onClick:()=>{e.read||I(e.id),e.action&&(O.push(e.action.url),T(!1))},children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[r.jsx(t,{className:`flex-shrink-0 h-4 w-4 mt-0.5 ${L(e.type)}`}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("h4",{className:`text-sm font-medium ${e.read?"text-gray-300":"text-white"}`,children:e.title}),r.jsx("span",{className:"text-xs text-gray-400",children:new Date(e.timestamp).toLocaleTimeString("id-ID",{hour:"2-digit",minute:"2-digit"})})]}),r.jsx("p",{className:"text-xs text-gray-400 mt-1 line-clamp-2",children:e.message}),e.action&&(0,r.jsxs)("span",{className:"inline-block mt-2 text-xs text-cyber-green",children:[e.action.label," →"]})]})]})},e.id)})})]})]}),e?(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{onClick:()=>S(!j),className:"flex items-center space-x-2 text-gray-300 hover:text-white transition-colors",children:[e.avatar?r.jsx("img",{src:e.avatar,alt:e.username,className:"h-8 w-8 rounded-full"}):r.jsx("div",{className:"h-8 w-8 bg-cyber-green rounded-full flex items-center justify-center",children:r.jsx("span",{className:"text-black font-semibold text-sm",children:e.username.charAt(0).toUpperCase()})}),r.jsx("span",{className:"hidden sm:block font-medium",children:e.username}),("cybersecurity"===e.plan||"bughunter"===e.plan)&&r.jsx(p.Z,{className:"h-4 w-4 text-nusantara-gold"})]}),j&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-gray-800 rounded-lg shadow-lg border border-gray-700 py-1 z-50",children:[(0,r.jsxs)("div",{className:"px-4 py-2 border-b border-gray-700",children:[r.jsx("p",{className:"text-sm font-medium text-white",children:e.username}),r.jsx("p",{className:"text-xs text-gray-400",children:e.role?.replace("_"," ").toUpperCase()||"User"}),(0,r.jsxs)("p",{className:"text-xs text-cyber-green",children:[e.plan?.toUpperCase()," Plan"]})]}),(0,r.jsxs)(i.default,{href:"/profile",className:"flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white",onClick:()=>S(!1),children:[r.jsx(y.Z,{className:"h-4 w-4 mr-2"}),"Profile & API Keys"]}),(0,r.jsxs)(i.default,{href:"/settings",className:"flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white",onClick:()=>S(!1),children:[r.jsx(x.Z,{className:"h-4 w-4 mr-2"}),"Settings"]}),("super_admin"===e.role||"admin"===e.role)&&(0,r.jsxs)(i.default,{href:"/admin",className:"flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white",onClick:()=>S(!1),children:[r.jsx(h.Z,{className:"h-4 w-4 mr-2"}),"Admin Panel"]}),r.jsx("hr",{className:"my-1 border-gray-700"}),(0,r.jsxs)("button",{onClick:()=>{localStorage.removeItem("auth-token"),O.push("/login")},className:"flex items-center w-full px-4 py-2 text-sm text-red-400 hover:bg-gray-700 text-left",children:[r.jsx(b.Z,{className:"h-4 w-4 mr-2"}),"Logout"]})]})]}):(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(i.default,{href:"/login",className:"cyber-btn",children:"Login"}),r.jsx(i.default,{href:"/register",className:"cyber-btn-primary",children:"Register"})]}),v&&r.jsx("button",{onClick:()=>E(!N),className:"lg:hidden text-gray-400 hover:text-white transition-colors",children:N?r.jsx(f.Z,{className:"h-6 w-6"}):r.jsx(w.Z,{className:"h-6 w-6"})})]})]}),v&&N&&r.jsx("div",{className:"lg:hidden border-t border-gray-800 bg-gray-900/98",children:(0,r.jsxs)("div",{className:"px-4 pt-2 pb-3 space-y-1",children:[_.map(e=>r.jsx(i.default,{href:e.href,className:"block px-3 py-2 text-gray-300 hover:text-cyber-green hover:bg-gray-800 rounded-md transition-colors",onClick:()=>E(!1),children:e.name},e.name)),r.jsx("div",{className:"border-t border-gray-700 pt-2 mt-2",children:(0,r.jsxs)("button",{className:"flex items-center px-3 py-2 text-gray-300 hover:text-white transition-colors",children:[r.jsx(m.Z,{className:"h-4 w-4 mr-2"}),"Language: Indonesia"]})}),!e&&(0,r.jsxs)("div",{className:"border-t border-gray-700 pt-2 mt-2 space-y-2",children:[r.jsx(i.default,{href:"/login",className:"block px-3 py-2 text-center cyber-btn",onClick:()=>E(!1),children:"Login"}),r.jsx(i.default,{href:"/register",className:"block px-3 py-2 text-center cyber-btn-primary",onClick:()=>E(!1),children:"Register"})]})]})})]})}},30829:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l,metadata:()=>c});var r=s(19510),a=s(77366),i=s.n(a);s(7633);var n=s(68570);let o=(0,n.createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\contexts\AuthContext.tsx#AuthProvider`);(0,n.createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\contexts\AuthContext.tsx#useAuth`);let c={title:"KodeXGuard - Platform Cybersecurity & Bug Hunting",description:"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting dengan dukungan Bot WhatsApp/Telegram",keywords:"cybersecurity, bug hunting, OSINT, vulnerability scanner, CVE, penetration testing, security tools",authors:[{name:"KodeXGuard Team"}],creator:"KodeXGuard",publisher:"KodeXGuard",robots:"index, follow",openGraph:{type:"website",locale:"id_ID",url:"https://kodexguard.com",title:"KodeXGuard - Platform Cybersecurity & Bug Hunting",description:"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting",siteName:"KodeXGuard"},twitter:{card:"summary_large_image",title:"KodeXGuard - Platform Cybersecurity & Bug Hunting",description:"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting"},viewport:"width=device-width, initial-scale=1",themeColor:"#00ff41"};function l({children:e}){return(0,r.jsxs)("html",{lang:"id",className:"dark",children:[(0,r.jsxs)("head",{children:[r.jsx("link",{rel:"icon",href:"/favicon.ico"}),r.jsx("link",{rel:"apple-touch-icon",href:"/apple-touch-icon.png"}),r.jsx("link",{rel:"manifest",href:"/manifest.json"})]}),(0,r.jsxs)("body",{className:`${i().className} min-h-screen bg-gradient-cyber text-white antialiased`,children:[r.jsx("div",{className:"matrix-bg",children:r.jsx("div",{className:"absolute inset-0 bg-gradient-to-b from-transparent via-cyber-dark/20 to-cyber-dark"})}),r.jsx("div",{className:"relative z-10",children:r.jsx(o,{children:e})}),r.jsx("script",{dangerouslySetInnerHTML:{__html:`
              // Matrix Rain Effect
              function createMatrixRain() {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.style.position = 'fixed';
                canvas.style.top = '0';
                canvas.style.left = '0';
                canvas.style.width = '100%';
                canvas.style.height = '100%';
                canvas.style.pointerEvents = 'none';
                canvas.style.zIndex = '-1';
                canvas.style.opacity = '0.1';
                document.body.appendChild(canvas);
                
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
                
                const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';
                const charArray = chars.split('');
                const fontSize = 14;
                const columns = canvas.width / fontSize;
                const drops = [];
                
                for (let i = 0; i < columns; i++) {
                  drops[i] = 1;
                }
                
                function draw() {
                  ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
                  ctx.fillRect(0, 0, canvas.width, canvas.height);
                  
                  ctx.fillStyle = '#00ff41';
                  ctx.font = fontSize + 'px monospace';
                  
                  for (let i = 0; i < drops.length; i++) {
                    const text = charArray[Math.floor(Math.random() * charArray.length)];
                    ctx.fillText(text, i * fontSize, drops[i] * fontSize);
                    
                    if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
                      drops[i] = 0;
                    }
                    drops[i]++;
                  }
                }
                
                setInterval(draw, 50);
                
                window.addEventListener('resize', () => {
                  canvas.width = window.innerWidth;
                  canvas.height = window.innerHeight;
                });
              }
              
              // Initialize matrix effect after page load
              if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', createMatrixRain);
              } else {
                createMatrixRain();
              }
            `}})]})]})}},90455:(e,t,s)=>{"use strict";s.d(t,{e8:()=>h});var r=s(98691),a=s(41482),i=s.n(a),n=s(84770),o=s.n(n),c=s(24544);let l=process.env.JWT_SECRET||"kodexguard-secret",d=process.env.JWT_EXPIRES_IN||"7d";class h{static async hashPassword(e){return await r.ZP.hash(e,12)}static async verifyPassword(e,t){return await r.ZP.compare(e,t)}static generateToken(e){return i().sign(e,l,{expiresIn:d})}static verifyToken(e){try{return i().verify(e,l)}catch(e){return null}}static generateApiKey(){return{apiKey:"kxg_"+o().randomBytes(32).toString("hex"),apiSecret:o().randomBytes(64).toString("hex")}}static async register(e){try{if((await c.db.query("SELECT id FROM users WHERE email = ? OR username = ?",[e.email,e.username])).length>0)return{success:!1,error:"User with this email or username already exists"};let t=await this.hashPassword(e.password),s=await c.db.query(`INSERT INTO users (username, email, password_hash, full_name, plan_id, created_at) 
         VALUES (?, ?, ?, ?, 1, NOW())`,[e.username,e.email,t,e.fullName||null]),r=await c.db.queryOne("SELECT * FROM users WHERE id = ?",[s.insertId]);if(!r)return{success:!1,error:"Failed to create user"};let a=this.generateToken({userId:r.id,username:r.username,email:r.email,role:r.role});return{success:!0,user:r,token:a,message:"User registered successfully"}}catch(e){return console.error("Registration error:",e),{success:!1,error:"Registration failed"}}}static async login(e,t){try{let s=await c.db.queryOne("SELECT * FROM users WHERE email = ? AND is_active = 1",[e]);if(!s||!await this.verifyPassword(t,s.password_hash))return{success:!1,error:"Invalid credentials"};await c.db.query("UPDATE users SET last_login = NOW() WHERE id = ?",[s.id]);let r=this.generateToken({userId:s.id,username:s.username,email:s.email,role:s.role});return{success:!0,user:s,token:r,message:"Login successful"}}catch(e){return console.error("Login error:",e),{success:!1,error:"Login failed"}}}static async getUserByToken(e){try{let t=this.verifyToken(e);if(!t)return null;return await c.db.queryOne("SELECT * FROM users WHERE id = ? AND is_active = 1",[t.userId])}catch(e){return console.error("Get user by token error:",e),null}}static async createApiKey(e,t,s=[]){try{let{apiKey:r,apiSecret:a}=this.generateApiKey(),i=await c.db.query(`INSERT INTO api_keys (user_id, key_name, api_key, api_secret, permissions, created_at) 
         VALUES (?, ?, ?, ?, ?, NOW())`,[e,t,r,a,JSON.stringify(s)]),n=await c.db.queryOne("SELECT * FROM api_keys WHERE id = ?",[i.insertId]);return{success:!0,apiKey:n,message:"API key created successfully"}}catch(e){return console.error("Create API key error:",e),{success:!1,error:"Failed to create API key"}}}static async verifyApiKey(e){try{let t=await c.db.queryOne("SELECT * FROM api_keys WHERE api_key = ? AND is_active = 1",[e]);if(!t)return null;let s=await c.db.queryOne("SELECT * FROM users WHERE id = ? AND is_active = 1",[t.userId]);if(!s)return null;return await c.db.query("UPDATE api_keys SET usage_count = usage_count + 1, last_used = NOW() WHERE id = ?",[t.id]),{user:s,apiKeyData:t}}catch(e){return console.error("Verify API key error:",e),null}}static async getUserApiKeys(e){try{return await c.db.query("SELECT * FROM api_keys WHERE user_id = ? ORDER BY created_at DESC",[e])}catch(e){return console.error("Get user API keys error:",e),[]}}static async deleteApiKey(e,t){try{return(await c.db.query("DELETE FROM api_keys WHERE id = ? AND user_id = ?",[t,e])).affectedRows>0}catch(e){return console.error("Delete API key error:",e),!1}}static async changePassword(e,t,s){try{let r=await c.db.queryOne("SELECT * FROM users WHERE id = ?",[e]);if(!r)return{success:!1,error:"User not found"};if(!await this.verifyPassword(t,r.password_hash))return{success:!1,error:"Current password is incorrect"};let a=await this.hashPassword(s);return await c.db.query("UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?",[a,e]),{success:!0,message:"Password changed successfully"}}catch(e){return console.error("Change password error:",e),{success:!1,error:"Failed to change password"}}}}},24544:(e,t,s)=>{"use strict";s.d(t,{db:()=>l,vo:()=>n});var r=s(73785),a=s(79984),i=s(31328);class n{constructor(){this.pool=r.createPool({host:process.env.DB_HOST||"localhost",port:parseInt(process.env.DB_PORT||"3306"),user:process.env.DB_USER||"root",password:process.env.DB_PASSWORD||"rootkan",database:process.env.DB_NAME||"db_kodexguard",waitForConnections:!0,connectionLimit:10,queueLimit:0,charset:"utf8mb4",timezone:"+00:00"})}static getInstance(){return n.instance||(n.instance=new n),n.instance}async query(e,t){try{let[s]=await this.pool.execute(e,t);return s}catch(e){throw console.error("Database query error:",e),e}}async transaction(e){let t=await this.pool.getConnection();try{await t.beginTransaction();let s=await e(t);return await t.commit(),s}catch(e){throw await t.rollback(),e}finally{t.release()}}async close(){await this.pool.end()}}class o{constructor(){this.client=(0,a.createClient)({url:process.env.REDIS_URL||"redis://localhost:6379",password:process.env.REDIS_PASSWORD||void 0,socket:{reconnectStrategy:e=>Math.min(50*e,500)}}),this.client.on("error",e=>{console.error("Redis Client Error:",e)}),this.client.on("connect",()=>{console.log("Redis Client Connected")})}static getInstance(){return o.instance||(o.instance=new o),o.instance}async connect(){this.client.isOpen||await this.client.connect()}async get(e){return await this.connect(),await this.client.get(e)}async set(e,t,s){await this.connect(),s?await this.client.setEx(e,s,t):await this.client.set(e,t)}async del(e){await this.connect(),await this.client.del(e)}async exists(e){return await this.connect(),await this.client.exists(e)===1}async incr(e){return await this.connect(),await this.client.incr(e)}async expire(e,t){await this.connect(),await this.client.expire(e,t)}async close(){this.client.isOpen&&await this.client.quit()}}class c{constructor(){this.client=new i.Client({node:process.env.ELASTICSEARCH_URL||"http://localhost:9200",auth:process.env.ELASTICSEARCH_USERNAME&&process.env.ELASTICSEARCH_PASSWORD?{username:process.env.ELASTICSEARCH_USERNAME,password:process.env.ELASTICSEARCH_PASSWORD}:void 0,requestTimeout:3e4,pingTimeout:3e3,sniffOnStart:!1})}static getInstance(){return c.instance||(c.instance=new c),c.instance}async index(e,t,s){try{await this.client.index({index:e,id:t,body:s})}catch(e){throw console.error("Elasticsearch index error:",e),e}}async search(e,t){try{return await this.client.search({index:e,body:t})}catch(e){throw console.error("Elasticsearch search error:",e),e}}async delete(e,t){try{await this.client.delete({index:e,id:t})}catch(e){throw console.error("Elasticsearch delete error:",e),e}}async createIndex(e,t){try{await this.client.indices.exists({index:e})||await this.client.indices.create({index:e,body:{mappings:t}})}catch(e){throw console.error("Elasticsearch create index error:",e),e}}async close(){await this.client.close()}}let l=n.getInstance();o.getInstance(),c.getInstance()},7633:()=>{}};