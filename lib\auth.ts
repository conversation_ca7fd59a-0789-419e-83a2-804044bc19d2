// KodeXGuard Authentication System
// JWT + API Key based authentication

import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import crypto from 'crypto'
import { db } from './database'
import type { User, Api<PERSON><PERSON> } from '@/types'

const JWT_SECRET = process.env.JWT_SECRET || 'kodexguard-secret'
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d'

export interface AuthResult {
  success: boolean
  user?: User
  token?: string
  message?: string
  error?: string
}

export interface ApiKeyResult {
  success: boolean
  apiKey?: ApiKey
  message?: string
  error?: string
}

export class AuthService {
  // Hash password
  static async hashPassword(password: string): Promise<string> {
    const saltRounds = 12
    return await bcrypt.hash(password, saltRounds)
  }

  // Verify password
  static async verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
    return await bcrypt.compare(password, hashedPassword)
  }

  // Generate JWT token
  static generateToken(payload: any): string {
    return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN })
  }

  // Verify JWT token
  static verifyToken(token: string): any {
    try {
      return jwt.verify(token, JWT_SECRET)
    } catch (error) {
      return null
    }
  }

  // Generate API Key
  static generateApiKey(): { apiKey: string; apiSecret: string } {
    const apiKey = 'kxg_' + crypto.randomBytes(32).toString('hex')
    const apiSecret = crypto.randomBytes(64).toString('hex')
    return { apiKey, apiSecret }
  }

  // Register new user
  static async register(userData: {
    username: string
    email: string
    password: string
    fullName?: string
  }): Promise<AuthResult> {
    try {
      // Check if user already exists
      const existingUser = await db.query(
        'SELECT id FROM users WHERE email = ? OR username = ?',
        [userData.email, userData.username]
      )

      if (existingUser.length > 0) {
        return {
          success: false,
          error: 'User with this email or username already exists'
        }
      }

      // Hash password
      const hashedPassword = await this.hashPassword(userData.password)

      // Create user
      const result = await db.query(
        `INSERT INTO users (username, email, password_hash, full_name, plan_id, created_at) 
         VALUES (?, ?, ?, ?, 1, NOW())`,
        [userData.username, userData.email, hashedPassword, userData.fullName || null]
      )

      // Get created user
      const user = await db.queryOne<User>(
        'SELECT * FROM users WHERE id = ?',
        [(result as any).insertId]
      )

      if (!user) {
        return {
          success: false,
          error: 'Failed to create user'
        }
      }

      // Generate token
      const token = this.generateToken({
        userId: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      })

      return {
        success: true,
        user,
        token,
        message: 'User registered successfully'
      }
    } catch (error) {
      console.error('Registration error:', error)
      return {
        success: false,
        error: 'Registration failed'
      }
    }
  }

  // Login user
  static async login(email: string, password: string): Promise<AuthResult> {
    try {
      // Get user by email
      const user = await db.queryOne<User>(
        'SELECT * FROM users WHERE email = ? AND is_active = 1',
        [email]
      )

      if (!user) {
        return {
          success: false,
          error: 'Invalid credentials'
        }
      }

      // Verify password
      const isValidPassword = await this.verifyPassword(password, user.password_hash)
      if (!isValidPassword) {
        return {
          success: false,
          error: 'Invalid credentials'
        }
      }

      // Update last login
      await db.query(
        'UPDATE users SET last_login = NOW() WHERE id = ?',
        [user.id]
      )

      // Generate token
      const token = this.generateToken({
        userId: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      })

      return {
        success: true,
        user,
        token,
        message: 'Login successful'
      }
    } catch (error) {
      console.error('Login error:', error)
      return {
        success: false,
        error: 'Login failed'
      }
    }
  }

  // Get user by token
  static async getUserByToken(token: string): Promise<User | null> {
    try {
      const decoded = this.verifyToken(token)
      if (!decoded) return null

      const user = await db.queryOne<User>(
        'SELECT * FROM users WHERE id = ? AND is_active = 1',
        [decoded.userId]
      )

      return user
    } catch (error) {
      console.error('Get user by token error:', error)
      return null
    }
  }

  // Create API Key
  static async createApiKey(userId: number, keyName: string, permissions: string[] = []): Promise<ApiKeyResult> {
    try {
      const { apiKey, apiSecret } = this.generateApiKey()

      const result = await db.query(
        `INSERT INTO api_keys (user_id, key_name, api_key, api_secret, permissions, created_at) 
         VALUES (?, ?, ?, ?, ?, NOW())`,
        [userId, keyName, apiKey, apiSecret, JSON.stringify(permissions)]
      )

      const createdKey = await db.queryOne<ApiKey>(
        'SELECT * FROM api_keys WHERE id = ?',
        [(result as any).insertId]
      )

      return {
        success: true,
        apiKey: createdKey!,
        message: 'API key created successfully'
      }
    } catch (error) {
      console.error('Create API key error:', error)
      return {
        success: false,
        error: 'Failed to create API key'
      }
    }
  }

  // Verify API Key
  static async verifyApiKey(apiKey: string): Promise<{ user: User; apiKeyData: ApiKey } | null> {
    try {
      const apiKeyData = await db.queryOne<ApiKey>(
        'SELECT * FROM api_keys WHERE api_key = ? AND is_active = 1',
        [apiKey]
      )

      if (!apiKeyData) return null

      const user = await db.queryOne<User>(
        'SELECT * FROM users WHERE id = ? AND is_active = 1',
        [apiKeyData.userId]
      )

      if (!user) return null

      // Update usage count
      await db.query(
        'UPDATE api_keys SET usage_count = usage_count + 1, last_used = NOW() WHERE id = ?',
        [apiKeyData.id]
      )

      return { user, apiKeyData }
    } catch (error) {
      console.error('Verify API key error:', error)
      return null
    }
  }

  // Get user API keys
  static async getUserApiKeys(userId: number): Promise<ApiKey[]> {
    try {
      return await db.query<ApiKey>(
        'SELECT * FROM api_keys WHERE user_id = ? ORDER BY created_at DESC',
        [userId]
      )
    } catch (error) {
      console.error('Get user API keys error:', error)
      return []
    }
  }

  // Delete API key
  static async deleteApiKey(userId: number, apiKeyId: number): Promise<boolean> {
    try {
      const result = await db.query(
        'DELETE FROM api_keys WHERE id = ? AND user_id = ?',
        [apiKeyId, userId]
      )

      return (result as any).affectedRows > 0
    } catch (error) {
      console.error('Delete API key error:', error)
      return false
    }
  }

  // Change password
  static async changePassword(userId: number, currentPassword: string, newPassword: string): Promise<AuthResult> {
    try {
      // Get current user
      const user = await db.queryOne<User>(
        'SELECT * FROM users WHERE id = ?',
        [userId]
      )

      if (!user) {
        return {
          success: false,
          error: 'User not found'
        }
      }

      // Verify current password
      const isValidPassword = await this.verifyPassword(currentPassword, user.password_hash)
      if (!isValidPassword) {
        return {
          success: false,
          error: 'Current password is incorrect'
        }
      }

      // Hash new password
      const hashedPassword = await this.hashPassword(newPassword)

      // Update password
      await db.query(
        'UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?',
        [hashedPassword, userId]
      )

      return {
        success: true,
        message: 'Password changed successfully'
      }
    } catch (error) {
      console.error('Change password error:', error)
      return {
        success: false,
        error: 'Failed to change password'
      }
    }
  }
}

// Middleware for JWT authentication
export function authenticateToken(req: any, res: any, next: any) {
  const authHeader = req.headers['authorization']
  const token = authHeader && authHeader.split(' ')[1]

  if (!token) {
    return res.status(401).json({
      success: false,
      error: 'Access token required'
    })
  }

  const decoded = AuthService.verifyToken(token)
  if (!decoded) {
    return res.status(403).json({
      success: false,
      error: 'Invalid or expired token'
    })
  }

  req.user = decoded
  next()
}

// Middleware for API key authentication
export async function authenticateApiKey(req: any, res: any, next: any) {
  const apiKey = req.headers['x-api-key'] || req.query.api_key

  if (!apiKey) {
    return res.status(401).json({
      success: false,
      error: 'API key required'
    })
  }

  const result = await AuthService.verifyApiKey(apiKey)
  if (!result) {
    return res.status(403).json({
      success: false,
      error: 'Invalid API key'
    })
  }

  req.user = result.user
  req.apiKey = result.apiKeyData
  next()
}

export default AuthService
