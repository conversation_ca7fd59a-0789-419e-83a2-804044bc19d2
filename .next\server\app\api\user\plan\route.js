"use strict";(()=>{var e={};e.id=461,e.ids=[461],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},56907:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>g,patchFetch:()=>S,requestAsyncStorage:()=>p,routeModule:()=>c,serverHooks:()=>m,staticGenerationAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{GET:()=>u,POST:()=>l});var a=r(49303),n=r(88716),o=r(60670),i=r(87070);async function u(e){try{let t=e.headers.get("authorization"),r=t?.replace("Bearer ","");if(!r)return i.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!r.startsWith("kxg_"))return i.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let s={current:{id:"plan_cybersecurity_001",name:"Cybersecurity Pro",type:"cybersecurity",price:25e5,currency:"IDR",billing:"yearly",startDate:new Date(Date.now()-2592e6).toISOString(),endDate:new Date(Date.now()+28944e6).toISOString(),isActive:!0,autoRenew:!0},features:{scans:{limit:null,used:45,resetDate:new Date(Date.now()+432e6).toISOString()},osintSearches:{limit:null,used:123,resetDate:new Date(Date.now()+432e6).toISOString()},apiCalls:{limit:1e5,used:28560,resetDate:new Date(Date.now()+432e6).toISOString()},storage:{limit:1e3,used:156.7,resetDate:new Date(Date.now()+28944e6).toISOString()},teamMembers:{limit:50,used:8},features:["Unlimited Vulnerability Scans","Unlimited OSINT Searches","Advanced Reporting & Analytics","API Access (100k calls/month)","Priority Support","Custom Scan Templates","Team Collaboration (50 members)","White-label Reports","Advanced Integrations","Custom Webhooks","Compliance Reports","Advanced Security Features"]},usage:{thisMonth:{scans:45,osintSearches:123,apiCalls:28560,storageUsed:12.3},lastMonth:{scans:67,osintSearches:189,apiCalls:34120,storageUsed:8.9}},billing:{nextBillingDate:new Date(Date.now()+28944e6).toISOString(),lastPayment:{amount:25e5,date:new Date(Date.now()-2592e6).toISOString(),method:"Bank Transfer",status:"success"},paymentMethod:{type:"bank",last4:"1234",brand:"BCA"}},availableUpgrades:[{id:"plan_enterprise_001",name:"Enterprise",type:"cybersecurity",price:5e6,currency:"IDR",billing:"yearly",features:["Everything in Cybersecurity Pro","Unlimited Team Members","Dedicated Support Manager","Custom Integrations","On-premise Deployment","Advanced Compliance Reports","Custom SLA"],popular:!0}]};return i.NextResponse.json({success:!0,data:s,timestamp:new Date().toISOString()})}catch(e){return console.error("User plan API error:",e),i.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}async function l(e){try{let t=e.headers.get("authorization"),r=t?.replace("Bearer ","");if(!r)return i.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!r.startsWith("kxg_"))return i.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let{planId:s,billing:a,paymentMethod:n}=await e.json();if(!s||!a)return i.NextResponse.json({success:!1,error:"Plan ID and billing type are required"},{status:400});return i.NextResponse.json({success:!0,message:"Plan upgrade initiated successfully",data:{planId:s,billing:a,paymentMethod:n,status:"pending",estimatedActivation:new Date(Date.now()+36e5).toISOString()},timestamp:new Date().toISOString()})}catch(e){return console.error("Plan upgrade error:",e),i.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/user/plan/route",pathname:"/api/user/plan",filename:"route",bundlePath:"app/api/user/plan/route"},resolvedPagePath:"D:\\Users\\Downloads\\kodeXGuard\\app\\api\\user\\plan\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:p,staticGenerationAsyncStorage:d,serverHooks:m}=c,g="/api/user/plan/route";function S(){return(0,o.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:d})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[216,592],()=>r(56907));module.exports=s})();