(()=>{var e={};e.id=2251,e.ids=[2251],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},62644:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>h,originalPathname:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c}),t(29034),t(30829),t(35866);var a=t(23191),l=t(88716),r=t(37922),i=t.n(r),n=t(95231),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c=["",{children:["file-analyzer",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,29034)),"D:\\Users\\Downloads\\kodeXGuard\\app\\file-analyzer\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\Users\\Downloads\\kodeXGuard\\app\\file-analyzer\\page.tsx"],m="/file-analyzer/page",h={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/file-analyzer/page",pathname:"/file-analyzer",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3596:(e,s,t)=>{Promise.resolve().then(t.bind(t,66412))},66412:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var a=t(10326),l=t(17577),r=t(83061),i=t(2262),n=t(37202),d=t(12714),c=t(54659),o=t(6530),m=t(94244),h=t(76828),x=t(58038),p=t(76557);let y=(0,p.Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]),u=(0,p.Z)("File",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}]]);var g=t(98091),j=t(21405),b=t(3634);function v(){let[e]=(0,l.useState)({username:"admin",avatar:"",role:"super_admin",plan:"cybersecurity"}),[s,t]=(0,l.useState)(!1),[p,v]=(0,l.useState)([]),[f,N]=(0,l.useState)([]),[w,k]=(0,l.useState)(!1),[M,D]=(0,l.useState)({totalFiles:234,malwareDetected:15,secretsFound:42,webshellsFound:8}),Z=[".php",".js",".py",".txt",".zip",".apk",".exe",".pdf",".doc",".docx"],A={safe:"text-green-400 bg-green-900/20",suspicious:"text-yellow-400 bg-yellow-900/20",malicious:"text-red-400 bg-red-900/20"},P=(0,l.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?t(!0):"dragleave"===e.type&&t(!1)},[]),z=(0,l.useCallback)(e=>{if(e.preventDefault(),e.stopPropagation(),t(!1),e.dataTransfer.files&&e.dataTransfer.files[0]){let s=Array.from(e.dataTransfer.files);v(e=>[...e,...s])}},[]),S=e=>{v(s=>s.filter((s,t)=>t!==e))},F=async()=>{if(0!==p.length){for(let e of(k(!0),p)){let s={id:Math.random().toString(36).substr(2,9),filename:e.name,fileSize:e.size,mimeType:e.type||"application/octet-stream",hash:"sha256:"+Math.random().toString(36).substr(2,64),threatLevel:"safe",analysisType:"general",results:{},timestamp:new Date().toISOString(),status:"analyzing"};N(e=>[...e,s]),setTimeout(async()=>{let t=await T(e);N(e=>e.map(e=>e.id===s.id?{...e,...t,status:"completed"}:e))},3e3*Math.random()+2e3)}v([]),k(!1)}},T=async e=>{let s=e.name.split(".").pop()?.toLowerCase();return"php"===s?{threatLevel:Math.random()>.7?"suspicious":"safe",analysisType:"webshell",results:{webshellDetection:{detected:Math.random()>.6,type:Math.random()>.5?"PHP Webshell":"Backdoor",obfuscated:Math.random()>.5},secretDetection:{detected:Math.random()>.4,secrets:[{type:"API Key",value:"sk-1234567890abcdef",confidence:95},{type:"Database Password",value:"admin123",confidence:87}]}}}:"exe"===s||"apk"===s?{threatLevel:Math.random()>.8?"malicious":"safe",analysisType:"malware",results:{malwareDetection:{detected:Math.random()>.7,signatures:["Trojan.Generic","Backdoor.Agent"],family:"Generic Trojan"}}}:{threatLevel:"safe",analysisType:"general",results:{generalAnalysis:{entropy:8*Math.random(),suspiciousStrings:["eval","base64_decode","shell_exec"],fileStructure:{sections:3,imports:12}}}}},_=e=>{if(0===e)return"0 Bytes";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(2))+" "+["Bytes","KB","MB","GB"][s]},L=e=>{switch(e){case"malicious":return n.Z;case"suspicious":return d.Z;default:return c.Z}};return a.jsx(r.Z,{user:e,title:"File Analyzer",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[a.jsx(o.Z,{className:"h-8 w-8 text-cyber-green"}),(0,a.jsxs)("h1",{className:"text-3xl font-bold font-cyber text-white",children:["File ",a.jsx("span",{className:"cyber-text",children:"Analyzer"})]})]}),a.jsx("p",{className:"text-gray-400 max-w-3xl",children:"Deteksi webshell, ransomware, DDoS script, dan secret/token/API key dalam berbagai format file. Analisis mendalam dengan threat level assessment."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[a.jsx(i.Rm,{title:"Total Files Analyzed",value:M.totalFiles,icon:o.Z,color:"green",trend:{value:23,isPositive:!0}}),a.jsx(i.Rm,{title:"Malware Detected",value:M.malwareDetected,icon:m.Z,color:"red",trend:{value:-5,isPositive:!1}}),a.jsx(i.Rm,{title:"Secrets Found",value:M.secretsFound,icon:h.Z,color:"purple",trend:{value:12,isPositive:!0}}),a.jsx(i.Rm,{title:"Webshells Found",value:M.webshellsFound,icon:x.Z,color:"blue",trend:{value:3,isPositive:!0}})]}),(0,a.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2",children:[a.jsx(i.Zb,{border:"green",glow:!0,children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h2",{className:"text-xl font-bold text-white mb-6",children:"Upload Files for Analysis"}),(0,a.jsxs)("div",{className:`border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200 ${s?"border-cyber-green bg-cyber-green/10":"border-gray-600 hover:border-gray-500"}`,onDragEnter:P,onDragLeave:P,onDragOver:P,onDrop:z,children:[a.jsx(y,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"Drop files here or click to upload"}),(0,a.jsxs)("p",{className:"text-gray-400 mb-4",children:["Supported formats: ",Z.join(", ")]}),a.jsx("input",{type:"file",multiple:!0,onChange:e=>{if(e.target.files){let s=Array.from(e.target.files);v(e=>[...e,...s])}},className:"hidden",id:"file-upload",accept:Z.join(",")}),a.jsx("label",{htmlFor:"file-upload",className:"cyber-btn-primary cursor-pointer inline-block",children:"Select Files"})]}),p.length>0&&(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-white mb-4",children:["Files to Analyze (",p.length,")"]}),a.jsx("div",{className:"space-y-2",children:p.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-800/50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(u,{className:"h-5 w-5 text-gray-400"}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium text-white",children:e.name}),(0,a.jsxs)("div",{className:"text-sm text-gray-400",children:[_(e.size)," • ",e.type||"Unknown type"]})]})]}),a.jsx("button",{onClick:()=>S(s),className:"text-red-400 hover:text-red-300 transition-colors",children:a.jsx(g.Z,{className:"h-5 w-5"})})]},s))}),a.jsx("button",{onClick:F,disabled:w,className:"w-full mt-4 cyber-btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:w?(0,a.jsxs)(a.Fragment,{children:[a.jsx(j.Z,{className:"h-5 w-5 mr-2 animate-spin"}),"Analyzing..."]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(b.Z,{className:"h-5 w-5 mr-2"}),"Start Analysis"]})})]})]})}),f.length>0&&a.jsx(i.Zb,{className:"mt-6",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-white mb-6",children:["Analysis Results (",f.length,")"]}),a.jsx("div",{className:"space-y-4",children:f.map(e=>{let s=L(e.threatLevel);return(0,a.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-4 border border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(u,{className:"h-6 w-6 text-gray-400"}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-semibold text-white",children:e.filename}),(0,a.jsxs)("p",{className:"text-sm text-gray-400",children:[_(e.fileSize)," • ",e.mimeType]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:["analyzing"===e.status?a.jsx(j.Z,{className:"h-5 w-5 text-cyber-green animate-spin"}):a.jsx(s,{className:`h-5 w-5 ${A[e.threatLevel].split(" ")[0]}`}),a.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-semibold ${A[e.threatLevel]}`,children:e.threatLevel.toUpperCase()})]})]}),"completed"===e.status&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-sm text-gray-400",children:"File Hash:"}),a.jsx("code",{className:"text-xs text-cyber-green ml-2 bg-gray-900 px-2 py-1 rounded",children:e.hash})]}),e.results.malwareDetection&&(0,a.jsxs)("div",{className:"bg-gray-900/50 rounded p-3",children:[a.jsx("h5",{className:"font-semibold text-white mb-2",children:"Malware Detection"}),e.results.malwareDetection.detected?(0,a.jsxs)("div",{className:"text-red-400",children:[a.jsx("p",{children:"⚠️ Malware detected!"}),(0,a.jsxs)("p",{className:"text-sm",children:["Family: ",e.results.malwareDetection.family]}),(0,a.jsxs)("p",{className:"text-sm",children:["Signatures: ",e.results.malwareDetection.signatures.join(", ")]})]}):a.jsx("p",{className:"text-green-400",children:"✓ No malware detected"})]}),e.results.webshellDetection&&(0,a.jsxs)("div",{className:"bg-gray-900/50 rounded p-3",children:[a.jsx("h5",{className:"font-semibold text-white mb-2",children:"Webshell Detection"}),e.results.webshellDetection.detected?(0,a.jsxs)("div",{className:"text-yellow-400",children:[a.jsx("p",{children:"⚠️ Webshell detected!"}),(0,a.jsxs)("p",{className:"text-sm",children:["Type: ",e.results.webshellDetection.type]}),(0,a.jsxs)("p",{className:"text-sm",children:["Obfuscated: ",e.results.webshellDetection.obfuscated?"Yes":"No"]})]}):a.jsx("p",{className:"text-green-400",children:"✓ No webshell detected"})]}),e.results.secretDetection&&(0,a.jsxs)("div",{className:"bg-gray-900/50 rounded p-3",children:[a.jsx("h5",{className:"font-semibold text-white mb-2",children:"Secret Detection"}),e.results.secretDetection.detected?(0,a.jsxs)("div",{className:"text-purple-400",children:[a.jsx("p",{children:"⚠️ Secrets found!"}),e.results.secretDetection.secrets.map((e,s)=>(0,a.jsxs)("div",{className:"text-sm mt-1",children:[(0,a.jsxs)("span",{className:"font-medium",children:[e.type,":"]}),a.jsx("code",{className:"ml-2 bg-gray-800 px-1 rounded",children:e.value}),(0,a.jsxs)("span",{className:"ml-2 text-xs",children:["(",e.confidence,"% confidence)"]})]},s))]}):a.jsx("p",{className:"text-green-400",children:"✓ No secrets detected"})]}),e.results.generalAnalysis&&(0,a.jsxs)("div",{className:"bg-gray-900/50 rounded p-3",children:[a.jsx("h5",{className:"font-semibold text-white mb-2",children:"General Analysis"}),(0,a.jsxs)("div",{className:"text-sm text-gray-300 space-y-1",children:[(0,a.jsxs)("p",{children:["Entropy: ",e.results.generalAnalysis.entropy.toFixed(2)]}),(0,a.jsxs)("p",{children:["Suspicious strings: ",e.results.generalAnalysis.suspiciousStrings.join(", ")]})]})]})]}),"analyzing"===e.status&&(0,a.jsxs)("div",{className:"text-center py-4",children:[a.jsx(j.Z,{className:"h-6 w-6 text-cyber-green animate-spin mx-auto mb-2"}),a.jsx("p",{className:"text-gray-400",children:"Analyzing file..."})]})]},e.id)})})]})})]}),(0,a.jsxs)("div",{children:[a.jsx(i.Zb,{children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Analysis Types"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"p-3 bg-gray-800/50 rounded-lg",children:[a.jsx("div",{className:"font-medium text-white",children:"Malware Detection"}),a.jsx("div",{className:"text-sm text-gray-400",children:"Detect viruses, trojans, ransomware"})]}),(0,a.jsxs)("div",{className:"p-3 bg-gray-800/50 rounded-lg",children:[a.jsx("div",{className:"font-medium text-white",children:"Webshell Detection"}),a.jsx("div",{className:"text-sm text-gray-400",children:"Identify PHP, ASP, JSP webshells"})]}),(0,a.jsxs)("div",{className:"p-3 bg-gray-800/50 rounded-lg",children:[a.jsx("div",{className:"font-medium text-white",children:"Secret Detection"}),a.jsx("div",{className:"text-sm text-gray-400",children:"Find API keys, passwords, tokens"})]}),(0,a.jsxs)("div",{className:"p-3 bg-gray-800/50 rounded-lg",children:[a.jsx("div",{className:"font-medium text-white",children:"General Analysis"}),a.jsx("div",{className:"text-sm text-gray-400",children:"Entropy, structure, suspicious patterns"})]})]})]})}),a.jsx(i.Zb,{className:"mt-6",border:"gold",children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Plan Limits"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Daily Uploads"}),a.jsx("span",{className:"text-white",children:"Unlimited"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Max File Size"}),a.jsx("span",{className:"text-white",children:"1GB"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Concurrent Analysis"}),a.jsx("span",{className:"text-white",children:"10 files"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Deep Analysis"}),a.jsx("span",{className:"text-cyber-green",children:"✓ Enabled"})]})]})]})})]})]})]})})}},37202:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},94244:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Bug",[["path",{d:"m8 2 1.88 1.88",key:"fmnt4t"}],["path",{d:"M14.12 3.88 16 2",key:"qol33r"}],["path",{d:"M9 7.13v-1a3.003 3.003 0 1 1 6 0v1",key:"d7y7pr"}],["path",{d:"M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6",key:"xs1cw7"}],["path",{d:"M12 20v-9",key:"1qisl0"}],["path",{d:"M6.53 9C4.6 8.8 3 7.1 3 5",key:"32zzws"}],["path",{d:"M6 13H2",key:"82j7cp"}],["path",{d:"M3 21c0-2.1 1.7-3.9 3.8-4",key:"4p0ekp"}],["path",{d:"M20.97 5c0 2.1-1.6 3.8-3.5 4",key:"18gb23"}],["path",{d:"M22 13h-4",key:"1jl80f"}],["path",{d:"M17.2 17c2.1.1 3.8 1.9 3.8 4",key:"k3fwyw"}]])},54659:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},12714:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},6530:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("FileSearch",[["path",{d:"M4 22h14a2 2 0 0 0 2-2V7.5L14.5 2H6a2 2 0 0 0-2 2v3",key:"am10z3"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["path",{d:"M5 17a3 3 0 1 0 0-6 3 3 0 0 0 0 6z",key:"ychnub"}],["path",{d:"m9 18-1.5-1.5",key:"1j6qii"}]])},76828:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Key",[["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["path",{d:"m15.5 7.5 3 3L22 7l-3-3",key:"1rn1fs"}]])},21405:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},58038:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},98091:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},3634:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},29034:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\file-analyzer\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[9276,82,7608,3061],()=>t(62644));module.exports=a})();