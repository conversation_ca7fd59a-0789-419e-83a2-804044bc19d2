"use strict";(()=>{var e={};e.id=710,e.ids=[710],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},35645:(e,t,s)=>{s.r(t),s.d(t,{originalPathname:()=>h,patchFetch:()=>D,requestAsyncStorage:()=>p,routeModule:()=>l,serverHooks:()=>m,staticGenerationAsyncStorage:()=>d});var r={};s.r(r),s.d(r,{DELETE:()=>u,GET:()=>c});var a=s(49303),n=s(88716),i=s(60670),o=s(87070);async function c(e){try{let t=e.headers.get("authorization"),s=t?.replace("Bearer ","");if(!s)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!s.startsWith("kxg_"))return o.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let r=[{id:"search_001",query:"<EMAIL>",type:"email",results:5,confidence:92,timestamp:new Date(Date.now()-18e5).toISOString(),status:"completed",sources:["Dukcapil Database","Social Media","GitHub","Email Breach DB"]},{id:"search_002",query:"+628123456789",type:"phone",results:3,confidence:87,timestamp:new Date(Date.now()-36e5).toISOString(),status:"completed",sources:["Phone Number DB","Social Media","Location Services"]},{id:"search_003",query:"1234567890123456",type:"nik",results:2,confidence:95,timestamp:new Date(Date.now()-54e5).toISOString(),status:"completed",sources:["Dukcapil Database","Kemkes Database"]},{id:"search_004",query:"example.com",type:"domain",results:8,confidence:78,timestamp:new Date(Date.now()-72e5).toISOString(),status:"completed",sources:["Domain WHOIS","GitHub","Social Media","Email Breach DB"]},{id:"search_005",query:"Jane Smith",type:"name",results:12,confidence:65,timestamp:new Date(Date.now()-108e5).toISOString(),status:"partial",sources:["Social Media","GitHub","Location Services"]},{id:"search_006",query:"12.345.678.9-012.345",type:"npwp",results:1,confidence:98,timestamp:new Date(Date.now()-144e5).toISOString(),status:"completed",sources:["Dukcapil Database"]},{id:"search_007",query:"123456789012345",type:"imei",results:0,confidence:0,timestamp:new Date(Date.now()-18e6).toISOString(),status:"failed",sources:[]},{id:"search_008",query:"Jakarta Selatan",type:"address",results:25,confidence:45,timestamp:new Date(Date.now()-216e5).toISOString(),status:"partial",sources:["Location Services","Social Media","Dukcapil Database"]},{id:"search_009",query:"<EMAIL>",type:"email",results:7,confidence:89,timestamp:new Date(Date.now()-252e5).toISOString(),status:"completed",sources:["Email Breach DB","GitHub","Social Media"]},{id:"search_010",query:"+6281987654321",type:"phone",results:4,confidence:82,timestamp:new Date(Date.now()-288e5).toISOString(),status:"completed",sources:["Phone Number DB","Social Media","Location Services","Dukcapil Database"]}];r.sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime());let a=new URL(e.url),n=parseInt(a.searchParams.get("limit")||"10"),i=a.searchParams.get("type"),c=a.searchParams.get("status"),u=r;return i&&(u=u.filter(e=>e.type===i)),c&&(u=u.filter(e=>e.status===c)),u=u.slice(0,n),o.NextResponse.json({success:!0,data:u,meta:{total:r.length,filtered:u.length,completed:r.filter(e=>"completed"===e.status).length,failed:r.filter(e=>"failed"===e.status).length,partial:r.filter(e=>"partial"===e.status).length,averageConfidence:Math.round(r.filter(e=>"completed"===e.status).reduce((e,t)=>e+t.confidence,0)/r.filter(e=>"completed"===e.status).length),searchTypes:{email:r.filter(e=>"email"===e.type).length,phone:r.filter(e=>"phone"===e.type).length,nik:r.filter(e=>"nik"===e.type).length,npwp:r.filter(e=>"npwp"===e.type).length,name:r.filter(e=>"name"===e.type).length,domain:r.filter(e=>"domain"===e.type).length,imei:r.filter(e=>"imei"===e.type).length,address:r.filter(e=>"address"===e.type).length}},timestamp:new Date().toISOString()})}catch(e){return console.error("OSINT recent searches API error:",e),o.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}async function u(e){try{let t=e.headers.get("authorization"),s=t?.replace("Bearer ","");if(!s)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!s.startsWith("kxg_"))return o.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let{searchId:r}=await e.json();if(!r)return o.NextResponse.json({success:!1,error:"Search ID is required"},{status:400});return o.NextResponse.json({success:!0,message:`Search ${r} deleted successfully`,timestamp:new Date().toISOString()})}catch(e){return console.error("Delete search error:",e),o.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/osint/recent/route",pathname:"/api/osint/recent",filename:"route",bundlePath:"app/api/osint/recent/route"},resolvedPagePath:"D:\\Users\\Downloads\\kodeXGuard\\app\\api\\osint\\recent\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:p,staticGenerationAsyncStorage:d,serverHooks:m}=l,h="/api/osint/recent/route";function D(){return(0,i.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:d})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[9276,5972],()=>s(35645));module.exports=r})();