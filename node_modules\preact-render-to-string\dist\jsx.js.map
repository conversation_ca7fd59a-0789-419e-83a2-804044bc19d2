{"version": 3, "file": "jsx.js", "sources": ["jsx.js"], "sourcesContent": ["!function(t,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?e(exports,require(\"preact\")):\"function\"==typeof define&&define.amd?define([\"exports\",\"preact\"],e):e((t||self).preactRenderToString={},t.preact)}(this,function(t,e){if(\"function\"!=typeof Symbol){var n=0;Symbol=function(t){return\"@@\"+t+ ++n},Symbol.for=function(t){return\"@@\"+t}}var r=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|^--/i,o=/^(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/,i=/[\\s\\n\\\\/='\"\\0<>]/,a=/^xlink:?./,l=/[\"&<]/;function s(t){if(!1===l.test(t+=\"\"))return t;for(var e=0,n=0,r=\"\",o=\"\";n<t.length;n++){switch(t.charCodeAt(n)){case 34:o=\"&quot;\";break;case 38:o=\"&amp;\";break;case 60:o=\"&lt;\";break;default:continue}n!==e&&(r+=t.slice(e,n)),r+=o,e=n+1}return n!==e&&(r+=t.slice(e,n)),r}var u=function(t,e){return String(t).replace(/(\\n+)/g,\"$1\"+(e||\"\\t\"))},c=function(t,e,n){return String(t).length>(e||40)||!n&&-1!==String(t).indexOf(\"\\n\")||-1!==String(t).indexOf(\"<\")},f={},p=/([A-Z])/g;function d(t){var e=\"\";for(var n in t){var o=t[n];null!=o&&\"\"!==o&&(e&&(e+=\" \"),e+=\"-\"==n[0]?n:f[n]||(f[n]=n.replace(p,\"-$1\").toLowerCase()),e=\"number\"==typeof o&&!1===r.test(n)?e+\": \"+o+\"px;\":e+\": \"+o+\";\")}return e||void 0}function _(t,e){return Array.isArray(e)?e.reduce(_,t):null!=e&&!1!==e&&t.push(e),t}function y(){this.__d=!0}function g(t,e){return{__v:t,context:e,props:t.props,setState:y,forceUpdate:y,__d:!0,__h:[]}}function v(t,e){var n=t.contextType,r=n&&e[n.__c];return null!=n?r?r.props.value:n.__:e}var b=[];function m(t,n,r,l,f,p){if(null==t||\"boolean\"==typeof t)return\"\";if(\"object\"!=typeof t)return\"function\"==typeof t?\"\":s(t);var y=r.pretty,h=y&&\"string\"==typeof y?y:\"\\t\";if(Array.isArray(t)){for(var j=\"\",x=0;x<t.length;x++)y&&x>0&&(j+=\"\\n\"),j+=m(t[x],n,r,l,f,p);return j}if(void 0!==t.constructor)return\"\";var S,k=t.type,A=t.props,O=!1;if(\"function\"==typeof k){if(O=!0,!r.shallow||!l&&!1!==r.renderRootComponent){if(k===e.Fragment){var w=[];return _(w,t.props.children),m(w,n,r,!1!==r.shallowHighOrder,f,p)}var F,C=t.__c=g(t,n);e.options.__b&&e.options.__b(t);var E=e.options.__r;if(k.prototype&&\"function\"==typeof k.prototype.render){var M=v(k,n);(C=t.__c=new k(A,M)).__v=t,C._dirty=C.__d=!0,C.props=A,null==C.state&&(C.state={}),null==C._nextState&&null==C.__s&&(C._nextState=C.__s=C.state),C.context=M,k.getDerivedStateFromProps?C.state=Object.assign({},C.state,k.getDerivedStateFromProps(C.props,C.state)):C.componentWillMount&&(C.componentWillMount(),C.state=C._nextState!==C.state?C._nextState:C.__s!==C.state?C.__s:C.state),E&&E(t),F=C.render(C.props,C.state,C.context)}else for(var H=v(k,n),N=0;C.__d&&N++<25;)C.__d=!1,E&&E(t),F=k.call(t.__c,A,H);return C.getChildContext&&(n=Object.assign({},n,C.getChildContext())),e.options.diffed&&e.options.diffed(t),m(F,n,r,!1!==r.shallowHighOrder,f,p)}k=(S=k).displayName||S!==Function&&S.name||function(t){var e=(Function.prototype.toString.call(t).match(/^\\s*function\\s+([^( ]+)/)||\"\")[1];if(!e){for(var n=-1,r=b.length;r--;)if(b[r]===t){n=r;break}n<0&&(n=b.push(t)-1),e=\"UnnamedComponent\"+n}return e}(S)}var D,I,T=\"<\"+k;if(A){var L=Object.keys(A);r&&!0===r.sortAttributes&&L.sort();for(var W=0;W<L.length;W++){var $=L[W],P=A[$];if(\"children\"!==$){if(!i.test($)&&(r&&r.allAttributes||\"key\"!==$&&\"ref\"!==$&&\"__self\"!==$&&\"__source\"!==$)){if(\"defaultValue\"===$)$=\"value\";else if(\"defaultChecked\"===$)$=\"checked\";else if(\"defaultSelected\"===$)$=\"selected\";else if(\"className\"===$){if(void 0!==A.class)continue;$=\"class\"}else f&&a.test($)&&($=$.toLowerCase().replace(/^xlink:?/,\"xlink:\"));if(\"htmlFor\"===$){if(A.for)continue;$=\"for\"}\"style\"===$&&P&&\"object\"==typeof P&&(P=d(P)),\"a\"===$[0]&&\"r\"===$[1]&&\"boolean\"==typeof P&&(P=String(P));var U=r.attributeHook&&r.attributeHook($,P,n,r,O);if(U||\"\"===U)T+=U;else if(\"dangerouslySetInnerHTML\"===$)I=P&&P.__html;else if(\"textarea\"===k&&\"value\"===$)D=P;else if((P||0===P||\"\"===P)&&\"function\"!=typeof P){if(!(!0!==P&&\"\"!==P||(P=$,r&&r.xml))){T=T+\" \"+$;continue}if(\"value\"===$){if(\"select\"===k){p=P;continue}\"option\"===k&&p==P&&void 0===A.selected&&(T+=\" selected\")}T=T+\" \"+$+'=\"'+s(P)+'\"'}}}else D=P}}if(y){var R=T.replace(/\\n\\s*/,\" \");R===T||~R.indexOf(\"\\n\")?y&&~T.indexOf(\"\\n\")&&(T+=\"\\n\"):T=R}if(T+=\">\",i.test(k))throw new Error(k+\" is not a valid HTML tag name in \"+T);var J,V=o.test(k)||r.voidElements&&r.voidElements.test(k),q=[];if(I)y&&c(I)&&(I=\"\\n\"+h+u(I,h)),T+=I;else if(null!=D&&_(J=[],D).length){for(var z=y&&~T.indexOf(\"\\n\"),B=!1,G=0;G<J.length;G++){var Z=J[G];if(null!=Z&&!1!==Z){var K=m(Z,n,r,!0,\"svg\"===k||\"foreignObject\"!==k&&f,p);if(y&&!z&&c(K)&&(z=!0),K)if(y){var Q=K.length>0&&\"<\"!=K[0];B&&Q?q[q.length-1]+=K:q.push(K),B=Q}else q.push(K)}}if(y&&z)for(var X=q.length;X--;)q[X]=\"\\n\"+h+u(q[X],h)}if(q.length||I)T+=q.join(\"\");else if(r&&r.xml)return T.substring(0,T.length-1)+\" />\";return!V||J||I?(y&&~T.indexOf(\"\\n\")&&(T+=\"\\n\"),T=T+\"</\"+k+\">\"):T=T.replace(/>$/,\" />\"),T}var h={shallow:!0};x.render=x;var j=[];function x(t,n,r){n=n||{};var o=e.options.__s;e.options.__s=!0;var i,a=e.h(e.Fragment,null);return a.__k=[t],i=r&&(r.pretty||r.voidElements||r.sortAttributes||r.shallow||r.allAttributes||r.xml||r.attributeHook)?m(t,n,r):F(t,n,!1,void 0,a),e.options.__c&&e.options.__c(t,j),e.options.__s=o,j.length=0,i}function S(t){return null==t||\"boolean\"==typeof t?null:\"string\"==typeof t||\"number\"==typeof t||\"bigint\"==typeof t?e.h(null,null,t):t}function k(t,e){return\"className\"===t?\"class\":\"htmlFor\"===t?\"for\":\"defaultValue\"===t?\"value\":\"defaultChecked\"===t?\"checked\":\"defaultSelected\"===t?\"selected\":e&&a.test(t)?t.toLowerCase().replace(/^xlink:?/,\"xlink:\"):t}function A(t,e){return\"style\"===t&&null!=e&&\"object\"==typeof e?d(e):\"a\"===t[0]&&\"r\"===t[1]&&\"boolean\"==typeof e?String(e):e}var O=Array.isArray,w=Object.assign;function F(t,n,r,a,l){if(null==t||!0===t||!1===t||\"\"===t)return\"\";if(\"object\"!=typeof t)return\"function\"==typeof t?\"\":s(t);if(O(t)){var u=\"\";l.__k=t;for(var c=0;c<t.length;c++)u+=F(t[c],n,r,a,l),t[c]=S(t[c]);return u}if(void 0!==t.constructor)return\"\";t.__=l,e.options.__b&&e.options.__b(t);var f=t.type,p=t.props;if(\"function\"==typeof f){var d;if(f===e.Fragment)d=p.children;else{d=f.prototype&&\"function\"==typeof f.prototype.render?function(t,n){var r=t.type,o=v(r,n),i=new r(t.props,o);t.__c=i,i.__v=t,i.__d=!0,i.props=t.props,null==i.state&&(i.state={}),null==i.__s&&(i.__s=i.state),i.context=o,r.getDerivedStateFromProps?i.state=w({},i.state,r.getDerivedStateFromProps(i.props,i.state)):i.componentWillMount&&(i.componentWillMount(),i.state=i.__s!==i.state?i.__s:i.state);var a=e.options.__r;return a&&a(t),i.render(i.props,i.state,i.context)}(t,n):function(t,n){var r,o=g(t,n),i=v(t.type,n);t.__c=o;for(var a=e.options.__r,l=0;o.__d&&l++<25;)o.__d=!1,a&&a(t),r=t.type.call(o,t.props,i);return r}(t,n);var _=t.__c;_.getChildContext&&(n=w({},n,_.getChildContext()))}var y=F(d=null!=d&&d.type===e.Fragment&&null==d.key?d.props.children:d,n,r,a,t);return e.options.diffed&&e.options.diffed(t),t.__=void 0,e.options.unmount&&e.options.unmount(t),y}var b,m,h=\"<\";if(h+=f,p)for(var j in b=p.children,p){var x=p[j];if(!(\"key\"===j||\"ref\"===j||\"__self\"===j||\"__source\"===j||\"children\"===j||\"className\"===j&&\"class\"in p||\"htmlFor\"===j&&\"for\"in p||i.test(j)))if(x=A(j=k(j,r),x),\"dangerouslySetInnerHTML\"===j)m=x&&x.__html;else if(\"textarea\"===f&&\"value\"===j)b=x;else if((x||0===x||\"\"===x)&&\"function\"!=typeof x){if(!0===x||\"\"===x){x=j,h=h+\" \"+j;continue}if(\"value\"===j){if(\"select\"===f){a=x;continue}\"option\"!==f||a!=x||\"selected\"in p||(h+=\" selected\")}h=h+\" \"+j+'=\"'+s(x)+'\"'}}var C=h;if(h+=\">\",i.test(f))throw new Error(f+\" is not a valid HTML tag name in \"+h);var E=\"\",M=!1;if(m)E+=m,M=!0;else if(\"string\"==typeof b)E+=s(b),M=!0;else if(O(b)){t.__k=b;for(var H=0;H<b.length;H++){var N=b[H];if(b[H]=S(N),null!=N&&!1!==N){var D=F(N,n,\"svg\"===f||\"foreignObject\"!==f&&r,a,t);D&&(E+=D,M=!0)}}}else if(null!=b&&!1!==b&&!0!==b){t.__k=[S(b)];var I=F(b,n,\"svg\"===f||\"foreignObject\"!==f&&r,a,t);I&&(E+=I,M=!0)}if(e.options.diffed&&e.options.diffed(t),t.__=void 0,e.options.unmount&&e.options.unmount(t),M)h+=E;else if(o.test(f))return C+\" />\";return h+\"</\"+f+\">\"}x.shallowRender=function(t,e){return x(t,e,h)};const C=/(\\\\|\\\"|\\')/g,E=Object.prototype.toString,M=Date.prototype.toISOString,H=Error.prototype.toString,N=RegExp.prototype.toString,D=Symbol.prototype.toString,I=/^Symbol\\((.*)\\)(.*)$/,T=/\\n/gi,L=Object.getOwnPropertySymbols||(t=>[]);function W(t){return\"[object Array]\"===t||\"[object ArrayBuffer]\"===t||\"[object DataView]\"===t||\"[object Float32Array]\"===t||\"[object Float64Array]\"===t||\"[object Int8Array]\"===t||\"[object Int16Array]\"===t||\"[object Int32Array]\"===t||\"[object Uint8Array]\"===t||\"[object Uint8ClampedArray]\"===t||\"[object Uint16Array]\"===t||\"[object Uint32Array]\"===t}function $(t){return\"\"===t.name?\"[Function anonymous]\":\"[Function \"+t.name+\"]\"}function P(t){return D.call(t).replace(I,\"Symbol($1)\")}function U(t){return\"[\"+H.call(t)+\"]\"}function R(t){if(!0===t||!1===t)return\"\"+t;if(void 0===t)return\"undefined\";if(null===t)return\"null\";const e=typeof t;if(\"number\"===e)return function(t){return t!=+t?\"NaN\":0===t&&1/t<0?\"-0\":\"\"+t}(t);if(\"string\"===e)return'\"'+function(t){return t.replace(C,\"\\\\$1\")}(t)+'\"';if(\"function\"===e)return $(t);if(\"symbol\"===e)return P(t);const n=E.call(t);return\"[object WeakMap]\"===n?\"WeakMap {}\":\"[object WeakSet]\"===n?\"WeakSet {}\":\"[object Function]\"===n||\"[object GeneratorFunction]\"===n?$(t,min):\"[object Symbol]\"===n?P(t):\"[object Date]\"===n?M.call(t):\"[object Error]\"===n?U(t):\"[object RegExp]\"===n?N.call(t):\"[object Arguments]\"===n&&0===t.length?\"Arguments []\":W(n)&&0===t.length?t.constructor.name+\" []\":t instanceof Error&&U(t)}function J(t,e,n,r,o,i,a,l,s,u){let c=\"\";if(t.length){c+=o;const f=n+e;for(let n=0;n<t.length;n++)c+=f+z(t[n],e,f,r,o,i,a,l,s,u),n<t.length-1&&(c+=\",\"+r);c+=o+n}return\"[\"+c+\"]\"}function V(t,e,n,r,o,i,a,l,s,u){if((i=i.slice()).indexOf(t)>-1)return\"[Circular]\";i.push(t);const c=++l>a;if(!c&&t.toJSON&&\"function\"==typeof t.toJSON)return z(t.toJSON(),e,n,r,o,i,a,l,s,u);const f=E.call(t);return\"[object Arguments]\"===f?c?\"[Arguments]\":function(t,e,n,r,o,i,a,l,s,u){return(u?\"\":\"Arguments \")+J(t,e,n,r,o,i,a,l,s,u)}(t,e,n,r,o,i,a,l,s,u):W(f)?c?\"[Array]\":function(t,e,n,r,o,i,a,l,s,u){return(u?\"\":t.constructor.name+\" \")+J(t,e,n,r,o,i,a,l,s,u)}(t,e,n,r,o,i,a,l,s,u):\"[object Map]\"===f?c?\"[Map]\":function(t,e,n,r,o,i,a,l,s,u){let c=\"Map {\";const f=t.entries();let p=f.next();if(!p.done){c+=o;const t=n+e;for(;!p.done;)c+=t+z(p.value[0],e,t,r,o,i,a,l,s,u)+\" => \"+z(p.value[1],e,t,r,o,i,a,l,s,u),p=f.next(),p.done||(c+=\",\"+r);c+=o+n}return c+\"}\"}(t,e,n,r,o,i,a,l,s,u):\"[object Set]\"===f?c?\"[Set]\":function(t,e,n,r,o,i,a,l,s,u){let c=\"Set {\";const f=t.entries();let p=f.next();if(!p.done){c+=o;const t=n+e;for(;!p.done;)c+=t+z(p.value[1],e,t,r,o,i,a,l,s,u),p=f.next(),p.done||(c+=\",\"+r);c+=o+n}return c+\"}\"}(t,e,n,r,o,i,a,l,s,u):\"object\"==typeof t?c?\"[Object]\":function(t,e,n,r,o,i,a,l,s,u){let c=(u?\"\":t.constructor?t.constructor.name+\" \":\"Object \")+\"{\",f=Object.keys(t).sort();const p=L(t);if(p.length&&(f=f.filter(t=>!(\"symbol\"==typeof t||\"[object Symbol]\"===E.call(t))).concat(p)),f.length){c+=o;const p=n+e;for(let n=0;n<f.length;n++){const d=f[n];c+=p+z(d,e,p,r,o,i,a,l,s,u)+\": \"+z(t[d],e,p,r,o,i,a,l,s,u),n<f.length-1&&(c+=\",\"+r)}c+=o+n}return c+\"}\"}(t,e,n,r,o,i,a,l,s,u):void 0}function q(t,e,n,r,o,i,a,l,s,u){let c,f=!1;for(let e=0;e<s.length;e++)if(c=s[e],c.test(t)){f=!0;break}return!!f&&c.print(t,function(t){return z(t,e,n,r,o,i,a,l,s,u)},function(t){const r=n+e;return r+t.replace(T,\"\\n\"+r)},{edgeSpacing:o,spacing:r})}function z(t,e,n,r,o,i,a,l,s,u){return R(t)||q(t,e,n,r,o,i,a,l,s,u)||V(t,e,n,r,o,i,a,l,s,u)}const B={indent:2,min:!1,maxDepth:Infinity,plugins:[]};function G(t){return new Array(t+1).join(\" \")}var Z={test:function(t){return t&&\"object\"==typeof t&&\"type\"in t&&\"props\"in t&&\"key\"in t},print:function(t,e,n){return x(t,Z.context,Z.opts)}},K={plugins:[Z]},Q={attributeHook:function(t,e,n,r,o){var i=typeof e;if(\"dangerouslySetInnerHTML\"===t)return!1;if(null==e||\"function\"===i&&!r.functions)return\"\";if(r.skipFalseAttributes&&!o&&(!1===e||(\"class\"===t||\"style\"===t)&&\"\"===e))return\"\";var a=\"string\"==typeof r.pretty?r.pretty:\"\\t\";return\"string\"!==i?(\"function\"!==i||r.functionNames?(Z.context=n,Z.opts=r,~(e=function(t,e){let n,r;e?(function(t){if(Object.keys(t).forEach(t=>{if(!B.hasOwnProperty(t))throw new Error(\"prettyFormat: Invalid option: \"+t)}),t.min&&void 0!==t.indent&&0!==t.indent)throw new Error(\"prettyFormat: Cannot run with min option and indent\")}(e),e=function(t){const e={};return Object.keys(B).forEach(n=>e[n]=t.hasOwnProperty(n)?t[n]:B[n]),e.min&&(e.indent=0),e}(e)):e=B;const o=e.min?\" \":\"\\n\",i=e.min?\"\":\"\\n\";if(e&&e.plugins.length){n=G(e.indent),r=[];var a=q(t,n,\"\",o,i,r,e.maxDepth,0,e.plugins,e.min);if(a)return a}return R(t)||(n||(n=G(e.indent)),r||(r=[]),V(t,n,\"\",o,i,r,e.maxDepth,0,e.plugins,e.min))}(e,K)).indexOf(\"\\n\")&&(e=u(\"\\n\"+e,a)+\"\\n\")):e=\"Function\",u(\"\\n\"+t+\"={\"+e+\"}\",a)):\"\\n\"+a+t+'=\"'+s(e)+'\"'},jsx:!0,xml:!1,functions:!0,functionNames:!0,skipFalseAttributes:!0,pretty:\"  \"};function X(t,e,n,r){return x(t,e,n=Object.assign({},Q,n||{}))}t.default=X,t.render=X});\n//# sourceMappingURL=jsx.js.map\n"], "names": ["t", "Symbol", "n", "s", "c"], "mappings": "mEAAA,SAAsBA,GAAA,MAAXC,KAAAA,KAAuBC,GACjCD,WAEAA,SAASD,GAAUG,MAClB,KAAAH,OAAkBI,EAAAA"}