'use client'

import { useState } from 'react'
import DashboardLayout from '@/components/DashboardLayout'
import { Card, StatCard } from '@/components/Card'
import { 
  BookOpen, 
  Code, 
  Terminal,
  Globe,
  Key,
  Shield,
  Zap,
  Bot,
  FileText,
  Download,
  ExternalLink,
  Copy,
  Search,
  ChevronRight,
  Play,
  Settings,
  Users,
  Database
} from 'lucide-react'

interface DocSection {
  id: string
  title: string
  description: string
  icon: any
  content: string[]
}

export default function DocsPage() {
  const [user] = useState({
    username: 'admin',
    avatar: '',
    role: 'super_admin',
    plan: 'cybersecurity'
  })

  const [activeSection, setActiveSection] = useState('getting-started')
  const [searchQuery, setSearchQuery] = useState('')

  const docSections: DocSection[] = [
    {
      id: 'getting-started',
      title: 'Getting Started',
      description: 'Quick start guide untuk menggunakan KodeXGuard',
      icon: Play,
      content: [
        '# Getting Started with KodeXGuard',
        '',
        '## Welcome to KodeXGuard',
        'KodeXGuard adalah platform cybersecurity mandiri Indonesia yang menyediakan tools untuk OSINT investigation, vulnerability scanning, file analysis, dan bot automation.',
        '',
        '## Quick Start',
        '1. **Register Account**: Daftar akun baru di `/register`',
        '2. **Choose Plan**: Pilih plan yang sesuai kebutuhan',
        '3. **Get API Key**: Generate API key di halaman profile',
        '4. **Start Scanning**: Mulai gunakan tools yang tersedia',
        '',
        '## Demo Credentials',
        '```',
        'Email: <EMAIL>',
        'Password: admin123',
        '```',
        '',
        '## Available Tools',
        '- **OSINT Investigator**: Pencarian NIK, NPWP, email, domain',
        '- **Vulnerability Scanner**: Deteksi SQLi, XSS, LFI, RCE',
        '- **File Analyzer**: Analisis malware, webshell, secrets',
        '- **CVE Intelligence**: Database CVE terupdate',
        '- **Security Tools**: Hash, encode, payload generator',
        '- **Bot Center**: WhatsApp & Telegram automation'
      ]
    },
    {
      id: 'api-reference',
      title: 'API Reference',
      description: 'Dokumentasi lengkap API endpoints KodeXGuard',
      icon: Code,
      content: [
        '# API Reference',
        '',
        '## Base URL',
        '```',
        'https://api.kodexguard.com/v1',
        '```',
        '',
        '## Authentication',
        'Semua API endpoint memerlukan authentication menggunakan Bearer token:',
        '',
        '```bash',
        'curl -H "Authorization: Bearer YOUR_API_KEY" \\',
        '     https://api.kodexguard.com/v1/endpoint',
        '```',
        '',
        '## Rate Limiting',
        'Rate limit berdasarkan plan:',
        '- **Gratis**: 100 calls/day',
        '- **Pelajar**: 1,000 calls/day',
        '- **Hobby**: 5,000 calls/day',
        '- **Bug Hunter**: 10,000 calls/day',
        '- **Cybersecurity**: 100,000 calls/day',
        '',
        '## Response Format',
        'Semua response dalam format JSON:',
        '',
        '```json',
        '{',
        '  "success": true,',
        '  "data": {...},',
        '  "message": "Success",',
        '  "timestamp": "2024-01-15T10:30:00Z"',
        '}',
        '```',
        '',
        '## Error Handling',
        'Error response format:',
        '',
        '```json',
        '{',
        '  "success": false,',
        '  "error": "Error message",',
        '  "code": "ERROR_CODE",',
        '  "timestamp": "2024-01-15T10:30:00Z"',
        '}',
        '```'
      ]
    },
    {
      id: 'osint-api',
      title: 'OSINT API',
      description: 'API untuk OSINT investigation dan data gathering',
      icon: Search,
      content: [
        '# OSINT API',
        '',
        '## Search Endpoint',
        '**POST** `/api/osint/search`',
        '',
        '### Request Body',
        '```json',
        '{',
        '  "query": "<EMAIL>",',
        '  "type": "email",',
        '  "deepSearch": true,',
        '  "sources": ["dukcapil", "kemkes", "github"]',
        '}',
        '```',
        '',
        '### Parameters',
        '- `query` (string, required): Search query',
        '- `type` (string, required): Search type (email, phone, nik, npwp, domain, imei, name, address)',
        '- `deepSearch` (boolean, optional): Enable deep search',
        '- `sources` (array, optional): Specific data sources',
        '',
        '### Response',
        '```json',
        '{',
        '  "success": true,',
        '  "data": {',
        '    "results": [',
        '      {',
        '        "source": "Dukcapil Database",',
        '        "data": {',
        '          "name": "John Doe",',
        '          "email": "<EMAIL>",',
        '          "verified": true',
        '        },',
        '        "confidence": 95,',
        '        "timestamp": "2024-01-15T10:30:00Z"',
        '      }',
        '    ],',
        '    "totalResults": 1,',
        '    "searchTime": "1.2s"',
        '  }',
        '}',
        '```',
        '',
        '## Track Location',
        '**POST** `/api/osint/track`',
        '',
        'Track location berdasarkan nomor HP atau IMEI.',
        '',
        '### Example',
        '```bash',
        'curl -X POST https://api.kodexguard.com/v1/osint/track \\',
        '  -H "Authorization: Bearer YOUR_API_KEY" \\',
        '  -H "Content-Type: application/json" \\',
        '  -d \'{"target": "+62812345678", "type": "phone"}\'',
        '```'
      ]
    },
    {
      id: 'scanner-api',
      title: 'Scanner API',
      description: 'API untuk vulnerability scanning dan security testing',
      icon: Shield,
      content: [
        '# Vulnerability Scanner API',
        '',
        '## Start Scan',
        '**POST** `/api/scan/vulnerability`',
        '',
        '### Request Body',
        '```json',
        '{',
        '  "target": "https://example.com",',
        '  "scanTypes": ["sqli", "xss", "lfi", "rce"],',
        '  "maxDepth": 3,',
        '  "timeout": 30,',
        '  "followRedirects": true',
        '}',
        '```',
        '',
        '### Parameters',
        '- `target` (string, required): Target URL to scan',
        '- `scanTypes` (array, required): Vulnerability types to scan',
        '- `maxDepth` (number, optional): Maximum crawl depth (default: 3)',
        '- `timeout` (number, optional): Request timeout in seconds (default: 30)',
        '- `followRedirects` (boolean, optional): Follow HTTP redirects (default: true)',
        '',
        '### Supported Scan Types',
        '- `sqli`: SQL Injection',
        '- `xss`: Cross-Site Scripting',
        '- `lfi`: Local File Inclusion',
        '- `rfi`: Remote File Inclusion',
        '- `rce`: Remote Code Execution',
        '- `csrf`: Cross-Site Request Forgery',
        '- `ssrf`: Server-Side Request Forgery',
        '- `xxe`: XML External Entity',
        '- `idor`: Insecure Direct Object Reference',
        '- `path_traversal`: Path Traversal',
        '',
        '### Response',
        '```json',
        '{',
        '  "success": true,',
        '  "data": {',
        '    "scanId": "scan_123456789",',
        '    "status": "started",',
        '    "estimatedTime": "5-10 minutes",',
        '    "target": "https://example.com"',
        '  }',
        '}',
        '```',
        '',
        '## Get Scan Results',
        '**GET** `/api/scan/{scanId}/results`',
        '',
        '### Response',
        '```json',
        '{',
        '  "success": true,',
        '  "data": {',
        '    "scanId": "scan_123456789",',
        '    "status": "completed",',
        '    "target": "https://example.com",',
        '    "vulnerabilities": [',
        '      {',
        '        "type": "sqli",',
        '        "severity": "critical",',
        '        "cvssScore": 9.8,',
        '        "url": "https://example.com/login.php",',
        '        "parameter": "username",',
        '        "payload": "admin\' OR \'1\'=\'1",',
        '        "evidence": "MySQL error detected",',
        '        "recommendation": "Use parameterized queries"',
        '      }',
        '    ],',
        '    "summary": {',
        '      "total": 1,',
        '      "critical": 1,',
        '      "high": 0,',
        '      "medium": 0,',
        '      "low": 0',
        '    }',
        '  }',
        '}',
        '```'
      ]
    },
    {
      id: 'file-api',
      title: 'File Analysis API',
      description: 'API untuk analisis file dan deteksi malware',
      icon: FileText,
      content: [
        '# File Analysis API',
        '',
        '## Analyze File',
        '**POST** `/api/file/analyze`',
        '',
        'Upload dan analisis file untuk deteksi malware, webshell, dan secrets.',
        '',
        '### Request (multipart/form-data)',
        '```bash',
        'curl -X POST https://api.kodexguard.com/v1/file/analyze \\',
        '  -H "Authorization: Bearer YOUR_API_KEY" \\',
        '  -F "file=@suspicious.php" \\',
        '  -F "analysisType=webshell"',
        '```',
        '',
        '### Parameters',
        '- `file` (file, required): File to analyze',
        '- `analysisType` (string, optional): Analysis type (malware, webshell, secret, general)',
        '',
        '### Supported File Types',
        '- **Scripts**: .php, .js, .py, .asp, .jsp',
        '- **Documents**: .txt, .pdf, .doc, .docx',
        '- **Archives**: .zip, .rar, .tar, .gz',
        '- **Executables**: .exe, .dll, .apk',
        '',
        '### Response',
        '```json',
        '{',
        '  "success": true,',
        '  "data": {',
        '    "analysisId": "analysis_789012345",',
        '    "filename": "suspicious.php",',
        '    "fileSize": 2048,',
        '    "mimeType": "text/x-php",',
        '    "hash": "sha256:abc123...",',
        '    "threatLevel": "suspicious",',
        '    "results": {',
        '      "webshellDetection": {',
        '        "detected": true,',
        '        "type": "PHP Webshell",',
        '        "obfuscated": false,',
        '        "confidence": 95',
        '      },',
        '      "secretDetection": {',
        '        "detected": true,',
        '        "secrets": [',
        '          {',
        '            "type": "API Key",',
        '            "value": "sk-1234567890abcdef",',
        '            "confidence": 87',
        '          }',
        '        ]',
        '      }',
        '    }',
        '  }',
        '}',
        '```',
        '',
        '## Get Analysis Results',
        '**GET** `/api/file/analysis/{analysisId}`',
        '',
        'Retrieve detailed analysis results by analysis ID.'
      ]
    },
    {
      id: 'cve-api',
      title: 'CVE API',
      description: 'API untuk CVE intelligence dan vulnerability database',
      icon: Database,
      content: [
        '# CVE Intelligence API',
        '',
        '## Search CVE Database',
        '**GET** `/api/cve/search`',
        '',
        '### Query Parameters',
        '- `query` (string, optional): Search query',
        '- `severity` (string, optional): Filter by severity (critical, high, medium, low)',
        '- `year` (string, optional): Filter by year (2024, 2023, etc.)',
        '- `limit` (number, optional): Number of results (default: 20, max: 100)',
        '- `offset` (number, optional): Pagination offset (default: 0)',
        '',
        '### Example',
        '```bash',
        'curl "https://api.kodexguard.com/v1/cve/search?query=SQL%20injection&severity=critical&limit=10" \\',
        '  -H "Authorization: Bearer YOUR_API_KEY"',
        '```',
        '',
        '### Response',
        '```json',
        '{',
        '  "success": true,',
        '  "data": {',
        '    "total": 156,',
        '    "cves": [',
        '      {',
        '        "cveId": "CVE-2024-0001",',
        '        "description": "SQL injection vulnerability...",',
        '        "severity": "critical",',
        '        "cvssScore": 9.8,',
        '        "cvssVector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H",',
        '        "publishedDate": "2024-01-15",',
        '        "modifiedDate": "2024-01-16",',
        '        "affectedProducts": ["WebApp 1.0", "WebApp 2.0"],',
        '        "references": ["https://nvd.nist.gov/vuln/detail/CVE-2024-0001"],',
        '        "exploits": [',
        '          {',
        '            "title": "SQL Injection Exploit",',
        '            "url": "https://exploit-db.com/exploits/12345",',
        '            "type": "Public Exploit"',
        '          }',
        '        ]',
        '      }',
        '    ]',
        '  }',
        '}',
        '```',
        '',
        '## Get CVE Details',
        '**GET** `/api/cve/{cveId}`',
        '',
        'Get detailed information about specific CVE.',
        '',
        '### Example',
        '```bash',
        'curl https://api.kodexguard.com/v1/cve/CVE-2024-0001 \\',
        '  -H "Authorization: Bearer YOUR_API_KEY"',
        '```'
      ]
    },
    {
      id: 'bot-integration',
      title: 'Bot Integration',
      description: 'Integrasi WhatsApp dan Telegram bot untuk automation',
      icon: Bot,
      content: [
        '# Bot Integration Guide',
        '',
        '## WhatsApp Bot Setup',
        '',
        '### Prerequisites',
        '- Plan Bug Hunter atau Cybersecurity',
        '- Super Admin access untuk setup',
        '- WhatsApp account untuk bot',
        '',
        '### Setup Steps',
        '1. **Login sebagai Super Admin**',
        '2. **Buka Bot Center** (`/bot`)',
        '3. **Add New Bot** dengan type WhatsApp',
        '4. **Scan QR Code** dengan WhatsApp',
        '5. **Bot Ready** untuk menerima commands',
        '',
        '### Available Commands',
        '```',
        '/scan <url>     - Start vulnerability scan',
        '/status         - Check plan status',
        '/help           - Show available commands',
        '/results <id>   - Get scan results',
        '```',
        '',
        '### Example Usage',
        '```',
        'User: /scan https://example.com',
        'Bot:  Starting vulnerability scan for https://example.com...',
        '      Scan ID: scan_123456789',
        '      Estimated time: 5-10 minutes',
        '',
        'User: /results scan_123456789',
        'Bot:  Scan Results:',
        '      ✅ Completed',
        '      🔴 1 Critical vulnerability found',
        '      📊 CVSS Score: 9.8',
        '      📄 Full report: https://kodexguard.com/scan/123456789',
        '```',
        '',
        '## Telegram Bot Setup',
        '',
        '### Setup Steps',
        '1. **Create Bot** dengan @BotFather',
        '2. **Get Bot Token**',
        '3. **Add Bot** di KodeXGuard Bot Center',
        '4. **Configure Webhook** (otomatis)',
        '',
        '### Bot Configuration',
        '```json',
        '{',
        '  "botToken": "YOUR_TELEGRAM_BOT_TOKEN",',
        '  "webhookUrl": "https://api.kodexguard.com/webhook/telegram",',
        '  "allowedUsers": ["@username1", "@username2"],',
        '  "commands": [',
        '    {"command": "scan", "description": "Start vulnerability scan"},',
        '    {"command": "status", "description": "Check plan status"},',
        '    {"command": "help", "description": "Show help"}',
        '  ]',
        '}',
        '```',
        '',
        '## Bot API Integration',
        '',
        '### Send Message via API',
        '**POST** `/api/bot/send`',
        '',
        '```json',
        '{',
        '  "botId": "bot_123",',
        '  "recipient": "+62812345678",',
        '  "message": "Scan completed! Check results at: https://..."',
        '}',
        '```',
        '',
        '### Get Bot Status',
        '**GET** `/api/bot/status`',
        '',
        'Returns status of all configured bots.'
      ]
    },
    {
      id: 'sdk-examples',
      title: 'SDK & Examples',
      description: 'Code examples dan SDK untuk berbagai bahasa pemrograman',
      icon: Terminal,
      content: [
        '# SDK & Code Examples',
        '',
        '## JavaScript/Node.js',
        '',
        '### Installation',
        '```bash',
        'npm install kodexguard-sdk',
        '```',
        '',
        '### Basic Usage',
        '```javascript',
        'const KodeXGuard = require(\'kodexguard-sdk\');',
        '',
        'const client = new KodeXGuard({',
        '  apiKey: \'your-api-key\',',
        '  baseUrl: \'https://api.kodexguard.com/v1\'',
        '});',
        '',
        '// OSINT Search',
        'const osintResult = await client.osint.search({',
        '  query: \'<EMAIL>\',',
        '  type: \'email\',',
        '  deepSearch: true',
        '});',
        '',
        '// Vulnerability Scan',
        'const scan = await client.scanner.start({',
        '  target: \'https://example.com\',',
        '  scanTypes: [\'sqli\', \'xss\', \'lfi\']',
        '});',
        '',
        '// Get Results',
        'const results = await client.scanner.getResults(scan.scanId);',
        '```',
        '',
        '## Python',
        '',
        '### Installation',
        '```bash',
        'pip install kodexguard-python',
        '```',
        '',
        '### Basic Usage',
        '```python',
        'from kodexguard import KodeXGuardClient',
        '',
        'client = KodeXGuardClient(',
        '    api_key="your-api-key",',
        '    base_url="https://api.kodexguard.com/v1"',
        ')',
        '',
        '# OSINT Search',
        'osint_result = client.osint.search(',
        '    query="<EMAIL>",',
        '    type="email",',
        '    deep_search=True',
        ')',
        '',
        '# Vulnerability Scan',
        'scan = client.scanner.start(',
        '    target="https://example.com",',
        '    scan_types=["sqli", "xss", "lfi"]',
        ')',
        '',
        '# Get Results',
        'results = client.scanner.get_results(scan["scanId"])',
        '```',
        '',
        '## PHP',
        '',
        '### Installation',
        '```bash',
        'composer require kodexguard/php-sdk',
        '```',
        '',
        '### Basic Usage',
        '```php',
        '<?php',
        'require_once \'vendor/autoload.php\';',
        '',
        'use KodeXGuard\\Client;',
        '',
        '$client = new Client([',
        '    \'api_key\' => \'your-api-key\',',
        '    \'base_url\' => \'https://api.kodexguard.com/v1\'',
        ']);',
        '',
        '// OSINT Search',
        '$osintResult = $client->osint()->search([',
        '    \'query\' => \'<EMAIL>\',',
        '    \'type\' => \'email\',',
        '    \'deepSearch\' => true',
        ']);',
        '',
        '// Vulnerability Scan',
        '$scan = $client->scanner()->start([',
        '    \'target\' => \'https://example.com\',',
        '    \'scanTypes\' => [\'sqli\', \'xss\', \'lfi\']',
        ']);',
        '?>',
        '```',
        '',
        '## cURL Examples',
        '',
        '### OSINT Search',
        '```bash',
        'curl -X POST https://api.kodexguard.com/v1/osint/search \\',
        '  -H "Authorization: Bearer YOUR_API_KEY" \\',
        '  -H "Content-Type: application/json" \\',
        '  -d \'{"query": "<EMAIL>", "type": "email"}\'',
        '```',
        '',
        '### Start Vulnerability Scan',
        '```bash',
        'curl -X POST https://api.kodexguard.com/v1/scan/vulnerability \\',
        '  -H "Authorization: Bearer YOUR_API_KEY" \\',
        '  -H "Content-Type: application/json" \\',
        '  -d \'{"target": "https://example.com", "scanTypes": ["sqli", "xss"]}\'',
        '```'
      ]
    }
  ]

  const filteredSections = docSections.filter(section =>
    section.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    section.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    section.content.some(line => line.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  const renderContent = (content: string[]) => {
    return content.map((line, index) => {
      if (line.startsWith('# ')) {
        return <h1 key={index} className="text-2xl font-bold text-white mb-4">{line.substring(2)}</h1>
      } else if (line.startsWith('## ')) {
        return <h2 key={index} className="text-xl font-semibold text-white mb-3 mt-6">{line.substring(3)}</h2>
      } else if (line.startsWith('### ')) {
        return <h3 key={index} className="text-lg font-semibold text-cyber-green mb-2 mt-4">{line.substring(4)}</h3>
      } else if (line.startsWith('```')) {
        const nextIndex = content.findIndex((l, i) => i > index && l.startsWith('```'))
        if (nextIndex > index) {
          const codeLines = content.slice(index + 1, nextIndex)
          const code = codeLines.join('\n')
          return (
            <div key={index} className="relative bg-gray-900/50 rounded-lg p-4 mb-4">
              <button
                onClick={() => copyToClipboard(code)}
                className="absolute top-2 right-2 text-gray-400 hover:text-white transition-colors"
              >
                <Copy className="h-4 w-4" />
              </button>
              <pre className="text-sm text-cyber-green overflow-x-auto">
                <code>{code}</code>
              </pre>
            </div>
          )
        }
        return null
      } else if (line.startsWith('- ')) {
        return <li key={index} className="text-gray-300 mb-1 ml-4">{line.substring(2)}</li>
      } else if (line === '') {
        return <br key={index} />
      } else {
        return <p key={index} className="text-gray-300 mb-2">{line}</p>
      }
    }).filter(Boolean)
  }

  return (
    <DashboardLayout user={user} title="Documentation">
      <div>
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <BookOpen className="h-8 w-8 text-cyber-green" />
              <h1 className="text-3xl font-bold font-cyber text-white">
                <span className="cyber-text">Documentation</span>
              </h1>
            </div>
            <p className="text-gray-400 max-w-3xl">
              Dokumentasi lengkap KodeXGuard API, SDK, dan panduan penggunaan untuk developer dan security researcher.
            </p>
          </div>

          {/* Search */}
          <div className="mb-8">
            <div className="relative max-w-md">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search documentation..."
                className="cyber-input pl-10 w-full"
              />
            </div>
          </div>

          <div className="grid lg:grid-cols-4 gap-8">
            {/* Sidebar */}
            <div>
              <Card>
                <div className="p-6">
                  <h3 className="text-lg font-bold text-white mb-4">Documentation</h3>
                  <nav className="space-y-2">
                    {filteredSections.map((section) => {
                      const Icon = section.icon
                      return (
                        <button
                          key={section.id}
                          onClick={() => setActiveSection(section.id)}
                          className={`w-full text-left flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 ${
                            activeSection === section.id
                              ? 'bg-cyber-green/10 text-cyber-green border border-cyber-green'
                              : 'text-gray-300 hover:text-white hover:bg-gray-800/50'
                          }`}
                        >
                          <Icon className="h-4 w-4 flex-shrink-0" />
                          <div className="flex-1">
                            <div className="font-medium">{section.title}</div>
                            <div className="text-xs text-gray-400">{section.description}</div>
                          </div>
                          <ChevronRight className="h-4 w-4" />
                        </button>
                      )
                    })}
                  </nav>
                </div>
              </Card>
            </div>

            {/* Content */}
            <div className="lg:col-span-3">
              <Card>
                <div className="p-8">
                  {(() => {
                    const section = docSections.find(s => s.id === activeSection)
                    if (!section) return null
                    
                    return (
                      <div className="prose prose-invert max-w-none">
                        {renderContent(section.content)}
                      </div>
                    )
                  })()}
                </div>
              </Card>
            </div>
          </div>
      </div>
    </DashboardLayout>
  )
}
