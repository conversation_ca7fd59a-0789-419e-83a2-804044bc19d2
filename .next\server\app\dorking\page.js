/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dorking/page";
exports.ids = ["app/dorking/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdorking%2Fpage&page=%2Fdorking%2Fpage&appPaths=%2Fdorking%2Fpage&pagePath=private-next-app-dir%2Fdorking%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdorking%2Fpage&page=%2Fdorking%2Fpage&appPaths=%2Fdorking%2Fpage&pagePath=private-next-app-dir%2Fdorking%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dorking',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dorking/page.tsx */ \"(rsc)/./app/dorking/page.tsx\")), \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dorking/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dorking/page\",\n        pathname: \"/dorking\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdorking%2Fpage&page=%2Fdorking%2Fpage&appPaths=%2Fdorking%2Fpage&pagePath=private-next-app-dir%2Fdorking%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Cdorking%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Cdorking%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dorking/page.tsx */ \"(ssr)/./app/dorking/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNVc2VycyU1QyU1Q0Rvd25sb2FkcyU1QyU1Q2tvZGVYR3VhcmQlNUMlNUNhcHAlNUMlNUNkb3JraW5nJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdKQUE2RiIsInNvdXJjZXMiOlsid2VicGFjazovL2tvZGV4Z3VhcmQvP2Q0YjMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxVc2Vyc1xcXFxEb3dubG9hZHNcXFxca29kZVhHdWFyZFxcXFxhcHBcXFxcZG9ya2luZ1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Cdorking%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNVc2VycyU1QyU1Q0Rvd25sb2FkcyU1QyU1Q2tvZGVYR3VhcmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNhcHAtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNVc2VycyU1QyU1Q0Rvd25sb2FkcyU1QyU1Q2tvZGVYR3VhcmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDVXNlcnMlNUMlNUNEb3dubG9hZHMlNUMlNUNrb2RlWEd1YXJkJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1VzZXJzJTVDJTVDRG93bmxvYWRzJTVDJTVDa29kZVhHdWFyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2xheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1VzZXJzJTVDJTVDRG93bmxvYWRzJTVDJTVDa29kZVhHdWFyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q25vdC1mb3VuZC1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDVXNlcnMlNUMlNUNEb3dubG9hZHMlNUMlNUNrb2RlWEd1YXJkJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQWtJO0FBQ2xJO0FBQ0Esb09BQW1JO0FBQ25JO0FBQ0EsME9BQXNJO0FBQ3RJO0FBQ0Esd09BQXFJO0FBQ3JJO0FBQ0Esa1BBQTBJO0FBQzFJO0FBQ0Esc1FBQW9KIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va29kZXhndWFyZC8/ODk4YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFVzZXJzXFxcXERvd25sb2Fkc1xcXFxrb2RlWEd1YXJkXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcYXBwLXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcVXNlcnNcXFxcRG93bmxvYWRzXFxcXGtvZGVYR3VhcmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcVXNlcnNcXFxcRG93bmxvYWRzXFxcXGtvZGVYR3VhcmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcVXNlcnNcXFxcRG93bmxvYWRzXFxcXGtvZGVYR3VhcmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxVc2Vyc1xcXFxEb3dubG9hZHNcXFxca29kZVhHdWFyZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG5vdC1mb3VuZC1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcVXNlcnNcXFxcRG93bmxvYWRzXFxcXGtvZGVYR3VhcmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/dorking/page.tsx":
/*!******************************!*\
  !*** ./app/dorking/page.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DorkingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navbar */ \"(ssr)/./components/Navbar.tsx\");\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Card */ \"(ssr)/./components/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Bookmark,Calendar,CheckCircle,Copy,ExternalLink,Eye,Play,RefreshCw,Save,Search,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Bookmark,Calendar,CheckCircle,Copy,ExternalLink,Eye,Play,RefreshCw,Save,Search,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Bookmark,Calendar,CheckCircle,Copy,ExternalLink,Eye,Play,RefreshCw,Save,Search,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bookmark.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Bookmark,Calendar,CheckCircle,Copy,ExternalLink,Eye,Play,RefreshCw,Save,Search,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Bookmark,Calendar,CheckCircle,Copy,ExternalLink,Eye,Play,RefreshCw,Save,Search,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Bookmark,Calendar,CheckCircle,Copy,ExternalLink,Eye,Play,RefreshCw,Save,Search,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Bookmark,Calendar,CheckCircle,Copy,ExternalLink,Eye,Play,RefreshCw,Save,Search,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Bookmark,Calendar,CheckCircle,Copy,ExternalLink,Eye,Play,RefreshCw,Save,Search,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Bookmark,Calendar,CheckCircle,Copy,ExternalLink,Eye,Play,RefreshCw,Save,Search,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Bookmark,Calendar,CheckCircle,Copy,ExternalLink,Eye,Play,RefreshCw,Save,Search,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Bookmark,Calendar,CheckCircle,Copy,ExternalLink,Eye,Play,RefreshCw,Save,Search,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Bookmark,Calendar,CheckCircle,Copy,ExternalLink,Eye,Play,RefreshCw,Save,Search,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Bookmark,Calendar,CheckCircle,Copy,ExternalLink,Eye,Play,RefreshCw,Save,Search,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Bookmark,Calendar,CheckCircle,Copy,ExternalLink,Eye,Play,RefreshCw,Save,Search,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Bookmark,Calendar,CheckCircle,Copy,ExternalLink,Eye,Play,RefreshCw,Save,Search,Shield,Star,Target,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DorkingPage() {\n    const [user] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: \"admin\",\n        avatar: \"\",\n        role: \"super_admin\",\n        plan: \"cybersecurity\"\n    });\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"dorking\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [customQuery, setCustomQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [savedQueries, setSavedQueries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const dorkPresets = [\n        {\n            id: \"1\",\n            name: \"Admin Login Pages\",\n            category: \"Authentication\",\n            description: \"Find admin login pages and panels\",\n            query: \"inurl:admin inurl:login\",\n            risk: \"medium\",\n            tags: [\n                \"admin\",\n                \"login\",\n                \"panel\"\n            ],\n            usage: 1247\n        },\n        {\n            id: \"2\",\n            name: \"Database Files\",\n            category: \"Database\",\n            description: \"Exposed database files and backups\",\n            query: 'filetype:sql \"INSERT INTO\" site:*.com',\n            risk: \"critical\",\n            tags: [\n                \"database\",\n                \"sql\",\n                \"backup\"\n            ],\n            usage: 892\n        },\n        {\n            id: \"3\",\n            name: \"Config Files\",\n            category: \"Configuration\",\n            description: \"Configuration files with sensitive data\",\n            query: 'filetype:conf OR filetype:config \"password\"',\n            risk: \"high\",\n            tags: [\n                \"config\",\n                \"password\",\n                \"sensitive\"\n            ],\n            usage: 756\n        },\n        {\n            id: \"4\",\n            name: \"Directory Listing\",\n            category: \"Directory\",\n            description: \"Open directory listings\",\n            query: 'intitle:\"Index of /\" \"Parent Directory\"',\n            risk: \"medium\",\n            tags: [\n                \"directory\",\n                \"listing\",\n                \"open\"\n            ],\n            usage: 634\n        },\n        {\n            id: \"5\",\n            name: \"Log Files\",\n            category: \"Logs\",\n            description: \"Exposed log files with sensitive information\",\n            query: 'filetype:log \"password\" OR \"username\"',\n            risk: \"high\",\n            tags: [\n                \"logs\",\n                \"password\",\n                \"username\"\n            ],\n            usage: 523\n        },\n        {\n            id: \"6\",\n            name: \"PHP Info Pages\",\n            category: \"Information\",\n            description: \"PHP info pages revealing server information\",\n            query: 'inurl:phpinfo.php \"PHP Version\"',\n            risk: \"medium\",\n            tags: [\n                \"php\",\n                \"info\",\n                \"server\"\n            ],\n            usage: 445\n        },\n        {\n            id: \"7\",\n            name: \"Backup Files\",\n            category: \"Backup\",\n            description: \"Database and website backup files\",\n            query: 'filetype:bak OR filetype:backup \"database\"',\n            risk: \"critical\",\n            tags: [\n                \"backup\",\n                \"database\",\n                \"files\"\n            ],\n            usage: 389\n        },\n        {\n            id: \"8\",\n            name: \"Error Messages\",\n            category: \"Errors\",\n            description: \"Error messages revealing system information\",\n            query: '\"Warning: mysql_connect()\" \"Access denied\"',\n            risk: \"low\",\n            tags: [\n                \"error\",\n                \"mysql\",\n                \"warning\"\n            ],\n            usage: 312\n        }\n    ];\n    const cveDaily = [\n        {\n            cveId: \"CVE-2024-0001\",\n            title: \"Critical SQL Injection in WebApp Framework\",\n            severity: \"critical\",\n            cvssScore: 9.8,\n            publishedDate: \"2024-01-15\",\n            affectedProducts: [\n                \"WebApp Framework 2.x\",\n                \"WebApp Framework 3.x\"\n            ],\n            description: \"A critical SQL injection vulnerability allows remote attackers to execute arbitrary SQL commands.\",\n            trending: true\n        },\n        {\n            cveId: \"CVE-2024-0002\",\n            title: \"Remote Code Execution in CMS Platform\",\n            severity: \"critical\",\n            cvssScore: 9.5,\n            publishedDate: \"2024-01-14\",\n            affectedProducts: [\n                \"CMS Platform 1.0-4.2\"\n            ],\n            description: \"Unauthenticated remote code execution via file upload functionality.\",\n            trending: true\n        },\n        {\n            cveId: \"CVE-2024-0003\",\n            title: \"Cross-Site Scripting in E-commerce Plugin\",\n            severity: \"high\",\n            cvssScore: 7.2,\n            publishedDate: \"2024-01-13\",\n            affectedProducts: [\n                \"E-commerce Plugin 2.1\",\n                \"E-commerce Plugin 2.2\"\n            ],\n            description: \"Stored XSS vulnerability in product review functionality.\",\n            trending: false\n        },\n        {\n            cveId: \"CVE-2024-0004\",\n            title: \"Authentication Bypass in API Gateway\",\n            severity: \"high\",\n            cvssScore: 8.1,\n            publishedDate: \"2024-01-12\",\n            affectedProducts: [\n                \"API Gateway 1.x\"\n            ],\n            description: \"Authentication bypass allows unauthorized access to protected endpoints.\",\n            trending: false\n        },\n        {\n            cveId: \"CVE-2024-0005\",\n            title: \"Path Traversal in File Manager\",\n            severity: \"medium\",\n            cvssScore: 6.5,\n            publishedDate: \"2024-01-11\",\n            affectedProducts: [\n                \"File Manager Pro 3.0\"\n            ],\n            description: \"Directory traversal vulnerability allows access to arbitrary files.\",\n            trending: false\n        }\n    ];\n    const categories = [\n        ...new Set(dorkPresets.map((preset)=>preset.category))\n    ];\n    const filteredPresets = selectedCategory === \"all\" ? dorkPresets : dorkPresets.filter((preset)=>preset.category === selectedCategory);\n    const executeSearch = async (query)=>{\n        setLoading(true);\n        setSearchResults([]);\n        try {\n            // Simulate search\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            const mockResults = [\n                {\n                    id: \"1\",\n                    title: \"Admin Login Panel - Company XYZ\",\n                    url: \"https://example.com/admin/login.php\",\n                    snippet: \"Admin login panel for Company XYZ management system. Please enter your credentials to access the admin area.\",\n                    domain: \"example.com\",\n                    risk: \"medium\",\n                    timestamp: \"2 hours ago\"\n                },\n                {\n                    id: \"2\",\n                    title: \"Database Backup File - backup.sql\",\n                    url: \"https://test-site.com/backup/backup.sql\",\n                    snippet: \"Database backup file containing user credentials and sensitive information. File size: 2.3MB\",\n                    domain: \"test-site.com\",\n                    risk: \"critical\",\n                    timestamp: \"5 hours ago\"\n                },\n                {\n                    id: \"3\",\n                    title: \"Configuration File - config.php\",\n                    url: \"https://demo.org/config/config.php\",\n                    snippet: \"Configuration file with database credentials and API keys exposed.\",\n                    domain: \"demo.org\",\n                    risk: \"high\",\n                    timestamp: \"1 day ago\"\n                }\n            ];\n            setSearchResults(mockResults);\n        } catch (error) {\n            console.error(\"Search error:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const saveQuery = (query)=>{\n        if (!savedQueries.includes(query)) {\n            setSavedQueries((prev)=>[\n                    ...prev,\n                    query\n                ]);\n        }\n    };\n    const copyQuery = (query)=>{\n        navigator.clipboard.writeText(query);\n    };\n    const getRiskColor = (risk)=>{\n        switch(risk){\n            case \"critical\":\n                return \"text-red-400 bg-red-900/20\";\n            case \"high\":\n                return \"text-orange-400 bg-orange-900/20\";\n            case \"medium\":\n                return \"text-yellow-400 bg-yellow-900/20\";\n            case \"low\":\n                return \"text-blue-400 bg-blue-900/20\";\n            default:\n                return \"text-gray-400 bg-gray-900/20\";\n        }\n    };\n    const getSeverityColor = (severity)=>{\n        switch(severity){\n            case \"critical\":\n                return \"text-red-400 bg-red-900/20\";\n            case \"high\":\n                return \"text-orange-400 bg-orange-900/20\";\n            case \"medium\":\n                return \"text-yellow-400 bg-yellow-900/20\";\n            case \"low\":\n                return \"text-blue-400 bg-blue-900/20\";\n            default:\n                return \"text-gray-400 bg-gray-900/20\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-cyber\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                user: user\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                lineNumber: 298,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-8 w-8 text-cyber-green\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold font-cyber text-white\",\n                                            children: [\n                                                \"Google \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"cyber-text\",\n                                                    children: \"Dorking\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \" & CVE Harian\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 max-w-3xl\",\n                                    children: \"Advanced Google dorking dengan preset queries untuk security research dan CVE intelligence harian. Temukan exposed files, admin panels, dan vulnerability terbaru.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.StatCard, {\n                                    title: \"Dork Presets\",\n                                    value: dorkPresets.length,\n                                    icon: _barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                                    color: \"green\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.StatCard, {\n                                    title: \"Saved Queries\",\n                                    value: savedQueries.length,\n                                    icon: _barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                                    color: \"blue\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.StatCard, {\n                                    title: \"CVE Today\",\n                                    value: cveDaily.length,\n                                    icon: _barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                                    color: \"red\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.StatCard, {\n                                    title: \"Trending CVE\",\n                                    value: cveDaily.filter((cve)=>cve.trending).length,\n                                    icon: _barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                                    color: \"gold\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 bg-gray-800/50 p-1 rounded-lg\",\n                                children: [\n                                    {\n                                        id: \"dorking\",\n                                        name: \"Google Dorking\",\n                                        icon: _barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n                                    },\n                                    {\n                                        id: \"cve\",\n                                        name: \"CVE Harian\",\n                                        icon: _barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                                    }\n                                ].map((tab)=>{\n                                    const Icon = tab.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(tab.id),\n                                        className: `flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 ${activeTab === tab.id ? \"bg-cyber-green text-black\" : \"text-gray-300 hover:text-white hover:bg-gray-700/50\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: tab.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, tab.id, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this),\n                        activeTab === \"dorking\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid lg:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            border: \"green\",\n                                            glow: true,\n                                            className: \"mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-bold text-white mb-4\",\n                                                        children: \"Custom Google Dork\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: customQuery,\n                                                                        onChange: (e)=>setCustomQuery(e.target.value),\n                                                                        placeholder: 'Enter custom dork query (e.g., filetype:sql \"INSERT INTO\")',\n                                                                        className: \"cyber-input flex-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 380,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>executeSearch(customQuery),\n                                                                        disabled: loading || !customQuery.trim(),\n                                                                        className: \"cyber-btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                    className: \"h-4 w-4 mr-2 animate-spin\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                    lineNumber: 394,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                \"Searching...\"\n                                                                            ]\n                                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                    className: \"h-4 w-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                    lineNumber: 399,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                \"Search\"\n                                                                            ]\n                                                                        }, void 0, true)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 387,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>saveQuery(customQuery),\n                                                                        disabled: !customQuery.trim(),\n                                                                        className: \"cyber-btn text-sm disabled:opacity-50\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                lineNumber: 411,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Save Query\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 406,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>copyQuery(customQuery),\n                                                                        disabled: !customQuery.trim(),\n                                                                        className: \"cyber-btn text-sm disabled:opacity-50\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                lineNumber: 419,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Copy\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 414,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                lineNumber: 405,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 17\n                                        }, this),\n                                        searchResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold text-white mb-4\",\n                                                        children: \"Search Results\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: searchResults.map((result)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-800/50 rounded-lg p-4 border border-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-start justify-between mb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center space-x-2 mb-1\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                                className: \"font-semibold text-white\",\n                                                                                                children: result.title\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                                lineNumber: 441,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: `px-2 py-1 rounded-full text-xs font-semibold ${getRiskColor(result.risk)}`,\n                                                                                                children: result.risk.toUpperCase()\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                                lineNumber: 442,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                        lineNumber: 440,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                                        href: result.url,\n                                                                                        target: \"_blank\",\n                                                                                        rel: \"noopener noreferrer\",\n                                                                                        className: \"text-cyber-green hover:text-cyber-blue transition-colors text-sm\",\n                                                                                        children: [\n                                                                                            result.url,\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                                className: \"h-3 w-3 ml-1 inline\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                                lineNumber: 453,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                        lineNumber: 446,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                lineNumber: 439,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-gray-400\",\n                                                                                children: result.timestamp\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                lineNumber: 456,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 438,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-300 text-sm mb-2\",\n                                                                        children: result.snippet\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 458,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between text-xs text-gray-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    \"Domain: \",\n                                                                                    result.domain\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                lineNumber: 460,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"text-cyber-green hover:text-white transition-colors\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                    lineNumber: 462,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                lineNumber: 461,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 459,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, result.id, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                lineNumber: 434,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-xl font-bold text-white\",\n                                                                children: \"Dork Presets\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                lineNumber: 476,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: selectedCategory,\n                                                                onChange: (e)=>setSelectedCategory(e.target.value),\n                                                                className: \"cyber-input text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"all\",\n                                                                        children: \"All Categories\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 482,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: category,\n                                                                            children: category\n                                                                        }, category, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                            lineNumber: 484,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                lineNumber: 477,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid md:grid-cols-2 gap-4\",\n                                                        children: filteredPresets.map((preset)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-800/50 rounded-lg p-4 border border-gray-700 hover:border-gray-600 transition-colors\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-start justify-between mb-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center space-x-2 mb-1\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                                className: \"font-semibold text-white\",\n                                                                                                children: preset.name\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                                lineNumber: 498,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: `px-2 py-1 rounded-full text-xs font-semibold ${getRiskColor(preset.risk)}`,\n                                                                                                children: preset.risk.toUpperCase()\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                                lineNumber: 499,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                        lineNumber: 497,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-400\",\n                                                                                        children: preset.description\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                        lineNumber: 503,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                lineNumber: 496,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-gray-400\",\n                                                                                children: [\n                                                                                    preset.usage,\n                                                                                    \" uses\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                lineNumber: 505,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 495,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gray-900/50 rounded p-3 mb-3\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                            className: \"text-sm text-cyber-green\",\n                                                                            children: preset.query\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                            lineNumber: 509,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 508,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex flex-wrap gap-1\",\n                                                                                children: preset.tags.map((tag, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"px-2 py-1 bg-gray-700 text-gray-300 rounded text-xs\",\n                                                                                        children: tag\n                                                                                    }, idx, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                        lineNumber: 515,\n                                                                                        columnNumber: 33\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                lineNumber: 513,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>copyQuery(preset.query),\n                                                                                        className: \"text-gray-400 hover:text-white transition-colors\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                            className: \"h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                            lineNumber: 528,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                        lineNumber: 524,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>{\n                                                                                            setCustomQuery(preset.query);\n                                                                                            executeSearch(preset.query);\n                                                                                        },\n                                                                                        className: \"text-gray-400 hover:text-cyber-green transition-colors\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                            className: \"h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                            lineNumber: 537,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                        lineNumber: 530,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                lineNumber: 523,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 512,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, preset.id, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                lineNumber: 491,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold text-white mb-4\",\n                                                        children: \"Saved Queries\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                        lineNumber: 552,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    savedQueries.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"No saved queries yet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: savedQueries.map((query, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-800/50 rounded p-3 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                        className: \"text-cyber-green\",\n                                                                        children: query\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 562,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-2 mt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>copyQuery(query),\n                                                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                    lineNumber: 568,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                lineNumber: 564,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>{\n                                                                                    setCustomQuery(query);\n                                                                                    executeSearch(query);\n                                                                                },\n                                                                                className: \"text-gray-400 hover:text-cyber-green transition-colors\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                    lineNumber: 577,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                lineNumber: 570,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 563,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, idx, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                lineNumber: 558,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"mt-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold text-white mb-4\",\n                                                        children: \"Dorking Tips\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 text-sm text-gray-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"• Use \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                        className: \"text-cyber-green\",\n                                                                        children: \"site:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 591,\n                                                                        columnNumber: 32\n                                                                    }, this),\n                                                                    \" to target specific domains\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"• \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                        className: \"text-cyber-green\",\n                                                                        children: \"filetype:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 592,\n                                                                        columnNumber: 28\n                                                                    }, this),\n                                                                    \" searches for specific file types\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                lineNumber: 592,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"• \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                        className: \"text-cyber-green\",\n                                                                        children: \"inurl:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 593,\n                                                                        columnNumber: 28\n                                                                    }, this),\n                                                                    \" finds text in URLs\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                lineNumber: 593,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"• \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                        className: \"text-cyber-green\",\n                                                                        children: \"intitle:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 594,\n                                                                        columnNumber: 28\n                                                                    }, this),\n                                                                    \" searches page titles\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                lineNumber: 594,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"• Use quotes for exact phrases\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                lineNumber: 595,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"• Combine operators with AND/OR\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                lineNumber: 596,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                lineNumber: 588,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                            lineNumber: 587,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                    lineNumber: 549,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === \"cve\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid lg:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        border: \"red\",\n                                        glow: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-xl font-bold text-white\",\n                                                            children: \"CVE Intelligence Harian\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 text-sm text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                    lineNumber: 613,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Updated: \",\n                                                                        new Date().toLocaleDateString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                    lineNumber: 614,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                            lineNumber: 612,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                    lineNumber: 610,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: cveDaily.map((cve)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gray-800/50 rounded-lg p-4 border border-gray-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start justify-between mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                            className: \"font-bold text-white\",\n                                                                                            children: cve.cveId\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                            lineNumber: 627,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: `px-2 py-1 rounded-full text-xs font-semibold ${getSeverityColor(cve.severity)}`,\n                                                                                            children: cve.severity.toUpperCase()\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                            lineNumber: 628,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"px-2 py-1 bg-gray-700 text-gray-300 rounded-full text-xs font-semibold\",\n                                                                                            children: [\n                                                                                                \"CVSS \",\n                                                                                                cve.cvssScore\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                            lineNumber: 631,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        cve.trending && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"px-2 py-1 bg-red-900/50 text-red-400 rounded-full text-xs font-semibold flex items-center\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                                    className: \"h-3 w-3 mr-1\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                                    lineNumber: 636,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                \"Trending\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                            lineNumber: 635,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                    lineNumber: 626,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"font-semibold text-white mb-2\",\n                                                                                    children: cve.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                    lineNumber: 641,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-gray-300 text-sm mb-3\",\n                                                                                    children: cve.description\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                    lineNumber: 642,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                            lineNumber: 625,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: new Date(cve.publishedDate).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                            lineNumber: 644,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                    lineNumber: 624,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                            className: \"text-sm font-semibold text-gray-300 mb-2\",\n                                                                            children: \"Affected Products:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                            lineNumber: 650,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-wrap gap-2\",\n                                                                            children: cve.affectedProducts.map((product, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"px-2 py-1 bg-blue-900/50 text-blue-400 rounded text-xs\",\n                                                                                    children: product\n                                                                                }, idx, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                    lineNumber: 653,\n                                                                                    columnNumber: 33\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                            lineNumber: 651,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                    lineNumber: 649,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"cyber-btn text-xs\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                            className: \"h-3 w-3 mr-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                            lineNumber: 666,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        \"View Details\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                    lineNumber: 665,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"cyber-btn text-xs\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                            className: \"h-3 w-3 mr-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                            lineNumber: 670,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        \"Search Exploits\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                    lineNumber: 669,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                            lineNumber: 664,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"text-gray-400 hover:text-yellow-400 transition-colors\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                        lineNumber: 676,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                    lineNumber: 675,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"text-gray-400 hover:text-white transition-colors\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                        lineNumber: 679,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                                    lineNumber: 678,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                            lineNumber: 674,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                    lineNumber: 663,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, cve.cveId, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                            lineNumber: 620,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                    lineNumber: 618,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                        lineNumber: 608,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                    lineNumber: 607,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold text-white mb-4\",\n                                                        children: \"CVE Statistics\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                        lineNumber: 694,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400\",\n                                                                        children: \"Total CVE Today\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 697,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white font-semibold\",\n                                                                        children: cveDaily.length\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 698,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                lineNumber: 696,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400\",\n                                                                        children: \"Critical\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 701,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-400 font-semibold\",\n                                                                        children: cveDaily.filter((cve)=>cve.severity === \"critical\").length\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 702,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                lineNumber: 700,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400\",\n                                                                        children: \"High\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 707,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-orange-400 font-semibold\",\n                                                                        children: cveDaily.filter((cve)=>cve.severity === \"high\").length\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 708,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                lineNumber: 706,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400\",\n                                                                        children: \"Medium\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 713,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-yellow-400 font-semibold\",\n                                                                        children: cveDaily.filter((cve)=>cve.severity === \"medium\").length\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 714,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                lineNumber: 712,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400\",\n                                                                        children: \"Trending\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 719,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-cyber-green font-semibold\",\n                                                                        children: cveDaily.filter((cve)=>cve.trending).length\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 720,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                lineNumber: 718,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                        lineNumber: 695,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                lineNumber: 693,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                            lineNumber: 692,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"mt-6\",\n                                            border: \"gold\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold text-white mb-4\",\n                                                        children: \"Quick Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                        lineNumber: 730,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"cyber-btn w-full text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 733,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Refresh CVE Feed\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                lineNumber: 732,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"cyber-btn w-full text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Download, {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 737,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Export CVE List\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                lineNumber: 736,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"cyber-btn w-full text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 741,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Setup Alerts\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                lineNumber: 740,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                        lineNumber: 731,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                lineNumber: 729,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                            lineNumber: 728,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"mt-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold text-white mb-4\",\n                                                        children: \"CVE Sources\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                        lineNumber: 750,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-green-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 753,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-300\",\n                                                                        children: \"NIST NVD\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 754,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                lineNumber: 752,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-green-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 757,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-300\",\n                                                                        children: \"MITRE CVE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 758,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                lineNumber: 756,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-green-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 761,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-300\",\n                                                                        children: \"Exploit-DB\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 762,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                lineNumber: 760,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Bookmark_Calendar_CheckCircle_Copy_ExternalLink_Eye_Play_RefreshCw_Save_Search_Shield_Star_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-green-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 765,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-300\",\n                                                                        children: \"Security Advisories\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                        lineNumber: 766,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                                lineNumber: 764,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                        lineNumber: 751,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                                lineNumber: 749,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                            lineNumber: 748,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                                    lineNumber: 691,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                            lineNumber: 606,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                    lineNumber: 301,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n                lineNumber: 300,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dorking\\\\page.tsx\",\n        lineNumber: 297,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/dorking/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Card.tsx":
/*!*****************************!*\
  !*** ./components/Card.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlertCard: () => (/* binding */ AlertCard),\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   FeatureCard: () => (/* binding */ FeatureCard),\n/* harmony export */   LoadingCard: () => (/* binding */ LoadingCard),\n/* harmony export */   StatCard: () => (/* binding */ StatCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ Card,StatCard,FeatureCard,LoadingCard,AlertCard auto */ \nfunction Card({ children, className = \"\", hover = false, glow = false, border = \"default\" }) {\n    const borderColors = {\n        default: \"border-gray-700\",\n        green: \"border-cyber-green\",\n        blue: \"border-cyber-blue\",\n        red: \"border-cyber-red\",\n        gold: \"border-nusantara-gold\"\n    };\n    const glowColors = {\n        default: \"\",\n        green: \"shadow-lg shadow-cyber-green/20\",\n        blue: \"shadow-lg shadow-cyber-blue/20\",\n        red: \"shadow-lg shadow-cyber-red/20\",\n        gold: \"shadow-lg shadow-nusantara-gold/20\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `\n        bg-gray-900/50 backdrop-blur-sm rounded-lg border ${borderColors[border]}\n        ${hover ? \"hover:border-cyber-green hover:shadow-lg hover:shadow-cyber-green/20 transition-all duration-300 cursor-pointer\" : \"\"}\n        ${glow ? glowColors[border] : \"\"}\n        ${className}\n      `,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\nfunction StatCard({ title, value, icon: Icon, color = \"green\", trend, loading = false }) {\n    const colors = {\n        green: \"text-cyber-green\",\n        blue: \"text-cyber-blue\",\n        red: \"text-cyber-red\",\n        purple: \"text-cyber-purple\",\n        gold: \"text-nusantara-gold\"\n    };\n    const bgColors = {\n        green: \"bg-cyber-green/10\",\n        blue: \"bg-cyber-blue/10\",\n        red: \"bg-cyber-red/10\",\n        purple: \"bg-cyber-purple/10\",\n        gold: \"bg-nusantara-gold/10\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        border: color,\n        glow: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-400\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-baseline space-x-2\",\n                                children: [\n                                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 w-20 bg-gray-700 animate-pulse rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: `text-2xl font-bold ${colors[color]}`,\n                                        children: typeof value === \"number\" ? value.toLocaleString() : value\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, this),\n                                    trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `text-sm font-medium ${trend.isPositive ? \"text-green-400\" : \"text-red-400\"}`,\n                                        children: [\n                                            trend.isPositive ? \"+\" : \"\",\n                                            trend.value,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `p-3 rounded-lg ${bgColors[color]}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            className: `h-6 w-6 ${colors[color]}`\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\nfunction FeatureCard({ title, description, icon: Icon, color = \"green\", onClick, disabled = false, badge }) {\n    const colors = {\n        green: \"text-cyber-green\",\n        blue: \"text-cyber-blue\",\n        red: \"text-cyber-red\",\n        purple: \"text-cyber-purple\",\n        gold: \"text-nusantara-gold\"\n    };\n    const bgColors = {\n        green: \"bg-cyber-green/10\",\n        blue: \"bg-cyber-blue/10\",\n        red: \"bg-cyber-red/10\",\n        purple: \"bg-cyber-purple/10\",\n        gold: \"bg-nusantara-gold/10\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        hover: !disabled && !!onClick,\n        border: color,\n        className: `relative ${disabled ? \"opacity-50 cursor-not-allowed\" : \"\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 h-full\",\n            onClick: disabled ? undefined : onClick,\n            children: [\n                badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-3 right-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `px-2 py-1 text-xs font-semibold rounded-full ${bgColors[color]} ${colors[color]}`,\n                        children: badge\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `inline-flex p-3 rounded-lg ${bgColors[color]} mb-4`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: `h-6 w-6 ${colors[color]}`\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-white mb-2\",\n                    children: title\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-400 text-sm leading-relaxed\",\n                    children: description\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this),\n                onClick && !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 pt-4 border-t border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-medium ${colors[color]} hover:underline`,\n                        children: \"Mulai Sekarang →\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\nfunction LoadingCard({ className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 animate-pulse\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 bg-gray-700 rounded-lg\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-gray-700 rounded w-3/4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-3 bg-gray-700 rounded w-1/2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                lineNumber: 184,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 183,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 182,\n        columnNumber: 5\n    }, this);\n}\nfunction AlertCard({ type = \"info\", title, message, onClose }) {\n    const styles = {\n        info: {\n            border: \"border-cyber-blue\",\n            bg: \"bg-cyber-blue/10\",\n            text: \"text-cyber-blue\",\n            icon: \"\\uD83D\\uDCA1\"\n        },\n        success: {\n            border: \"border-cyber-green\",\n            bg: \"bg-cyber-green/10\",\n            text: \"text-cyber-green\",\n            icon: \"✅\"\n        },\n        warning: {\n            border: \"border-nusantara-gold\",\n            bg: \"bg-nusantara-gold/10\",\n            text: \"text-nusantara-gold\",\n            icon: \"⚠️\"\n        },\n        error: {\n            border: \"border-cyber-red\",\n            bg: \"bg-cyber-red/10\",\n            text: \"text-cyber-red\",\n            icon: \"❌\"\n        }\n    };\n    const style = styles[type];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `border ${style.border} ${style.bg} rounded-lg p-4`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-lg\",\n                    children: style.icon\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: `font-semibold ${style.text}`,\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300 text-sm mt-1\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, this),\n                onClose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onClose,\n                    className: \"text-gray-400 hover:text-white transition-colors\",\n                    children: \"\\xd7\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 238,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 237,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Navbar.tsx":
/*!*******************************!*\
  !*** ./components/Navbar.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-search.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Navbar({ user }) {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isProfileOpen, setIsProfileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const navigation = [\n        {\n            name: \"Dashboard\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            name: \"OSINT\",\n            href: \"/osint\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            name: \"Scanner\",\n            href: \"/scanner\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            name: \"File Analyzer\",\n            href: \"/file-analyzer\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            name: \"CVE Intel\",\n            href: \"/cve\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            name: \"Playground\",\n            href: \"/playground\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            name: \"Bot Center\",\n            href: \"/bot\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            name: \"Leaderboard\",\n            href: \"/leaderboard\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        }\n    ];\n    const isActive = (href)=>pathname === href;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 w-full z-50 bg-cyber-dark/90 backdrop-blur-md border-b border-gray-800\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-8 w-8 text-cyber-green animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold font-cyber\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"cyber-text\",\n                                                children: \"Kode\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"nusantara-gold\",\n                                                children: \"X\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white\",\n                                                children: \"Guard\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-1\",\n                            children: user ? // Authenticated Navigation\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    navigation.map((item)=>{\n                                        const Icon = item.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: item.href,\n                                            className: `flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${isActive(item.href) ? \"bg-cyber-green/20 text-cyber-green border border-cyber-green/30\" : \"text-gray-300 hover:text-cyber-green hover:bg-gray-800/50\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, item.name, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 21\n                                        }, this);\n                                    }),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsProfileOpen(!isProfileOpen),\n                                                className: \"flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium text-gray-300 hover:text-cyber-green hover:bg-gray-800/50 transition-all duration-200\",\n                                                children: [\n                                                    user.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: user.avatar,\n                                                        alt: user.username,\n                                                        className: \"h-6 w-6 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: user.username\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs px-2 py-1 rounded-full bg-cyber-green/20 text-cyber-green\",\n                                                        children: user.plan.toUpperCase()\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 19\n                                            }, this),\n                                            isProfileOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-0 mt-2 w-48 bg-gray-900 border border-gray-700 rounded-lg shadow-lg py-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/profile\",\n                                                        className: \"flex items-center space-x-2 px-4 py-2 text-sm text-gray-300 hover:text-cyber-green hover:bg-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 110,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Profile\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 111,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/settings\",\n                                                        className: \"flex items-center space-x-2 px-4 py-2 text-sm text-gray-300 hover:text-cyber-green hover:bg-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 117,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Settings\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 118,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    user.role === \"super_admin\" || user.role === \"admin\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/admin\",\n                                                        className: \"flex items-center space-x-2 px-4 py-2 text-sm text-gray-300 hover:text-cyber-green hover:bg-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Admin Panel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 126,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 25\n                                                    }, this) : null,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                        className: \"my-1 border-gray-700\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{},\n                                                        className: \"flex items-center space-x-2 w-full px-4 py-2 text-sm text-red-400 hover:text-red-300 hover:bg-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 134,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Logout\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 135,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : // Guest Navigation\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/docs\",\n                                        className: \"text-gray-300 hover:text-cyber-green transition-colors\",\n                                        children: \"Dokumentasi\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/pricing\",\n                                        className: \"text-gray-300 hover:text-cyber-green transition-colors\",\n                                        children: \"Harga\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/login\",\n                                        className: \"cyber-btn\",\n                                        children: \"Masuk\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/register\",\n                                        className: \"cyber-btn-primary\",\n                                        children: \"Daftar\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsOpen(!isOpen),\n                                className: \"text-gray-300 hover:text-cyber-green p-2\",\n                                children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 25\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 53\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden bg-cyber-dark/95 backdrop-blur-md border-t border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-2 pt-2 pb-3 space-y-1\",\n                    children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            navigation.map((item)=>{\n                                const Icon = item.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: item.href,\n                                    className: `flex items-center space-x-3 px-3 py-2 rounded-lg text-base font-medium transition-all duration-200 ${isActive(item.href) ? \"bg-cyber-green/20 text-cyber-green border border-cyber-green/30\" : \"text-gray-300 hover:text-cyber-green hover:bg-gray-800/50\"}`,\n                                    onClick: ()=>setIsOpen(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 21\n                                }, this);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                className: \"my-2 border-gray-700\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/profile\",\n                                className: \"flex items-center space-x-3 px-3 py-2 rounded-lg text-base font-medium text-gray-300 hover:text-cyber-green hover:bg-gray-800/50\",\n                                onClick: ()=>setIsOpen(false),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Profile (\",\n                                            user.username,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{},\n                                className: \"flex items-center space-x-3 w-full px-3 py-2 rounded-lg text-base font-medium text-red-400 hover:text-red-300 hover:bg-gray-800/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Logout\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/docs\",\n                                className: \"block px-3 py-2 rounded-lg text-base font-medium text-gray-300 hover:text-cyber-green hover:bg-gray-800/50\",\n                                onClick: ()=>setIsOpen(false),\n                                children: \"Dokumentasi\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/pricing\",\n                                className: \"block px-3 py-2 rounded-lg text-base font-medium text-gray-300 hover:text-cyber-green hover:bg-gray-800/50\",\n                                onClick: ()=>setIsOpen(false),\n                                children: \"Harga\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/login\",\n                                className: \"block px-3 py-2 rounded-lg text-base font-medium cyber-btn text-center\",\n                                onClick: ()=>setIsOpen(false),\n                                children: \"Masuk\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/register\",\n                                className: \"block px-3 py-2 rounded-lg text-base font-medium cyber-btn-primary text-center\",\n                                onClick: ()=>setIsOpen(false),\n                                children: \"Daftar\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                lineNumber: 174,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Navbar.tsx\n");

/***/ }),

/***/ "(rsc)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7cffc49d1683\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb2RleGd1YXJkLy4vc3R5bGVzL2dsb2JhbHMuY3NzPzZkZDQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3Y2ZmYzQ5ZDE2ODNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/dorking/page.tsx":
/*!******************************!*\
  !*** ./app/dorking/page.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\dorking\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(rsc)/./styles/globals.css\");\n\n\n\nconst metadata = {\n    title: \"KodeXGuard - Platform Cybersecurity & Bug Hunting\",\n    description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting dengan dukungan Bot WhatsApp/Telegram\",\n    keywords: \"cybersecurity, bug hunting, OSINT, vulnerability scanner, CVE, penetration testing, security tools\",\n    authors: [\n        {\n            name: \"KodeXGuard Team\"\n        }\n    ],\n    creator: \"KodeXGuard\",\n    publisher: \"KodeXGuard\",\n    robots: \"index, follow\",\n    openGraph: {\n        type: \"website\",\n        locale: \"id_ID\",\n        url: \"https://kodexguard.com\",\n        title: \"KodeXGuard - Platform Cybersecurity & Bug Hunting\",\n        description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting\",\n        siteName: \"KodeXGuard\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"KodeXGuard - Platform Cybersecurity & Bug Hunting\",\n        description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting\"\n    },\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#00ff41\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"id\",\n        className: \"dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} min-h-screen bg-gradient-cyber text-white antialiased`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"matrix-bg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-b from-transparent via-cyber-dark/20 to-cyber-dark\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n              // Matrix Rain Effect\n              function createMatrixRain() {\n                const canvas = document.createElement('canvas');\n                const ctx = canvas.getContext('2d');\n                canvas.style.position = 'fixed';\n                canvas.style.top = '0';\n                canvas.style.left = '0';\n                canvas.style.width = '100%';\n                canvas.style.height = '100%';\n                canvas.style.pointerEvents = 'none';\n                canvas.style.zIndex = '-1';\n                canvas.style.opacity = '0.1';\n                document.body.appendChild(canvas);\n                \n                canvas.width = window.innerWidth;\n                canvas.height = window.innerHeight;\n                \n                const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';\n                const charArray = chars.split('');\n                const fontSize = 14;\n                const columns = canvas.width / fontSize;\n                const drops = [];\n                \n                for (let i = 0; i < columns; i++) {\n                  drops[i] = 1;\n                }\n                \n                function draw() {\n                  ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';\n                  ctx.fillRect(0, 0, canvas.width, canvas.height);\n                  \n                  ctx.fillStyle = '#00ff41';\n                  ctx.font = fontSize + 'px monospace';\n                  \n                  for (let i = 0; i < drops.length; i++) {\n                    const text = charArray[Math.floor(Math.random() * charArray.length)];\n                    ctx.fillText(text, i * fontSize, drops[i] * fontSize);\n                    \n                    if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {\n                      drops[i] = 0;\n                    }\n                    drops[i]++;\n                  }\n                }\n                \n                setInterval(draw, 50);\n                \n                window.addEventListener('resize', () => {\n                  canvas.width = window.innerWidth;\n                  canvas.height = window.innerHeight;\n                });\n              }\n              \n              // Initialize matrix effect after page load\n              if (document.readyState === 'loading') {\n                document.addEventListener('DOMContentLoaded', createMatrixRain);\n              } else {\n                createMatrixRain();\n              }\n            `\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdorking%2Fpage&page=%2Fdorking%2Fpage&appPaths=%2Fdorking%2Fpage&pagePath=private-next-app-dir%2Fdorking%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();