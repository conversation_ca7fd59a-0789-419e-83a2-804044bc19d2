'use client'

import { useState, useEffect } from 'react'
import DashboardLayout from '@/components/DashboardLayout'
import { Card, StatCard, AlertCard } from '@/components/Card'
import { 
  Shield, 
  Search, 
  AlertTriangle, 
  Calendar,
  Filter,
  Download,
  ExternalLink,
  Clock,
  TrendingUp,
  Database,
  RefreshCw,
  Star,
  Eye,
  BookOpen
} from 'lucide-react'

interface CVERecord {
  id: string
  cveId: string
  description: string
  severity: 'info' | 'low' | 'medium' | 'high' | 'critical'
  cvssScore: number
  cvssVector: string
  publishedDate: string
  modifiedDate: string
  affectedProducts: string[]
  references: string[]
  exploits: Array<{
    title: string
    url: string
    type: string
  }>
  trending: boolean
}

export default function CVEPage() {
  const [user] = useState({
    username: 'admin',
    avatar: '',
    role: 'super_admin',
    plan: 'cybersecurity'
  })

  const [searchQuery, setSearchQuery] = useState('')
  const [selectedSeverity, setSelectedSeverity] = useState('all')
  const [selectedYear, setSelectedYear] = useState('all')
  const [loading, setLoading] = useState(false)
  const [cveRecords, setCveRecords] = useState<CVERecord[]>([])
  const [stats, setStats] = useState({
    totalCVEs: 189234,
    newToday: 23,
    criticalActive: 156,
    exploitsAvailable: 2847
  })

  const severityColors = {
    info: 'text-blue-400 bg-blue-900/20',
    low: 'text-green-400 bg-green-900/20',
    medium: 'text-yellow-400 bg-yellow-900/20',
    high: 'text-orange-400 bg-orange-900/20',
    critical: 'text-red-400 bg-red-900/20'
  }

  const mockCVEs: CVERecord[] = [
    {
      id: '1',
      cveId: 'CVE-2024-0001',
      description: 'SQL injection vulnerability in web application login form allows remote attackers to execute arbitrary SQL commands via the username parameter.',
      severity: 'critical',
      cvssScore: 9.8,
      cvssVector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H',
      publishedDate: '2024-01-15',
      modifiedDate: '2024-01-16',
      affectedProducts: ['WebApp 1.0', 'WebApp 2.0', 'WebApp 2.1'],
      references: [
        'https://example.com/advisory-001',
        'https://nvd.nist.gov/vuln/detail/CVE-2024-0001'
      ],
      exploits: [
        {
          title: 'SQL Injection Exploit',
          url: 'https://exploit-db.com/exploits/12345',
          type: 'Public Exploit'
        }
      ],
      trending: true
    },
    {
      id: '2',
      cveId: 'CVE-2024-0002',
      description: 'Cross-site scripting (XSS) vulnerability in search functionality allows attackers to inject malicious scripts.',
      severity: 'high',
      cvssScore: 7.5,
      cvssVector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:N/A:N',
      publishedDate: '2024-01-14',
      modifiedDate: '2024-01-14',
      affectedProducts: ['Framework 3.0', 'Framework 3.1'],
      references: [
        'https://example.com/advisory-002'
      ],
      exploits: [],
      trending: false
    },
    {
      id: '3',
      cveId: 'CVE-2024-0003',
      description: 'Remote code execution vulnerability in file upload functionality allows authenticated users to execute arbitrary code.',
      severity: 'critical',
      cvssScore: 9.1,
      cvssVector: 'CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H',
      publishedDate: '2024-01-13',
      modifiedDate: '2024-01-15',
      affectedProducts: ['CMS 4.0', 'CMS 4.1', 'CMS 4.2'],
      references: [
        'https://example.com/advisory-003',
        'https://github.com/vendor/cms/security/advisories'
      ],
      exploits: [
        {
          title: 'RCE via File Upload',
          url: 'https://exploit-db.com/exploits/12346',
          type: 'Public Exploit'
        },
        {
          title: 'Metasploit Module',
          url: 'https://github.com/rapid7/metasploit-framework',
          type: 'Framework Module'
        }
      ],
      trending: true
    }
  ]

  useEffect(() => {
    setCveRecords(mockCVEs)
  }, [])

  const handleSearch = async () => {
    setLoading(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    let filtered = mockCVEs
    
    if (searchQuery) {
      filtered = filtered.filter(cve => 
        cve.cveId.toLowerCase().includes(searchQuery.toLowerCase()) ||
        cve.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        cve.affectedProducts.some(product => 
          product.toLowerCase().includes(searchQuery.toLowerCase())
        )
      )
    }
    
    if (selectedSeverity !== 'all') {
      filtered = filtered.filter(cve => cve.severity === selectedSeverity)
    }
    
    setCveRecords(filtered)
    setLoading(false)
  }

  const exportCVEs = () => {
    const dataStr = JSON.stringify(cveRecords, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `cve-export-${Date.now()}.json`
    link.click()
  }

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return AlertTriangle
      case 'high': return AlertTriangle
      case 'medium': return Shield
      default: return Shield
    }
  }

  return (
    <DashboardLayout user={user} title="CVE Intelligence">
      <div>
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <Shield className="h-8 w-8 text-cyber-green" />
              <h1 className="text-3xl font-bold font-cyber text-white">
                CVE <span className="cyber-text">Intelligence</span>
              </h1>
            </div>
            <p className="text-gray-400 max-w-3xl">
              Database CVE terupdate harian dengan referensi lengkap, exploit availability, dan CVSS scoring. 
              Cari CVE berdasarkan keyword, tahun, kategori, dan tingkat keparahan.
            </p>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <StatCard
              title="Total CVEs"
              value={stats.totalCVEs}
              icon={Database}
              color="green"
              trend={{ value: 5, isPositive: true }}
            />
            <StatCard
              title="New Today"
              value={stats.newToday}
              icon={TrendingUp}
              color="blue"
              trend={{ value: 12, isPositive: true }}
            />
            <StatCard
              title="Critical Active"
              value={stats.criticalActive}
              icon={AlertTriangle}
              color="red"
              trend={{ value: -3, isPositive: false }}
            />
            <StatCard
              title="Exploits Available"
              value={stats.exploitsAvailable}
              icon={Eye}
              color="purple"
              trend={{ value: 8, isPositive: true }}
            />
          </div>

          <div className="grid lg:grid-cols-4 gap-8">
            {/* Search & Filters */}
            <div>
              <Card border="green" glow>
                <div className="p-6">
                  <h2 className="text-lg font-bold text-white mb-4">Search & Filter</h2>
                  
                  {/* Search Input */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Search CVEs
                    </label>
                    <div className="flex space-x-2">
                      <input
                        type="text"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        placeholder="CVE-2024-0001, SQL injection..."
                        className="cyber-input flex-1 text-sm"
                        onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                      />
                      <button
                        onClick={handleSearch}
                        disabled={loading}
                        className="cyber-btn-primary px-3"
                      >
                        {loading ? (
                          <RefreshCw className="h-4 w-4 animate-spin" />
                        ) : (
                          <Search className="h-4 w-4" />
                        )}
                      </button>
                    </div>
                  </div>

                  {/* Severity Filter */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Severity
                    </label>
                    <select
                      value={selectedSeverity}
                      onChange={(e) => setSelectedSeverity(e.target.value)}
                      className="cyber-input w-full text-sm"
                    >
                      <option value="all">All Severities</option>
                      <option value="critical">Critical</option>
                      <option value="high">High</option>
                      <option value="medium">Medium</option>
                      <option value="low">Low</option>
                      <option value="info">Info</option>
                    </select>
                  </div>

                  {/* Year Filter */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Year
                    </label>
                    <select
                      value={selectedYear}
                      onChange={(e) => setSelectedYear(e.target.value)}
                      className="cyber-input w-full text-sm"
                    >
                      <option value="all">All Years</option>
                      <option value="2024">2024</option>
                      <option value="2023">2023</option>
                      <option value="2022">2022</option>
                      <option value="2021">2021</option>
                    </select>
                  </div>

                  {/* Quick Filters */}
                  <div className="space-y-2">
                    <button className="w-full text-left p-2 rounded bg-gray-800/50 hover:bg-gray-700/50 transition-colors text-sm text-gray-300">
                      🔥 Trending CVEs
                    </button>
                    <button className="w-full text-left p-2 rounded bg-gray-800/50 hover:bg-gray-700/50 transition-colors text-sm text-gray-300">
                      💥 With Exploits
                    </button>
                    <button className="w-full text-left p-2 rounded bg-gray-800/50 hover:bg-gray-700/50 transition-colors text-sm text-gray-300">
                      🆕 Last 7 Days
                    </button>
                    <button className="w-full text-left p-2 rounded bg-gray-800/50 hover:bg-gray-700/50 transition-colors text-sm text-gray-300">
                      ⚡ CVSS 9.0+
                    </button>
                  </div>
                </div>
              </Card>

              {/* Daily CVE Summary */}
              <Card className="mt-6">
                <div className="p-6">
                  <h3 className="text-lg font-bold text-white mb-4">Today's Summary</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-400">New CVEs</span>
                      <span className="text-cyber-green font-semibold">23</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Critical</span>
                      <span className="text-red-400 font-semibold">3</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">High</span>
                      <span className="text-orange-400 font-semibold">8</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Medium</span>
                      <span className="text-yellow-400 font-semibold">12</span>
                    </div>
                  </div>
                </div>
              </Card>
            </div>

            {/* CVE Results */}
            <div className="lg:col-span-3">
              <Card>
                <div className="p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-bold text-white">
                      CVE Records ({cveRecords.length})
                    </h2>
                    <button
                      onClick={exportCVEs}
                      className="cyber-btn text-sm"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </button>
                  </div>

                  <div className="space-y-4">
                    {cveRecords.map((cve) => {
                      const SeverityIcon = getSeverityIcon(cve.severity)
                      return (
                        <div
                          key={cve.id}
                          className="bg-gray-800/50 rounded-lg p-4 border border-gray-700 hover:border-gray-600 transition-colors"
                        >
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex items-center space-x-3">
                              <SeverityIcon className={`h-5 w-5 ${severityColors[cve.severity].split(' ')[0]}`} />
                              <div>
                                <div className="flex items-center space-x-2">
                                  <h3 className="font-bold text-white">{cve.cveId}</h3>
                                  {cve.trending && (
                                    <span className="px-2 py-1 bg-red-900/50 text-red-400 rounded-full text-xs font-semibold">
                                      🔥 Trending
                                    </span>
                                  )}
                                </div>
                                <p className="text-sm text-gray-400">
                                  Published: {new Date(cve.publishedDate).toLocaleDateString()}
                                </p>
                              </div>
                            </div>
                            <div className="text-right">
                              <span className={`px-2 py-1 rounded-full text-xs font-semibold ${severityColors[cve.severity]}`}>
                                {cve.severity.toUpperCase()}
                              </span>
                              <div className="text-sm text-gray-400 mt-1">
                                CVSS: {cve.cvssScore}
                              </div>
                            </div>
                          </div>

                          <p className="text-gray-300 mb-4">{cve.description}</p>

                          <div className="grid md:grid-cols-2 gap-4 mb-4">
                            <div>
                              <h4 className="text-sm font-semibold text-white mb-2">Affected Products</h4>
                              <div className="flex flex-wrap gap-1">
                                {cve.affectedProducts.map((product, idx) => (
                                  <span
                                    key={idx}
                                    className="px-2 py-1 bg-gray-700 text-gray-300 rounded text-xs"
                                  >
                                    {product}
                                  </span>
                                ))}
                              </div>
                            </div>
                            {cve.exploits.length > 0 && (
                              <div>
                                <h4 className="text-sm font-semibold text-white mb-2">Available Exploits</h4>
                                <div className="space-y-1">
                                  {cve.exploits.map((exploit, idx) => (
                                    <a
                                      key={idx}
                                      href={exploit.url}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="flex items-center space-x-2 text-sm text-cyber-green hover:text-cyber-blue transition-colors"
                                    >
                                      <ExternalLink className="h-3 w-3" />
                                      <span>{exploit.title}</span>
                                    </a>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>

                          <div className="border-t border-gray-700 pt-3">
                            <div className="flex items-center justify-between">
                              <div className="flex space-x-4 text-sm text-gray-400">
                                <span>CVSS Vector: {cve.cvssVector}</span>
                              </div>
                              <div className="flex space-x-2">
                                {cve.references.map((ref, idx) => (
                                  <a
                                    key={idx}
                                    href={ref}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-cyber-green hover:text-cyber-blue transition-colors"
                                  >
                                    <ExternalLink className="h-4 w-4" />
                                  </a>
                                ))}
                              </div>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>

                  {cveRecords.length === 0 && !loading && (
                    <div className="text-center py-8">
                      <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-white mb-2">No CVEs Found</h3>
                      <p className="text-gray-400">Try adjusting your search criteria</p>
                    </div>
                  )}

                  {loading && (
                    <div className="text-center py-8">
                      <RefreshCw className="h-8 w-8 text-cyber-green animate-spin mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-white mb-2">Searching CVEs...</h3>
                      <p className="text-gray-400">Please wait while we fetch the latest data</p>
                    </div>
                  )}
                </div>
              </Card>
            </div>
          </div>
      </div>
    </DashboardLayout>
  )
}
