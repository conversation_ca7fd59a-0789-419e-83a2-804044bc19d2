'use client'

import { useState, useEffect } from 'react'
import Navbar from '@/components/Navbar'
import { Card, StatCard, AlertCard } from '@/components/Card'
import { 
  Trophy, 
  Users, 
  Target, 
  Zap,
  Crown,
  Medal,
  Award,
  TrendingUp,
  Calendar,
  MessageCircle,
  ExternalLink,
  Star,
  Shield,
  Bug,
  FileSearch,
  Search,
  Activity,
  Clock,
  Globe
} from 'lucide-react'

interface LeaderboardUser {
  id: string
  username: string
  avatar?: string
  rank: number
  score: number
  totalScans: number
  vulnerabilitiesFound: number
  validReports: number
  apiUsage: number
  joinedAt: string
  lastActive: string
  badges: string[]
  country: string
}

interface CommunityGroup {
  id: string
  name: string
  type: 'whatsapp' | 'telegram'
  members: number
  isPrivate: boolean
  description: string
  inviteLink?: string
  lastActivity: string
}

export default function LeaderboardPage() {
  const [user] = useState({
    username: 'admin',
    avatar: '',
    role: 'super_admin',
    plan: 'cybersecurity'
  })

  const [activeTab, setActiveTab] = useState('leaderboard')
  const [timeFilter, setTimeFilter] = useState('all')
  const [categoryFilter, setCategoryFilter] = useState('overall')

  const leaderboardUsers: LeaderboardUser[] = [
    {
      id: '1',
      username: 'CyberNinja',
      avatar: '',
      rank: 1,
      score: 15420,
      totalScans: 2847,
      vulnerabilitiesFound: 156,
      validReports: 89,
      apiUsage: 45230,
      joinedAt: '2023-01-15',
      lastActive: '2 hours ago',
      badges: ['Elite Hunter', 'CVE Discoverer', 'API Master'],
      country: 'Indonesia'
    },
    {
      id: '2',
      username: 'SecurityGuru',
      avatar: '',
      rank: 2,
      score: 12890,
      totalScans: 2156,
      vulnerabilitiesFound: 134,
      validReports: 78,
      apiUsage: 38940,
      joinedAt: '2023-02-20',
      lastActive: '1 hour ago',
      badges: ['Bug Master', 'OSINT Expert'],
      country: 'Indonesia'
    },
    {
      id: '3',
      username: 'HackTheBox',
      avatar: '',
      rank: 3,
      score: 11250,
      totalScans: 1923,
      vulnerabilitiesFound: 112,
      validReports: 67,
      apiUsage: 32150,
      joinedAt: '2023-03-10',
      lastActive: '30 minutes ago',
      badges: ['Scanner Pro', 'File Analyzer'],
      country: 'Indonesia'
    },
    {
      id: '4',
      username: 'admin',
      avatar: '',
      rank: 4,
      score: 9870,
      totalScans: 1247,
      vulnerabilitiesFound: 89,
      validReports: 45,
      apiUsage: 28560,
      joinedAt: '2023-01-01',
      lastActive: 'Online',
      badges: ['Super Admin', 'Platform Creator'],
      country: 'Indonesia'
    },
    {
      id: '5',
      username: 'PentestPro',
      avatar: '',
      rank: 5,
      score: 8640,
      totalScans: 1089,
      vulnerabilitiesFound: 76,
      validReports: 42,
      apiUsage: 24890,
      joinedAt: '2023-04-05',
      lastActive: '1 day ago',
      badges: ['Vulnerability Hunter'],
      country: 'Indonesia'
    }
  ]

  const communityGroups: CommunityGroup[] = [
    {
      id: '1',
      name: 'KodeXGuard Indonesia',
      type: 'telegram',
      members: 1247,
      isPrivate: false,
      description: 'Official KodeXGuard community for Indonesian bug hunters and cybersecurity enthusiasts',
      inviteLink: 'https://t.me/kodexguard_id',
      lastActivity: '5 minutes ago'
    },
    {
      id: '2',
      name: 'Elite Bug Hunters',
      type: 'whatsapp',
      members: 89,
      isPrivate: true,
      description: 'Exclusive group for top-tier bug hunters with 1000+ score',
      lastActivity: '1 hour ago'
    },
    {
      id: '3',
      name: 'CVE Research Team',
      type: 'telegram',
      members: 234,
      isPrivate: false,
      description: 'Collaborative CVE research and 0-day discovery discussions',
      inviteLink: 'https://t.me/kodexguard_cve',
      lastActivity: '2 hours ago'
    },
    {
      id: '4',
      name: 'OSINT Investigators',
      type: 'telegram',
      members: 567,
      isPrivate: false,
      description: 'Open Source Intelligence gathering and investigation techniques',
      inviteLink: 'https://t.me/kodexguard_osint',
      lastActivity: '30 minutes ago'
    },
    {
      id: '5',
      name: 'Malware Analysis Lab',
      type: 'whatsapp',
      members: 156,
      isPrivate: true,
      description: 'Advanced malware analysis and reverse engineering discussions',
      lastActivity: '3 hours ago'
    }
  ]

  const stats = {
    totalHunters: 5847,
    activeToday: 1234,
    totalCommunities: 12,
    totalMembers: 8956
  }

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return Crown
      case 2: return Medal
      case 3: return Award
      default: return Trophy
    }
  }

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1: return 'text-yellow-400'
      case 2: return 'text-gray-300'
      case 3: return 'text-orange-400'
      default: return 'text-gray-400'
    }
  }

  const getBadgeColor = (badge: string) => {
    const colors = [
      'bg-red-900/50 text-red-400',
      'bg-blue-900/50 text-blue-400',
      'bg-green-900/50 text-green-400',
      'bg-purple-900/50 text-purple-400',
      'bg-yellow-900/50 text-yellow-400'
    ]
    return colors[badge.length % colors.length]
  }

  return (
    <div className="min-h-screen bg-gradient-cyber">
      <Navbar user={user} />
      
      <div className="pt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <Trophy className="h-8 w-8 text-cyber-green" />
              <h1 className="text-3xl font-bold font-cyber text-white">
                Leaderboard & <span className="cyber-text">Komunitas</span>
              </h1>
            </div>
            <p className="text-gray-400 max-w-3xl">
              Statistik nasional Bug Hunter Indonesia dengan skor berdasarkan laporan valid, eksploitasi sukses, dan API usage. 
              Bergabung dengan komunitas WhatsApp & Telegram untuk kolaborasi.
            </p>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <StatCard
              title="Total Hunters"
              value={stats.totalHunters}
              icon={Users}
              color="green"
              trend={{ value: 12, isPositive: true }}
            />
            <StatCard
              title="Active Today"
              value={stats.activeToday}
              icon={Activity}
              color="blue"
              trend={{ value: 8, isPositive: true }}
            />
            <StatCard
              title="Communities"
              value={stats.totalCommunities}
              icon={MessageCircle}
              color="purple"
            />
            <StatCard
              title="Total Members"
              value={stats.totalMembers}
              icon={Globe}
              color="gold"
              trend={{ value: 15, isPositive: true }}
            />
          </div>

          {/* Tab Navigation */}
          <div className="mb-8">
            <div className="flex space-x-1 bg-gray-800/50 p-1 rounded-lg">
              {[
                { id: 'leaderboard', name: 'Leaderboard', icon: Trophy },
                { id: 'community', name: 'Community Groups', icon: Users }
              ].map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                      activeTab === tab.id
                        ? 'bg-cyber-green text-black'
                        : 'text-gray-300 hover:text-white hover:bg-gray-700/50'
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    <span>{tab.name}</span>
                  </button>
                )
              })}
            </div>
          </div>

          {/* Leaderboard Tab */}
          {activeTab === 'leaderboard' && (
            <div className="grid lg:grid-cols-4 gap-8">
              <div className="lg:col-span-3">
                <Card border="green" glow>
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-6">
                      <h2 className="text-xl font-bold text-white">Bug Hunter Rankings</h2>
                      <div className="flex space-x-3">
                        <select
                          value={timeFilter}
                          onChange={(e) => setTimeFilter(e.target.value)}
                          className="cyber-input text-sm"
                        >
                          <option value="all">All Time</option>
                          <option value="month">This Month</option>
                          <option value="week">This Week</option>
                          <option value="today">Today</option>
                        </select>
                        <select
                          value={categoryFilter}
                          onChange={(e) => setCategoryFilter(e.target.value)}
                          className="cyber-input text-sm"
                        >
                          <option value="overall">Overall Score</option>
                          <option value="scans">Total Scans</option>
                          <option value="vulnerabilities">Vulnerabilities</option>
                          <option value="reports">Valid Reports</option>
                        </select>
                      </div>
                    </div>

                    <div className="space-y-4">
                      {leaderboardUsers.map((hunter) => {
                        const RankIcon = getRankIcon(hunter.rank)
                        return (
                          <div
                            key={hunter.id}
                            className={`bg-gray-800/50 rounded-lg p-4 border transition-all duration-200 ${
                              hunter.username === user.username
                                ? 'border-cyber-green bg-cyber-green/5'
                                : 'border-gray-700 hover:border-gray-600'
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-4">
                                <div className="flex items-center space-x-2">
                                  <RankIcon className={`h-6 w-6 ${getRankColor(hunter.rank)}`} />
                                  <span className="text-2xl font-bold text-white">#{hunter.rank}</span>
                                </div>
                                <div className="flex items-center space-x-3">
                                  {hunter.avatar ? (
                                    <img src={hunter.avatar} alt={hunter.username} className="h-10 w-10 rounded-full" />
                                  ) : (
                                    <div className="h-10 w-10 rounded-full bg-cyber-green/20 flex items-center justify-center">
                                      <span className="text-cyber-green font-bold">
                                        {hunter.username.charAt(0).toUpperCase()}
                                      </span>
                                    </div>
                                  )}
                                  <div>
                                    <div className="flex items-center space-x-2">
                                      <h3 className="font-bold text-white">{hunter.username}</h3>
                                      {hunter.username === user.username && (
                                        <span className="px-2 py-1 bg-cyber-green/20 text-cyber-green rounded-full text-xs font-semibold">
                                          You
                                        </span>
                                      )}
                                    </div>
                                    <div className="flex items-center space-x-2 text-sm text-gray-400">
                                      <span>{hunter.country}</span>
                                      <span>•</span>
                                      <span>Joined {new Date(hunter.joinedAt).toLocaleDateString()}</span>
                                      <span>•</span>
                                      <span>Active {hunter.lastActive}</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="text-2xl font-bold cyber-text">{hunter.score.toLocaleString()}</div>
                                <div className="text-sm text-gray-400">points</div>
                              </div>
                            </div>

                            <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                              <div className="flex items-center space-x-2">
                                <Target className="h-4 w-4 text-blue-400" />
                                <span className="text-gray-400">Scans:</span>
                                <span className="text-white font-semibold">{hunter.totalScans.toLocaleString()}</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Bug className="h-4 w-4 text-red-400" />
                                <span className="text-gray-400">Vulns:</span>
                                <span className="text-white font-semibold">{hunter.vulnerabilitiesFound}</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Shield className="h-4 w-4 text-green-400" />
                                <span className="text-gray-400">Reports:</span>
                                <span className="text-white font-semibold">{hunter.validReports}</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Zap className="h-4 w-4 text-purple-400" />
                                <span className="text-gray-400">API:</span>
                                <span className="text-white font-semibold">{hunter.apiUsage.toLocaleString()}</span>
                              </div>
                            </div>

                            {hunter.badges.length > 0 && (
                              <div className="mt-4 flex flex-wrap gap-2">
                                {hunter.badges.map((badge, idx) => (
                                  <span
                                    key={idx}
                                    className={`px-2 py-1 rounded-full text-xs font-semibold ${getBadgeColor(badge)}`}
                                  >
                                    {badge}
                                  </span>
                                ))}
                              </div>
                            )}
                          </div>
                        )
                      })}
                    </div>
                  </div>
                </Card>
              </div>

              {/* Scoring System */}
              <div>
                <Card>
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-white mb-4">Scoring System</h3>
                    <div className="space-y-3">
                      <div className="p-3 bg-gray-800/50 rounded-lg">
                        <div className="font-medium text-white">Valid Report</div>
                        <div className="text-sm text-gray-400">+100 points</div>
                      </div>
                      <div className="p-3 bg-gray-800/50 rounded-lg">
                        <div className="font-medium text-white">Critical Vuln</div>
                        <div className="text-sm text-gray-400">+50 points</div>
                      </div>
                      <div className="p-3 bg-gray-800/50 rounded-lg">
                        <div className="font-medium text-white">High Vuln</div>
                        <div className="text-sm text-gray-400">+30 points</div>
                      </div>
                      <div className="p-3 bg-gray-800/50 rounded-lg">
                        <div className="font-medium text-white">Successful Scan</div>
                        <div className="text-sm text-gray-400">+5 points</div>
                      </div>
                      <div className="p-3 bg-gray-800/50 rounded-lg">
                        <div className="font-medium text-white">API Usage</div>
                        <div className="text-sm text-gray-400">+1 per 100 calls</div>
                      </div>
                    </div>
                  </div>
                </Card>

                <Card className="mt-6" border="gold">
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-white mb-4">Your Rank</h3>
                    <div className="text-center">
                      <div className="text-3xl font-bold cyber-text mb-2">#4</div>
                      <div className="text-gray-400 mb-4">out of {stats.totalHunters.toLocaleString()} hunters</div>
                      <div className="text-sm text-gray-300">
                        You need <span className="text-cyber-green font-semibold">1,380 points</span> to reach rank #3
                      </div>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          )}

          {/* Community Tab */}
          {activeTab === 'community' && (
            <div className="grid lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <Card border="green" glow>
                  <div className="p-6">
                    <h2 className="text-xl font-bold text-white mb-6">Community Groups</h2>
                    <div className="space-y-4">
                      {communityGroups.map((group) => (
                        <div
                          key={group.id}
                          className="bg-gray-800/50 rounded-lg p-4 border border-gray-700 hover:border-gray-600 transition-colors"
                        >
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex items-center space-x-3">
                              {group.type === 'whatsapp' ? (
                                <MessageCircle className="h-6 w-6 text-green-400" />
                              ) : (
                                <MessageCircle className="h-6 w-6 text-blue-400" />
                              )}
                              <div>
                                <div className="flex items-center space-x-2">
                                  <h3 className="font-bold text-white">{group.name}</h3>
                                  {group.isPrivate && (
                                    <span className="px-2 py-1 bg-orange-900/50 text-orange-400 rounded-full text-xs font-semibold">
                                      Private
                                    </span>
                                  )}
                                </div>
                                <div className="flex items-center space-x-2 text-sm text-gray-400">
                                  <Users className="h-3 w-3" />
                                  <span>{group.members.toLocaleString()} members</span>
                                  <span>•</span>
                                  <span className="capitalize">{group.type}</span>
                                </div>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="text-sm text-gray-400">Last activity</div>
                              <div className="text-sm text-white">{group.lastActivity}</div>
                            </div>
                          </div>

                          <p className="text-gray-300 mb-4">{group.description}</p>

                          <div className="flex items-center justify-between">
                            <div className="text-sm text-gray-400">
                              {group.isPrivate ? 'Invitation required' : 'Open to join'}
                            </div>
                            {group.inviteLink && !group.isPrivate && (
                              <a
                                href={group.inviteLink}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="cyber-btn text-sm"
                              >
                                <ExternalLink className="h-4 w-4 mr-2" />
                                Join Group
                              </a>
                            )}
                            {group.isPrivate && (
                              <button className="cyber-btn text-sm" disabled>
                                Request Access
                              </button>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </Card>
              </div>

              {/* Community Stats */}
              <div>
                <Card>
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-white mb-4">Community Stats</h3>
                    <div className="space-y-4">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Total Groups</span>
                        <span className="text-white font-semibold">12</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">WhatsApp Groups</span>
                        <span className="text-white font-semibold">5</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Telegram Groups</span>
                        <span className="text-white font-semibold">7</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Private Groups</span>
                        <span className="text-white font-semibold">3</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Active Members</span>
                        <span className="text-cyber-green font-semibold">2,847</span>
                      </div>
                    </div>
                  </div>
                </Card>

                <Card className="mt-6">
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-white mb-4">Group Guidelines</h3>
                    <div className="space-y-2 text-sm text-gray-300">
                      <p>• Be respectful to all members</p>
                      <p>• Share knowledge and help others</p>
                      <p>• No spam or self-promotion</p>
                      <p>• Follow responsible disclosure</p>
                      <p>• Keep discussions on-topic</p>
                      <p>• Report violations to admins</p>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
