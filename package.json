{"name": "kodexguard", "version": "1.0.0", "private": true, "description": "KodeXGuard - Platform Cybersecurity & Bug Hunting", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "db:init": "bun run lib/init-database.ts", "db:reset": "bun run lib/init-database.ts", "db:seed": "bun run lib/init-database.ts"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "typescript": "^5.0.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "mysql2": "^3.6.5", "redis": "^4.6.10", "@elastic/elasticsearch": "^8.11.0", "axios": "^1.6.0", "zod": "^3.22.4", "date-fns": "^2.30.0", "react-hook-form": "^7.47.0", "@hookform/resolvers": "^3.3.2", "venom-bot": "^5.0.0", "node-telegram-bot-api": "^0.64.0", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "crypto-js": "^4.2.0"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/multer": "^1.4.11", "@types/crypto-js": "^4.2.1", "@types/node-telegram-bot-api": "^0.64.7", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0"}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}}