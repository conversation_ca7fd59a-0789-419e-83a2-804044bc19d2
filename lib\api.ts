// KodeXGuard API Client Library
// Comprehensive API client with authentication and error handling

import { API_BASE_URL, HTTP_STATUS, ERROR_CODES } from '@/constants/endpoints'
import type { ApiResponse, PaginatedResponse } from '@/types'

export interface ApiClientConfig {
  baseURL?: string
  apiKey?: string
  token?: string
  timeout?: number
}

export interface RequestOptions {
  headers?: Record<string, string>
  timeout?: number
  retries?: number
}

export class ApiClient {
  private baseURL: string
  private apiKey?: string
  private token?: string
  private timeout: number

  constructor(config: ApiClientConfig = {}) {
    this.baseURL = config.baseURL || API_BASE_URL
    this.apiKey = config.apiKey
    this.token = config.token
    this.timeout = config.timeout || 30000
  }

  // Set authentication token
  setToken(token: string): void {
    this.token = token
  }

  // Set API key
  setApiKey(apiKey: string): void {
    this.apiKey = apiKey
  }

  // Build headers
  private buildHeaders(options?: RequestOptions): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...options?.headers
    }

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`
    }

    if (this.apiKey) {
      headers['X-API-Key'] = this.apiKey
    }

    return headers
  }

  // Handle API response
  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    const contentType = response.headers.get('content-type')
    const isJson = contentType?.includes('application/json')

    let data: any
    if (isJson) {
      data = await response.json()
    } else {
      data = await response.text()
    }

    if (!response.ok) {
      throw new ApiError(
        data.error || `HTTP ${response.status}`,
        response.status,
        data.code || ERROR_CODES.EXTERNAL_SERVICE_ERROR,
        data
      )
    }

    return data
  }

  // Generic request method
  private async request<T>(
    method: string,
    endpoint: string,
    data?: any,
    options?: RequestOptions
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`
    const headers = this.buildHeaders(options)

    const config: RequestInit = {
      method,
      headers
    }

    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      if (data instanceof FormData) {
        delete headers['Content-Type'] // Let browser set it for FormData
        config.body = data
      } else {
        config.body = JSON.stringify(data)
      }
    }

    try {
      const response = await fetch(url, config)
      return await this.handleResponse<T>(response)
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      throw new ApiError(
        'Network error or timeout',
        0,
        ERROR_CODES.EXTERNAL_SERVICE_ERROR,
        error
      )
    }
  }

  // HTTP methods
  async get<T>(endpoint: string, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.request<T>('GET', endpoint, undefined, options)
  }

  async post<T>(endpoint: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.request<T>('POST', endpoint, data, options)
  }

  async put<T>(endpoint: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.request<T>('PUT', endpoint, data, options)
  }

  async patch<T>(endpoint: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.request<T>('PATCH', endpoint, data, options)
  }

  async delete<T>(endpoint: string, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.request<T>('DELETE', endpoint, undefined, options)
  }

  // File upload
  async upload<T>(endpoint: string, file: File, additionalData?: Record<string, any>): Promise<ApiResponse<T>> {
    const formData = new FormData()
    formData.append('file', file)

    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, value)
      })
    }

    return this.request<T>('POST', endpoint, formData)
  }
}

// Custom API Error class
export class ApiError extends Error {
  public status: number
  public code: string
  public details: any

  constructor(message: string, status: number, code: string, details?: any) {
    super(message)
    this.name = 'ApiError'
    this.status = status
    this.code = code
    this.details = details
  }
}

// Default API client instance
export const api = new ApiClient()

// Specialized API clients for different services
export class AuthAPI extends ApiClient {
  async login(email: string, password: string) {
    return this.post('/auth/login', { email, password })
  }

  async register(userData: any) {
    return this.post('/auth/register', userData)
  }

  async logout() {
    return this.post('/auth/logout')
  }

  async refreshToken() {
    return this.post('/auth/refresh')
  }

  async changePassword(currentPassword: string, newPassword: string) {
    return this.post('/auth/change-password', { currentPassword, newPassword })
  }
}

export class ScanAPI extends ApiClient {
  async startScan(scanData: any) {
    return this.post('/scan/vulnerability', scanData)
  }

  async getScanResults(scanId: string) {
    return this.get(`/scan/results/${scanId}`)
  }

  async getScanHistory(page = 1, limit = 20) {
    return this.get(`/scan/history?page=${page}&limit=${limit}`)
  }

  async cancelScan(scanId: string) {
    return this.post(`/scan/cancel/${scanId}`)
  }
}

export class OSINTAPI extends ApiClient {
  async search(searchData: any) {
    return this.post('/osint/search', searchData)
  }

  async trackLocation(target: string, type: string) {
    return this.post('/osint/track', { target, type })
  }

  async getHistory(page = 1, limit = 20) {
    return this.get(`/osint/history?page=${page}&limit=${limit}`)
  }
}

export class FileAPI extends ApiClient {
  async analyzeFile(file: File, analysisType?: string) {
    return this.upload('/file/analyze', file, { analysisType })
  }

  async getAnalysisResults(analysisId: string) {
    return this.get(`/file/results/${analysisId}`)
  }

  async getAnalysisHistory(page = 1, limit = 20) {
    return this.get(`/file/history?page=${page}&limit=${limit}`)
  }
}

export class CVEAPI extends ApiClient {
  async searchCVE(query?: string, filters?: any) {
    const params = new URLSearchParams()
    if (query) params.append('query', query)
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        params.append(key, String(value))
      })
    }
    return this.get(`/cve/search?${params.toString()}`)
  }

  async getCVE(cveId: string) {
    return this.get(`/cve/${cveId}`)
  }

  async getLatestCVEs(limit = 20) {
    return this.get(`/cve/latest?limit=${limit}`)
  }
}

// Export specialized API instances
export const authAPI = new AuthAPI()
export const scanAPI = new ScanAPI()
export const osintAPI = new OSINTAPI()
export const fileAPI = new FileAPI()
export const cveAPI = new CVEAPI()

export default api
