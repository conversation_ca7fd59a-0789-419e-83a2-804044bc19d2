import { NextRequest, NextResponse } from 'next/server'

export async function GET(
  request: NextRequest,
  { params }: { params: { scanId: string } }
) {
  try {
    const { scanId } = params

    if (!scanId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Scan ID is required',
          code: 'MISSING_SCAN_ID'
        },
        { status: 400 }
      )
    }

    // Check if scan exists in memory (in real app, query database)
    const scan = global.activeScan

    if (!scan || scan.scanId !== scanId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Scan not found',
          code: 'SCAN_NOT_FOUND'
        },
        { status: 404 }
      )
    }

    // Return scan results based on status
    if (scan.status === 'started' || scan.status === 'running') {
      return NextResponse.json({
        success: true,
        data: {
          scanId: scan.scanId,
          status: scan.status,
          target: scan.target,
          progress: scan.progress || 0,
          startTime: scan.startTime,
          scanTypes: scan.scanTypes
        },
        message: '<PERSON>an is still in progress',
        timestamp: new Date().toISOString()
      })
    }

    if (scan.status === 'completed') {
      return NextResponse.json({
        success: true,
        data: {
          scanId: scan.scanId,
          status: scan.status,
          target: scan.target,
          startTime: scan.startTime,
          endTime: scan.endTime,
          scanTypes: scan.scanTypes,
          vulnerabilities: scan.vulnerabilities || [],
          summary: scan.summary || {
            total: 0,
            critical: 0,
            high: 0,
            medium: 0,
            low: 0
          }
        },
        message: 'Scan completed successfully',
        timestamp: new Date().toISOString()
      })
    }

    if (scan.status === 'failed') {
      return NextResponse.json({
        success: true,
        data: {
          scanId: scan.scanId,
          status: scan.status,
          target: scan.target,
          startTime: scan.startTime,
          endTime: scan.endTime,
          error: scan.error || 'Scan failed due to unknown error'
        },
        message: 'Scan failed',
        timestamp: new Date().toISOString()
      })
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'Invalid scan status',
        code: 'INVALID_STATUS'
      },
      { status: 500 }
    )

  } catch (error) {
    console.error('Get scan results error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
