// KodeXGuard Vulnerability Scanner Engine
// Advanced vulnerability detection and exploitation framework

import axios from 'axios'
import { db } from './database'
import type { Vulnerability, VulnerabilityType, Severity, ScanHistory } from '@/types'

export interface ScanRequest {
  target: string
  scanTypes: VulnerabilityType[]
  maxDepth?: number
  timeout?: number
  followRedirects?: boolean
  userId: number
}

export interface ScanResult {
  scanId: string
  target: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  vulnerabilities: Vulnerability[]
  summary: {
    total: number
    critical: number
    high: number
    medium: number
    low: number
    info: number
  }
  scanDuration: number
  startTime: Date
  endTime?: Date
}

export interface VulnerabilityTest {
  name: string
  type: VulnerabilityType
  payloads: string[]
  patterns: RegExp[]
  severity: Severity
  description: string
  recommendation: string
}

export class VulnerabilityScanner {
  private scanTests: Map<VulnerabilityType, VulnerabilityTest[]> = new Map()
  private activeScan: Map<string, any> = new Map()

  constructor() {
    this.initializeScanTests()
  }

  // Initialize vulnerability tests
  private initializeScanTests(): void {
    // SQL Injection Tests
    this.scanTests.set('sqli', [
      {
        name: 'Basic SQL Injection',
        type: 'sqli',
        payloads: [
          "' OR '1'='1",
          "' OR 1=1--",
          "' UNION SELECT NULL--",
          "'; DROP TABLE users--",
          "' AND (SELECT COUNT(*) FROM information_schema.tables)>0--"
        ],
        patterns: [
          /mysql_fetch_array/i,
          /ORA-\d{5}/i,
          /Microsoft.*ODBC.*SQL Server/i,
          /PostgreSQL.*ERROR/i,
          /Warning.*mysql_/i,
          /valid MySQL result/i,
          /MySqlClient\./i
        ],
        severity: 'critical',
        description: 'SQL Injection vulnerability allows attackers to manipulate database queries',
        recommendation: 'Use parameterized queries and input validation'
      }
    ])

    // XSS Tests
    this.scanTests.set('xss', [
      {
        name: 'Cross-Site Scripting',
        type: 'xss',
        payloads: [
          '<script>alert("XSS")</script>',
          '<img src=x onerror=alert("XSS")>',
          '<svg onload=alert("XSS")>',
          'javascript:alert("XSS")',
          '"><script>alert("XSS")</script>'
        ],
        patterns: [
          /<script[^>]*>.*?<\/script>/i,
          /javascript:/i,
          /on\w+\s*=/i
        ],
        severity: 'high',
        description: 'Cross-Site Scripting allows execution of malicious scripts',
        recommendation: 'Implement proper input sanitization and output encoding'
      }
    ])

    // LFI Tests
    this.scanTests.set('lfi', [
      {
        name: 'Local File Inclusion',
        type: 'lfi',
        payloads: [
          '../../../etc/passwd',
          '..\\..\\..\\windows\\system32\\drivers\\etc\\hosts',
          '/etc/passwd',
          'C:\\windows\\system32\\drivers\\etc\\hosts',
          '....//....//....//etc/passwd'
        ],
        patterns: [
          /root:.*:0:0:/,
          /\[drivers\]/i,
          /# Copyright \(c\) 1993-2009 Microsoft Corp/i
        ],
        severity: 'high',
        description: 'Local File Inclusion allows reading sensitive files',
        recommendation: 'Validate and sanitize file path inputs'
      }
    ])

    // RCE Tests
    this.scanTests.set('rce', [
      {
        name: 'Remote Code Execution',
        type: 'rce',
        payloads: [
          '; ls -la',
          '| whoami',
          '`id`',
          '$(whoami)',
          '; cat /etc/passwd',
          '& dir',
          '| type C:\\windows\\system32\\drivers\\etc\\hosts'
        ],
        patterns: [
          /uid=\d+\(.*?\)/,
          /gid=\d+\(.*?\)/,
          /root:.*:0:0:/,
          /Volume.*Serial Number/i,
          /Directory of/i
        ],
        severity: 'critical',
        description: 'Remote Code Execution allows arbitrary command execution',
        recommendation: 'Avoid system calls with user input, use whitelisting'
      }
    ])

    // Path Traversal Tests
    this.scanTests.set('path_traversal', [
      {
        name: 'Path Traversal',
        type: 'path_traversal',
        payloads: [
          '../',
          '..\\',
          '..../',
          '....\\',
          '%2e%2e%2f',
          '%2e%2e%5c',
          '..%2f',
          '..%5c'
        ],
        patterns: [
          /root:.*:0:0:/,
          /\[boot loader\]/i,
          /\[operating systems\]/i
        ],
        severity: 'medium',
        description: 'Path traversal allows access to files outside intended directory',
        recommendation: 'Implement proper path validation and canonicalization'
      }
    ])
  }

  // Start vulnerability scan
  async startScan(request: ScanRequest): Promise<{ scanId: string; status: string }> {
    const scanId = this.generateScanId()
    
    try {
      // Save scan to database
      await db.query(
        `INSERT INTO scan_history (user_id, scan_type, target, status, created_at)
         VALUES (?, 'vulnerability', ?, 'pending', NOW())`,
        [request.userId, request.target]
      )

      // Start scan in background
      this.executeScan(scanId, request).catch(error => {
        console.error(`Scan ${scanId} failed:`, error)
        this.updateScanStatus(scanId, 'failed')
      })

      return {
        scanId,
        status: 'started'
      }
    } catch (error) {
      console.error('Failed to start scan:', error)
      throw new Error('Failed to start scan')
    }
  }

  // Execute vulnerability scan
  private async executeScan(scanId: string, request: ScanRequest): Promise<void> {
    const startTime = Date.now()
    
    try {
      // Update status to running
      await this.updateScanStatus(scanId, 'running')

      const vulnerabilities: Vulnerability[] = []
      
      // Discover endpoints
      const endpoints = await this.discoverEndpoints(request.target, request.maxDepth || 3)
      
      // Test each vulnerability type
      for (const scanType of request.scanTypes) {
        const tests = this.scanTests.get(scanType) || []
        
        for (const test of tests) {
          for (const endpoint of endpoints) {
            const vulns = await this.testEndpoint(endpoint, test, request.timeout || 30000)
            vulnerabilities.push(...vulns)
          }
        }
      }

      // Calculate summary
      const summary = this.calculateSummary(vulnerabilities)
      const scanDuration = Date.now() - startTime

      // Save results
      const scanResult: ScanResult = {
        scanId,
        target: request.target,
        status: 'completed',
        vulnerabilities,
        summary,
        scanDuration,
        startTime: new Date(startTime),
        endTime: new Date()
      }

      await this.saveScanResults(scanResult, request.userId)
      
      // Update status
      await this.updateScanStatus(scanId, 'completed')
      
    } catch (error) {
      console.error(`Scan execution failed for ${scanId}:`, error)
      await this.updateScanStatus(scanId, 'failed')
    }
  }

  // Discover endpoints for scanning
  private async discoverEndpoints(target: string, maxDepth: number): Promise<string[]> {
    const endpoints: Set<string> = new Set()
    const baseUrl = new URL(target)
    
    // Add base URL
    endpoints.add(target)
    
    try {
      // Try to get robots.txt
      const robotsUrl = `${baseUrl.origin}/robots.txt`
      const robotsResponse = await axios.get(robotsUrl, { timeout: 10000 })
      
      if (robotsResponse.status === 200) {
        const robotsContent = robotsResponse.data
        const disallowMatches = robotsContent.match(/Disallow:\s*(.+)/gi)
        
        if (disallowMatches) {
          disallowMatches.forEach((match: string) => {
            const path = match.replace(/Disallow:\s*/i, '').trim()
            if (path && path !== '/') {
              endpoints.add(`${baseUrl.origin}${path}`)
            }
          })
        }
      }
    } catch (error) {
      // Robots.txt not found or error, continue
    }

    // Add common endpoints
    const commonPaths = [
      '/admin',
      '/login',
      '/api',
      '/search',
      '/contact',
      '/upload',
      '/download',
      '/user',
      '/profile'
    ]

    commonPaths.forEach(path => {
      endpoints.add(`${baseUrl.origin}${path}`)
    })

    return Array.from(endpoints).slice(0, 20) // Limit to 20 endpoints
  }

  // Test endpoint for vulnerabilities
  private async testEndpoint(endpoint: string, test: VulnerabilityTest, timeout: number): Promise<Vulnerability[]> {
    const vulnerabilities: Vulnerability[] = []
    
    try {
      for (const payload of test.payloads) {
        // Test GET parameters
        const getUrl = `${endpoint}?test=${encodeURIComponent(payload)}`
        const getVuln = await this.testRequest('GET', getUrl, null, test, timeout)
        if (getVuln) vulnerabilities.push(getVuln)

        // Test POST data
        const postVuln = await this.testRequest('POST', endpoint, { test: payload }, test, timeout)
        if (postVuln) vulnerabilities.push(postVuln)
      }
    } catch (error) {
      // Continue with next test
    }

    return vulnerabilities
  }

  // Test individual request
  private async testRequest(
    method: string,
    url: string,
    data: any,
    test: VulnerabilityTest,
    timeout: number
  ): Promise<Vulnerability | null> {
    try {
      const config: any = {
        method,
        url,
        timeout,
        validateStatus: () => true, // Don't throw on HTTP errors
        maxRedirects: 5
      }

      if (data && method === 'POST') {
        config.data = data
        config.headers = { 'Content-Type': 'application/x-www-form-urlencoded' }
      }

      const response = await axios(config)
      const responseText = response.data

      // Check for vulnerability patterns
      for (const pattern of test.patterns) {
        if (pattern.test(responseText)) {
          return {
            id: this.generateVulnId(),
            type: test.type,
            severity: test.severity,
            url,
            parameter: data ? Object.keys(data)[0] : 'query parameter',
            payload: data ? Object.values(data)[0] as string : new URL(url).searchParams.get('test') || '',
            evidence: responseText.substring(0, 500),
            recommendation: test.recommendation,
            description: test.description
          }
        }
      }

      return null
    } catch (error) {
      return null
    }
  }

  // Calculate vulnerability summary
  private calculateSummary(vulnerabilities: Vulnerability[]) {
    const summary = {
      total: vulnerabilities.length,
      critical: 0,
      high: 0,
      medium: 0,
      low: 0,
      info: 0
    }

    vulnerabilities.forEach(vuln => {
      summary[vuln.severity]++
    })

    return summary
  }

  // Save scan results to database
  private async saveScanResults(result: ScanResult, userId: number): Promise<void> {
    try {
      await db.query(
        `UPDATE scan_history 
         SET results = ?, status = ?, vulnerabilities_found = ?, scan_duration = ?, completed_at = NOW()
         WHERE user_id = ? AND target = ? AND status = 'running'`,
        [
          JSON.stringify(result),
          result.status,
          result.vulnerabilities.length,
          result.scanDuration,
          userId,
          result.target
        ]
      )
    } catch (error) {
      console.error('Failed to save scan results:', error)
    }
  }

  // Update scan status
  private async updateScanStatus(scanId: string, status: string): Promise<void> {
    try {
      // In a real implementation, you'd update by scanId
      // For now, we'll use a simple approach
      console.log(`Scan ${scanId} status updated to: ${status}`)
    } catch (error) {
      console.error('Failed to update scan status:', error)
    }
  }

  // Get scan results
  async getScanResults(scanId: string, userId: number): Promise<ScanResult | null> {
    try {
      const result = await db.queryOne<ScanHistory>(
        'SELECT * FROM scan_history WHERE user_id = ? ORDER BY created_at DESC LIMIT 1',
        [userId]
      )

      if (!result || !result.results) {
        return null
      }

      return JSON.parse(result.results as string)
    } catch (error) {
      console.error('Failed to get scan results:', error)
      return null
    }
  }

  // Get scan history
  async getScanHistory(userId: number, limit: number = 20): Promise<ScanHistory[]> {
    try {
      return await db.query<ScanHistory>(
        `SELECT * FROM scan_history 
         WHERE user_id = ? AND scan_type = 'vulnerability'
         ORDER BY created_at DESC 
         LIMIT ?`,
        [userId, limit]
      )
    } catch (error) {
      console.error('Failed to get scan history:', error)
      return []
    }
  }

  // Generate scan ID
  private generateScanId(): string {
    return 'scan_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }

  // Generate vulnerability ID
  private generateVulnId(): string {
    return 'vuln_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }
}

// Export singleton instance
export const vulnerabilityScanner = new VulnerabilityScanner()
export default VulnerabilityScanner
