"use strict";(()=>{var e={};e.id=3884,e.ids=[3884],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},24803:(e,t,s)=>{s.r(t),s.d(t,{originalPathname:()=>h,patchFetch:()=>x,requestAsyncStorage:()=>d,routeModule:()=>l,serverHooks:()=>m,staticGenerationAsyncStorage:()=>g});var r={};s.r(r),s.d(r,{GET:()=>u,PATCH:()=>p,PUT:()=>c});var a=s(49303),o=s(88716),n=s(60670),i=s(87070);async function u(e){try{let t=e.headers.get("authorization"),s=t?.replace("Bearer ","");if(!s)return i.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!s.startsWith("kxg_"))return i.NextResponse.json({success:!1,error:"Invalid token"},{status:401});return i.NextResponse.json({success:!0,data:{notifications:{email:!0,push:!0,scanComplete:!0,quotaWarning:!0,securityAlerts:!0,weeklyReport:!1,marketingEmails:!1},security:{twoFactorAuth:!1,sessionTimeout:24,ipWhitelist:["*************","*********"],loginNotifications:!0,passwordExpiry:90},preferences:{theme:"dark",language:"id",timezone:"Asia/Jakarta",dateFormat:"DD/MM/YYYY",timeFormat:"24h",autoSave:!0},api:{rateLimit:1e3,allowedOrigins:["https://kodexguard.com","https://app.kodexguard.com"],webhookUrl:"https://webhook.example.com/kodexguard",enableLogging:!0},privacy:{profileVisibility:"private",shareUsageStats:!1,allowAnalytics:!0,dataRetention:365}},timestamp:new Date().toISOString()})}catch(e){return console.error("User settings API error:",e),i.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}async function c(e){try{let t=e.headers.get("authorization"),s=t?.replace("Bearer ","");if(!s)return i.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!s.startsWith("kxg_"))return i.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let{notifications:r,security:a,preferences:o,api:n,privacy:u}=await e.json();if(!r&&!a&&!o&&!n&&!u)return i.NextResponse.json({success:!1,error:"At least one settings category is required"},{status:400});let c={notifications:r||{email:!0,push:!0,scanComplete:!0,quotaWarning:!0,securityAlerts:!0,weeklyReport:!1,marketingEmails:!1},security:a||{twoFactorAuth:!1,sessionTimeout:24,ipWhitelist:["*************"],loginNotifications:!0,passwordExpiry:90},preferences:o||{theme:"dark",language:"id",timezone:"Asia/Jakarta",dateFormat:"DD/MM/YYYY",timeFormat:"24h",autoSave:!0},api:n||{rateLimit:1e3,allowedOrigins:["https://kodexguard.com"],enableLogging:!0},privacy:u||{profileVisibility:"private",shareUsageStats:!1,allowAnalytics:!0,dataRetention:365},updatedAt:new Date().toISOString()};return i.NextResponse.json({success:!0,data:c,message:"Settings updated successfully",timestamp:new Date().toISOString()})}catch(e){return console.error("Update settings error:",e),i.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}async function p(e){try{let t=e.headers.get("authorization"),s=t?.replace("Bearer ","");if(!s)return i.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!s.startsWith("kxg_"))return i.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let{category:r,setting:a,value:o}=await e.json();if(!r||!a||void 0===o)return i.NextResponse.json({success:!1,error:"Category, setting, and value are required"},{status:400});return i.NextResponse.json({success:!0,message:`Setting ${r}.${a} updated to ${o}`,data:{category:r,setting:a,value:o,updatedAt:new Date().toISOString()},timestamp:new Date().toISOString()})}catch(e){return console.error("Patch setting error:",e),i.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/user/settings/route",pathname:"/api/user/settings",filename:"route",bundlePath:"app/api/user/settings/route"},resolvedPagePath:"D:\\Users\\Downloads\\kodeXGuard\\app\\api\\user\\settings\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:d,staticGenerationAsyncStorage:g,serverHooks:m}=l,h="/api/user/settings/route";function x(){return(0,n.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:g})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[9276,5972],()=>s(24803));module.exports=r})();