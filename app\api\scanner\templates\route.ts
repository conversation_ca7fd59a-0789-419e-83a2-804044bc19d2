import { NextRequest, NextResponse } from 'next/server'

interface ScanTemplate {
  id: string
  name: string
  description: string
  category: 'web' | 'api' | 'mobile' | 'network' | 'custom'
  scanTypes: string[]
  settings: {
    maxDepth?: number
    timeout?: number
    threads?: number
    userAgent?: string
    headers?: Record<string, string>
    excludePaths?: string[]
    includePaths?: string[]
  }
  isDefault: boolean
  createdAt: string
  updatedAt: string
  usageCount: number
}

export async function GET(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Simple token validation for testing
    if (!token.startsWith('kxg_')) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    // Mock scan templates data
    const templates: ScanTemplate[] = [
      {
        id: 'template_001',
        name: 'Quick Web Scan',
        description: 'Fast vulnerability scan for web applications covering common security issues',
        category: 'web',
        scanTypes: ['sqli', 'xss', 'csrf', 'directory_traversal'],
        settings: {
          maxDepth: 3,
          timeout: 30,
          threads: 5,
          userAgent: 'KodeXGuard Scanner v1.0'
        },
        isDefault: true,
        createdAt: new Date(Date.now() - 86400000 * 30).toISOString(), // 30 days ago
        updatedAt: new Date(Date.now() - 86400000 * 5).toISOString(), // 5 days ago
        usageCount: 156
      },
      {
        id: 'template_002',
        name: 'Comprehensive Web Audit',
        description: 'Deep security audit covering all OWASP Top 10 vulnerabilities and more',
        category: 'web',
        scanTypes: ['sqli', 'xss', 'csrf', 'xxe', 'broken_auth', 'sensitive_data', 'xml_injection', 'broken_access', 'security_misconfig', 'vulnerable_components'],
        settings: {
          maxDepth: 10,
          timeout: 120,
          threads: 10,
          userAgent: 'Mozilla/5.0 (compatible; KodeXGuard/1.0)',
          excludePaths: ['/admin/logout', '/user/delete']
        },
        isDefault: true,
        createdAt: new Date(Date.now() - 86400000 * 45).toISOString(),
        updatedAt: new Date(Date.now() - 86400000 * 2).toISOString(),
        usageCount: 89
      },
      {
        id: 'template_003',
        name: 'API Security Scan',
        description: 'Specialized scan for REST APIs and GraphQL endpoints',
        category: 'api',
        scanTypes: ['api_security', 'auth_bypass', 'rate_limiting', 'input_validation', 'data_exposure'],
        settings: {
          timeout: 60,
          threads: 8,
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        },
        isDefault: true,
        createdAt: new Date(Date.now() - 86400000 * 20).toISOString(),
        updatedAt: new Date(Date.now() - 86400000 * 1).toISOString(),
        usageCount: 67
      },
      {
        id: 'template_004',
        name: 'Mobile App Security',
        description: 'Security assessment for mobile applications and APIs',
        category: 'mobile',
        scanTypes: ['mobile_security', 'ssl_pinning', 'data_storage', 'crypto_implementation'],
        settings: {
          timeout: 90,
          threads: 6,
          userAgent: 'KodeXGuard Mobile Scanner'
        },
        isDefault: true,
        createdAt: new Date(Date.now() - 86400000 * 15).toISOString(),
        updatedAt: new Date(Date.now() - 86400000 * 3).toISOString(),
        usageCount: 34
      },
      {
        id: 'template_005',
        name: 'Network Infrastructure',
        description: 'Network security scan including port scanning and service enumeration',
        category: 'network',
        scanTypes: ['port_scan', 'service_enum', 'ssl_check', 'dns_enum'],
        settings: {
          timeout: 300,
          threads: 20
        },
        isDefault: true,
        createdAt: new Date(Date.now() - 86400000 * 60).toISOString(),
        updatedAt: new Date(Date.now() - 86400000 * 7).toISOString(),
        usageCount: 123
      },
      {
        id: 'template_006',
        name: 'E-commerce Security',
        description: 'Custom template for e-commerce platforms with payment security focus',
        category: 'custom',
        scanTypes: ['sqli', 'xss', 'payment_security', 'session_management', 'pci_compliance'],
        settings: {
          maxDepth: 5,
          timeout: 60,
          threads: 8,
          excludePaths: ['/payment/process', '/checkout/complete'],
          includePaths: ['/shop', '/cart', '/checkout', '/payment']
        },
        isDefault: false,
        createdAt: new Date(Date.now() - 86400000 * 10).toISOString(),
        updatedAt: new Date(Date.now() - 86400000 * 1).toISOString(),
        usageCount: 45
      }
    ]

    // Sort by usage count (most used first)
    templates.sort((a, b) => b.usageCount - a.usageCount)

    return NextResponse.json({
      success: true,
      data: templates,
      meta: {
        total: templates.length,
        default: templates.filter(t => t.isDefault).length,
        custom: templates.filter(t => !t.isDefault).length,
        categories: {
          web: templates.filter(t => t.category === 'web').length,
          api: templates.filter(t => t.category === 'api').length,
          mobile: templates.filter(t => t.category === 'mobile').length,
          network: templates.filter(t => t.category === 'network').length,
          custom: templates.filter(t => t.category === 'custom').length
        }
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Scanner templates API error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Simple token validation for testing
    if (!token.startsWith('kxg_')) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    const body = await request.json()
    const { name, description, category, scanTypes, settings } = body

    // Validate input
    if (!name || !description || !category || !scanTypes || !Array.isArray(scanTypes)) {
      return NextResponse.json({
        success: false,
        error: 'Name, description, category, and scanTypes are required'
      }, { status: 400 })
    }

    // Create new template
    const newTemplate: ScanTemplate = {
      id: `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name,
      description,
      category,
      scanTypes,
      settings: settings || {},
      isDefault: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      usageCount: 0
    }

    // In a real app, save to database
    // For demo, just return the created template
    
    return NextResponse.json({
      success: true,
      data: newTemplate,
      message: 'Template created successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Create template error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}
