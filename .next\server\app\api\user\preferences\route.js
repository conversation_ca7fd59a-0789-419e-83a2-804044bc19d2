"use strict";(()=>{var e={};e.id=826,e.ids=[826],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},52174:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>m,patchFetch:()=>f,requestAsyncStorage:()=>p,routeModule:()=>d,serverHooks:()=>h,staticGenerationAsyncStorage:()=>l});var a={};t.r(a),t.d(a,{GET:()=>c,PUT:()=>u});var s=t(49303),n=t(88716),o=t(60670),i=t(87070);async function c(e){try{let r=e.headers.get("authorization"),t=r?.replace("Bearer ","");if(!t)return i.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!t.startsWith("kxg_"))return i.NextResponse.json({success:!1,error:"Invalid token"},{status:401});return i.NextResponse.json({success:!0,data:{general:{language:"id",timezone:"Asia/Jakarta",theme:"dark",dateFormat:"DD/MM/YYYY",timeFormat:"24h",currency:"IDR",autoSave:!0,compactMode:!1},dashboard:{defaultView:"overview",showWelcomeMessage:!0,refreshInterval:30,chartsType:"line",showQuickActions:!0,recentItemsCount:10},notifications:{desktop:!0,sound:!0,vibration:!1,emailDigest:"daily",pushFrequency:"immediate"},scanning:{defaultScanType:["sqli","xss","csrf"],autoStartScans:!1,saveReports:!0,reportFormat:"pdf",maxConcurrentScans:3,defaultTimeout:300},osint:{defaultSearchTypes:["email","phone","name"],autoSaveSearches:!0,confidenceThreshold:70,maxResults:50,includeBreaches:!0,includeSocialMedia:!0},privacy:{shareUsageData:!1,allowCookies:!0,trackingOptOut:!1,dataRetentionDays:365},accessibility:{highContrast:!1,largeText:!1,reducedMotion:!1,screenReader:!1,keyboardNavigation:!0}},timestamp:new Date().toISOString()})}catch(e){return console.error("User preferences API error:",e),i.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}async function u(e){try{let r=e.headers.get("authorization"),t=r?.replace("Bearer ","");if(!t)return i.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!t.startsWith("kxg_"))return i.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let{general:a,dashboard:s,notifications:n,scanning:o,osint:c,privacy:u,accessibility:d}=await e.json();if(!a&&!s&&!n&&!o&&!c&&!u&&!d)return i.NextResponse.json({success:!1,error:"At least one preference category is required"},{status:400});let p={general:a||{language:"id",timezone:"Asia/Jakarta",theme:"dark",dateFormat:"DD/MM/YYYY",timeFormat:"24h",currency:"IDR",autoSave:!0,compactMode:!1},dashboard:s||{defaultView:"overview",showWelcomeMessage:!0,refreshInterval:30,chartsType:"line",showQuickActions:!0,recentItemsCount:10},notifications:n||{desktop:!0,sound:!0,vibration:!1,emailDigest:"daily",pushFrequency:"immediate"},scanning:o||{defaultScanType:["sqli","xss","csrf"],autoStartScans:!1,saveReports:!0,reportFormat:"pdf",maxConcurrentScans:3,defaultTimeout:300},osint:c||{defaultSearchTypes:["email","phone","name"],autoSaveSearches:!0,confidenceThreshold:70,maxResults:50,includeBreaches:!0,includeSocialMedia:!0},privacy:u||{shareUsageData:!1,allowCookies:!0,trackingOptOut:!1,dataRetentionDays:365},accessibility:d||{highContrast:!1,largeText:!1,reducedMotion:!1,screenReader:!1,keyboardNavigation:!0},updatedAt:new Date().toISOString()};return i.NextResponse.json({success:!0,data:p,message:"Preferences updated successfully",timestamp:new Date().toISOString()})}catch(e){return console.error("Update preferences error:",e),i.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let d=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/user/preferences/route",pathname:"/api/user/preferences",filename:"route",bundlePath:"app/api/user/preferences/route"},resolvedPagePath:"D:\\Users\\Downloads\\kodeXGuard\\app\\api\\user\\preferences\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:p,staticGenerationAsyncStorage:l,serverHooks:h}=d,m="/api/user/preferences/route";function f(){return(0,o.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:l})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[216,592],()=>t(52174));module.exports=a})();