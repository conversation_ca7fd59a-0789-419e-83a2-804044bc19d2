(()=>{var e={};e.id=2383,e.ids=[2383],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},23374:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c}),s(96612),s(30829),s(35866);var a=s(23191),r=s(88716),i=s(37922),l=s.n(i),n=s(95231),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(t,d);let c=["",{children:["playground",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,96612)),"D:\\Users\\Downloads\\kodeXGuard\\app\\playground\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\Users\\Downloads\\kodeXGuard\\app\\playground\\page.tsx"],m="/playground/page",p={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/playground/page",pathname:"/playground",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},99038:(e,t,s)=>{Promise.resolve().then(s.bind(s,32708))},32708:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v});var a=s(10326),r=s(17577),i=s(83061),l=s(2262),n=s(92498),d=s(54014),c=s(88319),o=s(31215),m=s(3634),p=s(94893),x=s(6343),h=s(21405),u=s(43810),y=s(76828),g=s(33734),b=s(70003),j=s(98091);function v(){let[e]=(0,r.useState)({username:"admin",avatar:"",role:"super_admin",plan:"cybersecurity"}),[t,s]=(0,r.useState)(null),[v,N]=(0,r.useState)("GET"),[f,w]=(0,r.useState)(""),[k,q]=(0,r.useState)('{\n  "Authorization": "Bearer your-api-key",\n  "Content-Type": "application/json"\n}'),[S,Z]=(0,r.useState)("{}"),[P,T]=(0,r.useState)(""),[A,C]=(0,r.useState)(!1),[E,I]=(0,r.useState)("playground"),[D,O]=(0,r.useState)([]),R=[{id:"1",method:"POST",path:"/api/auth/login",description:"Authenticate user and get access token",category:"Authentication",requiresAuth:!1,parameters:[{name:"email",type:"string",required:!0,description:"User email address"},{name:"password",type:"string",required:!0,description:"User password"}],example:{request:{email:"<EMAIL>",password:"admin123"},response:{token:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",user:{id:"1",username:"admin",role:"super_admin"}}}},{id:"2",method:"GET",path:"/api/user/profile",description:"Get current user profile information",category:"User",requiresAuth:!0,parameters:[],example:{request:{},response:{id:"1",username:"admin",email:"<EMAIL>",plan:"cybersecurity",stats:{totalScans:1247}}}},{id:"3",method:"POST",path:"/api/scan/vulnerability",description:"Start a new vulnerability scan",category:"Scanning",requiresAuth:!0,parameters:[{name:"target",type:"string",required:!0,description:"Target URL to scan"},{name:"scanTypes",type:"array",required:!0,description:"Types of vulnerabilities to scan for"},{name:"maxDepth",type:"number",required:!1,description:"Maximum crawl depth (default: 3)"}],example:{request:{target:"https://example.com",scanTypes:["sqli","xss","lfi"],maxDepth:3},response:{scanId:"scan_123456",status:"started",estimatedTime:"5-10 minutes"}}},{id:"4",method:"GET",path:"/api/scan/{scanId}/results",description:"Get vulnerability scan results",category:"Scanning",requiresAuth:!0,parameters:[{name:"scanId",type:"string",required:!0,description:"Scan ID from scan initiation"}],example:{request:{},response:{scanId:"scan_123456",status:"completed",vulnerabilities:[{type:"sqli",severity:"high",url:"https://example.com/login"}]}}},{id:"5",method:"POST",path:"/api/osint/search",description:"Perform OSINT investigation",category:"OSINT",requiresAuth:!0,parameters:[{name:"query",type:"string",required:!0,description:"Search query (email, phone, domain, etc.)"},{name:"type",type:"string",required:!0,description:"Search type (email, phone, nik, domain, etc.)"},{name:"deepSearch",type:"boolean",required:!1,description:"Enable deep search (default: false)"}],example:{request:{query:"<EMAIL>",type:"email",deepSearch:!0},response:{results:[{source:"Dukcapil",data:{name:"John Doe",verified:!0},confidence:95}]}}},{id:"6",method:"POST",path:"/api/file/analyze",description:"Analyze uploaded file for threats",category:"File Analysis",requiresAuth:!0,parameters:[{name:"file",type:"file",required:!0,description:"File to analyze (multipart/form-data)"},{name:"analysisType",type:"string",required:!1,description:"Type of analysis (malware, webshell, secret, general)"}],example:{request:{file:"suspicious.php",analysisType:"webshell"},response:{analysisId:"analysis_789",threatLevel:"suspicious",results:{webshellDetected:!0,type:"PHP Webshell"}}}},{id:"7",method:"GET",path:"/api/cve/search",description:"Search CVE database",category:"CVE Intelligence",requiresAuth:!0,parameters:[{name:"query",type:"string",required:!1,description:"Search query"},{name:"severity",type:"string",required:!1,description:"Filter by severity (critical, high, medium, low)"},{name:"year",type:"string",required:!1,description:"Filter by year"},{name:"limit",type:"number",required:!1,description:"Number of results (default: 20)"}],example:{request:{query:"SQL injection",severity:"critical",limit:10},response:{total:156,cves:[{cveId:"CVE-2024-0001",severity:"critical",cvssScore:9.8,description:"SQL injection vulnerability..."}]}}}],G=async()=>{C(!0),T("");try{await new Promise(e=>setTimeout(e,1500));let e={status:200,statusText:"OK",headers:{"Content-Type":"application/json","X-RateLimit-Remaining":"99","X-Response-Time":"142ms"},data:t?.example.response||{message:"Success",timestamp:new Date().toISOString()}};T(JSON.stringify(e,null,2))}catch(e){T(JSON.stringify({status:500,error:"Internal Server Error",message:"An error occurred while processing your request"},null,2))}finally{C(!1)}},M=e=>{N(e.method),w(e.url),q(JSON.stringify(e.headers,null,2)),Z(e.body),I("playground")},_=e=>{O(t=>t.map(t=>t.id===e?{...t,favorite:!t.favorite}:t))},U=e=>{O(t=>t.filter(t=>t.id!==e))},z=e=>{navigator.clipboard.writeText(e)},L=[...new Set(R.map(e=>e.category))];return a.jsx(i.Z,{user:e,title:"API Playground",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[a.jsx(n.Z,{className:"h-8 w-8 text-cyber-green"}),(0,a.jsxs)("h1",{className:"text-3xl font-bold font-cyber text-white",children:["API ",a.jsx("span",{className:"cyber-text",children:"Playground"})]})]}),a.jsx("p",{className:"text-gray-400 max-w-3xl",children:"Interactive API testing environment dengan Swagger documentation, request builder, dan endpoint management. Test semua endpoint KodeXGuard dengan real-time response."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[a.jsx(l.Rm,{title:"API Endpoints",value:R.length,icon:d.Z,color:"green"}),a.jsx(l.Rm,{title:"Categories",value:L.length,icon:c.Z,color:"blue"}),a.jsx(l.Rm,{title:"Saved Requests",value:D.length,icon:o.Z,color:"purple"}),a.jsx(l.Rm,{title:"API Calls Today",value:"1,247",icon:m.Z,color:"gold"})]}),a.jsx("div",{className:"mb-8",children:a.jsx("div",{className:"flex space-x-1 bg-gray-800/50 p-1 rounded-lg",children:[{id:"playground",name:"API Playground",icon:p.Z},{id:"docs",name:"Documentation",icon:x.Z},{id:"saved",name:"Saved Requests",icon:o.Z}].map(e=>{let t=e.icon;return(0,a.jsxs)("button",{onClick:()=>I(e.id),className:`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${E===e.id?"bg-cyber-green text-black":"text-gray-300 hover:text-white hover:bg-gray-700/50"}`,children:[a.jsx(t,{className:"h-4 w-4"}),a.jsx("span",{children:e.name})]},e.id)})})}),"playground"===E&&(0,a.jsxs)("div",{className:"grid lg:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-3",children:[a.jsx(l.Zb,{border:"green",glow:!0,children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h2",{className:"text-xl font-bold text-white mb-6",children:"Request Builder"}),(0,a.jsxs)("div",{className:"grid md:grid-cols-4 gap-4 mb-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Method"}),(0,a.jsxs)("select",{value:v,onChange:e=>N(e.target.value),className:"cyber-input w-full",children:[a.jsx("option",{value:"GET",children:"GET"}),a.jsx("option",{value:"POST",children:"POST"}),a.jsx("option",{value:"PUT",children:"PUT"}),a.jsx("option",{value:"DELETE",children:"DELETE"})]})]}),(0,a.jsxs)("div",{className:"md:col-span-3",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"URL"}),a.jsx("input",{type:"text",value:f,onChange:e=>w(e.target.value),placeholder:"https://api.kodexguard.com/v1/endpoint",className:"cyber-input w-full"})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Headers"}),a.jsx("textarea",{value:k,onChange:e=>q(e.target.value),className:"cyber-input w-full h-24 font-mono text-sm",placeholder:'{"Authorization": "Bearer your-api-key"}'})]}),("POST"===v||"PUT"===v)&&(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Request Body"}),a.jsx("textarea",{value:S,onChange:e=>Z(e.target.value),className:"cyber-input w-full h-32 font-mono text-sm",placeholder:'{"key": "value"}'})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[a.jsx("button",{onClick:G,disabled:A||!f,className:"cyber-btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:A?(0,a.jsxs)(a.Fragment,{children:[a.jsx(h.Z,{className:"h-4 w-4 mr-2 animate-spin"}),"Executing..."]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(p.Z,{className:"h-4 w-4 mr-2"}),"Send Request"]})}),(0,a.jsxs)("button",{onClick:()=>{let e={id:Date.now().toString(),name:`${v} ${f}`,method:v,url:f,headers:JSON.parse(k),body:S,createdAt:new Date().toISOString(),favorite:!1};O(t=>[...t,e])},disabled:!f,className:"cyber-btn disabled:opacity-50 disabled:cursor-not-allowed",children:[a.jsx(o.Z,{className:"h-4 w-4 mr-2"}),"Save"]}),(0,a.jsxs)("button",{onClick:()=>z(f),className:"cyber-btn",children:[a.jsx(u.Z,{className:"h-4 w-4 mr-2"}),"Copy URL"]})]})]})}),P&&a.jsx(l.Zb,{className:"mt-6",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[a.jsx("h3",{className:"text-lg font-bold text-white",children:"Response"}),(0,a.jsxs)("button",{onClick:()=>z(P),className:"cyber-btn text-sm",children:[a.jsx(u.Z,{className:"h-4 w-4 mr-2"}),"Copy"]})]}),a.jsx("div",{className:"bg-gray-900/50 rounded-lg p-4",children:a.jsx("pre",{className:"text-sm text-cyber-green whitespace-pre-wrap overflow-x-auto",children:P})})]})})]}),a.jsx("div",{children:a.jsx(l.Zb,{children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"API Endpoints"}),a.jsx("div",{className:"space-y-2",children:L.map(e=>(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"text-sm font-semibold text-gray-400 mb-2",children:e}),R.filter(t=>t.category===e).map(e=>(0,a.jsxs)("button",{onClick:()=>{s(e),N(e.method),w(`https://api.kodexguard.com/v1${e.path}`),Z(JSON.stringify(e.example.request,null,2))},className:`w-full text-left p-3 rounded-lg border transition-all duration-200 ${t?.id===e.id?"border-cyber-green bg-cyber-green/10":"border-gray-700 hover:border-gray-600"}`,children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[a.jsx("span",{className:`px-2 py-1 rounded text-xs font-semibold ${"GET"===e.method?"bg-blue-900/50 text-blue-400":"POST"===e.method?"bg-green-900/50 text-green-400":"PUT"===e.method?"bg-yellow-900/50 text-yellow-400":"bg-red-900/50 text-red-400"}`,children:e.method}),e.requiresAuth&&a.jsx(y.Z,{className:"h-3 w-3 text-gray-400"})]}),a.jsx("div",{className:"text-sm font-medium text-white",children:e.path}),a.jsx("div",{className:"text-xs text-gray-400 mt-1",children:e.description})]},e.id))]},e))})]})})})]}),"docs"===E&&t&&a.jsx(l.Zb,{children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[a.jsx("span",{className:`px-3 py-1 rounded font-semibold ${"GET"===t.method?"bg-blue-900/50 text-blue-400":"POST"===t.method?"bg-green-900/50 text-green-400":"PUT"===t.method?"bg-yellow-900/50 text-yellow-400":"bg-red-900/50 text-red-400"}`,children:t.method}),a.jsx("h2",{className:"text-xl font-bold text-white",children:t.path}),t.requiresAuth&&a.jsx("span",{className:"px-2 py-1 bg-orange-900/50 text-orange-400 rounded text-sm",children:"\uD83D\uDD12 Auth Required"})]}),a.jsx("p",{className:"text-gray-300 mb-6",children:t.description}),t.parameters.length>0&&(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Parameters"}),a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full text-sm",children:[a.jsx("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-gray-700",children:[a.jsx("th",{className:"text-left py-2 text-gray-400",children:"Name"}),a.jsx("th",{className:"text-left py-2 text-gray-400",children:"Type"}),a.jsx("th",{className:"text-left py-2 text-gray-400",children:"Required"}),a.jsx("th",{className:"text-left py-2 text-gray-400",children:"Description"})]})}),a.jsx("tbody",{children:t.parameters.map((e,t)=>(0,a.jsxs)("tr",{className:"border-b border-gray-800",children:[a.jsx("td",{className:"py-2 text-white font-mono",children:e.name}),a.jsx("td",{className:"py-2 text-cyber-green",children:e.type}),a.jsx("td",{className:"py-2",children:e.required?a.jsx("span",{className:"text-red-400",children:"Yes"}):a.jsx("span",{className:"text-gray-400",children:"No"})}),a.jsx("td",{className:"py-2 text-gray-300",children:e.description})]},t))})]})})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Example Request"}),a.jsx("div",{className:"bg-gray-900/50 rounded-lg p-4",children:a.jsx("pre",{className:"text-sm text-gray-300 whitespace-pre-wrap",children:JSON.stringify(t.example.request,null,2)})})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Example Response"}),a.jsx("div",{className:"bg-gray-900/50 rounded-lg p-4",children:a.jsx("pre",{className:"text-sm text-cyber-green whitespace-pre-wrap",children:JSON.stringify(t.example.response,null,2)})})]})]})]})}),"saved"===E&&a.jsx(l.Zb,{children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h2",{className:"text-xl font-bold text-white mb-6",children:"Saved Requests"}),0===D.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[a.jsx(o.Z,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"No Saved Requests"}),a.jsx("p",{className:"text-gray-400",children:"Save your API requests to access them later"})]}):a.jsx("div",{className:"space-y-4",children:D.map(e=>(0,a.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-4 border border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("span",{className:`px-2 py-1 rounded text-xs font-semibold ${"GET"===e.method?"bg-blue-900/50 text-blue-400":"POST"===e.method?"bg-green-900/50 text-green-400":"PUT"===e.method?"bg-yellow-900/50 text-yellow-400":"bg-red-900/50 text-red-400"}`,children:e.method}),a.jsx("h3",{className:"font-semibold text-white",children:e.name}),e.favorite&&a.jsx(g.Z,{className:"h-4 w-4 text-yellow-400 fill-current"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx("button",{onClick:()=>_(e.id),className:`text-gray-400 hover:text-yellow-400 transition-colors ${e.favorite?"text-yellow-400":""}`,children:a.jsx(g.Z,{className:"h-4 w-4"})}),a.jsx("button",{onClick:()=>M(e),className:"text-gray-400 hover:text-cyber-green transition-colors",children:a.jsx(b.Z,{className:"h-4 w-4"})}),a.jsx("button",{onClick:()=>U(e.id),className:"text-gray-400 hover:text-red-400 transition-colors",children:a.jsx(j.Z,{className:"h-4 w-4"})})]})]}),a.jsx("div",{className:"text-sm text-gray-300 mb-2",children:e.url}),(0,a.jsxs)("div",{className:"text-xs text-gray-400",children:["Saved: ",new Date(e.createdAt).toLocaleString()]})]},e.id))})]})})]})})}},6343:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},92498:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},43810:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},88319:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},54014:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},76828:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Key",[["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["path",{d:"m15.5 7.5 3 3L22 7l-3-3",key:"1rn1fs"}]])},70003:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},94893:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]])},21405:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},31215:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},33734:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},98091:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},3634:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},96612:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\playground\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[9276,82,7608,3061],()=>s(23374));module.exports=a})();