import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // In real app, verify JWT token from Authorization header
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Authorization token required',
          code: 'MISSING_TOKEN'
        },
        { status: 401 }
      )
    }

    // Mock user profile data
    const userProfile = {
      id: 'user_123456789',
      username: 'admin',
      email: '<EMAIL>',
      fullName: 'Super Administrator',
      avatar: null,
      bio: 'Platform creator and cybersecurity enthusiast. Building tools to make the internet safer.',
      role: 'super_admin',
      plan: 'cybersecurity',
      planExpiry: '2024-12-31',
      isActive: true,
      emailVerified: true,
      createdAt: '2023-01-01T00:00:00Z',
      lastLogin: new Date().toISOString(),
      stats: {
        totalScans: 1247,
        vulnerabilitiesFound: 89,
        filesAnalyzed: 456,
        apiCalls: 28560,
        score: 9870,
        rank: 4
      },
      apiKeys: [
        {
          id: 'key_1',
          name: 'Production API',
          keyPrefix: 'kxg_prod_',
          isActive: true,
          lastUsed: '2 hours ago',
          usageCount: 15420,
          createdAt: '2023-06-15'
        },
        {
          id: 'key_2',
          name: 'Development API',
          keyPrefix: 'kxg_dev_',
          isActive: true,
          lastUsed: '1 day ago',
          usageCount: 2847,
          createdAt: '2023-08-20'
        }
      ]
    }

    return NextResponse.json({
      success: true,
      data: userProfile,
      message: 'Profile retrieved successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Get profile error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    // In real app, verify JWT token
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Authorization token required',
          code: 'MISSING_TOKEN'
        },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { fullName, username, bio } = body

    // Validate input
    if (!fullName || !username) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Full name and username are required',
          code: 'MISSING_FIELDS'
        },
        { status: 400 }
      )
    }

    // Mock updated profile
    const updatedProfile = {
      id: 'user_123456789',
      username,
      email: '<EMAIL>',
      fullName,
      bio: bio || '',
      role: 'super_admin',
      plan: 'cybersecurity',
      updatedAt: new Date().toISOString()
    }

    return NextResponse.json({
      success: true,
      data: updatedProfile,
      message: 'Profile updated successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Update profile error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, PUT, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
