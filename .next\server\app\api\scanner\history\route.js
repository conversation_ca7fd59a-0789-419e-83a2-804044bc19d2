"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/scanner/history/route";
exports.ids = ["app/api/scanner/history/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fscanner%2Fhistory%2Froute&page=%2Fapi%2Fscanner%2Fhistory%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscanner%2Fhistory%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fscanner%2Fhistory%2Froute&page=%2Fapi%2Fscanner%2Fhistory%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscanner%2Fhistory%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Users_Downloads_kodeXGuard_app_api_scanner_history_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/scanner/history/route.ts */ \"(rsc)/./app/api/scanner/history/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/scanner/history/route\",\n        pathname: \"/api/scanner/history\",\n        filename: \"route\",\n        bundlePath: \"app/api/scanner/history/route\"\n    },\n    resolvedPagePath: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\api\\\\scanner\\\\history\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Users_Downloads_kodeXGuard_app_api_scanner_history_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/scanner/history/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fscanner%2Fhistory%2Froute&page=%2Fapi%2Fscanner%2Fhistory%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscanner%2Fhistory%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/scanner/history/route.ts":
/*!******************************************!*\
  !*** ./app/api/scanner/history/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function GET(request) {\n    try {\n        // Get auth token\n        const authHeader = request.headers.get(\"authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        // Simple token validation for testing\n        if (!token.startsWith(\"kxg_\")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid token\"\n            }, {\n                status: 401\n            });\n        }\n        // Mock scan history data\n        const scanHistory = [\n            {\n                id: \"scan_001\",\n                target: \"https://example.com\",\n                scanType: [\n                    \"sqli\",\n                    \"xss\",\n                    \"csrf\"\n                ],\n                status: \"completed\",\n                vulnerabilities: {\n                    critical: 2,\n                    high: 5,\n                    medium: 8,\n                    low: 12,\n                    info: 3\n                },\n                startTime: new Date(Date.now() - 3600000).toISOString(),\n                endTime: new Date(Date.now() - 3300000).toISOString(),\n                duration: 300,\n                progress: 100\n            },\n            {\n                id: \"scan_002\",\n                target: \"https://testsite.com\",\n                scanType: [\n                    \"port_scan\",\n                    \"ssl_check\"\n                ],\n                status: \"completed\",\n                vulnerabilities: {\n                    critical: 0,\n                    high: 1,\n                    medium: 3,\n                    low: 5,\n                    info: 2\n                },\n                startTime: new Date(Date.now() - 7200000).toISOString(),\n                endTime: new Date(Date.now() - 6900000).toISOString(),\n                duration: 180,\n                progress: 100\n            },\n            {\n                id: \"scan_003\",\n                target: \"https://webapp.local\",\n                scanType: [\n                    \"sqli\",\n                    \"xss\",\n                    \"lfi\",\n                    \"rfi\"\n                ],\n                status: \"running\",\n                vulnerabilities: {\n                    critical: 1,\n                    high: 2,\n                    medium: 4,\n                    low: 6,\n                    info: 1\n                },\n                startTime: new Date(Date.now() - 1800000).toISOString(),\n                duration: 1800,\n                progress: 65\n            },\n            {\n                id: \"scan_004\",\n                target: \"https://api.example.com\",\n                scanType: [\n                    \"api_security\",\n                    \"auth_bypass\"\n                ],\n                status: \"failed\",\n                vulnerabilities: {\n                    critical: 0,\n                    high: 0,\n                    medium: 0,\n                    low: 0,\n                    info: 0\n                },\n                startTime: new Date(Date.now() - 10800000).toISOString(),\n                endTime: new Date(Date.now() - 10740000).toISOString(),\n                duration: 60,\n                progress: 15\n            },\n            {\n                id: \"scan_005\",\n                target: \"https://mobile-app.com\",\n                scanType: [\n                    \"mobile_security\",\n                    \"ssl_check\"\n                ],\n                status: \"queued\",\n                vulnerabilities: {\n                    critical: 0,\n                    high: 0,\n                    medium: 0,\n                    low: 0,\n                    info: 0\n                },\n                startTime: new Date().toISOString(),\n                progress: 0\n            }\n        ];\n        // Sort by start time (newest first)\n        scanHistory.sort((a, b)=>new Date(b.startTime).getTime() - new Date(a.startTime).getTime());\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: scanHistory,\n            meta: {\n                total: scanHistory.length,\n                completed: scanHistory.filter((s)=>s.status === \"completed\").length,\n                running: scanHistory.filter((s)=>s.status === \"running\").length,\n                failed: scanHistory.filter((s)=>s.status === \"failed\").length,\n                queued: scanHistory.filter((s)=>s.status === \"queued\").length\n            },\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"Scanner history API error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request) {\n    try {\n        // Get auth token\n        const authHeader = request.headers.get(\"authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        // Simple token validation for testing\n        if (!token.startsWith(\"kxg_\")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid token\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { scanId } = body;\n        if (!scanId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Scan ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        // In a real app, delete the scan from database\n        // For demo, just return success\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: `Scan ${scanId} deleted successfully`,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"Delete scan error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/scanner/history/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fscanner%2Fhistory%2Froute&page=%2Fapi%2Fscanner%2Fhistory%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscanner%2Fhistory%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();