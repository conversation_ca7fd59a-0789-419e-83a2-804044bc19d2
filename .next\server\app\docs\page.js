/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/docs/page";
exports.ids = ["app/docs/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdocs%2Fpage&page=%2Fdocs%2Fpage&appPaths=%2Fdocs%2Fpage&pagePath=private-next-app-dir%2Fdocs%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdocs%2Fpage&page=%2Fdocs%2Fpage&appPaths=%2Fdocs%2Fpage&pagePath=private-next-app-dir%2Fdocs%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'docs',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/docs/page.tsx */ \"(rsc)/./app/docs/page.tsx\")), \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/docs/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/docs/page\",\n        pathname: \"/docs\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdocs%2Fpage&page=%2Fdocs%2Fpage&appPaths=%2Fdocs%2Fpage&pagePath=private-next-app-dir%2Fdocs%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Cdocs%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Cdocs%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/docs/page.tsx */ \"(ssr)/./app/docs/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNVc2VycyU1QyU1Q0Rvd25sb2FkcyU1QyU1Q2tvZGVYR3VhcmQlNUMlNUNhcHAlNUMlNUNkb2NzJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtKQUEwRiIsInNvdXJjZXMiOlsid2VicGFjazovL2tvZGV4Z3VhcmQvPzMzY2EiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxVc2Vyc1xcXFxEb3dubG9hZHNcXFxca29kZVhHdWFyZFxcXFxhcHBcXFxcZG9jc1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Cdocs%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/docs/page.tsx":
/*!***************************!*\
  !*** ./app/docs/page.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navbar */ \"(ssr)/./components/Navbar.tsx\");\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Card */ \"(ssr)/./components/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Bot_ChevronRight_Code_Copy_Database_FileText_Play_Search_Shield_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Bot,ChevronRight,Code,Copy,Database,FileText,Play,Search,Shield,Terminal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Bot_ChevronRight_Code_Copy_Database_FileText_Play_Search_Shield_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Bot,ChevronRight,Code,Copy,Database,FileText,Play,Search,Shield,Terminal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Bot_ChevronRight_Code_Copy_Database_FileText_Play_Search_Shield_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Bot,ChevronRight,Code,Copy,Database,FileText,Play,Search,Shield,Terminal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Bot_ChevronRight_Code_Copy_Database_FileText_Play_Search_Shield_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Bot,ChevronRight,Code,Copy,Database,FileText,Play,Search,Shield,Terminal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Bot_ChevronRight_Code_Copy_Database_FileText_Play_Search_Shield_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Bot,ChevronRight,Code,Copy,Database,FileText,Play,Search,Shield,Terminal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Bot_ChevronRight_Code_Copy_Database_FileText_Play_Search_Shield_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Bot,ChevronRight,Code,Copy,Database,FileText,Play,Search,Shield,Terminal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Bot_ChevronRight_Code_Copy_Database_FileText_Play_Search_Shield_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Bot,ChevronRight,Code,Copy,Database,FileText,Play,Search,Shield,Terminal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Bot_ChevronRight_Code_Copy_Database_FileText_Play_Search_Shield_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Bot,ChevronRight,Code,Copy,Database,FileText,Play,Search,Shield,Terminal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Bot_ChevronRight_Code_Copy_Database_FileText_Play_Search_Shield_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Bot,ChevronRight,Code,Copy,Database,FileText,Play,Search,Shield,Terminal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Bot_ChevronRight_Code_Copy_Database_FileText_Play_Search_Shield_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Bot,ChevronRight,Code,Copy,Database,FileText,Play,Search,Shield,Terminal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Bot_ChevronRight_Code_Copy_Database_FileText_Play_Search_Shield_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Bot,ChevronRight,Code,Copy,Database,FileText,Play,Search,Shield,Terminal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DocsPage() {\n    const [user] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: \"admin\",\n        avatar: \"\",\n        role: \"super_admin\",\n        plan: \"cybersecurity\"\n    });\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"getting-started\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const docSections = [\n        {\n            id: \"getting-started\",\n            title: \"Getting Started\",\n            description: \"Quick start guide untuk menggunakan KodeXGuard\",\n            icon: _barrel_optimize_names_BookOpen_Bot_ChevronRight_Code_Copy_Database_FileText_Play_Search_Shield_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            content: [\n                \"# Getting Started with KodeXGuard\",\n                \"\",\n                \"## Welcome to KodeXGuard\",\n                \"KodeXGuard adalah platform cybersecurity mandiri Indonesia yang menyediakan tools untuk OSINT investigation, vulnerability scanning, file analysis, dan bot automation.\",\n                \"\",\n                \"## Quick Start\",\n                \"1. **Register Account**: Daftar akun baru di `/register`\",\n                \"2. **Choose Plan**: Pilih plan yang sesuai kebutuhan\",\n                \"3. **Get API Key**: Generate API key di halaman profile\",\n                \"4. **Start Scanning**: Mulai gunakan tools yang tersedia\",\n                \"\",\n                \"## Demo Credentials\",\n                \"```\",\n                \"Email: <EMAIL>\",\n                \"Password: admin123\",\n                \"```\",\n                \"\",\n                \"## Available Tools\",\n                \"- **OSINT Investigator**: Pencarian NIK, NPWP, email, domain\",\n                \"- **Vulnerability Scanner**: Deteksi SQLi, XSS, LFI, RCE\",\n                \"- **File Analyzer**: Analisis malware, webshell, secrets\",\n                \"- **CVE Intelligence**: Database CVE terupdate\",\n                \"- **Security Tools**: Hash, encode, payload generator\",\n                \"- **Bot Center**: WhatsApp & Telegram automation\"\n            ]\n        },\n        {\n            id: \"api-reference\",\n            title: \"API Reference\",\n            description: \"Dokumentasi lengkap API endpoints KodeXGuard\",\n            icon: _barrel_optimize_names_BookOpen_Bot_ChevronRight_Code_Copy_Database_FileText_Play_Search_Shield_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            content: [\n                \"# API Reference\",\n                \"\",\n                \"## Base URL\",\n                \"```\",\n                \"https://api.kodexguard.com/v1\",\n                \"```\",\n                \"\",\n                \"## Authentication\",\n                \"Semua API endpoint memerlukan authentication menggunakan Bearer token:\",\n                \"\",\n                \"```bash\",\n                'curl -H \"Authorization: Bearer YOUR_API_KEY\" \\\\',\n                \"     https://api.kodexguard.com/v1/endpoint\",\n                \"```\",\n                \"\",\n                \"## Rate Limiting\",\n                \"Rate limit berdasarkan plan:\",\n                \"- **Gratis**: 100 calls/day\",\n                \"- **Pelajar**: 1,000 calls/day\",\n                \"- **Hobby**: 5,000 calls/day\",\n                \"- **Bug Hunter**: 10,000 calls/day\",\n                \"- **Cybersecurity**: 100,000 calls/day\",\n                \"\",\n                \"## Response Format\",\n                \"Semua response dalam format JSON:\",\n                \"\",\n                \"```json\",\n                \"{\",\n                '  \"success\": true,',\n                '  \"data\": {...},',\n                '  \"message\": \"Success\",',\n                '  \"timestamp\": \"2024-01-15T10:30:00Z\"',\n                \"}\",\n                \"```\",\n                \"\",\n                \"## Error Handling\",\n                \"Error response format:\",\n                \"\",\n                \"```json\",\n                \"{\",\n                '  \"success\": false,',\n                '  \"error\": \"Error message\",',\n                '  \"code\": \"ERROR_CODE\",',\n                '  \"timestamp\": \"2024-01-15T10:30:00Z\"',\n                \"}\",\n                \"```\"\n            ]\n        },\n        {\n            id: \"osint-api\",\n            title: \"OSINT API\",\n            description: \"API untuk OSINT investigation dan data gathering\",\n            icon: _barrel_optimize_names_BookOpen_Bot_ChevronRight_Code_Copy_Database_FileText_Play_Search_Shield_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            content: [\n                \"# OSINT API\",\n                \"\",\n                \"## Search Endpoint\",\n                \"**POST** `/api/osint/search`\",\n                \"\",\n                \"### Request Body\",\n                \"```json\",\n                \"{\",\n                '  \"query\": \"<EMAIL>\",',\n                '  \"type\": \"email\",',\n                '  \"deepSearch\": true,',\n                '  \"sources\": [\"dukcapil\", \"kemkes\", \"github\"]',\n                \"}\",\n                \"```\",\n                \"\",\n                \"### Parameters\",\n                \"- `query` (string, required): Search query\",\n                \"- `type` (string, required): Search type (email, phone, nik, npwp, domain, imei, name, address)\",\n                \"- `deepSearch` (boolean, optional): Enable deep search\",\n                \"- `sources` (array, optional): Specific data sources\",\n                \"\",\n                \"### Response\",\n                \"```json\",\n                \"{\",\n                '  \"success\": true,',\n                '  \"data\": {',\n                '    \"results\": [',\n                \"      {\",\n                '        \"source\": \"Dukcapil Database\",',\n                '        \"data\": {',\n                '          \"name\": \"John Doe\",',\n                '          \"email\": \"<EMAIL>\",',\n                '          \"verified\": true',\n                \"        },\",\n                '        \"confidence\": 95,',\n                '        \"timestamp\": \"2024-01-15T10:30:00Z\"',\n                \"      }\",\n                \"    ],\",\n                '    \"totalResults\": 1,',\n                '    \"searchTime\": \"1.2s\"',\n                \"  }\",\n                \"}\",\n                \"```\",\n                \"\",\n                \"## Track Location\",\n                \"**POST** `/api/osint/track`\",\n                \"\",\n                \"Track location berdasarkan nomor HP atau IMEI.\",\n                \"\",\n                \"### Example\",\n                \"```bash\",\n                \"curl -X POST https://api.kodexguard.com/v1/osint/track \\\\\",\n                '  -H \"Authorization: Bearer YOUR_API_KEY\" \\\\',\n                '  -H \"Content-Type: application/json\" \\\\',\n                '  -d \\'{\"target\": \"+62812345678\", \"type\": \"phone\"}\\'',\n                \"```\"\n            ]\n        },\n        {\n            id: \"scanner-api\",\n            title: \"Scanner API\",\n            description: \"API untuk vulnerability scanning dan security testing\",\n            icon: _barrel_optimize_names_BookOpen_Bot_ChevronRight_Code_Copy_Database_FileText_Play_Search_Shield_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            content: [\n                \"# Vulnerability Scanner API\",\n                \"\",\n                \"## Start Scan\",\n                \"**POST** `/api/scan/vulnerability`\",\n                \"\",\n                \"### Request Body\",\n                \"```json\",\n                \"{\",\n                '  \"target\": \"https://example.com\",',\n                '  \"scanTypes\": [\"sqli\", \"xss\", \"lfi\", \"rce\"],',\n                '  \"maxDepth\": 3,',\n                '  \"timeout\": 30,',\n                '  \"followRedirects\": true',\n                \"}\",\n                \"```\",\n                \"\",\n                \"### Parameters\",\n                \"- `target` (string, required): Target URL to scan\",\n                \"- `scanTypes` (array, required): Vulnerability types to scan\",\n                \"- `maxDepth` (number, optional): Maximum crawl depth (default: 3)\",\n                \"- `timeout` (number, optional): Request timeout in seconds (default: 30)\",\n                \"- `followRedirects` (boolean, optional): Follow HTTP redirects (default: true)\",\n                \"\",\n                \"### Supported Scan Types\",\n                \"- `sqli`: SQL Injection\",\n                \"- `xss`: Cross-Site Scripting\",\n                \"- `lfi`: Local File Inclusion\",\n                \"- `rfi`: Remote File Inclusion\",\n                \"- `rce`: Remote Code Execution\",\n                \"- `csrf`: Cross-Site Request Forgery\",\n                \"- `ssrf`: Server-Side Request Forgery\",\n                \"- `xxe`: XML External Entity\",\n                \"- `idor`: Insecure Direct Object Reference\",\n                \"- `path_traversal`: Path Traversal\",\n                \"\",\n                \"### Response\",\n                \"```json\",\n                \"{\",\n                '  \"success\": true,',\n                '  \"data\": {',\n                '    \"scanId\": \"scan_123456789\",',\n                '    \"status\": \"started\",',\n                '    \"estimatedTime\": \"5-10 minutes\",',\n                '    \"target\": \"https://example.com\"',\n                \"  }\",\n                \"}\",\n                \"```\",\n                \"\",\n                \"## Get Scan Results\",\n                \"**GET** `/api/scan/{scanId}/results`\",\n                \"\",\n                \"### Response\",\n                \"```json\",\n                \"{\",\n                '  \"success\": true,',\n                '  \"data\": {',\n                '    \"scanId\": \"scan_123456789\",',\n                '    \"status\": \"completed\",',\n                '    \"target\": \"https://example.com\",',\n                '    \"vulnerabilities\": [',\n                \"      {\",\n                '        \"type\": \"sqli\",',\n                '        \"severity\": \"critical\",',\n                '        \"cvssScore\": 9.8,',\n                '        \"url\": \"https://example.com/login.php\",',\n                '        \"parameter\": \"username\",',\n                \"        \\\"payload\\\": \\\"admin' OR '1'='1\\\",\",\n                '        \"evidence\": \"MySQL error detected\",',\n                '        \"recommendation\": \"Use parameterized queries\"',\n                \"      }\",\n                \"    ],\",\n                '    \"summary\": {',\n                '      \"total\": 1,',\n                '      \"critical\": 1,',\n                '      \"high\": 0,',\n                '      \"medium\": 0,',\n                '      \"low\": 0',\n                \"    }\",\n                \"  }\",\n                \"}\",\n                \"```\"\n            ]\n        },\n        {\n            id: \"file-api\",\n            title: \"File Analysis API\",\n            description: \"API untuk analisis file dan deteksi malware\",\n            icon: _barrel_optimize_names_BookOpen_Bot_ChevronRight_Code_Copy_Database_FileText_Play_Search_Shield_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            content: [\n                \"# File Analysis API\",\n                \"\",\n                \"## Analyze File\",\n                \"**POST** `/api/file/analyze`\",\n                \"\",\n                \"Upload dan analisis file untuk deteksi malware, webshell, dan secrets.\",\n                \"\",\n                \"### Request (multipart/form-data)\",\n                \"```bash\",\n                \"curl -X POST https://api.kodexguard.com/v1/file/analyze \\\\\",\n                '  -H \"Authorization: Bearer YOUR_API_KEY\" \\\\',\n                '  -F \"file=@suspicious.php\" \\\\',\n                '  -F \"analysisType=webshell\"',\n                \"```\",\n                \"\",\n                \"### Parameters\",\n                \"- `file` (file, required): File to analyze\",\n                \"- `analysisType` (string, optional): Analysis type (malware, webshell, secret, general)\",\n                \"\",\n                \"### Supported File Types\",\n                \"- **Scripts**: .php, .js, .py, .asp, .jsp\",\n                \"- **Documents**: .txt, .pdf, .doc, .docx\",\n                \"- **Archives**: .zip, .rar, .tar, .gz\",\n                \"- **Executables**: .exe, .dll, .apk\",\n                \"\",\n                \"### Response\",\n                \"```json\",\n                \"{\",\n                '  \"success\": true,',\n                '  \"data\": {',\n                '    \"analysisId\": \"analysis_789012345\",',\n                '    \"filename\": \"suspicious.php\",',\n                '    \"fileSize\": 2048,',\n                '    \"mimeType\": \"text/x-php\",',\n                '    \"hash\": \"sha256:abc123...\",',\n                '    \"threatLevel\": \"suspicious\",',\n                '    \"results\": {',\n                '      \"webshellDetection\": {',\n                '        \"detected\": true,',\n                '        \"type\": \"PHP Webshell\",',\n                '        \"obfuscated\": false,',\n                '        \"confidence\": 95',\n                \"      },\",\n                '      \"secretDetection\": {',\n                '        \"detected\": true,',\n                '        \"secrets\": [',\n                \"          {\",\n                '            \"type\": \"API Key\",',\n                '            \"value\": \"sk-1234567890abcdef\",',\n                '            \"confidence\": 87',\n                \"          }\",\n                \"        ]\",\n                \"      }\",\n                \"    }\",\n                \"  }\",\n                \"}\",\n                \"```\",\n                \"\",\n                \"## Get Analysis Results\",\n                \"**GET** `/api/file/analysis/{analysisId}`\",\n                \"\",\n                \"Retrieve detailed analysis results by analysis ID.\"\n            ]\n        },\n        {\n            id: \"cve-api\",\n            title: \"CVE API\",\n            description: \"API untuk CVE intelligence dan vulnerability database\",\n            icon: _barrel_optimize_names_BookOpen_Bot_ChevronRight_Code_Copy_Database_FileText_Play_Search_Shield_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            content: [\n                \"# CVE Intelligence API\",\n                \"\",\n                \"## Search CVE Database\",\n                \"**GET** `/api/cve/search`\",\n                \"\",\n                \"### Query Parameters\",\n                \"- `query` (string, optional): Search query\",\n                \"- `severity` (string, optional): Filter by severity (critical, high, medium, low)\",\n                \"- `year` (string, optional): Filter by year (2024, 2023, etc.)\",\n                \"- `limit` (number, optional): Number of results (default: 20, max: 100)\",\n                \"- `offset` (number, optional): Pagination offset (default: 0)\",\n                \"\",\n                \"### Example\",\n                \"```bash\",\n                'curl \"https://api.kodexguard.com/v1/cve/search?query=SQL%20injection&severity=critical&limit=10\" \\\\',\n                '  -H \"Authorization: Bearer YOUR_API_KEY\"',\n                \"```\",\n                \"\",\n                \"### Response\",\n                \"```json\",\n                \"{\",\n                '  \"success\": true,',\n                '  \"data\": {',\n                '    \"total\": 156,',\n                '    \"cves\": [',\n                \"      {\",\n                '        \"cveId\": \"CVE-2024-0001\",',\n                '        \"description\": \"SQL injection vulnerability...\",',\n                '        \"severity\": \"critical\",',\n                '        \"cvssScore\": 9.8,',\n                '        \"cvssVector\": \"CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H\",',\n                '        \"publishedDate\": \"2024-01-15\",',\n                '        \"modifiedDate\": \"2024-01-16\",',\n                '        \"affectedProducts\": [\"WebApp 1.0\", \"WebApp 2.0\"],',\n                '        \"references\": [\"https://nvd.nist.gov/vuln/detail/CVE-2024-0001\"],',\n                '        \"exploits\": [',\n                \"          {\",\n                '            \"title\": \"SQL Injection Exploit\",',\n                '            \"url\": \"https://exploit-db.com/exploits/12345\",',\n                '            \"type\": \"Public Exploit\"',\n                \"          }\",\n                \"        ]\",\n                \"      }\",\n                \"    ]\",\n                \"  }\",\n                \"}\",\n                \"```\",\n                \"\",\n                \"## Get CVE Details\",\n                \"**GET** `/api/cve/{cveId}`\",\n                \"\",\n                \"Get detailed information about specific CVE.\",\n                \"\",\n                \"### Example\",\n                \"```bash\",\n                \"curl https://api.kodexguard.com/v1/cve/CVE-2024-0001 \\\\\",\n                '  -H \"Authorization: Bearer YOUR_API_KEY\"',\n                \"```\"\n            ]\n        },\n        {\n            id: \"bot-integration\",\n            title: \"Bot Integration\",\n            description: \"Integrasi WhatsApp dan Telegram bot untuk automation\",\n            icon: _barrel_optimize_names_BookOpen_Bot_ChevronRight_Code_Copy_Database_FileText_Play_Search_Shield_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            content: [\n                \"# Bot Integration Guide\",\n                \"\",\n                \"## WhatsApp Bot Setup\",\n                \"\",\n                \"### Prerequisites\",\n                \"- Plan Bug Hunter atau Cybersecurity\",\n                \"- Super Admin access untuk setup\",\n                \"- WhatsApp account untuk bot\",\n                \"\",\n                \"### Setup Steps\",\n                \"1. **Login sebagai Super Admin**\",\n                \"2. **Buka Bot Center** (`/bot`)\",\n                \"3. **Add New Bot** dengan type WhatsApp\",\n                \"4. **Scan QR Code** dengan WhatsApp\",\n                \"5. **Bot Ready** untuk menerima commands\",\n                \"\",\n                \"### Available Commands\",\n                \"```\",\n                \"/scan <url>     - Start vulnerability scan\",\n                \"/status         - Check plan status\",\n                \"/help           - Show available commands\",\n                \"/results <id>   - Get scan results\",\n                \"```\",\n                \"\",\n                \"### Example Usage\",\n                \"```\",\n                \"User: /scan https://example.com\",\n                \"Bot:  Starting vulnerability scan for https://example.com...\",\n                \"      Scan ID: scan_123456789\",\n                \"      Estimated time: 5-10 minutes\",\n                \"\",\n                \"User: /results scan_123456789\",\n                \"Bot:  Scan Results:\",\n                \"      ✅ Completed\",\n                \"      \\uD83D\\uDD34 1 Critical vulnerability found\",\n                \"      \\uD83D\\uDCCA CVSS Score: 9.8\",\n                \"      \\uD83D\\uDCC4 Full report: https://kodexguard.com/scan/123456789\",\n                \"```\",\n                \"\",\n                \"## Telegram Bot Setup\",\n                \"\",\n                \"### Setup Steps\",\n                \"1. **Create Bot** dengan @BotFather\",\n                \"2. **Get Bot Token**\",\n                \"3. **Add Bot** di KodeXGuard Bot Center\",\n                \"4. **Configure Webhook** (otomatis)\",\n                \"\",\n                \"### Bot Configuration\",\n                \"```json\",\n                \"{\",\n                '  \"botToken\": \"YOUR_TELEGRAM_BOT_TOKEN\",',\n                '  \"webhookUrl\": \"https://api.kodexguard.com/webhook/telegram\",',\n                '  \"allowedUsers\": [\"@username1\", \"@username2\"],',\n                '  \"commands\": [',\n                '    {\"command\": \"scan\", \"description\": \"Start vulnerability scan\"},',\n                '    {\"command\": \"status\", \"description\": \"Check plan status\"},',\n                '    {\"command\": \"help\", \"description\": \"Show help\"}',\n                \"  ]\",\n                \"}\",\n                \"```\",\n                \"\",\n                \"## Bot API Integration\",\n                \"\",\n                \"### Send Message via API\",\n                \"**POST** `/api/bot/send`\",\n                \"\",\n                \"```json\",\n                \"{\",\n                '  \"botId\": \"bot_123\",',\n                '  \"recipient\": \"+62812345678\",',\n                '  \"message\": \"Scan completed! Check results at: https://...\"',\n                \"}\",\n                \"```\",\n                \"\",\n                \"### Get Bot Status\",\n                \"**GET** `/api/bot/status`\",\n                \"\",\n                \"Returns status of all configured bots.\"\n            ]\n        },\n        {\n            id: \"sdk-examples\",\n            title: \"SDK & Examples\",\n            description: \"Code examples dan SDK untuk berbagai bahasa pemrograman\",\n            icon: _barrel_optimize_names_BookOpen_Bot_ChevronRight_Code_Copy_Database_FileText_Play_Search_Shield_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            content: [\n                \"# SDK & Code Examples\",\n                \"\",\n                \"## JavaScript/Node.js\",\n                \"\",\n                \"### Installation\",\n                \"```bash\",\n                \"npm install kodexguard-sdk\",\n                \"```\",\n                \"\",\n                \"### Basic Usage\",\n                \"```javascript\",\n                \"const KodeXGuard = require('kodexguard-sdk');\",\n                \"\",\n                \"const client = new KodeXGuard({\",\n                \"  apiKey: 'your-api-key',\",\n                \"  baseUrl: 'https://api.kodexguard.com/v1'\",\n                \"});\",\n                \"\",\n                \"// OSINT Search\",\n                \"const osintResult = await client.osint.search({\",\n                \"  query: '<EMAIL>',\",\n                \"  type: 'email',\",\n                \"  deepSearch: true\",\n                \"});\",\n                \"\",\n                \"// Vulnerability Scan\",\n                \"const scan = await client.scanner.start({\",\n                \"  target: 'https://example.com',\",\n                \"  scanTypes: ['sqli', 'xss', 'lfi']\",\n                \"});\",\n                \"\",\n                \"// Get Results\",\n                \"const results = await client.scanner.getResults(scan.scanId);\",\n                \"```\",\n                \"\",\n                \"## Python\",\n                \"\",\n                \"### Installation\",\n                \"```bash\",\n                \"pip install kodexguard-python\",\n                \"```\",\n                \"\",\n                \"### Basic Usage\",\n                \"```python\",\n                \"from kodexguard import KodeXGuardClient\",\n                \"\",\n                \"client = KodeXGuardClient(\",\n                '    api_key=\"your-api-key\",',\n                '    base_url=\"https://api.kodexguard.com/v1\"',\n                \")\",\n                \"\",\n                \"# OSINT Search\",\n                \"osint_result = client.osint.search(\",\n                '    query=\"<EMAIL>\",',\n                '    type=\"email\",',\n                \"    deep_search=True\",\n                \")\",\n                \"\",\n                \"# Vulnerability Scan\",\n                \"scan = client.scanner.start(\",\n                '    target=\"https://example.com\",',\n                '    scan_types=[\"sqli\", \"xss\", \"lfi\"]',\n                \")\",\n                \"\",\n                \"# Get Results\",\n                'results = client.scanner.get_results(scan[\"scanId\"])',\n                \"```\",\n                \"\",\n                \"## PHP\",\n                \"\",\n                \"### Installation\",\n                \"```bash\",\n                \"composer require kodexguard/php-sdk\",\n                \"```\",\n                \"\",\n                \"### Basic Usage\",\n                \"```php\",\n                \"<?php\",\n                \"require_once 'vendor/autoload.php';\",\n                \"\",\n                \"use KodeXGuard\\\\Client;\",\n                \"\",\n                \"$client = new Client([\",\n                \"    'api_key' => 'your-api-key',\",\n                \"    'base_url' => 'https://api.kodexguard.com/v1'\",\n                \"]);\",\n                \"\",\n                \"// OSINT Search\",\n                \"$osintResult = $client->osint()->search([\",\n                \"    'query' => '<EMAIL>',\",\n                \"    'type' => 'email',\",\n                \"    'deepSearch' => true\",\n                \"]);\",\n                \"\",\n                \"// Vulnerability Scan\",\n                \"$scan = $client->scanner()->start([\",\n                \"    'target' => 'https://example.com',\",\n                \"    'scanTypes' => ['sqli', 'xss', 'lfi']\",\n                \"]);\",\n                \"?>\",\n                \"```\",\n                \"\",\n                \"## cURL Examples\",\n                \"\",\n                \"### OSINT Search\",\n                \"```bash\",\n                \"curl -X POST https://api.kodexguard.com/v1/osint/search \\\\\",\n                '  -H \"Authorization: Bearer YOUR_API_KEY\" \\\\',\n                '  -H \"Content-Type: application/json\" \\\\',\n                '  -d \\'{\"query\": \"<EMAIL>\", \"type\": \"email\"}\\'',\n                \"```\",\n                \"\",\n                \"### Start Vulnerability Scan\",\n                \"```bash\",\n                \"curl -X POST https://api.kodexguard.com/v1/scan/vulnerability \\\\\",\n                '  -H \"Authorization: Bearer YOUR_API_KEY\" \\\\',\n                '  -H \"Content-Type: application/json\" \\\\',\n                '  -d \\'{\"target\": \"https://example.com\", \"scanTypes\": [\"sqli\", \"xss\"]}\\'',\n                \"```\"\n            ]\n        }\n    ];\n    const filteredSections = docSections.filter((section)=>section.title.toLowerCase().includes(searchQuery.toLowerCase()) || section.description.toLowerCase().includes(searchQuery.toLowerCase()) || section.content.some((line)=>line.toLowerCase().includes(searchQuery.toLowerCase())));\n    const copyToClipboard = (text)=>{\n        navigator.clipboard.writeText(text);\n    };\n    const renderContent = (content)=>{\n        return content.map((line, index)=>{\n            if (line.startsWith(\"# \")) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold text-white mb-4\",\n                    children: line.substring(2)\n                }, index, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                    lineNumber: 650,\n                    columnNumber: 16\n                }, this);\n            } else if (line.startsWith(\"## \")) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-semibold text-white mb-3 mt-6\",\n                    children: line.substring(3)\n                }, index, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                    lineNumber: 652,\n                    columnNumber: 16\n                }, this);\n            } else if (line.startsWith(\"### \")) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-cyber-green mb-2 mt-4\",\n                    children: line.substring(4)\n                }, index, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                    lineNumber: 654,\n                    columnNumber: 16\n                }, this);\n            } else if (line.startsWith(\"```\")) {\n                const nextIndex = content.findIndex((l, i)=>i > index && l.startsWith(\"```\"));\n                if (nextIndex > index) {\n                    const codeLines = content.slice(index + 1, nextIndex);\n                    const code = codeLines.join(\"\\n\");\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative bg-gray-900/50 rounded-lg p-4 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>copyToClipboard(code),\n                                className: \"absolute top-2 right-2 text-gray-400 hover:text-white transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Bot_ChevronRight_Code_Copy_Database_FileText_Play_Search_Shield_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                                    lineNumber: 666,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 662,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"text-sm text-cyber-green overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    children: code\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                                    lineNumber: 669,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 668,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                        lineNumber: 661,\n                        columnNumber: 13\n                    }, this);\n                }\n                return null;\n            } else if (line.startsWith(\"- \")) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"text-gray-300 mb-1 ml-4\",\n                    children: line.substring(2)\n                }, index, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                    lineNumber: 676,\n                    columnNumber: 16\n                }, this);\n            } else if (line === \"\") {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, index, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                    lineNumber: 678,\n                    columnNumber: 16\n                }, this);\n            } else {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-300 mb-2\",\n                    children: line\n                }, index, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                    lineNumber: 680,\n                    columnNumber: 16\n                }, this);\n            }\n        }).filter(Boolean);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-cyber\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                user: user\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 687,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Bot_ChevronRight_Code_Copy_Database_FileText_Play_Search_Shield_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-8 w-8 text-cyber-green\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 694,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold font-cyber text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"cyber-text\",\n                                                children: \"Documentation\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                lineNumber: 696,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 695,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                                    lineNumber: 693,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 max-w-3xl\",\n                                    children: \"Dokumentasi lengkap KodeXGuard API, SDK, dan panduan penggunaan untuk developer dan security researcher.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                                    lineNumber: 699,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 692,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative max-w-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Bot_ChevronRight_Code_Copy_Database_FileText_Play_Search_Shield_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"absolute left-3 top-3 h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 707,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        placeholder: \"Search documentation...\",\n                                        className: \"cyber-input pl-10 w-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 708,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                                lineNumber: 706,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 705,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid lg:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-bold text-white mb-4\",\n                                                    children: \"Documentation\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                    lineNumber: 723,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                                    className: \"space-y-2\",\n                                                    children: filteredSections.map((section)=>{\n                                                        const Icon = section.icon;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setActiveSection(section.id),\n                                                            className: `w-full text-left flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 ${activeSection === section.id ? \"bg-cyber-green/10 text-cyber-green border border-cyber-green\" : \"text-gray-300 hover:text-white hover:bg-gray-800/50\"}`,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                    className: \"h-4 w-4 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 737,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium\",\n                                                                            children: section.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 739,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: section.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                            lineNumber: 740,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 738,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Bot_ChevronRight_Code_Copy_Database_FileText_Play_Search_Shield_Terminal_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                                    lineNumber: 742,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, section.id, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                            lineNumber: 728,\n                                                            columnNumber: 25\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                    lineNumber: 724,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 722,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 721,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                                    lineNumber: 720,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-8\",\n                                            children: (()=>{\n                                                const section = docSections.find((s)=>s.id === activeSection);\n                                                if (!section) return null;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"prose prose-invert max-w-none\",\n                                                    children: renderContent(section.content)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                                                    lineNumber: 760,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                                            lineNumber: 754,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                                        lineNumber: 753,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                                    lineNumber: 752,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                            lineNumber: 718,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                    lineNumber: 690,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n                lineNumber: 689,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\docs\\\\page.tsx\",\n        lineNumber: 686,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvZG9jcy9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRWdDO0FBQ1E7QUFDVTtBQW9CN0I7QUFVTixTQUFTYztJQUN0QixNQUFNLENBQUNDLEtBQUssR0FBR2YsK0NBQVFBLENBQUM7UUFDdEJnQixVQUFVO1FBQ1ZDLFFBQVE7UUFDUkMsTUFBTTtRQUNOQyxNQUFNO0lBQ1I7SUFFQSxNQUFNLENBQUNDLGVBQWVDLGlCQUFpQixHQUFHckIsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDc0IsYUFBYUMsZUFBZSxHQUFHdkIsK0NBQVFBLENBQUM7SUFFL0MsTUFBTXdCLGNBQTRCO1FBQ2hDO1lBQ0VDLElBQUk7WUFDSkMsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLE1BQU1oQiw2SkFBSUE7WUFDVmlCLFNBQVM7Z0JBQ1A7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtRQUNIO1FBQ0E7WUFDRUosSUFBSTtZQUNKQyxPQUFPO1lBQ1BDLGFBQWE7WUFDYkMsTUFBTXhCLDZKQUFJQTtZQUNWeUIsU0FBUztnQkFDUDtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTthQUNEO1FBQ0g7UUFDQTtZQUNFSixJQUFJO1lBQ0pDLE9BQU87WUFDUEMsYUFBYTtZQUNiQyxNQUFNbEIsNkpBQU1BO1lBQ1ptQixTQUFTO2dCQUNQO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2FBQ0Q7UUFDSDtRQUNBO1lBQ0VKLElBQUk7WUFDSkMsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLE1BQU10Qiw2SkFBTUE7WUFDWnVCLFNBQVM7Z0JBQ1A7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtRQUNIO1FBQ0E7WUFDRUosSUFBSTtZQUNKQyxPQUFPO1lBQ1BDLGFBQWE7WUFDYkMsTUFBTXBCLDZKQUFRQTtZQUNkcUIsU0FBUztnQkFDUDtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTthQUNEO1FBQ0g7UUFDQTtZQUNFSixJQUFJO1lBQ0pDLE9BQU87WUFDUEMsYUFBYTtZQUNiQyxNQUFNZiw2SkFBUUE7WUFDZGdCLFNBQVM7Z0JBQ1A7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtRQUNIO1FBQ0E7WUFDRUosSUFBSTtZQUNKQyxPQUFPO1lBQ1BDLGFBQWE7WUFDYkMsTUFBTXJCLDhKQUFHQTtZQUNUc0IsU0FBUztnQkFDUDtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTthQUNEO1FBQ0g7UUFDQTtZQUNFSixJQUFJO1lBQ0pDLE9BQU87WUFDUEMsYUFBYTtZQUNiQyxNQUFNdkIsOEpBQVFBO1lBQ2R3QixTQUFTO2dCQUNQO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2FBQ0Q7UUFDSDtLQUNEO0lBRUQsTUFBTUMsbUJBQW1CTixZQUFZTyxNQUFNLENBQUNDLENBQUFBLFVBQzFDQSxRQUFRTixLQUFLLENBQUNPLFdBQVcsR0FBR0MsUUFBUSxDQUFDWixZQUFZVyxXQUFXLE9BQzVERCxRQUFRTCxXQUFXLENBQUNNLFdBQVcsR0FBR0MsUUFBUSxDQUFDWixZQUFZVyxXQUFXLE9BQ2xFRCxRQUFRSCxPQUFPLENBQUNNLElBQUksQ0FBQ0MsQ0FBQUEsT0FBUUEsS0FBS0gsV0FBVyxHQUFHQyxRQUFRLENBQUNaLFlBQVlXLFdBQVc7SUFHbEYsTUFBTUksa0JBQWtCLENBQUNDO1FBQ3ZCQyxVQUFVQyxTQUFTLENBQUNDLFNBQVMsQ0FBQ0g7SUFDaEM7SUFFQSxNQUFNSSxnQkFBZ0IsQ0FBQ2I7UUFDckIsT0FBT0EsUUFBUWMsR0FBRyxDQUFDLENBQUNQLE1BQU1RO1lBQ3hCLElBQUlSLEtBQUtTLFVBQVUsQ0FBQyxPQUFPO2dCQUN6QixxQkFBTyw4REFBQ0M7b0JBQWVDLFdBQVU7OEJBQXNDWCxLQUFLWSxTQUFTLENBQUM7bUJBQXRFSjs7Ozs7WUFDbEIsT0FBTyxJQUFJUixLQUFLUyxVQUFVLENBQUMsUUFBUTtnQkFDakMscUJBQU8sOERBQUNJO29CQUFlRixXQUFVOzhCQUE4Q1gsS0FBS1ksU0FBUyxDQUFDO21CQUE5RUo7Ozs7O1lBQ2xCLE9BQU8sSUFBSVIsS0FBS1MsVUFBVSxDQUFDLFNBQVM7Z0JBQ2xDLHFCQUFPLDhEQUFDSztvQkFBZUgsV0FBVTs4QkFBb0RYLEtBQUtZLFNBQVMsQ0FBQzttQkFBcEZKOzs7OztZQUNsQixPQUFPLElBQUlSLEtBQUtTLFVBQVUsQ0FBQyxRQUFRO2dCQUNqQyxNQUFNTSxZQUFZdEIsUUFBUXVCLFNBQVMsQ0FBQyxDQUFDQyxHQUFHQyxJQUFNQSxJQUFJVixTQUFTUyxFQUFFUixVQUFVLENBQUM7Z0JBQ3hFLElBQUlNLFlBQVlQLE9BQU87b0JBQ3JCLE1BQU1XLFlBQVkxQixRQUFRMkIsS0FBSyxDQUFDWixRQUFRLEdBQUdPO29CQUMzQyxNQUFNTSxPQUFPRixVQUFVRyxJQUFJLENBQUM7b0JBQzVCLHFCQUNFLDhEQUFDQzt3QkFBZ0JaLFdBQVU7OzBDQUN6Qiw4REFBQ2E7Z0NBQ0NDLFNBQVMsSUFBTXhCLGdCQUFnQm9CO2dDQUMvQlYsV0FBVTswQ0FFViw0RUFBQ3RDLDhKQUFJQTtvQ0FBQ3NDLFdBQVU7Ozs7Ozs7Ozs7OzBDQUVsQiw4REFBQ2U7Z0NBQUlmLFdBQVU7MENBQ2IsNEVBQUNVOzhDQUFNQTs7Ozs7Ozs7Ozs7O3VCQVJEYjs7Ozs7Z0JBWWQ7Z0JBQ0EsT0FBTztZQUNULE9BQU8sSUFBSVIsS0FBS1MsVUFBVSxDQUFDLE9BQU87Z0JBQ2hDLHFCQUFPLDhEQUFDa0I7b0JBQWVoQixXQUFVOzhCQUEyQlgsS0FBS1ksU0FBUyxDQUFDO21CQUEzREo7Ozs7O1lBQ2xCLE9BQU8sSUFBSVIsU0FBUyxJQUFJO2dCQUN0QixxQkFBTyw4REFBQzRCLFVBQVFwQjs7Ozs7WUFDbEIsT0FBTztnQkFDTCxxQkFBTyw4REFBQ3FCO29CQUFjbEIsV0FBVTs4QkFBc0JYO21CQUF2Q1E7Ozs7O1lBQ2pCO1FBQ0YsR0FBR2IsTUFBTSxDQUFDbUM7SUFDWjtJQUVBLHFCQUNFLDhEQUFDUDtRQUFJWixXQUFVOzswQkFDYiw4REFBQzlDLDBEQUFNQTtnQkFBQ2MsTUFBTUE7Ozs7OzswQkFFZCw4REFBQzRDO2dCQUFJWixXQUFVOzBCQUNiLDRFQUFDWTtvQkFBSVosV0FBVTs7c0NBRWIsOERBQUNZOzRCQUFJWixXQUFVOzs4Q0FDYiw4REFBQ1k7b0NBQUlaLFdBQVU7O3NEQUNiLDhEQUFDNUMsOEpBQVFBOzRDQUFDNEMsV0FBVTs7Ozs7O3NEQUNwQiw4REFBQ0Q7NENBQUdDLFdBQVU7c0RBQ1osNEVBQUNvQjtnREFBS3BCLFdBQVU7MERBQWE7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUdqQyw4REFBQ2tCO29DQUFFbEIsV0FBVTs4Q0FBMEI7Ozs7Ozs7Ozs7OztzQ0FNekMsOERBQUNZOzRCQUFJWixXQUFVO3NDQUNiLDRFQUFDWTtnQ0FBSVosV0FBVTs7a0RBQ2IsOERBQUNyQyw2SkFBTUE7d0NBQUNxQyxXQUFVOzs7Ozs7a0RBQ2xCLDhEQUFDcUI7d0NBQ0NDLE1BQUs7d0NBQ0xDLE9BQU9oRDt3Q0FDUGlELFVBQVUsQ0FBQ0MsSUFBTWpELGVBQWVpRCxFQUFFQyxNQUFNLENBQUNILEtBQUs7d0NBQzlDSSxhQUFZO3dDQUNaM0IsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBS2hCLDhEQUFDWTs0QkFBSVosV0FBVTs7OENBRWIsOERBQUNZOzhDQUNDLDRFQUFDekQsa0RBQUlBO2tEQUNILDRFQUFDeUQ7NENBQUlaLFdBQVU7OzhEQUNiLDhEQUFDRztvREFBR0gsV0FBVTs4REFBb0M7Ozs7Ozs4REFDbEQsOERBQUM0QjtvREFBSTVCLFdBQVU7OERBQ1pqQixpQkFBaUJhLEdBQUcsQ0FBQyxDQUFDWDt3REFDckIsTUFBTTRDLE9BQU81QyxRQUFRSixJQUFJO3dEQUN6QixxQkFDRSw4REFBQ2dDOzREQUVDQyxTQUFTLElBQU14QyxpQkFBaUJXLFFBQVFQLEVBQUU7NERBQzFDc0IsV0FBVyxDQUFDLHdGQUF3RixFQUNsRzNCLGtCQUFrQlksUUFBUVAsRUFBRSxHQUN4QixpRUFDQSxzREFDTCxDQUFDOzs4RUFFRiw4REFBQ21EO29FQUFLN0IsV0FBVTs7Ozs7OzhFQUNoQiw4REFBQ1k7b0VBQUlaLFdBQVU7O3NGQUNiLDhEQUFDWTs0RUFBSVosV0FBVTtzRkFBZWYsUUFBUU4sS0FBSzs7Ozs7O3NGQUMzQyw4REFBQ2lDOzRFQUFJWixXQUFVO3NGQUF5QmYsUUFBUUwsV0FBVzs7Ozs7Ozs7Ozs7OzhFQUU3RCw4REFBQ2hCLDhKQUFZQTtvRUFBQ29DLFdBQVU7Ozs7Ozs7MkRBYm5CZixRQUFRUCxFQUFFOzs7OztvREFnQnJCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQU9SLDhEQUFDa0M7b0NBQUlaLFdBQVU7OENBQ2IsNEVBQUM3QyxrREFBSUE7a0RBQ0gsNEVBQUN5RDs0Q0FBSVosV0FBVTtzREFDWixDQUFDO2dEQUNBLE1BQU1mLFVBQVVSLFlBQVlxRCxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVyRCxFQUFFLEtBQUtMO2dEQUMvQyxJQUFJLENBQUNZLFNBQVMsT0FBTztnREFFckIscUJBQ0UsOERBQUMyQjtvREFBSVosV0FBVTs4REFDWkwsY0FBY1YsUUFBUUgsT0FBTzs7Ozs7OzRDQUdwQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBU2xCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va29kZXhndWFyZC8uL2FwcC9kb2NzL3BhZ2UudHN4PzNiOGYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgTmF2YmFyIGZyb20gJ0AvY29tcG9uZW50cy9OYXZiYXInXG5pbXBvcnQgeyBDYXJkLCBTdGF0Q2FyZCB9IGZyb20gJ0AvY29tcG9uZW50cy9DYXJkJ1xuaW1wb3J0IHsgXG4gIEJvb2tPcGVuLCBcbiAgQ29kZSwgXG4gIFRlcm1pbmFsLFxuICBHbG9iZSxcbiAgS2V5LFxuICBTaGllbGQsXG4gIFphcCxcbiAgQm90LFxuICBGaWxlVGV4dCxcbiAgRG93bmxvYWQsXG4gIEV4dGVybmFsTGluayxcbiAgQ29weSxcbiAgU2VhcmNoLFxuICBDaGV2cm9uUmlnaHQsXG4gIFBsYXksXG4gIFNldHRpbmdzLFxuICBVc2VycyxcbiAgRGF0YWJhc2Vcbn0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuXG5pbnRlcmZhY2UgRG9jU2VjdGlvbiB7XG4gIGlkOiBzdHJpbmdcbiAgdGl0bGU6IHN0cmluZ1xuICBkZXNjcmlwdGlvbjogc3RyaW5nXG4gIGljb246IGFueVxuICBjb250ZW50OiBzdHJpbmdbXVxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBEb2NzUGFnZSgpIHtcbiAgY29uc3QgW3VzZXJdID0gdXNlU3RhdGUoe1xuICAgIHVzZXJuYW1lOiAnYWRtaW4nLFxuICAgIGF2YXRhcjogJycsXG4gICAgcm9sZTogJ3N1cGVyX2FkbWluJyxcbiAgICBwbGFuOiAnY3liZXJzZWN1cml0eSdcbiAgfSlcblxuICBjb25zdCBbYWN0aXZlU2VjdGlvbiwgc2V0QWN0aXZlU2VjdGlvbl0gPSB1c2VTdGF0ZSgnZ2V0dGluZy1zdGFydGVkJylcbiAgY29uc3QgW3NlYXJjaFF1ZXJ5LCBzZXRTZWFyY2hRdWVyeV0gPSB1c2VTdGF0ZSgnJylcblxuICBjb25zdCBkb2NTZWN0aW9uczogRG9jU2VjdGlvbltdID0gW1xuICAgIHtcbiAgICAgIGlkOiAnZ2V0dGluZy1zdGFydGVkJyxcbiAgICAgIHRpdGxlOiAnR2V0dGluZyBTdGFydGVkJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnUXVpY2sgc3RhcnQgZ3VpZGUgdW50dWsgbWVuZ2d1bmFrYW4gS29kZVhHdWFyZCcsXG4gICAgICBpY29uOiBQbGF5LFxuICAgICAgY29udGVudDogW1xuICAgICAgICAnIyBHZXR0aW5nIFN0YXJ0ZWQgd2l0aCBLb2RlWEd1YXJkJyxcbiAgICAgICAgJycsXG4gICAgICAgICcjIyBXZWxjb21lIHRvIEtvZGVYR3VhcmQnLFxuICAgICAgICAnS29kZVhHdWFyZCBhZGFsYWggcGxhdGZvcm0gY3liZXJzZWN1cml0eSBtYW5kaXJpIEluZG9uZXNpYSB5YW5nIG1lbnllZGlha2FuIHRvb2xzIHVudHVrIE9TSU5UIGludmVzdGlnYXRpb24sIHZ1bG5lcmFiaWxpdHkgc2Nhbm5pbmcsIGZpbGUgYW5hbHlzaXMsIGRhbiBib3QgYXV0b21hdGlvbi4nLFxuICAgICAgICAnJyxcbiAgICAgICAgJyMjIFF1aWNrIFN0YXJ0JyxcbiAgICAgICAgJzEuICoqUmVnaXN0ZXIgQWNjb3VudCoqOiBEYWZ0YXIgYWt1biBiYXJ1IGRpIGAvcmVnaXN0ZXJgJyxcbiAgICAgICAgJzIuICoqQ2hvb3NlIFBsYW4qKjogUGlsaWggcGxhbiB5YW5nIHNlc3VhaSBrZWJ1dHVoYW4nLFxuICAgICAgICAnMy4gKipHZXQgQVBJIEtleSoqOiBHZW5lcmF0ZSBBUEkga2V5IGRpIGhhbGFtYW4gcHJvZmlsZScsXG4gICAgICAgICc0LiAqKlN0YXJ0IFNjYW5uaW5nKio6IE11bGFpIGd1bmFrYW4gdG9vbHMgeWFuZyB0ZXJzZWRpYScsXG4gICAgICAgICcnLFxuICAgICAgICAnIyMgRGVtbyBDcmVkZW50aWFscycsXG4gICAgICAgICdgYGAnLFxuICAgICAgICAnRW1haWw6IGFkbWluQGtvZGV4Z3VhcmQuY29tJyxcbiAgICAgICAgJ1Bhc3N3b3JkOiBhZG1pbjEyMycsXG4gICAgICAgICdgYGAnLFxuICAgICAgICAnJyxcbiAgICAgICAgJyMjIEF2YWlsYWJsZSBUb29scycsXG4gICAgICAgICctICoqT1NJTlQgSW52ZXN0aWdhdG9yKio6IFBlbmNhcmlhbiBOSUssIE5QV1AsIGVtYWlsLCBkb21haW4nLFxuICAgICAgICAnLSAqKlZ1bG5lcmFiaWxpdHkgU2Nhbm5lcioqOiBEZXRla3NpIFNRTGksIFhTUywgTEZJLCBSQ0UnLFxuICAgICAgICAnLSAqKkZpbGUgQW5hbHl6ZXIqKjogQW5hbGlzaXMgbWFsd2FyZSwgd2Vic2hlbGwsIHNlY3JldHMnLFxuICAgICAgICAnLSAqKkNWRSBJbnRlbGxpZ2VuY2UqKjogRGF0YWJhc2UgQ1ZFIHRlcnVwZGF0ZScsXG4gICAgICAgICctICoqU2VjdXJpdHkgVG9vbHMqKjogSGFzaCwgZW5jb2RlLCBwYXlsb2FkIGdlbmVyYXRvcicsXG4gICAgICAgICctICoqQm90IENlbnRlcioqOiBXaGF0c0FwcCAmIFRlbGVncmFtIGF1dG9tYXRpb24nXG4gICAgICBdXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJ2FwaS1yZWZlcmVuY2UnLFxuICAgICAgdGl0bGU6ICdBUEkgUmVmZXJlbmNlJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnRG9rdW1lbnRhc2kgbGVuZ2thcCBBUEkgZW5kcG9pbnRzIEtvZGVYR3VhcmQnLFxuICAgICAgaWNvbjogQ29kZSxcbiAgICAgIGNvbnRlbnQ6IFtcbiAgICAgICAgJyMgQVBJIFJlZmVyZW5jZScsXG4gICAgICAgICcnLFxuICAgICAgICAnIyMgQmFzZSBVUkwnLFxuICAgICAgICAnYGBgJyxcbiAgICAgICAgJ2h0dHBzOi8vYXBpLmtvZGV4Z3VhcmQuY29tL3YxJyxcbiAgICAgICAgJ2BgYCcsXG4gICAgICAgICcnLFxuICAgICAgICAnIyMgQXV0aGVudGljYXRpb24nLFxuICAgICAgICAnU2VtdWEgQVBJIGVuZHBvaW50IG1lbWVybHVrYW4gYXV0aGVudGljYXRpb24gbWVuZ2d1bmFrYW4gQmVhcmVyIHRva2VuOicsXG4gICAgICAgICcnLFxuICAgICAgICAnYGBgYmFzaCcsXG4gICAgICAgICdjdXJsIC1IIFwiQXV0aG9yaXphdGlvbjogQmVhcmVyIFlPVVJfQVBJX0tFWVwiIFxcXFwnLFxuICAgICAgICAnICAgICBodHRwczovL2FwaS5rb2RleGd1YXJkLmNvbS92MS9lbmRwb2ludCcsXG4gICAgICAgICdgYGAnLFxuICAgICAgICAnJyxcbiAgICAgICAgJyMjIFJhdGUgTGltaXRpbmcnLFxuICAgICAgICAnUmF0ZSBsaW1pdCBiZXJkYXNhcmthbiBwbGFuOicsXG4gICAgICAgICctICoqR3JhdGlzKio6IDEwMCBjYWxscy9kYXknLFxuICAgICAgICAnLSAqKlBlbGFqYXIqKjogMSwwMDAgY2FsbHMvZGF5JyxcbiAgICAgICAgJy0gKipIb2JieSoqOiA1LDAwMCBjYWxscy9kYXknLFxuICAgICAgICAnLSAqKkJ1ZyBIdW50ZXIqKjogMTAsMDAwIGNhbGxzL2RheScsXG4gICAgICAgICctICoqQ3liZXJzZWN1cml0eSoqOiAxMDAsMDAwIGNhbGxzL2RheScsXG4gICAgICAgICcnLFxuICAgICAgICAnIyMgUmVzcG9uc2UgRm9ybWF0JyxcbiAgICAgICAgJ1NlbXVhIHJlc3BvbnNlIGRhbGFtIGZvcm1hdCBKU09OOicsXG4gICAgICAgICcnLFxuICAgICAgICAnYGBganNvbicsXG4gICAgICAgICd7JyxcbiAgICAgICAgJyAgXCJzdWNjZXNzXCI6IHRydWUsJyxcbiAgICAgICAgJyAgXCJkYXRhXCI6IHsuLi59LCcsXG4gICAgICAgICcgIFwibWVzc2FnZVwiOiBcIlN1Y2Nlc3NcIiwnLFxuICAgICAgICAnICBcInRpbWVzdGFtcFwiOiBcIjIwMjQtMDEtMTVUMTA6MzA6MDBaXCInLFxuICAgICAgICAnfScsXG4gICAgICAgICdgYGAnLFxuICAgICAgICAnJyxcbiAgICAgICAgJyMjIEVycm9yIEhhbmRsaW5nJyxcbiAgICAgICAgJ0Vycm9yIHJlc3BvbnNlIGZvcm1hdDonLFxuICAgICAgICAnJyxcbiAgICAgICAgJ2BgYGpzb24nLFxuICAgICAgICAneycsXG4gICAgICAgICcgIFwic3VjY2Vzc1wiOiBmYWxzZSwnLFxuICAgICAgICAnICBcImVycm9yXCI6IFwiRXJyb3IgbWVzc2FnZVwiLCcsXG4gICAgICAgICcgIFwiY29kZVwiOiBcIkVSUk9SX0NPREVcIiwnLFxuICAgICAgICAnICBcInRpbWVzdGFtcFwiOiBcIjIwMjQtMDEtMTVUMTA6MzA6MDBaXCInLFxuICAgICAgICAnfScsXG4gICAgICAgICdgYGAnXG4gICAgICBdXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJ29zaW50LWFwaScsXG4gICAgICB0aXRsZTogJ09TSU5UIEFQSScsXG4gICAgICBkZXNjcmlwdGlvbjogJ0FQSSB1bnR1ayBPU0lOVCBpbnZlc3RpZ2F0aW9uIGRhbiBkYXRhIGdhdGhlcmluZycsXG4gICAgICBpY29uOiBTZWFyY2gsXG4gICAgICBjb250ZW50OiBbXG4gICAgICAgICcjIE9TSU5UIEFQSScsXG4gICAgICAgICcnLFxuICAgICAgICAnIyMgU2VhcmNoIEVuZHBvaW50JyxcbiAgICAgICAgJyoqUE9TVCoqIGAvYXBpL29zaW50L3NlYXJjaGAnLFxuICAgICAgICAnJyxcbiAgICAgICAgJyMjIyBSZXF1ZXN0IEJvZHknLFxuICAgICAgICAnYGBganNvbicsXG4gICAgICAgICd7JyxcbiAgICAgICAgJyAgXCJxdWVyeVwiOiBcImpvaG5AZXhhbXBsZS5jb21cIiwnLFxuICAgICAgICAnICBcInR5cGVcIjogXCJlbWFpbFwiLCcsXG4gICAgICAgICcgIFwiZGVlcFNlYXJjaFwiOiB0cnVlLCcsXG4gICAgICAgICcgIFwic291cmNlc1wiOiBbXCJkdWtjYXBpbFwiLCBcImtlbWtlc1wiLCBcImdpdGh1YlwiXScsXG4gICAgICAgICd9JyxcbiAgICAgICAgJ2BgYCcsXG4gICAgICAgICcnLFxuICAgICAgICAnIyMjIFBhcmFtZXRlcnMnLFxuICAgICAgICAnLSBgcXVlcnlgIChzdHJpbmcsIHJlcXVpcmVkKTogU2VhcmNoIHF1ZXJ5JyxcbiAgICAgICAgJy0gYHR5cGVgIChzdHJpbmcsIHJlcXVpcmVkKTogU2VhcmNoIHR5cGUgKGVtYWlsLCBwaG9uZSwgbmlrLCBucHdwLCBkb21haW4sIGltZWksIG5hbWUsIGFkZHJlc3MpJyxcbiAgICAgICAgJy0gYGRlZXBTZWFyY2hgIChib29sZWFuLCBvcHRpb25hbCk6IEVuYWJsZSBkZWVwIHNlYXJjaCcsXG4gICAgICAgICctIGBzb3VyY2VzYCAoYXJyYXksIG9wdGlvbmFsKTogU3BlY2lmaWMgZGF0YSBzb3VyY2VzJyxcbiAgICAgICAgJycsXG4gICAgICAgICcjIyMgUmVzcG9uc2UnLFxuICAgICAgICAnYGBganNvbicsXG4gICAgICAgICd7JyxcbiAgICAgICAgJyAgXCJzdWNjZXNzXCI6IHRydWUsJyxcbiAgICAgICAgJyAgXCJkYXRhXCI6IHsnLFxuICAgICAgICAnICAgIFwicmVzdWx0c1wiOiBbJyxcbiAgICAgICAgJyAgICAgIHsnLFxuICAgICAgICAnICAgICAgICBcInNvdXJjZVwiOiBcIkR1a2NhcGlsIERhdGFiYXNlXCIsJyxcbiAgICAgICAgJyAgICAgICAgXCJkYXRhXCI6IHsnLFxuICAgICAgICAnICAgICAgICAgIFwibmFtZVwiOiBcIkpvaG4gRG9lXCIsJyxcbiAgICAgICAgJyAgICAgICAgICBcImVtYWlsXCI6IFwiam9obkBleGFtcGxlLmNvbVwiLCcsXG4gICAgICAgICcgICAgICAgICAgXCJ2ZXJpZmllZFwiOiB0cnVlJyxcbiAgICAgICAgJyAgICAgICAgfSwnLFxuICAgICAgICAnICAgICAgICBcImNvbmZpZGVuY2VcIjogOTUsJyxcbiAgICAgICAgJyAgICAgICAgXCJ0aW1lc3RhbXBcIjogXCIyMDI0LTAxLTE1VDEwOjMwOjAwWlwiJyxcbiAgICAgICAgJyAgICAgIH0nLFxuICAgICAgICAnICAgIF0sJyxcbiAgICAgICAgJyAgICBcInRvdGFsUmVzdWx0c1wiOiAxLCcsXG4gICAgICAgICcgICAgXCJzZWFyY2hUaW1lXCI6IFwiMS4yc1wiJyxcbiAgICAgICAgJyAgfScsXG4gICAgICAgICd9JyxcbiAgICAgICAgJ2BgYCcsXG4gICAgICAgICcnLFxuICAgICAgICAnIyMgVHJhY2sgTG9jYXRpb24nLFxuICAgICAgICAnKipQT1NUKiogYC9hcGkvb3NpbnQvdHJhY2tgJyxcbiAgICAgICAgJycsXG4gICAgICAgICdUcmFjayBsb2NhdGlvbiBiZXJkYXNhcmthbiBub21vciBIUCBhdGF1IElNRUkuJyxcbiAgICAgICAgJycsXG4gICAgICAgICcjIyMgRXhhbXBsZScsXG4gICAgICAgICdgYGBiYXNoJyxcbiAgICAgICAgJ2N1cmwgLVggUE9TVCBodHRwczovL2FwaS5rb2RleGd1YXJkLmNvbS92MS9vc2ludC90cmFjayBcXFxcJyxcbiAgICAgICAgJyAgLUggXCJBdXRob3JpemF0aW9uOiBCZWFyZXIgWU9VUl9BUElfS0VZXCIgXFxcXCcsXG4gICAgICAgICcgIC1IIFwiQ29udGVudC1UeXBlOiBhcHBsaWNhdGlvbi9qc29uXCIgXFxcXCcsXG4gICAgICAgICcgIC1kIFxcJ3tcInRhcmdldFwiOiBcIis2MjgxMjM0NTY3OFwiLCBcInR5cGVcIjogXCJwaG9uZVwifVxcJycsXG4gICAgICAgICdgYGAnXG4gICAgICBdXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJ3NjYW5uZXItYXBpJyxcbiAgICAgIHRpdGxlOiAnU2Nhbm5lciBBUEknLFxuICAgICAgZGVzY3JpcHRpb246ICdBUEkgdW50dWsgdnVsbmVyYWJpbGl0eSBzY2FubmluZyBkYW4gc2VjdXJpdHkgdGVzdGluZycsXG4gICAgICBpY29uOiBTaGllbGQsXG4gICAgICBjb250ZW50OiBbXG4gICAgICAgICcjIFZ1bG5lcmFiaWxpdHkgU2Nhbm5lciBBUEknLFxuICAgICAgICAnJyxcbiAgICAgICAgJyMjIFN0YXJ0IFNjYW4nLFxuICAgICAgICAnKipQT1NUKiogYC9hcGkvc2Nhbi92dWxuZXJhYmlsaXR5YCcsXG4gICAgICAgICcnLFxuICAgICAgICAnIyMjIFJlcXVlc3QgQm9keScsXG4gICAgICAgICdgYGBqc29uJyxcbiAgICAgICAgJ3snLFxuICAgICAgICAnICBcInRhcmdldFwiOiBcImh0dHBzOi8vZXhhbXBsZS5jb21cIiwnLFxuICAgICAgICAnICBcInNjYW5UeXBlc1wiOiBbXCJzcWxpXCIsIFwieHNzXCIsIFwibGZpXCIsIFwicmNlXCJdLCcsXG4gICAgICAgICcgIFwibWF4RGVwdGhcIjogMywnLFxuICAgICAgICAnICBcInRpbWVvdXRcIjogMzAsJyxcbiAgICAgICAgJyAgXCJmb2xsb3dSZWRpcmVjdHNcIjogdHJ1ZScsXG4gICAgICAgICd9JyxcbiAgICAgICAgJ2BgYCcsXG4gICAgICAgICcnLFxuICAgICAgICAnIyMjIFBhcmFtZXRlcnMnLFxuICAgICAgICAnLSBgdGFyZ2V0YCAoc3RyaW5nLCByZXF1aXJlZCk6IFRhcmdldCBVUkwgdG8gc2NhbicsXG4gICAgICAgICctIGBzY2FuVHlwZXNgIChhcnJheSwgcmVxdWlyZWQpOiBWdWxuZXJhYmlsaXR5IHR5cGVzIHRvIHNjYW4nLFxuICAgICAgICAnLSBgbWF4RGVwdGhgIChudW1iZXIsIG9wdGlvbmFsKTogTWF4aW11bSBjcmF3bCBkZXB0aCAoZGVmYXVsdDogMyknLFxuICAgICAgICAnLSBgdGltZW91dGAgKG51bWJlciwgb3B0aW9uYWwpOiBSZXF1ZXN0IHRpbWVvdXQgaW4gc2Vjb25kcyAoZGVmYXVsdDogMzApJyxcbiAgICAgICAgJy0gYGZvbGxvd1JlZGlyZWN0c2AgKGJvb2xlYW4sIG9wdGlvbmFsKTogRm9sbG93IEhUVFAgcmVkaXJlY3RzIChkZWZhdWx0OiB0cnVlKScsXG4gICAgICAgICcnLFxuICAgICAgICAnIyMjIFN1cHBvcnRlZCBTY2FuIFR5cGVzJyxcbiAgICAgICAgJy0gYHNxbGlgOiBTUUwgSW5qZWN0aW9uJyxcbiAgICAgICAgJy0gYHhzc2A6IENyb3NzLVNpdGUgU2NyaXB0aW5nJyxcbiAgICAgICAgJy0gYGxmaWA6IExvY2FsIEZpbGUgSW5jbHVzaW9uJyxcbiAgICAgICAgJy0gYHJmaWA6IFJlbW90ZSBGaWxlIEluY2x1c2lvbicsXG4gICAgICAgICctIGByY2VgOiBSZW1vdGUgQ29kZSBFeGVjdXRpb24nLFxuICAgICAgICAnLSBgY3NyZmA6IENyb3NzLVNpdGUgUmVxdWVzdCBGb3JnZXJ5JyxcbiAgICAgICAgJy0gYHNzcmZgOiBTZXJ2ZXItU2lkZSBSZXF1ZXN0IEZvcmdlcnknLFxuICAgICAgICAnLSBgeHhlYDogWE1MIEV4dGVybmFsIEVudGl0eScsXG4gICAgICAgICctIGBpZG9yYDogSW5zZWN1cmUgRGlyZWN0IE9iamVjdCBSZWZlcmVuY2UnLFxuICAgICAgICAnLSBgcGF0aF90cmF2ZXJzYWxgOiBQYXRoIFRyYXZlcnNhbCcsXG4gICAgICAgICcnLFxuICAgICAgICAnIyMjIFJlc3BvbnNlJyxcbiAgICAgICAgJ2BgYGpzb24nLFxuICAgICAgICAneycsXG4gICAgICAgICcgIFwic3VjY2Vzc1wiOiB0cnVlLCcsXG4gICAgICAgICcgIFwiZGF0YVwiOiB7JyxcbiAgICAgICAgJyAgICBcInNjYW5JZFwiOiBcInNjYW5fMTIzNDU2Nzg5XCIsJyxcbiAgICAgICAgJyAgICBcInN0YXR1c1wiOiBcInN0YXJ0ZWRcIiwnLFxuICAgICAgICAnICAgIFwiZXN0aW1hdGVkVGltZVwiOiBcIjUtMTAgbWludXRlc1wiLCcsXG4gICAgICAgICcgICAgXCJ0YXJnZXRcIjogXCJodHRwczovL2V4YW1wbGUuY29tXCInLFxuICAgICAgICAnICB9JyxcbiAgICAgICAgJ30nLFxuICAgICAgICAnYGBgJyxcbiAgICAgICAgJycsXG4gICAgICAgICcjIyBHZXQgU2NhbiBSZXN1bHRzJyxcbiAgICAgICAgJyoqR0VUKiogYC9hcGkvc2Nhbi97c2NhbklkfS9yZXN1bHRzYCcsXG4gICAgICAgICcnLFxuICAgICAgICAnIyMjIFJlc3BvbnNlJyxcbiAgICAgICAgJ2BgYGpzb24nLFxuICAgICAgICAneycsXG4gICAgICAgICcgIFwic3VjY2Vzc1wiOiB0cnVlLCcsXG4gICAgICAgICcgIFwiZGF0YVwiOiB7JyxcbiAgICAgICAgJyAgICBcInNjYW5JZFwiOiBcInNjYW5fMTIzNDU2Nzg5XCIsJyxcbiAgICAgICAgJyAgICBcInN0YXR1c1wiOiBcImNvbXBsZXRlZFwiLCcsXG4gICAgICAgICcgICAgXCJ0YXJnZXRcIjogXCJodHRwczovL2V4YW1wbGUuY29tXCIsJyxcbiAgICAgICAgJyAgICBcInZ1bG5lcmFiaWxpdGllc1wiOiBbJyxcbiAgICAgICAgJyAgICAgIHsnLFxuICAgICAgICAnICAgICAgICBcInR5cGVcIjogXCJzcWxpXCIsJyxcbiAgICAgICAgJyAgICAgICAgXCJzZXZlcml0eVwiOiBcImNyaXRpY2FsXCIsJyxcbiAgICAgICAgJyAgICAgICAgXCJjdnNzU2NvcmVcIjogOS44LCcsXG4gICAgICAgICcgICAgICAgIFwidXJsXCI6IFwiaHR0cHM6Ly9leGFtcGxlLmNvbS9sb2dpbi5waHBcIiwnLFxuICAgICAgICAnICAgICAgICBcInBhcmFtZXRlclwiOiBcInVzZXJuYW1lXCIsJyxcbiAgICAgICAgJyAgICAgICAgXCJwYXlsb2FkXCI6IFwiYWRtaW5cXCcgT1IgXFwnMVxcJz1cXCcxXCIsJyxcbiAgICAgICAgJyAgICAgICAgXCJldmlkZW5jZVwiOiBcIk15U1FMIGVycm9yIGRldGVjdGVkXCIsJyxcbiAgICAgICAgJyAgICAgICAgXCJyZWNvbW1lbmRhdGlvblwiOiBcIlVzZSBwYXJhbWV0ZXJpemVkIHF1ZXJpZXNcIicsXG4gICAgICAgICcgICAgICB9JyxcbiAgICAgICAgJyAgICBdLCcsXG4gICAgICAgICcgICAgXCJzdW1tYXJ5XCI6IHsnLFxuICAgICAgICAnICAgICAgXCJ0b3RhbFwiOiAxLCcsXG4gICAgICAgICcgICAgICBcImNyaXRpY2FsXCI6IDEsJyxcbiAgICAgICAgJyAgICAgIFwiaGlnaFwiOiAwLCcsXG4gICAgICAgICcgICAgICBcIm1lZGl1bVwiOiAwLCcsXG4gICAgICAgICcgICAgICBcImxvd1wiOiAwJyxcbiAgICAgICAgJyAgICB9JyxcbiAgICAgICAgJyAgfScsXG4gICAgICAgICd9JyxcbiAgICAgICAgJ2BgYCdcbiAgICAgIF1cbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAnZmlsZS1hcGknLFxuICAgICAgdGl0bGU6ICdGaWxlIEFuYWx5c2lzIEFQSScsXG4gICAgICBkZXNjcmlwdGlvbjogJ0FQSSB1bnR1ayBhbmFsaXNpcyBmaWxlIGRhbiBkZXRla3NpIG1hbHdhcmUnLFxuICAgICAgaWNvbjogRmlsZVRleHQsXG4gICAgICBjb250ZW50OiBbXG4gICAgICAgICcjIEZpbGUgQW5hbHlzaXMgQVBJJyxcbiAgICAgICAgJycsXG4gICAgICAgICcjIyBBbmFseXplIEZpbGUnLFxuICAgICAgICAnKipQT1NUKiogYC9hcGkvZmlsZS9hbmFseXplYCcsXG4gICAgICAgICcnLFxuICAgICAgICAnVXBsb2FkIGRhbiBhbmFsaXNpcyBmaWxlIHVudHVrIGRldGVrc2kgbWFsd2FyZSwgd2Vic2hlbGwsIGRhbiBzZWNyZXRzLicsXG4gICAgICAgICcnLFxuICAgICAgICAnIyMjIFJlcXVlc3QgKG11bHRpcGFydC9mb3JtLWRhdGEpJyxcbiAgICAgICAgJ2BgYGJhc2gnLFxuICAgICAgICAnY3VybCAtWCBQT1NUIGh0dHBzOi8vYXBpLmtvZGV4Z3VhcmQuY29tL3YxL2ZpbGUvYW5hbHl6ZSBcXFxcJyxcbiAgICAgICAgJyAgLUggXCJBdXRob3JpemF0aW9uOiBCZWFyZXIgWU9VUl9BUElfS0VZXCIgXFxcXCcsXG4gICAgICAgICcgIC1GIFwiZmlsZT1Ac3VzcGljaW91cy5waHBcIiBcXFxcJyxcbiAgICAgICAgJyAgLUYgXCJhbmFseXNpc1R5cGU9d2Vic2hlbGxcIicsXG4gICAgICAgICdgYGAnLFxuICAgICAgICAnJyxcbiAgICAgICAgJyMjIyBQYXJhbWV0ZXJzJyxcbiAgICAgICAgJy0gYGZpbGVgIChmaWxlLCByZXF1aXJlZCk6IEZpbGUgdG8gYW5hbHl6ZScsXG4gICAgICAgICctIGBhbmFseXNpc1R5cGVgIChzdHJpbmcsIG9wdGlvbmFsKTogQW5hbHlzaXMgdHlwZSAobWFsd2FyZSwgd2Vic2hlbGwsIHNlY3JldCwgZ2VuZXJhbCknLFxuICAgICAgICAnJyxcbiAgICAgICAgJyMjIyBTdXBwb3J0ZWQgRmlsZSBUeXBlcycsXG4gICAgICAgICctICoqU2NyaXB0cyoqOiAucGhwLCAuanMsIC5weSwgLmFzcCwgLmpzcCcsXG4gICAgICAgICctICoqRG9jdW1lbnRzKio6IC50eHQsIC5wZGYsIC5kb2MsIC5kb2N4JyxcbiAgICAgICAgJy0gKipBcmNoaXZlcyoqOiAuemlwLCAucmFyLCAudGFyLCAuZ3onLFxuICAgICAgICAnLSAqKkV4ZWN1dGFibGVzKio6IC5leGUsIC5kbGwsIC5hcGsnLFxuICAgICAgICAnJyxcbiAgICAgICAgJyMjIyBSZXNwb25zZScsXG4gICAgICAgICdgYGBqc29uJyxcbiAgICAgICAgJ3snLFxuICAgICAgICAnICBcInN1Y2Nlc3NcIjogdHJ1ZSwnLFxuICAgICAgICAnICBcImRhdGFcIjogeycsXG4gICAgICAgICcgICAgXCJhbmFseXNpc0lkXCI6IFwiYW5hbHlzaXNfNzg5MDEyMzQ1XCIsJyxcbiAgICAgICAgJyAgICBcImZpbGVuYW1lXCI6IFwic3VzcGljaW91cy5waHBcIiwnLFxuICAgICAgICAnICAgIFwiZmlsZVNpemVcIjogMjA0OCwnLFxuICAgICAgICAnICAgIFwibWltZVR5cGVcIjogXCJ0ZXh0L3gtcGhwXCIsJyxcbiAgICAgICAgJyAgICBcImhhc2hcIjogXCJzaGEyNTY6YWJjMTIzLi4uXCIsJyxcbiAgICAgICAgJyAgICBcInRocmVhdExldmVsXCI6IFwic3VzcGljaW91c1wiLCcsXG4gICAgICAgICcgICAgXCJyZXN1bHRzXCI6IHsnLFxuICAgICAgICAnICAgICAgXCJ3ZWJzaGVsbERldGVjdGlvblwiOiB7JyxcbiAgICAgICAgJyAgICAgICAgXCJkZXRlY3RlZFwiOiB0cnVlLCcsXG4gICAgICAgICcgICAgICAgIFwidHlwZVwiOiBcIlBIUCBXZWJzaGVsbFwiLCcsXG4gICAgICAgICcgICAgICAgIFwib2JmdXNjYXRlZFwiOiBmYWxzZSwnLFxuICAgICAgICAnICAgICAgICBcImNvbmZpZGVuY2VcIjogOTUnLFxuICAgICAgICAnICAgICAgfSwnLFxuICAgICAgICAnICAgICAgXCJzZWNyZXREZXRlY3Rpb25cIjogeycsXG4gICAgICAgICcgICAgICAgIFwiZGV0ZWN0ZWRcIjogdHJ1ZSwnLFxuICAgICAgICAnICAgICAgICBcInNlY3JldHNcIjogWycsXG4gICAgICAgICcgICAgICAgICAgeycsXG4gICAgICAgICcgICAgICAgICAgICBcInR5cGVcIjogXCJBUEkgS2V5XCIsJyxcbiAgICAgICAgJyAgICAgICAgICAgIFwidmFsdWVcIjogXCJzay0xMjM0NTY3ODkwYWJjZGVmXCIsJyxcbiAgICAgICAgJyAgICAgICAgICAgIFwiY29uZmlkZW5jZVwiOiA4NycsXG4gICAgICAgICcgICAgICAgICAgfScsXG4gICAgICAgICcgICAgICAgIF0nLFxuICAgICAgICAnICAgICAgfScsXG4gICAgICAgICcgICAgfScsXG4gICAgICAgICcgIH0nLFxuICAgICAgICAnfScsXG4gICAgICAgICdgYGAnLFxuICAgICAgICAnJyxcbiAgICAgICAgJyMjIEdldCBBbmFseXNpcyBSZXN1bHRzJyxcbiAgICAgICAgJyoqR0VUKiogYC9hcGkvZmlsZS9hbmFseXNpcy97YW5hbHlzaXNJZH1gJyxcbiAgICAgICAgJycsXG4gICAgICAgICdSZXRyaWV2ZSBkZXRhaWxlZCBhbmFseXNpcyByZXN1bHRzIGJ5IGFuYWx5c2lzIElELidcbiAgICAgIF1cbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAnY3ZlLWFwaScsXG4gICAgICB0aXRsZTogJ0NWRSBBUEknLFxuICAgICAgZGVzY3JpcHRpb246ICdBUEkgdW50dWsgQ1ZFIGludGVsbGlnZW5jZSBkYW4gdnVsbmVyYWJpbGl0eSBkYXRhYmFzZScsXG4gICAgICBpY29uOiBEYXRhYmFzZSxcbiAgICAgIGNvbnRlbnQ6IFtcbiAgICAgICAgJyMgQ1ZFIEludGVsbGlnZW5jZSBBUEknLFxuICAgICAgICAnJyxcbiAgICAgICAgJyMjIFNlYXJjaCBDVkUgRGF0YWJhc2UnLFxuICAgICAgICAnKipHRVQqKiBgL2FwaS9jdmUvc2VhcmNoYCcsXG4gICAgICAgICcnLFxuICAgICAgICAnIyMjIFF1ZXJ5IFBhcmFtZXRlcnMnLFxuICAgICAgICAnLSBgcXVlcnlgIChzdHJpbmcsIG9wdGlvbmFsKTogU2VhcmNoIHF1ZXJ5JyxcbiAgICAgICAgJy0gYHNldmVyaXR5YCAoc3RyaW5nLCBvcHRpb25hbCk6IEZpbHRlciBieSBzZXZlcml0eSAoY3JpdGljYWwsIGhpZ2gsIG1lZGl1bSwgbG93KScsXG4gICAgICAgICctIGB5ZWFyYCAoc3RyaW5nLCBvcHRpb25hbCk6IEZpbHRlciBieSB5ZWFyICgyMDI0LCAyMDIzLCBldGMuKScsXG4gICAgICAgICctIGBsaW1pdGAgKG51bWJlciwgb3B0aW9uYWwpOiBOdW1iZXIgb2YgcmVzdWx0cyAoZGVmYXVsdDogMjAsIG1heDogMTAwKScsXG4gICAgICAgICctIGBvZmZzZXRgIChudW1iZXIsIG9wdGlvbmFsKTogUGFnaW5hdGlvbiBvZmZzZXQgKGRlZmF1bHQ6IDApJyxcbiAgICAgICAgJycsXG4gICAgICAgICcjIyMgRXhhbXBsZScsXG4gICAgICAgICdgYGBiYXNoJyxcbiAgICAgICAgJ2N1cmwgXCJodHRwczovL2FwaS5rb2RleGd1YXJkLmNvbS92MS9jdmUvc2VhcmNoP3F1ZXJ5PVNRTCUyMGluamVjdGlvbiZzZXZlcml0eT1jcml0aWNhbCZsaW1pdD0xMFwiIFxcXFwnLFxuICAgICAgICAnICAtSCBcIkF1dGhvcml6YXRpb246IEJlYXJlciBZT1VSX0FQSV9LRVlcIicsXG4gICAgICAgICdgYGAnLFxuICAgICAgICAnJyxcbiAgICAgICAgJyMjIyBSZXNwb25zZScsXG4gICAgICAgICdgYGBqc29uJyxcbiAgICAgICAgJ3snLFxuICAgICAgICAnICBcInN1Y2Nlc3NcIjogdHJ1ZSwnLFxuICAgICAgICAnICBcImRhdGFcIjogeycsXG4gICAgICAgICcgICAgXCJ0b3RhbFwiOiAxNTYsJyxcbiAgICAgICAgJyAgICBcImN2ZXNcIjogWycsXG4gICAgICAgICcgICAgICB7JyxcbiAgICAgICAgJyAgICAgICAgXCJjdmVJZFwiOiBcIkNWRS0yMDI0LTAwMDFcIiwnLFxuICAgICAgICAnICAgICAgICBcImRlc2NyaXB0aW9uXCI6IFwiU1FMIGluamVjdGlvbiB2dWxuZXJhYmlsaXR5Li4uXCIsJyxcbiAgICAgICAgJyAgICAgICAgXCJzZXZlcml0eVwiOiBcImNyaXRpY2FsXCIsJyxcbiAgICAgICAgJyAgICAgICAgXCJjdnNzU2NvcmVcIjogOS44LCcsXG4gICAgICAgICcgICAgICAgIFwiY3Zzc1ZlY3RvclwiOiBcIkNWU1M6My4xL0FWOk4vQUM6TC9QUjpOL1VJOk4vUzpVL0M6SC9JOkgvQTpIXCIsJyxcbiAgICAgICAgJyAgICAgICAgXCJwdWJsaXNoZWREYXRlXCI6IFwiMjAyNC0wMS0xNVwiLCcsXG4gICAgICAgICcgICAgICAgIFwibW9kaWZpZWREYXRlXCI6IFwiMjAyNC0wMS0xNlwiLCcsXG4gICAgICAgICcgICAgICAgIFwiYWZmZWN0ZWRQcm9kdWN0c1wiOiBbXCJXZWJBcHAgMS4wXCIsIFwiV2ViQXBwIDIuMFwiXSwnLFxuICAgICAgICAnICAgICAgICBcInJlZmVyZW5jZXNcIjogW1wiaHR0cHM6Ly9udmQubmlzdC5nb3YvdnVsbi9kZXRhaWwvQ1ZFLTIwMjQtMDAwMVwiXSwnLFxuICAgICAgICAnICAgICAgICBcImV4cGxvaXRzXCI6IFsnLFxuICAgICAgICAnICAgICAgICAgIHsnLFxuICAgICAgICAnICAgICAgICAgICAgXCJ0aXRsZVwiOiBcIlNRTCBJbmplY3Rpb24gRXhwbG9pdFwiLCcsXG4gICAgICAgICcgICAgICAgICAgICBcInVybFwiOiBcImh0dHBzOi8vZXhwbG9pdC1kYi5jb20vZXhwbG9pdHMvMTIzNDVcIiwnLFxuICAgICAgICAnICAgICAgICAgICAgXCJ0eXBlXCI6IFwiUHVibGljIEV4cGxvaXRcIicsXG4gICAgICAgICcgICAgICAgICAgfScsXG4gICAgICAgICcgICAgICAgIF0nLFxuICAgICAgICAnICAgICAgfScsXG4gICAgICAgICcgICAgXScsXG4gICAgICAgICcgIH0nLFxuICAgICAgICAnfScsXG4gICAgICAgICdgYGAnLFxuICAgICAgICAnJyxcbiAgICAgICAgJyMjIEdldCBDVkUgRGV0YWlscycsXG4gICAgICAgICcqKkdFVCoqIGAvYXBpL2N2ZS97Y3ZlSWR9YCcsXG4gICAgICAgICcnLFxuICAgICAgICAnR2V0IGRldGFpbGVkIGluZm9ybWF0aW9uIGFib3V0IHNwZWNpZmljIENWRS4nLFxuICAgICAgICAnJyxcbiAgICAgICAgJyMjIyBFeGFtcGxlJyxcbiAgICAgICAgJ2BgYGJhc2gnLFxuICAgICAgICAnY3VybCBodHRwczovL2FwaS5rb2RleGd1YXJkLmNvbS92MS9jdmUvQ1ZFLTIwMjQtMDAwMSBcXFxcJyxcbiAgICAgICAgJyAgLUggXCJBdXRob3JpemF0aW9uOiBCZWFyZXIgWU9VUl9BUElfS0VZXCInLFxuICAgICAgICAnYGBgJ1xuICAgICAgXVxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICdib3QtaW50ZWdyYXRpb24nLFxuICAgICAgdGl0bGU6ICdCb3QgSW50ZWdyYXRpb24nLFxuICAgICAgZGVzY3JpcHRpb246ICdJbnRlZ3Jhc2kgV2hhdHNBcHAgZGFuIFRlbGVncmFtIGJvdCB1bnR1ayBhdXRvbWF0aW9uJyxcbiAgICAgIGljb246IEJvdCxcbiAgICAgIGNvbnRlbnQ6IFtcbiAgICAgICAgJyMgQm90IEludGVncmF0aW9uIEd1aWRlJyxcbiAgICAgICAgJycsXG4gICAgICAgICcjIyBXaGF0c0FwcCBCb3QgU2V0dXAnLFxuICAgICAgICAnJyxcbiAgICAgICAgJyMjIyBQcmVyZXF1aXNpdGVzJyxcbiAgICAgICAgJy0gUGxhbiBCdWcgSHVudGVyIGF0YXUgQ3liZXJzZWN1cml0eScsXG4gICAgICAgICctIFN1cGVyIEFkbWluIGFjY2VzcyB1bnR1ayBzZXR1cCcsXG4gICAgICAgICctIFdoYXRzQXBwIGFjY291bnQgdW50dWsgYm90JyxcbiAgICAgICAgJycsXG4gICAgICAgICcjIyMgU2V0dXAgU3RlcHMnLFxuICAgICAgICAnMS4gKipMb2dpbiBzZWJhZ2FpIFN1cGVyIEFkbWluKionLFxuICAgICAgICAnMi4gKipCdWthIEJvdCBDZW50ZXIqKiAoYC9ib3RgKScsXG4gICAgICAgICczLiAqKkFkZCBOZXcgQm90KiogZGVuZ2FuIHR5cGUgV2hhdHNBcHAnLFxuICAgICAgICAnNC4gKipTY2FuIFFSIENvZGUqKiBkZW5nYW4gV2hhdHNBcHAnLFxuICAgICAgICAnNS4gKipCb3QgUmVhZHkqKiB1bnR1ayBtZW5lcmltYSBjb21tYW5kcycsXG4gICAgICAgICcnLFxuICAgICAgICAnIyMjIEF2YWlsYWJsZSBDb21tYW5kcycsXG4gICAgICAgICdgYGAnLFxuICAgICAgICAnL3NjYW4gPHVybD4gICAgIC0gU3RhcnQgdnVsbmVyYWJpbGl0eSBzY2FuJyxcbiAgICAgICAgJy9zdGF0dXMgICAgICAgICAtIENoZWNrIHBsYW4gc3RhdHVzJyxcbiAgICAgICAgJy9oZWxwICAgICAgICAgICAtIFNob3cgYXZhaWxhYmxlIGNvbW1hbmRzJyxcbiAgICAgICAgJy9yZXN1bHRzIDxpZD4gICAtIEdldCBzY2FuIHJlc3VsdHMnLFxuICAgICAgICAnYGBgJyxcbiAgICAgICAgJycsXG4gICAgICAgICcjIyMgRXhhbXBsZSBVc2FnZScsXG4gICAgICAgICdgYGAnLFxuICAgICAgICAnVXNlcjogL3NjYW4gaHR0cHM6Ly9leGFtcGxlLmNvbScsXG4gICAgICAgICdCb3Q6ICBTdGFydGluZyB2dWxuZXJhYmlsaXR5IHNjYW4gZm9yIGh0dHBzOi8vZXhhbXBsZS5jb20uLi4nLFxuICAgICAgICAnICAgICAgU2NhbiBJRDogc2Nhbl8xMjM0NTY3ODknLFxuICAgICAgICAnICAgICAgRXN0aW1hdGVkIHRpbWU6IDUtMTAgbWludXRlcycsXG4gICAgICAgICcnLFxuICAgICAgICAnVXNlcjogL3Jlc3VsdHMgc2Nhbl8xMjM0NTY3ODknLFxuICAgICAgICAnQm90OiAgU2NhbiBSZXN1bHRzOicsXG4gICAgICAgICcgICAgICDinIUgQ29tcGxldGVkJyxcbiAgICAgICAgJyAgICAgIPCflLQgMSBDcml0aWNhbCB2dWxuZXJhYmlsaXR5IGZvdW5kJyxcbiAgICAgICAgJyAgICAgIPCfk4ogQ1ZTUyBTY29yZTogOS44JyxcbiAgICAgICAgJyAgICAgIPCfk4QgRnVsbCByZXBvcnQ6IGh0dHBzOi8va29kZXhndWFyZC5jb20vc2Nhbi8xMjM0NTY3ODknLFxuICAgICAgICAnYGBgJyxcbiAgICAgICAgJycsXG4gICAgICAgICcjIyBUZWxlZ3JhbSBCb3QgU2V0dXAnLFxuICAgICAgICAnJyxcbiAgICAgICAgJyMjIyBTZXR1cCBTdGVwcycsXG4gICAgICAgICcxLiAqKkNyZWF0ZSBCb3QqKiBkZW5nYW4gQEJvdEZhdGhlcicsXG4gICAgICAgICcyLiAqKkdldCBCb3QgVG9rZW4qKicsXG4gICAgICAgICczLiAqKkFkZCBCb3QqKiBkaSBLb2RlWEd1YXJkIEJvdCBDZW50ZXInLFxuICAgICAgICAnNC4gKipDb25maWd1cmUgV2ViaG9vayoqIChvdG9tYXRpcyknLFxuICAgICAgICAnJyxcbiAgICAgICAgJyMjIyBCb3QgQ29uZmlndXJhdGlvbicsXG4gICAgICAgICdgYGBqc29uJyxcbiAgICAgICAgJ3snLFxuICAgICAgICAnICBcImJvdFRva2VuXCI6IFwiWU9VUl9URUxFR1JBTV9CT1RfVE9LRU5cIiwnLFxuICAgICAgICAnICBcIndlYmhvb2tVcmxcIjogXCJodHRwczovL2FwaS5rb2RleGd1YXJkLmNvbS93ZWJob29rL3RlbGVncmFtXCIsJyxcbiAgICAgICAgJyAgXCJhbGxvd2VkVXNlcnNcIjogW1wiQHVzZXJuYW1lMVwiLCBcIkB1c2VybmFtZTJcIl0sJyxcbiAgICAgICAgJyAgXCJjb21tYW5kc1wiOiBbJyxcbiAgICAgICAgJyAgICB7XCJjb21tYW5kXCI6IFwic2NhblwiLCBcImRlc2NyaXB0aW9uXCI6IFwiU3RhcnQgdnVsbmVyYWJpbGl0eSBzY2FuXCJ9LCcsXG4gICAgICAgICcgICAge1wiY29tbWFuZFwiOiBcInN0YXR1c1wiLCBcImRlc2NyaXB0aW9uXCI6IFwiQ2hlY2sgcGxhbiBzdGF0dXNcIn0sJyxcbiAgICAgICAgJyAgICB7XCJjb21tYW5kXCI6IFwiaGVscFwiLCBcImRlc2NyaXB0aW9uXCI6IFwiU2hvdyBoZWxwXCJ9JyxcbiAgICAgICAgJyAgXScsXG4gICAgICAgICd9JyxcbiAgICAgICAgJ2BgYCcsXG4gICAgICAgICcnLFxuICAgICAgICAnIyMgQm90IEFQSSBJbnRlZ3JhdGlvbicsXG4gICAgICAgICcnLFxuICAgICAgICAnIyMjIFNlbmQgTWVzc2FnZSB2aWEgQVBJJyxcbiAgICAgICAgJyoqUE9TVCoqIGAvYXBpL2JvdC9zZW5kYCcsXG4gICAgICAgICcnLFxuICAgICAgICAnYGBganNvbicsXG4gICAgICAgICd7JyxcbiAgICAgICAgJyAgXCJib3RJZFwiOiBcImJvdF8xMjNcIiwnLFxuICAgICAgICAnICBcInJlY2lwaWVudFwiOiBcIis2MjgxMjM0NTY3OFwiLCcsXG4gICAgICAgICcgIFwibWVzc2FnZVwiOiBcIlNjYW4gY29tcGxldGVkISBDaGVjayByZXN1bHRzIGF0OiBodHRwczovLy4uLlwiJyxcbiAgICAgICAgJ30nLFxuICAgICAgICAnYGBgJyxcbiAgICAgICAgJycsXG4gICAgICAgICcjIyMgR2V0IEJvdCBTdGF0dXMnLFxuICAgICAgICAnKipHRVQqKiBgL2FwaS9ib3Qvc3RhdHVzYCcsXG4gICAgICAgICcnLFxuICAgICAgICAnUmV0dXJucyBzdGF0dXMgb2YgYWxsIGNvbmZpZ3VyZWQgYm90cy4nXG4gICAgICBdXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJ3Nkay1leGFtcGxlcycsXG4gICAgICB0aXRsZTogJ1NESyAmIEV4YW1wbGVzJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnQ29kZSBleGFtcGxlcyBkYW4gU0RLIHVudHVrIGJlcmJhZ2FpIGJhaGFzYSBwZW1yb2dyYW1hbicsXG4gICAgICBpY29uOiBUZXJtaW5hbCxcbiAgICAgIGNvbnRlbnQ6IFtcbiAgICAgICAgJyMgU0RLICYgQ29kZSBFeGFtcGxlcycsXG4gICAgICAgICcnLFxuICAgICAgICAnIyMgSmF2YVNjcmlwdC9Ob2RlLmpzJyxcbiAgICAgICAgJycsXG4gICAgICAgICcjIyMgSW5zdGFsbGF0aW9uJyxcbiAgICAgICAgJ2BgYGJhc2gnLFxuICAgICAgICAnbnBtIGluc3RhbGwga29kZXhndWFyZC1zZGsnLFxuICAgICAgICAnYGBgJyxcbiAgICAgICAgJycsXG4gICAgICAgICcjIyMgQmFzaWMgVXNhZ2UnLFxuICAgICAgICAnYGBgamF2YXNjcmlwdCcsXG4gICAgICAgICdjb25zdCBLb2RlWEd1YXJkID0gcmVxdWlyZShcXCdrb2RleGd1YXJkLXNka1xcJyk7JyxcbiAgICAgICAgJycsXG4gICAgICAgICdjb25zdCBjbGllbnQgPSBuZXcgS29kZVhHdWFyZCh7JyxcbiAgICAgICAgJyAgYXBpS2V5OiBcXCd5b3VyLWFwaS1rZXlcXCcsJyxcbiAgICAgICAgJyAgYmFzZVVybDogXFwnaHR0cHM6Ly9hcGkua29kZXhndWFyZC5jb20vdjFcXCcnLFxuICAgICAgICAnfSk7JyxcbiAgICAgICAgJycsXG4gICAgICAgICcvLyBPU0lOVCBTZWFyY2gnLFxuICAgICAgICAnY29uc3Qgb3NpbnRSZXN1bHQgPSBhd2FpdCBjbGllbnQub3NpbnQuc2VhcmNoKHsnLFxuICAgICAgICAnICBxdWVyeTogXFwnam9obkBleGFtcGxlLmNvbVxcJywnLFxuICAgICAgICAnICB0eXBlOiBcXCdlbWFpbFxcJywnLFxuICAgICAgICAnICBkZWVwU2VhcmNoOiB0cnVlJyxcbiAgICAgICAgJ30pOycsXG4gICAgICAgICcnLFxuICAgICAgICAnLy8gVnVsbmVyYWJpbGl0eSBTY2FuJyxcbiAgICAgICAgJ2NvbnN0IHNjYW4gPSBhd2FpdCBjbGllbnQuc2Nhbm5lci5zdGFydCh7JyxcbiAgICAgICAgJyAgdGFyZ2V0OiBcXCdodHRwczovL2V4YW1wbGUuY29tXFwnLCcsXG4gICAgICAgICcgIHNjYW5UeXBlczogW1xcJ3NxbGlcXCcsIFxcJ3hzc1xcJywgXFwnbGZpXFwnXScsXG4gICAgICAgICd9KTsnLFxuICAgICAgICAnJyxcbiAgICAgICAgJy8vIEdldCBSZXN1bHRzJyxcbiAgICAgICAgJ2NvbnN0IHJlc3VsdHMgPSBhd2FpdCBjbGllbnQuc2Nhbm5lci5nZXRSZXN1bHRzKHNjYW4uc2NhbklkKTsnLFxuICAgICAgICAnYGBgJyxcbiAgICAgICAgJycsXG4gICAgICAgICcjIyBQeXRob24nLFxuICAgICAgICAnJyxcbiAgICAgICAgJyMjIyBJbnN0YWxsYXRpb24nLFxuICAgICAgICAnYGBgYmFzaCcsXG4gICAgICAgICdwaXAgaW5zdGFsbCBrb2RleGd1YXJkLXB5dGhvbicsXG4gICAgICAgICdgYGAnLFxuICAgICAgICAnJyxcbiAgICAgICAgJyMjIyBCYXNpYyBVc2FnZScsXG4gICAgICAgICdgYGBweXRob24nLFxuICAgICAgICAnZnJvbSBrb2RleGd1YXJkIGltcG9ydCBLb2RlWEd1YXJkQ2xpZW50JyxcbiAgICAgICAgJycsXG4gICAgICAgICdjbGllbnQgPSBLb2RlWEd1YXJkQ2xpZW50KCcsXG4gICAgICAgICcgICAgYXBpX2tleT1cInlvdXItYXBpLWtleVwiLCcsXG4gICAgICAgICcgICAgYmFzZV91cmw9XCJodHRwczovL2FwaS5rb2RleGd1YXJkLmNvbS92MVwiJyxcbiAgICAgICAgJyknLFxuICAgICAgICAnJyxcbiAgICAgICAgJyMgT1NJTlQgU2VhcmNoJyxcbiAgICAgICAgJ29zaW50X3Jlc3VsdCA9IGNsaWVudC5vc2ludC5zZWFyY2goJyxcbiAgICAgICAgJyAgICBxdWVyeT1cImpvaG5AZXhhbXBsZS5jb21cIiwnLFxuICAgICAgICAnICAgIHR5cGU9XCJlbWFpbFwiLCcsXG4gICAgICAgICcgICAgZGVlcF9zZWFyY2g9VHJ1ZScsXG4gICAgICAgICcpJyxcbiAgICAgICAgJycsXG4gICAgICAgICcjIFZ1bG5lcmFiaWxpdHkgU2NhbicsXG4gICAgICAgICdzY2FuID0gY2xpZW50LnNjYW5uZXIuc3RhcnQoJyxcbiAgICAgICAgJyAgICB0YXJnZXQ9XCJodHRwczovL2V4YW1wbGUuY29tXCIsJyxcbiAgICAgICAgJyAgICBzY2FuX3R5cGVzPVtcInNxbGlcIiwgXCJ4c3NcIiwgXCJsZmlcIl0nLFxuICAgICAgICAnKScsXG4gICAgICAgICcnLFxuICAgICAgICAnIyBHZXQgUmVzdWx0cycsXG4gICAgICAgICdyZXN1bHRzID0gY2xpZW50LnNjYW5uZXIuZ2V0X3Jlc3VsdHMoc2NhbltcInNjYW5JZFwiXSknLFxuICAgICAgICAnYGBgJyxcbiAgICAgICAgJycsXG4gICAgICAgICcjIyBQSFAnLFxuICAgICAgICAnJyxcbiAgICAgICAgJyMjIyBJbnN0YWxsYXRpb24nLFxuICAgICAgICAnYGBgYmFzaCcsXG4gICAgICAgICdjb21wb3NlciByZXF1aXJlIGtvZGV4Z3VhcmQvcGhwLXNkaycsXG4gICAgICAgICdgYGAnLFxuICAgICAgICAnJyxcbiAgICAgICAgJyMjIyBCYXNpYyBVc2FnZScsXG4gICAgICAgICdgYGBwaHAnLFxuICAgICAgICAnPD9waHAnLFxuICAgICAgICAncmVxdWlyZV9vbmNlIFxcJ3ZlbmRvci9hdXRvbG9hZC5waHBcXCc7JyxcbiAgICAgICAgJycsXG4gICAgICAgICd1c2UgS29kZVhHdWFyZFxcXFxDbGllbnQ7JyxcbiAgICAgICAgJycsXG4gICAgICAgICckY2xpZW50ID0gbmV3IENsaWVudChbJyxcbiAgICAgICAgJyAgICBcXCdhcGlfa2V5XFwnID0+IFxcJ3lvdXItYXBpLWtleVxcJywnLFxuICAgICAgICAnICAgIFxcJ2Jhc2VfdXJsXFwnID0+IFxcJ2h0dHBzOi8vYXBpLmtvZGV4Z3VhcmQuY29tL3YxXFwnJyxcbiAgICAgICAgJ10pOycsXG4gICAgICAgICcnLFxuICAgICAgICAnLy8gT1NJTlQgU2VhcmNoJyxcbiAgICAgICAgJyRvc2ludFJlc3VsdCA9ICRjbGllbnQtPm9zaW50KCktPnNlYXJjaChbJyxcbiAgICAgICAgJyAgICBcXCdxdWVyeVxcJyA9PiBcXCdqb2huQGV4YW1wbGUuY29tXFwnLCcsXG4gICAgICAgICcgICAgXFwndHlwZVxcJyA9PiBcXCdlbWFpbFxcJywnLFxuICAgICAgICAnICAgIFxcJ2RlZXBTZWFyY2hcXCcgPT4gdHJ1ZScsXG4gICAgICAgICddKTsnLFxuICAgICAgICAnJyxcbiAgICAgICAgJy8vIFZ1bG5lcmFiaWxpdHkgU2NhbicsXG4gICAgICAgICckc2NhbiA9ICRjbGllbnQtPnNjYW5uZXIoKS0+c3RhcnQoWycsXG4gICAgICAgICcgICAgXFwndGFyZ2V0XFwnID0+IFxcJ2h0dHBzOi8vZXhhbXBsZS5jb21cXCcsJyxcbiAgICAgICAgJyAgICBcXCdzY2FuVHlwZXNcXCcgPT4gW1xcJ3NxbGlcXCcsIFxcJ3hzc1xcJywgXFwnbGZpXFwnXScsXG4gICAgICAgICddKTsnLFxuICAgICAgICAnPz4nLFxuICAgICAgICAnYGBgJyxcbiAgICAgICAgJycsXG4gICAgICAgICcjIyBjVVJMIEV4YW1wbGVzJyxcbiAgICAgICAgJycsXG4gICAgICAgICcjIyMgT1NJTlQgU2VhcmNoJyxcbiAgICAgICAgJ2BgYGJhc2gnLFxuICAgICAgICAnY3VybCAtWCBQT1NUIGh0dHBzOi8vYXBpLmtvZGV4Z3VhcmQuY29tL3YxL29zaW50L3NlYXJjaCBcXFxcJyxcbiAgICAgICAgJyAgLUggXCJBdXRob3JpemF0aW9uOiBCZWFyZXIgWU9VUl9BUElfS0VZXCIgXFxcXCcsXG4gICAgICAgICcgIC1IIFwiQ29udGVudC1UeXBlOiBhcHBsaWNhdGlvbi9qc29uXCIgXFxcXCcsXG4gICAgICAgICcgIC1kIFxcJ3tcInF1ZXJ5XCI6IFwiam9obkBleGFtcGxlLmNvbVwiLCBcInR5cGVcIjogXCJlbWFpbFwifVxcJycsXG4gICAgICAgICdgYGAnLFxuICAgICAgICAnJyxcbiAgICAgICAgJyMjIyBTdGFydCBWdWxuZXJhYmlsaXR5IFNjYW4nLFxuICAgICAgICAnYGBgYmFzaCcsXG4gICAgICAgICdjdXJsIC1YIFBPU1QgaHR0cHM6Ly9hcGkua29kZXhndWFyZC5jb20vdjEvc2Nhbi92dWxuZXJhYmlsaXR5IFxcXFwnLFxuICAgICAgICAnICAtSCBcIkF1dGhvcml6YXRpb246IEJlYXJlciBZT1VSX0FQSV9LRVlcIiBcXFxcJyxcbiAgICAgICAgJyAgLUggXCJDb250ZW50LVR5cGU6IGFwcGxpY2F0aW9uL2pzb25cIiBcXFxcJyxcbiAgICAgICAgJyAgLWQgXFwne1widGFyZ2V0XCI6IFwiaHR0cHM6Ly9leGFtcGxlLmNvbVwiLCBcInNjYW5UeXBlc1wiOiBbXCJzcWxpXCIsIFwieHNzXCJdfVxcJycsXG4gICAgICAgICdgYGAnXG4gICAgICBdXG4gICAgfVxuICBdXG5cbiAgY29uc3QgZmlsdGVyZWRTZWN0aW9ucyA9IGRvY1NlY3Rpb25zLmZpbHRlcihzZWN0aW9uID0+XG4gICAgc2VjdGlvbi50aXRsZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFF1ZXJ5LnRvTG93ZXJDYXNlKCkpIHx8XG4gICAgc2VjdGlvbi5kZXNjcmlwdGlvbi50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFF1ZXJ5LnRvTG93ZXJDYXNlKCkpIHx8XG4gICAgc2VjdGlvbi5jb250ZW50LnNvbWUobGluZSA9PiBsaW5lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoUXVlcnkudG9Mb3dlckNhc2UoKSkpXG4gIClcblxuICBjb25zdCBjb3B5VG9DbGlwYm9hcmQgPSAodGV4dDogc3RyaW5nKSA9PiB7XG4gICAgbmF2aWdhdG9yLmNsaXBib2FyZC53cml0ZVRleHQodGV4dClcbiAgfVxuXG4gIGNvbnN0IHJlbmRlckNvbnRlbnQgPSAoY29udGVudDogc3RyaW5nW10pID0+IHtcbiAgICByZXR1cm4gY29udGVudC5tYXAoKGxpbmUsIGluZGV4KSA9PiB7XG4gICAgICBpZiAobGluZS5zdGFydHNXaXRoKCcjICcpKSB7XG4gICAgICAgIHJldHVybiA8aDEga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItNFwiPntsaW5lLnN1YnN0cmluZygyKX08L2gxPlxuICAgICAgfSBlbHNlIGlmIChsaW5lLnN0YXJ0c1dpdGgoJyMjICcpKSB7XG4gICAgICAgIHJldHVybiA8aDIga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtd2hpdGUgbWItMyBtdC02XCI+e2xpbmUuc3Vic3RyaW5nKDMpfTwvaDI+XG4gICAgICB9IGVsc2UgaWYgKGxpbmUuc3RhcnRzV2l0aCgnIyMjICcpKSB7XG4gICAgICAgIHJldHVybiA8aDMga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtY3liZXItZ3JlZW4gbWItMiBtdC00XCI+e2xpbmUuc3Vic3RyaW5nKDQpfTwvaDM+XG4gICAgICB9IGVsc2UgaWYgKGxpbmUuc3RhcnRzV2l0aCgnYGBgJykpIHtcbiAgICAgICAgY29uc3QgbmV4dEluZGV4ID0gY29udGVudC5maW5kSW5kZXgoKGwsIGkpID0+IGkgPiBpbmRleCAmJiBsLnN0YXJ0c1dpdGgoJ2BgYCcpKVxuICAgICAgICBpZiAobmV4dEluZGV4ID4gaW5kZXgpIHtcbiAgICAgICAgICBjb25zdCBjb2RlTGluZXMgPSBjb250ZW50LnNsaWNlKGluZGV4ICsgMSwgbmV4dEluZGV4KVxuICAgICAgICAgIGNvbnN0IGNvZGUgPSBjb2RlTGluZXMuam9pbignXFxuJylcbiAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJyZWxhdGl2ZSBiZy1ncmF5LTkwMC81MCByb3VuZGVkLWxnIHAtNCBtYi00XCI+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBjb3B5VG9DbGlwYm9hcmQoY29kZSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTIgcmlnaHQtMiB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPENvcHkgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8cHJlIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1jeWJlci1ncmVlbiBvdmVyZmxvdy14LWF1dG9cIj5cbiAgICAgICAgICAgICAgICA8Y29kZT57Y29kZX08L2NvZGU+XG4gICAgICAgICAgICAgIDwvcHJlPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiBudWxsXG4gICAgICB9IGVsc2UgaWYgKGxpbmUuc3RhcnRzV2l0aCgnLSAnKSkge1xuICAgICAgICByZXR1cm4gPGxpIGtleT17aW5kZXh9IGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgbWItMSBtbC00XCI+e2xpbmUuc3Vic3RyaW5nKDIpfTwvbGk+XG4gICAgICB9IGVsc2UgaWYgKGxpbmUgPT09ICcnKSB7XG4gICAgICAgIHJldHVybiA8YnIga2V5PXtpbmRleH0gLz5cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHJldHVybiA8cCBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIG1iLTJcIj57bGluZX08L3A+XG4gICAgICB9XG4gICAgfSkuZmlsdGVyKEJvb2xlYW4pXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LWN5YmVyXCI+XG4gICAgICA8TmF2YmFyIHVzZXI9e3VzZXJ9IC8+XG4gICAgICBcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHQtMTZcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOCBweS04XCI+XG4gICAgICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLThcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIG1iLTRcIj5cbiAgICAgICAgICAgICAgPEJvb2tPcGVuIGNsYXNzTmFtZT1cImgtOCB3LTggdGV4dC1jeWJlci1ncmVlblwiIC8+XG4gICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgZm9udC1jeWJlciB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiY3liZXItdGV4dFwiPkRvY3VtZW50YXRpb248L3NwYW4+XG4gICAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgbWF4LXctM3hsXCI+XG4gICAgICAgICAgICAgIERva3VtZW50YXNpIGxlbmdrYXAgS29kZVhHdWFyZCBBUEksIFNESywgZGFuIHBhbmR1YW4gcGVuZ2d1bmFhbiB1bnR1ayBkZXZlbG9wZXIgZGFuIHNlY3VyaXR5IHJlc2VhcmNoZXIuXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogU2VhcmNoICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBtYXgtdy1tZFwiPlxuICAgICAgICAgICAgICA8U2VhcmNoIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMyB0b3AtMyBoLTQgdy00IHRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e3NlYXJjaFF1ZXJ5fVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2VhcmNoUXVlcnkoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VhcmNoIGRvY3VtZW50YXRpb24uLi5cIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImN5YmVyLWlucHV0IHBsLTEwIHctZnVsbFwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBsZzpncmlkLWNvbHMtNCBnYXAtOFwiPlxuICAgICAgICAgICAgey8qIFNpZGViYXIgKi99XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNlwiPlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItNFwiPkRvY3VtZW50YXRpb248L2gzPlxuICAgICAgICAgICAgICAgICAgPG5hdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAge2ZpbHRlcmVkU2VjdGlvbnMubWFwKChzZWN0aW9uKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgY29uc3QgSWNvbiA9IHNlY3Rpb24uaWNvblxuICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17c2VjdGlvbi5pZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlU2VjdGlvbihzZWN0aW9uLmlkKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy1mdWxsIHRleHQtbGVmdCBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgcC0zIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWN0aXZlU2VjdGlvbiA9PT0gc2VjdGlvbi5pZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctY3liZXItZ3JlZW4vMTAgdGV4dC1jeWJlci1ncmVlbiBib3JkZXIgYm9yZGVyLWN5YmVyLWdyZWVuJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAndGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LXdoaXRlIGhvdmVyOmJnLWdyYXktODAwLzUwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEljb24gY2xhc3NOYW1lPVwiaC00IHctNCBmbGV4LXNocmluay0wXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e3NlY3Rpb24udGl0bGV9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj57c2VjdGlvbi5kZXNjcmlwdGlvbn08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGV2cm9uUmlnaHQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICAgICAgPC9uYXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogQ29udGVudCAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6Y29sLXNwYW4tM1wiPlxuICAgICAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtOFwiPlxuICAgICAgICAgICAgICAgICAgeygoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHNlY3Rpb24gPSBkb2NTZWN0aW9ucy5maW5kKHMgPT4gcy5pZCA9PT0gYWN0aXZlU2VjdGlvbilcbiAgICAgICAgICAgICAgICAgICAgaWYgKCFzZWN0aW9uKSByZXR1cm4gbnVsbFxuICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInByb3NlIHByb3NlLWludmVydCBtYXgtdy1ub25lXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7cmVuZGVyQ29udGVudChzZWN0aW9uLmNvbnRlbnQpfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICB9KSgpfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwiTmF2YmFyIiwiQ2FyZCIsIkJvb2tPcGVuIiwiQ29kZSIsIlRlcm1pbmFsIiwiU2hpZWxkIiwiQm90IiwiRmlsZVRleHQiLCJDb3B5IiwiU2VhcmNoIiwiQ2hldnJvblJpZ2h0IiwiUGxheSIsIkRhdGFiYXNlIiwiRG9jc1BhZ2UiLCJ1c2VyIiwidXNlcm5hbWUiLCJhdmF0YXIiLCJyb2xlIiwicGxhbiIsImFjdGl2ZVNlY3Rpb24iLCJzZXRBY3RpdmVTZWN0aW9uIiwic2VhcmNoUXVlcnkiLCJzZXRTZWFyY2hRdWVyeSIsImRvY1NlY3Rpb25zIiwiaWQiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiaWNvbiIsImNvbnRlbnQiLCJmaWx0ZXJlZFNlY3Rpb25zIiwiZmlsdGVyIiwic2VjdGlvbiIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiLCJzb21lIiwibGluZSIsImNvcHlUb0NsaXBib2FyZCIsInRleHQiLCJuYXZpZ2F0b3IiLCJjbGlwYm9hcmQiLCJ3cml0ZVRleHQiLCJyZW5kZXJDb250ZW50IiwibWFwIiwiaW5kZXgiLCJzdGFydHNXaXRoIiwiaDEiLCJjbGFzc05hbWUiLCJzdWJzdHJpbmciLCJoMiIsImgzIiwibmV4dEluZGV4IiwiZmluZEluZGV4IiwibCIsImkiLCJjb2RlTGluZXMiLCJzbGljZSIsImNvZGUiLCJqb2luIiwiZGl2IiwiYnV0dG9uIiwib25DbGljayIsInByZSIsImxpIiwiYnIiLCJwIiwiQm9vbGVhbiIsInNwYW4iLCJpbnB1dCIsInR5cGUiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsInBsYWNlaG9sZGVyIiwibmF2IiwiSWNvbiIsImZpbmQiLCJzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/docs/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Card.tsx":
/*!*****************************!*\
  !*** ./components/Card.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlertCard: () => (/* binding */ AlertCard),\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   FeatureCard: () => (/* binding */ FeatureCard),\n/* harmony export */   LoadingCard: () => (/* binding */ LoadingCard),\n/* harmony export */   StatCard: () => (/* binding */ StatCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ Card,StatCard,FeatureCard,LoadingCard,AlertCard auto */ \nfunction Card({ children, className = \"\", hover = false, glow = false, border = \"default\" }) {\n    const borderColors = {\n        default: \"border-gray-700\",\n        green: \"border-cyber-green\",\n        blue: \"border-cyber-blue\",\n        red: \"border-cyber-red\",\n        gold: \"border-nusantara-gold\"\n    };\n    const glowColors = {\n        default: \"\",\n        green: \"shadow-lg shadow-cyber-green/20\",\n        blue: \"shadow-lg shadow-cyber-blue/20\",\n        red: \"shadow-lg shadow-cyber-red/20\",\n        gold: \"shadow-lg shadow-nusantara-gold/20\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `\n        bg-gray-900/50 backdrop-blur-sm rounded-lg border ${borderColors[border]}\n        ${hover ? \"hover:border-cyber-green hover:shadow-lg hover:shadow-cyber-green/20 transition-all duration-300 cursor-pointer\" : \"\"}\n        ${glow ? glowColors[border] : \"\"}\n        ${className}\n      `,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\nfunction StatCard({ title, value, icon: Icon, color = \"green\", trend, loading = false }) {\n    const colors = {\n        green: \"text-cyber-green\",\n        blue: \"text-cyber-blue\",\n        red: \"text-cyber-red\",\n        purple: \"text-cyber-purple\",\n        gold: \"text-nusantara-gold\"\n    };\n    const bgColors = {\n        green: \"bg-cyber-green/10\",\n        blue: \"bg-cyber-blue/10\",\n        red: \"bg-cyber-red/10\",\n        purple: \"bg-cyber-purple/10\",\n        gold: \"bg-nusantara-gold/10\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        border: color,\n        glow: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-400\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-baseline space-x-2\",\n                                children: [\n                                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 w-20 bg-gray-700 animate-pulse rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: `text-2xl font-bold ${colors[color]}`,\n                                        children: typeof value === \"number\" ? value.toLocaleString() : value\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, this),\n                                    trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `text-sm font-medium ${trend.isPositive ? \"text-green-400\" : \"text-red-400\"}`,\n                                        children: [\n                                            trend.isPositive ? \"+\" : \"\",\n                                            trend.value,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `p-3 rounded-lg ${bgColors[color]}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            className: `h-6 w-6 ${colors[color]}`\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\nfunction FeatureCard({ title, description, icon: Icon, color = \"green\", onClick, disabled = false, badge }) {\n    const colors = {\n        green: \"text-cyber-green\",\n        blue: \"text-cyber-blue\",\n        red: \"text-cyber-red\",\n        purple: \"text-cyber-purple\",\n        gold: \"text-nusantara-gold\"\n    };\n    const bgColors = {\n        green: \"bg-cyber-green/10\",\n        blue: \"bg-cyber-blue/10\",\n        red: \"bg-cyber-red/10\",\n        purple: \"bg-cyber-purple/10\",\n        gold: \"bg-nusantara-gold/10\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        hover: !disabled && !!onClick,\n        border: color,\n        className: `relative ${disabled ? \"opacity-50 cursor-not-allowed\" : \"\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 h-full\",\n            onClick: disabled ? undefined : onClick,\n            children: [\n                badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-3 right-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `px-2 py-1 text-xs font-semibold rounded-full ${bgColors[color]} ${colors[color]}`,\n                        children: badge\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `inline-flex p-3 rounded-lg ${bgColors[color]} mb-4`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: `h-6 w-6 ${colors[color]}`\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-white mb-2\",\n                    children: title\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-400 text-sm leading-relaxed\",\n                    children: description\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this),\n                onClick && !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 pt-4 border-t border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-medium ${colors[color]} hover:underline`,\n                        children: \"Mulai Sekarang →\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\nfunction LoadingCard({ className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 animate-pulse\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 bg-gray-700 rounded-lg\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-gray-700 rounded w-3/4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-3 bg-gray-700 rounded w-1/2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                lineNumber: 184,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 183,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 182,\n        columnNumber: 5\n    }, this);\n}\nfunction AlertCard({ type = \"info\", title, message, onClose }) {\n    const styles = {\n        info: {\n            border: \"border-cyber-blue\",\n            bg: \"bg-cyber-blue/10\",\n            text: \"text-cyber-blue\",\n            icon: \"\\uD83D\\uDCA1\"\n        },\n        success: {\n            border: \"border-cyber-green\",\n            bg: \"bg-cyber-green/10\",\n            text: \"text-cyber-green\",\n            icon: \"✅\"\n        },\n        warning: {\n            border: \"border-nusantara-gold\",\n            bg: \"bg-nusantara-gold/10\",\n            text: \"text-nusantara-gold\",\n            icon: \"⚠️\"\n        },\n        error: {\n            border: \"border-cyber-red\",\n            bg: \"bg-cyber-red/10\",\n            text: \"text-cyber-red\",\n            icon: \"❌\"\n        }\n    };\n    const style = styles[type];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `border ${style.border} ${style.bg} rounded-lg p-4`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-lg\",\n                    children: style.icon\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: `font-semibold ${style.text}`,\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300 text-sm mt-1\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, this),\n                onClose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onClose,\n                    className: \"text-gray-400 hover:text-white transition-colors\",\n                    children: \"\\xd7\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 238,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 237,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Navbar.tsx":
/*!*******************************!*\
  !*** ./components/Navbar.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-search.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Bug,Code,FileSearch,LogOut,Menu,Search,Settings,Shield,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Navbar({ user }) {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isProfileOpen, setIsProfileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const navigation = [\n        {\n            name: \"Dashboard\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            name: \"OSINT\",\n            href: \"/osint\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            name: \"Scanner\",\n            href: \"/scanner\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            name: \"File Analyzer\",\n            href: \"/file-analyzer\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            name: \"CVE Intel\",\n            href: \"/cve\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            name: \"Playground\",\n            href: \"/playground\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            name: \"Bot Center\",\n            href: \"/bot\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            name: \"Leaderboard\",\n            href: \"/leaderboard\",\n            icon: _barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        }\n    ];\n    const isActive = (href)=>pathname === href;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 w-full z-50 bg-cyber-dark/90 backdrop-blur-md border-b border-gray-800\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-8 w-8 text-cyber-green animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold font-cyber\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"cyber-text\",\n                                                children: \"Kode\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"nusantara-gold\",\n                                                children: \"X\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white\",\n                                                children: \"Guard\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-1\",\n                            children: user ? // Authenticated Navigation\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    navigation.map((item)=>{\n                                        const Icon = item.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: item.href,\n                                            className: `flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${isActive(item.href) ? \"bg-cyber-green/20 text-cyber-green border border-cyber-green/30\" : \"text-gray-300 hover:text-cyber-green hover:bg-gray-800/50\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, item.name, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 21\n                                        }, this);\n                                    }),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsProfileOpen(!isProfileOpen),\n                                                className: \"flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium text-gray-300 hover:text-cyber-green hover:bg-gray-800/50 transition-all duration-200\",\n                                                children: [\n                                                    user.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: user.avatar,\n                                                        alt: user.username,\n                                                        className: \"h-6 w-6 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: user.username\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs px-2 py-1 rounded-full bg-cyber-green/20 text-cyber-green\",\n                                                        children: user.plan.toUpperCase()\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 19\n                                            }, this),\n                                            isProfileOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-0 mt-2 w-48 bg-gray-900 border border-gray-700 rounded-lg shadow-lg py-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/profile\",\n                                                        className: \"flex items-center space-x-2 px-4 py-2 text-sm text-gray-300 hover:text-cyber-green hover:bg-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 110,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Profile\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 111,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/settings\",\n                                                        className: \"flex items-center space-x-2 px-4 py-2 text-sm text-gray-300 hover:text-cyber-green hover:bg-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 117,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Settings\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 118,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    user.role === \"super_admin\" || user.role === \"admin\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/admin\",\n                                                        className: \"flex items-center space-x-2 px-4 py-2 text-sm text-gray-300 hover:text-cyber-green hover:bg-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Admin Panel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 126,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 25\n                                                    }, this) : null,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                        className: \"my-1 border-gray-700\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{},\n                                                        className: \"flex items-center space-x-2 w-full px-4 py-2 text-sm text-red-400 hover:text-red-300 hover:bg-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 134,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Logout\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                                lineNumber: 135,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : // Guest Navigation\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/docs\",\n                                        className: \"text-gray-300 hover:text-cyber-green transition-colors\",\n                                        children: \"Dokumentasi\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/pricing\",\n                                        className: \"text-gray-300 hover:text-cyber-green transition-colors\",\n                                        children: \"Harga\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/login\",\n                                        className: \"cyber-btn\",\n                                        children: \"Masuk\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/register\",\n                                        className: \"cyber-btn-primary\",\n                                        children: \"Daftar\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsOpen(!isOpen),\n                                className: \"text-gray-300 hover:text-cyber-green p-2\",\n                                children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 25\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 53\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden bg-cyber-dark/95 backdrop-blur-md border-t border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-2 pt-2 pb-3 space-y-1\",\n                    children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            navigation.map((item)=>{\n                                const Icon = item.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: item.href,\n                                    className: `flex items-center space-x-3 px-3 py-2 rounded-lg text-base font-medium transition-all duration-200 ${isActive(item.href) ? \"bg-cyber-green/20 text-cyber-green border border-cyber-green/30\" : \"text-gray-300 hover:text-cyber-green hover:bg-gray-800/50\"}`,\n                                    onClick: ()=>setIsOpen(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 21\n                                }, this);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                className: \"my-2 border-gray-700\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/profile\",\n                                className: \"flex items-center space-x-3 px-3 py-2 rounded-lg text-base font-medium text-gray-300 hover:text-cyber-green hover:bg-gray-800/50\",\n                                onClick: ()=>setIsOpen(false),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Profile (\",\n                                            user.username,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{},\n                                className: \"flex items-center space-x-3 w-full px-3 py-2 rounded-lg text-base font-medium text-red-400 hover:text-red-300 hover:bg-gray-800/50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Bug_Code_FileSearch_LogOut_Menu_Search_Settings_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Logout\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/docs\",\n                                className: \"block px-3 py-2 rounded-lg text-base font-medium text-gray-300 hover:text-cyber-green hover:bg-gray-800/50\",\n                                onClick: ()=>setIsOpen(false),\n                                children: \"Dokumentasi\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/pricing\",\n                                className: \"block px-3 py-2 rounded-lg text-base font-medium text-gray-300 hover:text-cyber-green hover:bg-gray-800/50\",\n                                onClick: ()=>setIsOpen(false),\n                                children: \"Harga\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/login\",\n                                className: \"block px-3 py-2 rounded-lg text-base font-medium cyber-btn text-center\",\n                                onClick: ()=>setIsOpen(false),\n                                children: \"Masuk\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/register\",\n                                className: \"block px-3 py-2 rounded-lg text-base font-medium cyber-btn-primary text-center\",\n                                onClick: ()=>setIsOpen(false),\n                                children: \"Daftar\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n                lineNumber: 174,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Navbar.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL05hdmJhci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFZ0M7QUFDSjtBQUNpQjtBQWN4QjtBQVdOLFNBQVNlLE9BQU8sRUFBRUMsSUFBSSxFQUFlO0lBQ2xELE1BQU0sQ0FBQ0MsUUFBUUMsVUFBVSxHQUFHbEIsK0NBQVFBLENBQUM7SUFDckMsTUFBTSxDQUFDbUIsZUFBZUMsaUJBQWlCLEdBQUdwQiwrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNcUIsV0FBV25CLDREQUFXQTtJQUU1QixNQUFNb0IsYUFBYTtRQUNqQjtZQUFFQyxNQUFNO1lBQWFDLE1BQU07WUFBY0MsTUFBTXRCLG1KQUFNQTtRQUFDO1FBQ3REO1lBQUVvQixNQUFNO1lBQVNDLE1BQU07WUFBVUMsTUFBTW5CLG1KQUFNQTtRQUFDO1FBQzlDO1lBQUVpQixNQUFNO1lBQVdDLE1BQU07WUFBWUMsTUFBTWpCLG1KQUFHQTtRQUFDO1FBQy9DO1lBQUVlLE1BQU07WUFBaUJDLE1BQU07WUFBa0JDLE1BQU1sQixtSkFBVUE7UUFBQztRQUNsRTtZQUFFZ0IsTUFBTTtZQUFhQyxNQUFNO1lBQVFDLE1BQU10QixtSkFBTUE7UUFBQztRQUNoRDtZQUFFb0IsTUFBTTtZQUFjQyxNQUFNO1lBQWVDLE1BQU1mLG1KQUFJQTtRQUFDO1FBQ3REO1lBQUVhLE1BQU07WUFBY0MsTUFBTTtZQUFRQyxNQUFNaEIsbUpBQUdBO1FBQUM7UUFDOUM7WUFBRWMsTUFBTTtZQUFlQyxNQUFNO1lBQWdCQyxNQUFNZCxvSkFBS0E7UUFBQztLQUMxRDtJQUVELE1BQU1lLFdBQVcsQ0FBQ0YsT0FBaUJILGFBQWFHO0lBRWhELHFCQUNFLDhEQUFDRztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0M7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNDO29CQUFJRCxXQUFVOztzQ0FFYiw4REFBQ0M7NEJBQUlELFdBQVU7c0NBQ2IsNEVBQUMzQixpREFBSUE7Z0NBQUN1QixNQUFLO2dDQUFJSSxXQUFVOztrREFDdkIsOERBQUN6QixtSkFBTUE7d0NBQUN5QixXQUFVOzs7Ozs7a0RBQ2xCLDhEQUFDRTt3Q0FBS0YsV0FBVTs7MERBQ2QsOERBQUNFO2dEQUFLRixXQUFVOzBEQUFhOzs7Ozs7MERBQzdCLDhEQUFDRTtnREFBS0YsV0FBVTswREFBaUI7Ozs7OzswREFDakMsOERBQUNFO2dEQUFLRixXQUFVOzBEQUFhOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FNbkMsOERBQUNDOzRCQUFJRCxXQUFVO3NDQUNaWixPQUNDLDJCQUEyQjswQ0FDM0I7O29DQUNHTSxXQUFXUyxHQUFHLENBQUMsQ0FBQ0M7d0NBQ2YsTUFBTUMsT0FBT0QsS0FBS1AsSUFBSTt3Q0FDdEIscUJBQ0UsOERBQUN4QixpREFBSUE7NENBRUh1QixNQUFNUSxLQUFLUixJQUFJOzRDQUNmSSxXQUFXLENBQUMsaUdBQWlHLEVBQzNHRixTQUFTTSxLQUFLUixJQUFJLElBQ2Qsb0VBQ0EsNERBQ0wsQ0FBQzs7OERBRUYsOERBQUNTO29EQUFLTCxXQUFVOzs7Ozs7OERBQ2hCLDhEQUFDRTs4REFBTUUsS0FBS1QsSUFBSTs7Ozs7OzsyQ0FUWFMsS0FBS1QsSUFBSTs7Ozs7b0NBWXBCO2tEQUdBLDhEQUFDTTt3Q0FBSUQsV0FBVTs7MERBQ2IsOERBQUNNO2dEQUNDQyxTQUFTLElBQU1mLGlCQUFpQixDQUFDRDtnREFDakNTLFdBQVU7O29EQUVUWixLQUFLb0IsTUFBTSxpQkFDViw4REFBQ0M7d0RBQUlDLEtBQUt0QixLQUFLb0IsTUFBTTt3REFBRUcsS0FBS3ZCLEtBQUt3QixRQUFRO3dEQUFFWixXQUFVOzs7Ozs2RUFFckQsOERBQUNmLG9KQUFJQTt3REFBQ2UsV0FBVTs7Ozs7O2tFQUVsQiw4REFBQ0U7a0VBQU1kLEtBQUt3QixRQUFROzs7Ozs7a0VBQ3BCLDhEQUFDVjt3REFBS0YsV0FBVTtrRUFDYlosS0FBS3lCLElBQUksQ0FBQ0MsV0FBVzs7Ozs7Ozs7Ozs7OzRDQUl6QnZCLCtCQUNDLDhEQUFDVTtnREFBSUQsV0FBVTs7a0VBQ2IsOERBQUMzQixpREFBSUE7d0RBQ0h1QixNQUFLO3dEQUNMSSxXQUFVOzswRUFFViw4REFBQ2Ysb0pBQUlBO2dFQUFDZSxXQUFVOzs7Ozs7MEVBQ2hCLDhEQUFDRTswRUFBSzs7Ozs7Ozs7Ozs7O2tFQUVSLDhEQUFDN0IsaURBQUlBO3dEQUNIdUIsTUFBSzt3REFDTEksV0FBVTs7MEVBRVYsOERBQUNoQixvSkFBUUE7Z0VBQUNnQixXQUFVOzs7Ozs7MEVBQ3BCLDhEQUFDRTswRUFBSzs7Ozs7Ozs7Ozs7O29EQUVQZCxLQUFLMkIsSUFBSSxLQUFLLGlCQUFpQjNCLEtBQUsyQixJQUFJLEtBQUssd0JBQzVDLDhEQUFDMUMsaURBQUlBO3dEQUNIdUIsTUFBSzt3REFDTEksV0FBVTs7MEVBRVYsOERBQUN6QixtSkFBTUE7Z0VBQUN5QixXQUFVOzs7Ozs7MEVBQ2xCLDhEQUFDRTswRUFBSzs7Ozs7Ozs7Ozs7K0RBRU47a0VBQ0osOERBQUNjO3dEQUFHaEIsV0FBVTs7Ozs7O2tFQUNkLDhEQUFDTTt3REFDQ0MsU0FBUyxLQUEwQjt3REFDbkNQLFdBQVU7OzBFQUVWLDhEQUFDZCxvSkFBTUE7Z0VBQUNjLFdBQVU7Ozs7OzswRUFDbEIsOERBQUNFOzBFQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OytDQU9oQixtQkFBbUI7MENBQ25CLDhEQUFDRDtnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUMzQixpREFBSUE7d0NBQUN1QixNQUFLO3dDQUFRSSxXQUFVO2tEQUF5RDs7Ozs7O2tEQUd0Riw4REFBQzNCLGlEQUFJQTt3Q0FBQ3VCLE1BQUs7d0NBQVdJLFdBQVU7a0RBQXlEOzs7Ozs7a0RBR3pGLDhEQUFDM0IsaURBQUlBO3dDQUFDdUIsTUFBSzt3Q0FBU0ksV0FBVTtrREFBWTs7Ozs7O2tEQUcxQyw4REFBQzNCLGlEQUFJQTt3Q0FBQ3VCLE1BQUs7d0NBQVlJLFdBQVU7a0RBQW9COzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FRM0QsOERBQUNDOzRCQUFJRCxXQUFVO3NDQUNiLDRFQUFDTTtnQ0FDQ0MsU0FBUyxJQUFNakIsVUFBVSxDQUFDRDtnQ0FDMUJXLFdBQVU7MENBRVRYLHVCQUFTLDhEQUFDWixvSkFBQ0E7b0NBQUN1QixXQUFVOzs7Ozt5REFBZSw4REFBQ3hCLG9KQUFJQTtvQ0FBQ3dCLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQU83RFgsd0JBQ0MsOERBQUNZO2dCQUFJRCxXQUFVOzBCQUNiLDRFQUFDQztvQkFBSUQsV0FBVTs4QkFDWloscUJBQ0M7OzRCQUNHTSxXQUFXUyxHQUFHLENBQUMsQ0FBQ0M7Z0NBQ2YsTUFBTUMsT0FBT0QsS0FBS1AsSUFBSTtnQ0FDdEIscUJBQ0UsOERBQUN4QixpREFBSUE7b0NBRUh1QixNQUFNUSxLQUFLUixJQUFJO29DQUNmSSxXQUFXLENBQUMsbUdBQW1HLEVBQzdHRixTQUFTTSxLQUFLUixJQUFJLElBQ2Qsb0VBQ0EsNERBQ0wsQ0FBQztvQ0FDRlcsU0FBUyxJQUFNakIsVUFBVTs7c0RBRXpCLDhEQUFDZTs0Q0FBS0wsV0FBVTs7Ozs7O3NEQUNoQiw4REFBQ0U7c0RBQU1FLEtBQUtULElBQUk7Ozs7Ozs7bUNBVlhTLEtBQUtULElBQUk7Ozs7OzRCQWFwQjswQ0FDQSw4REFBQ3FCO2dDQUFHaEIsV0FBVTs7Ozs7OzBDQUNkLDhEQUFDM0IsaURBQUlBO2dDQUNIdUIsTUFBSztnQ0FDTEksV0FBVTtnQ0FDVk8sU0FBUyxJQUFNakIsVUFBVTs7a0RBRXpCLDhEQUFDTCxvSkFBSUE7d0NBQUNlLFdBQVU7Ozs7OztrREFDaEIsOERBQUNFOzs0Q0FBSzs0Q0FBVWQsS0FBS3dCLFFBQVE7NENBQUM7Ozs7Ozs7Ozs7Ozs7MENBRWhDLDhEQUFDTjtnQ0FDQ0MsU0FBUyxLQUEwQjtnQ0FDbkNQLFdBQVU7O2tEQUVWLDhEQUFDZCxvSkFBTUE7d0NBQUNjLFdBQVU7Ozs7OztrREFDbEIsOERBQUNFO2tEQUFLOzs7Ozs7Ozs7Ozs7O3FEQUlWOzswQ0FDRSw4REFBQzdCLGlEQUFJQTtnQ0FDSHVCLE1BQUs7Z0NBQ0xJLFdBQVU7Z0NBQ1ZPLFNBQVMsSUFBTWpCLFVBQVU7MENBQzFCOzs7Ozs7MENBR0QsOERBQUNqQixpREFBSUE7Z0NBQ0h1QixNQUFLO2dDQUNMSSxXQUFVO2dDQUNWTyxTQUFTLElBQU1qQixVQUFVOzBDQUMxQjs7Ozs7OzBDQUdELDhEQUFDakIsaURBQUlBO2dDQUNIdUIsTUFBSztnQ0FDTEksV0FBVTtnQ0FDVk8sU0FBUyxJQUFNakIsVUFBVTswQ0FDMUI7Ozs7OzswQ0FHRCw4REFBQ2pCLGlEQUFJQTtnQ0FDSHVCLE1BQUs7Z0NBQ0xJLFdBQVU7Z0NBQ1ZPLFNBQVMsSUFBTWpCLFVBQVU7MENBQzFCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFVakIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb2RleGd1YXJkLy4vY29tcG9uZW50cy9OYXZiYXIudHN4PzFiODMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnXG5pbXBvcnQgeyB1c2VQYXRobmFtZSB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcbmltcG9ydCB7IFxuICBTaGllbGQsIFxuICBNZW51LCBcbiAgWCwgXG4gIFNlYXJjaCwgXG4gIEZpbGVTZWFyY2gsIFxuICBCdWcsIFxuICBCb3QsIFxuICBDb2RlLCBcbiAgVXNlcnMsIFxuICBTZXR0aW5ncyxcbiAgVXNlcixcbiAgTG9nT3V0XG59IGZyb20gJ2x1Y2lkZS1yZWFjdCdcblxuaW50ZXJmYWNlIE5hdmJhclByb3BzIHtcbiAgdXNlcj86IHtcbiAgICB1c2VybmFtZTogc3RyaW5nXG4gICAgYXZhdGFyPzogc3RyaW5nXG4gICAgcm9sZTogc3RyaW5nXG4gICAgcGxhbjogc3RyaW5nXG4gIH1cbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTmF2YmFyKHsgdXNlciB9OiBOYXZiYXJQcm9wcykge1xuICBjb25zdCBbaXNPcGVuLCBzZXRJc09wZW5dID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtpc1Byb2ZpbGVPcGVuLCBzZXRJc1Byb2ZpbGVPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBwYXRobmFtZSA9IHVzZVBhdGhuYW1lKClcblxuICBjb25zdCBuYXZpZ2F0aW9uID0gW1xuICAgIHsgbmFtZTogJ0Rhc2hib2FyZCcsIGhyZWY6ICcvZGFzaGJvYXJkJywgaWNvbjogU2hpZWxkIH0sXG4gICAgeyBuYW1lOiAnT1NJTlQnLCBocmVmOiAnL29zaW50JywgaWNvbjogU2VhcmNoIH0sXG4gICAgeyBuYW1lOiAnU2Nhbm5lcicsIGhyZWY6ICcvc2Nhbm5lcicsIGljb246IEJ1ZyB9LFxuICAgIHsgbmFtZTogJ0ZpbGUgQW5hbHl6ZXInLCBocmVmOiAnL2ZpbGUtYW5hbHl6ZXInLCBpY29uOiBGaWxlU2VhcmNoIH0sXG4gICAgeyBuYW1lOiAnQ1ZFIEludGVsJywgaHJlZjogJy9jdmUnLCBpY29uOiBTaGllbGQgfSxcbiAgICB7IG5hbWU6ICdQbGF5Z3JvdW5kJywgaHJlZjogJy9wbGF5Z3JvdW5kJywgaWNvbjogQ29kZSB9LFxuICAgIHsgbmFtZTogJ0JvdCBDZW50ZXInLCBocmVmOiAnL2JvdCcsIGljb246IEJvdCB9LFxuICAgIHsgbmFtZTogJ0xlYWRlcmJvYXJkJywgaHJlZjogJy9sZWFkZXJib2FyZCcsIGljb246IFVzZXJzIH0sXG4gIF1cblxuICBjb25zdCBpc0FjdGl2ZSA9IChocmVmOiBzdHJpbmcpID0+IHBhdGhuYW1lID09PSBocmVmXG5cbiAgcmV0dXJuIChcbiAgICA8bmF2IGNsYXNzTmFtZT1cImZpeGVkIHRvcC0wIHctZnVsbCB6LTUwIGJnLWN5YmVyLWRhcmsvOTAgYmFja2Ryb3AtYmx1ci1tZCBib3JkZXItYiBib3JkZXItZ3JheS04MDBcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgaC0xNlwiPlxuICAgICAgICAgIHsvKiBMb2dvICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICA8U2hpZWxkIGNsYXNzTmFtZT1cImgtOCB3LTggdGV4dC1jeWJlci1ncmVlbiBhbmltYXRlLXB1bHNlXCIgLz5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgZm9udC1jeWJlclwiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImN5YmVyLXRleHRcIj5Lb2RlPC9zcGFuPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm51c2FudGFyYS1nb2xkXCI+WDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlXCI+R3VhcmQ8L3NwYW4+XG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBEZXNrdG9wIE5hdmlnYXRpb24gKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoaWRkZW4gbWQ6ZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xXCI+XG4gICAgICAgICAgICB7dXNlciA/IChcbiAgICAgICAgICAgICAgLy8gQXV0aGVudGljYXRlZCBOYXZpZ2F0aW9uXG4gICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAge25hdmlnYXRpb24ubWFwKChpdGVtKSA9PiB7XG4gICAgICAgICAgICAgICAgICBjb25zdCBJY29uID0gaXRlbS5pY29uXG4gICAgICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAgICAgIGtleT17aXRlbS5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgIGhyZWY9e2l0ZW0uaHJlZn1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgcHgtMyBweS0yIHJvdW5kZWQtbGcgdGV4dC1zbSBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzQWN0aXZlKGl0ZW0uaHJlZilcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctY3liZXItZ3JlZW4vMjAgdGV4dC1jeWJlci1ncmVlbiBib3JkZXIgYm9yZGVyLWN5YmVyLWdyZWVuLzMwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktMzAwIGhvdmVyOnRleHQtY3liZXItZ3JlZW4gaG92ZXI6YmctZ3JheS04MDAvNTAnXG4gICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8SWNvbiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj57aXRlbS5uYW1lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIHsvKiBQcm9maWxlIERyb3Bkb3duICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgbWwtNFwiPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc1Byb2ZpbGVPcGVuKCFpc1Byb2ZpbGVPcGVuKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHB4LTMgcHktMiByb3VuZGVkLWxnIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LWN5YmVyLWdyZWVuIGhvdmVyOmJnLWdyYXktODAwLzUwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHt1c2VyLmF2YXRhciA/IChcbiAgICAgICAgICAgICAgICAgICAgICA8aW1nIHNyYz17dXNlci5hdmF0YXJ9IGFsdD17dXNlci51c2VybmFtZX0gY2xhc3NOYW1lPVwiaC02IHctNiByb3VuZGVkLWZ1bGxcIiAvPlxuICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgIDxVc2VyIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj57dXNlci51c2VybmFtZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgcHgtMiBweS0xIHJvdW5kZWQtZnVsbCBiZy1jeWJlci1ncmVlbi8yMCB0ZXh0LWN5YmVyLWdyZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAge3VzZXIucGxhbi50b1VwcGVyQ2FzZSgpfVxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAge2lzUHJvZmlsZU9wZW4gJiYgKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHJpZ2h0LTAgbXQtMiB3LTQ4IGJnLWdyYXktOTAwIGJvcmRlciBib3JkZXItZ3JheS03MDAgcm91bmRlZC1sZyBzaGFkb3ctbGcgcHktMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICAgICAgICBocmVmPVwiL3Byb2ZpbGVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHB4LTQgcHktMiB0ZXh0LXNtIHRleHQtZ3JheS0zMDAgaG92ZXI6dGV4dC1jeWJlci1ncmVlbiBob3ZlcjpiZy1ncmF5LTgwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFVzZXIgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5Qcm9maWxlPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9zZXR0aW5nc1wiXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgcHgtNCBweS0yIHRleHQtc20gdGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LWN5YmVyLWdyZWVuIGhvdmVyOmJnLWdyYXktODAwXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8U2V0dGluZ3MgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5TZXR0aW5nczwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgICAgICAge3VzZXIucm9sZSA9PT0gJ3N1cGVyX2FkbWluJyB8fCB1c2VyLnJvbGUgPT09ICdhZG1pbicgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPVwiL2FkbWluXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHB4LTQgcHktMiB0ZXh0LXNtIHRleHQtZ3JheS0zMDAgaG92ZXI6dGV4dC1jeWJlci1ncmVlbiBob3ZlcjpiZy1ncmF5LTgwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxTaGllbGQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPkFkbWluIFBhbmVsPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgICAgICAgICkgOiBudWxsfVxuICAgICAgICAgICAgICAgICAgICAgIDxociBjbGFzc05hbWU9XCJteS0xIGJvcmRlci1ncmF5LTcwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gey8qIEhhbmRsZSBsb2dvdXQgKi99fVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHctZnVsbCBweC00IHB5LTIgdGV4dC1zbSB0ZXh0LXJlZC00MDAgaG92ZXI6dGV4dC1yZWQtMzAwIGhvdmVyOmJnLWdyYXktODAwXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8TG9nT3V0IGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+TG9nb3V0PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgLy8gR3Vlc3QgTmF2aWdhdGlvblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvZG9jc1wiIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgaG92ZXI6dGV4dC1jeWJlci1ncmVlbiB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgICAgICAgRG9rdW1lbnRhc2lcbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9wcmljaW5nXCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LWN5YmVyLWdyZWVuIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgICBIYXJnYVxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2xvZ2luXCIgY2xhc3NOYW1lPVwiY3liZXItYnRuXCI+XG4gICAgICAgICAgICAgICAgICBNYXN1a1xuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL3JlZ2lzdGVyXCIgY2xhc3NOYW1lPVwiY3liZXItYnRuLXByaW1hcnlcIj5cbiAgICAgICAgICAgICAgICAgIERhZnRhclxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIE1vYmlsZSBtZW51IGJ1dHRvbiAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1kOmhpZGRlblwiPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc09wZW4oIWlzT3Blbil9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgaG92ZXI6dGV4dC1jeWJlci1ncmVlbiBwLTJcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7aXNPcGVuID8gPFggY2xhc3NOYW1lPVwiaC02IHctNlwiIC8+IDogPE1lbnUgY2xhc3NOYW1lPVwiaC02IHctNlwiIC8+fVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBNb2JpbGUgTmF2aWdhdGlvbiAqL31cbiAgICAgIHtpc09wZW4gJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1kOmhpZGRlbiBiZy1jeWJlci1kYXJrLzk1IGJhY2tkcm9wLWJsdXItbWQgYm9yZGVyLXQgYm9yZGVyLWdyYXktODAwXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweC0yIHB0LTIgcGItMyBzcGFjZS15LTFcIj5cbiAgICAgICAgICAgIHt1c2VyID8gKFxuICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgIHtuYXZpZ2F0aW9uLm1hcCgoaXRlbSkgPT4ge1xuICAgICAgICAgICAgICAgICAgY29uc3QgSWNvbiA9IGl0ZW0uaWNvblxuICAgICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgICAgICBrZXk9e2l0ZW0ubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICBocmVmPXtpdGVtLmhyZWZ9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIHB4LTMgcHktMiByb3VuZGVkLWxnIHRleHQtYmFzZSBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzQWN0aXZlKGl0ZW0uaHJlZilcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctY3liZXItZ3JlZW4vMjAgdGV4dC1jeWJlci1ncmVlbiBib3JkZXIgYm9yZGVyLWN5YmVyLWdyZWVuLzMwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktMzAwIGhvdmVyOnRleHQtY3liZXItZ3JlZW4gaG92ZXI6YmctZ3JheS04MDAvNTAnXG4gICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNPcGVuKGZhbHNlKX1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxJY29uIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPntpdGVtLm5hbWV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgICAgPGhyIGNsYXNzTmFtZT1cIm15LTIgYm9yZGVyLWdyYXktNzAwXCIgLz5cbiAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAgaHJlZj1cIi9wcm9maWxlXCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBweC0zIHB5LTIgcm91bmRlZC1sZyB0ZXh0LWJhc2UgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LWN5YmVyLWdyZWVuIGhvdmVyOmJnLWdyYXktODAwLzUwXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzT3BlbihmYWxzZSl9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPFVzZXIgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj5Qcm9maWxlICh7dXNlci51c2VybmFtZX0pPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7LyogSGFuZGxlIGxvZ291dCAqL319XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgdy1mdWxsIHB4LTMgcHktMiByb3VuZGVkLWxnIHRleHQtYmFzZSBmb250LW1lZGl1bSB0ZXh0LXJlZC00MDAgaG92ZXI6dGV4dC1yZWQtMzAwIGhvdmVyOmJnLWdyYXktODAwLzUwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8TG9nT3V0IGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgICAgICAgPHNwYW4+TG9nb3V0PC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgIGhyZWY9XCIvZG9jc1wiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJibG9jayBweC0zIHB5LTIgcm91bmRlZC1sZyB0ZXh0LWJhc2UgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LWN5YmVyLWdyZWVuIGhvdmVyOmJnLWdyYXktODAwLzUwXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzT3BlbihmYWxzZSl9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgRG9rdW1lbnRhc2lcbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgIGhyZWY9XCIvcHJpY2luZ1wiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJibG9jayBweC0zIHB5LTIgcm91bmRlZC1sZyB0ZXh0LWJhc2UgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LWN5YmVyLWdyZWVuIGhvdmVyOmJnLWdyYXktODAwLzUwXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzT3BlbihmYWxzZSl9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgSGFyZ2FcbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgIGhyZWY9XCIvbG9naW5cIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmxvY2sgcHgtMyBweS0yIHJvdW5kZWQtbGcgdGV4dC1iYXNlIGZvbnQtbWVkaXVtIGN5YmVyLWJ0biB0ZXh0LWNlbnRlclwiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc09wZW4oZmFsc2UpfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIE1hc3VrXG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICBocmVmPVwiL3JlZ2lzdGVyXCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHB4LTMgcHktMiByb3VuZGVkLWxnIHRleHQtYmFzZSBmb250LW1lZGl1bSBjeWJlci1idG4tcHJpbWFyeSB0ZXh0LWNlbnRlclwiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc09wZW4oZmFsc2UpfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIERhZnRhclxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG4gICAgPC9uYXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsIkxpbmsiLCJ1c2VQYXRobmFtZSIsIlNoaWVsZCIsIk1lbnUiLCJYIiwiU2VhcmNoIiwiRmlsZVNlYXJjaCIsIkJ1ZyIsIkJvdCIsIkNvZGUiLCJVc2VycyIsIlNldHRpbmdzIiwiVXNlciIsIkxvZ091dCIsIk5hdmJhciIsInVzZXIiLCJpc09wZW4iLCJzZXRJc09wZW4iLCJpc1Byb2ZpbGVPcGVuIiwic2V0SXNQcm9maWxlT3BlbiIsInBhdGhuYW1lIiwibmF2aWdhdGlvbiIsIm5hbWUiLCJocmVmIiwiaWNvbiIsImlzQWN0aXZlIiwibmF2IiwiY2xhc3NOYW1lIiwiZGl2Iiwic3BhbiIsIm1hcCIsIml0ZW0iLCJJY29uIiwiYnV0dG9uIiwib25DbGljayIsImF2YXRhciIsImltZyIsInNyYyIsImFsdCIsInVzZXJuYW1lIiwicGxhbiIsInRvVXBwZXJDYXNlIiwicm9sZSIsImhyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/Navbar.tsx\n");

/***/ }),

/***/ "(rsc)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7cffc49d1683\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb2RleGd1YXJkLy4vc3R5bGVzL2dsb2JhbHMuY3NzPzZkZDQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3Y2ZmYzQ5ZDE2ODNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/docs/page.tsx":
/*!***************************!*\
  !*** ./app/docs/page.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\docs\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(rsc)/./styles/globals.css\");\n\n\n\nconst metadata = {\n    title: \"KodeXGuard - Platform Cybersecurity & Bug Hunting\",\n    description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting dengan dukungan Bot WhatsApp/Telegram\",\n    keywords: \"cybersecurity, bug hunting, OSINT, vulnerability scanner, CVE, penetration testing, security tools\",\n    authors: [\n        {\n            name: \"KodeXGuard Team\"\n        }\n    ],\n    creator: \"KodeXGuard\",\n    publisher: \"KodeXGuard\",\n    robots: \"index, follow\",\n    openGraph: {\n        type: \"website\",\n        locale: \"id_ID\",\n        url: \"https://kodexguard.com\",\n        title: \"KodeXGuard - Platform Cybersecurity & Bug Hunting\",\n        description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting\",\n        siteName: \"KodeXGuard\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"KodeXGuard - Platform Cybersecurity & Bug Hunting\",\n        description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting\"\n    },\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#00ff41\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"id\",\n        className: \"dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} min-h-screen bg-gradient-cyber text-white antialiased`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"matrix-bg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-b from-transparent via-cyber-dark/20 to-cyber-dark\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n              // Matrix Rain Effect\n              function createMatrixRain() {\n                const canvas = document.createElement('canvas');\n                const ctx = canvas.getContext('2d');\n                canvas.style.position = 'fixed';\n                canvas.style.top = '0';\n                canvas.style.left = '0';\n                canvas.style.width = '100%';\n                canvas.style.height = '100%';\n                canvas.style.pointerEvents = 'none';\n                canvas.style.zIndex = '-1';\n                canvas.style.opacity = '0.1';\n                document.body.appendChild(canvas);\n                \n                canvas.width = window.innerWidth;\n                canvas.height = window.innerHeight;\n                \n                const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';\n                const charArray = chars.split('');\n                const fontSize = 14;\n                const columns = canvas.width / fontSize;\n                const drops = [];\n                \n                for (let i = 0; i < columns; i++) {\n                  drops[i] = 1;\n                }\n                \n                function draw() {\n                  ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';\n                  ctx.fillRect(0, 0, canvas.width, canvas.height);\n                  \n                  ctx.fillStyle = '#00ff41';\n                  ctx.font = fontSize + 'px monospace';\n                  \n                  for (let i = 0; i < drops.length; i++) {\n                    const text = charArray[Math.floor(Math.random() * charArray.length)];\n                    ctx.fillText(text, i * fontSize, drops[i] * fontSize);\n                    \n                    if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {\n                      drops[i] = 0;\n                    }\n                    drops[i]++;\n                  }\n                }\n                \n                setInterval(draw, 50);\n                \n                window.addEventListener('resize', () => {\n                  canvas.width = window.innerWidth;\n                  canvas.height = window.innerHeight;\n                });\n              }\n              \n              // Initialize matrix effect after page load\n              if (document.readyState === 'loading') {\n                document.addEventListener('DOMContentLoaded', createMatrixRain);\n              } else {\n                createMatrixRain();\n              }\n            `\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUlNQTtBQUZ3QjtBQUl2QixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFVBQVU7SUFDVkMsU0FBUztRQUFDO1lBQUVDLE1BQU07UUFBa0I7S0FBRTtJQUN0Q0MsU0FBUztJQUNUQyxXQUFXO0lBQ1hDLFFBQVE7SUFDUkMsV0FBVztRQUNUQyxNQUFNO1FBQ05DLFFBQVE7UUFDUkMsS0FBSztRQUNMWCxPQUFPO1FBQ1BDLGFBQWE7UUFDYlcsVUFBVTtJQUNaO0lBQ0FDLFNBQVM7UUFDUEMsTUFBTTtRQUNOZCxPQUFPO1FBQ1BDLGFBQWE7SUFDZjtJQUNBYyxVQUFVO0lBQ1ZDLFlBQVk7QUFDZCxFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBS0MsV0FBVTs7MEJBQ3hCLDhEQUFDQzs7a0NBQ0MsOERBQUNDO3dCQUFLQyxLQUFJO3dCQUFPQyxNQUFLOzs7Ozs7a0NBQ3RCLDhEQUFDRjt3QkFBS0MsS0FBSTt3QkFBbUJDLE1BQUs7Ozs7OztrQ0FDbEMsOERBQUNGO3dCQUFLQyxLQUFJO3dCQUFXQyxNQUFLOzs7Ozs7Ozs7Ozs7MEJBRTVCLDhEQUFDQztnQkFBS0wsV0FBVyxDQUFDLEVBQUV2QiwySkFBZSxDQUFDLHNEQUFzRCxDQUFDOztrQ0FFekYsOERBQUM2Qjt3QkFBSU4sV0FBVTtrQ0FDYiw0RUFBQ007NEJBQUlOLFdBQVU7Ozs7Ozs7Ozs7O2tDQUlqQiw4REFBQ007d0JBQUlOLFdBQVU7a0NBQ1pIOzs7Ozs7a0NBSUgsOERBQUNVO3dCQUNDQyx5QkFBeUI7NEJBQ3ZCQyxRQUFRLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQTREVCxDQUFDO3dCQUNIOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLViIsInNvdXJjZXMiOlsid2VicGFjazovL2tvZGV4Z3VhcmQvLi9hcHAvbGF5b3V0LnRzeD85OTg4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuLi9zdHlsZXMvZ2xvYmFscy5jc3MnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdLb2RlWEd1YXJkIC0gUGxhdGZvcm0gQ3liZXJzZWN1cml0eSAmIEJ1ZyBIdW50aW5nJyxcbiAgZGVzY3JpcHRpb246ICdQbGF0Zm9ybSBtYW5kaXJpIHVudHVrIE9TSU5ULCBWdWxuZXJhYmlsaXR5IFNjYW5uZXIsIEZpbGUgQW5hbHl6ZXIsIENWRSBJbnRlbGxpZ2VuY2UsIGRhbiBCdWcgSHVudGluZyBkZW5nYW4gZHVrdW5nYW4gQm90IFdoYXRzQXBwL1RlbGVncmFtJyxcbiAga2V5d29yZHM6ICdjeWJlcnNlY3VyaXR5LCBidWcgaHVudGluZywgT1NJTlQsIHZ1bG5lcmFiaWxpdHkgc2Nhbm5lciwgQ1ZFLCBwZW5ldHJhdGlvbiB0ZXN0aW5nLCBzZWN1cml0eSB0b29scycsXG4gIGF1dGhvcnM6IFt7IG5hbWU6ICdLb2RlWEd1YXJkIFRlYW0nIH1dLFxuICBjcmVhdG9yOiAnS29kZVhHdWFyZCcsXG4gIHB1Ymxpc2hlcjogJ0tvZGVYR3VhcmQnLFxuICByb2JvdHM6ICdpbmRleCwgZm9sbG93JyxcbiAgb3BlbkdyYXBoOiB7XG4gICAgdHlwZTogJ3dlYnNpdGUnLFxuICAgIGxvY2FsZTogJ2lkX0lEJyxcbiAgICB1cmw6ICdodHRwczovL2tvZGV4Z3VhcmQuY29tJyxcbiAgICB0aXRsZTogJ0tvZGVYR3VhcmQgLSBQbGF0Zm9ybSBDeWJlcnNlY3VyaXR5ICYgQnVnIEh1bnRpbmcnLFxuICAgIGRlc2NyaXB0aW9uOiAnUGxhdGZvcm0gbWFuZGlyaSB1bnR1ayBPU0lOVCwgVnVsbmVyYWJpbGl0eSBTY2FubmVyLCBGaWxlIEFuYWx5emVyLCBDVkUgSW50ZWxsaWdlbmNlLCBkYW4gQnVnIEh1bnRpbmcnLFxuICAgIHNpdGVOYW1lOiAnS29kZVhHdWFyZCcsXG4gIH0sXG4gIHR3aXR0ZXI6IHtcbiAgICBjYXJkOiAnc3VtbWFyeV9sYXJnZV9pbWFnZScsXG4gICAgdGl0bGU6ICdLb2RlWEd1YXJkIC0gUGxhdGZvcm0gQ3liZXJzZWN1cml0eSAmIEJ1ZyBIdW50aW5nJyxcbiAgICBkZXNjcmlwdGlvbjogJ1BsYXRmb3JtIG1hbmRpcmkgdW50dWsgT1NJTlQsIFZ1bG5lcmFiaWxpdHkgU2Nhbm5lciwgRmlsZSBBbmFseXplciwgQ1ZFIEludGVsbGlnZW5jZSwgZGFuIEJ1ZyBIdW50aW5nJyxcbiAgfSxcbiAgdmlld3BvcnQ6ICd3aWR0aD1kZXZpY2Utd2lkdGgsIGluaXRpYWwtc2NhbGU9MScsXG4gIHRoZW1lQ29sb3I6ICcjMDBmZjQxJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImlkXCIgY2xhc3NOYW1lPVwiZGFya1wiPlxuICAgICAgPGhlYWQ+XG4gICAgICAgIDxsaW5rIHJlbD1cImljb25cIiBocmVmPVwiL2Zhdmljb24uaWNvXCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwiYXBwbGUtdG91Y2gtaWNvblwiIGhyZWY9XCIvYXBwbGUtdG91Y2gtaWNvbi5wbmdcIiAvPlxuICAgICAgICA8bGluayByZWw9XCJtYW5pZmVzdFwiIGhyZWY9XCIvbWFuaWZlc3QuanNvblwiIC8+XG4gICAgICA8L2hlYWQ+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2Ake2ludGVyLmNsYXNzTmFtZX0gbWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LWN5YmVyIHRleHQtd2hpdGUgYW50aWFsaWFzZWRgfT5cbiAgICAgICAgey8qIE1hdHJpeCBCYWNrZ3JvdW5kIEVmZmVjdCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXRyaXgtYmdcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tYiBmcm9tLXRyYW5zcGFyZW50IHZpYS1jeWJlci1kYXJrLzIwIHRvLWN5YmVyLWRhcmtcIj48L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIFxuICAgICAgICB7LyogTWFpbiBDb250ZW50ICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHotMTBcIj5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvZGl2PlxuICAgICAgICBcbiAgICAgICAgey8qIEdsb2JhbCBTY3JpcHRzICovfVxuICAgICAgICA8c2NyaXB0XG4gICAgICAgICAgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw9e3tcbiAgICAgICAgICAgIF9faHRtbDogYFxuICAgICAgICAgICAgICAvLyBNYXRyaXggUmFpbiBFZmZlY3RcbiAgICAgICAgICAgICAgZnVuY3Rpb24gY3JlYXRlTWF0cml4UmFpbigpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBjYW52YXMgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdjYW52YXMnKTtcbiAgICAgICAgICAgICAgICBjb25zdCBjdHggPSBjYW52YXMuZ2V0Q29udGV4dCgnMmQnKTtcbiAgICAgICAgICAgICAgICBjYW52YXMuc3R5bGUucG9zaXRpb24gPSAnZml4ZWQnO1xuICAgICAgICAgICAgICAgIGNhbnZhcy5zdHlsZS50b3AgPSAnMCc7XG4gICAgICAgICAgICAgICAgY2FudmFzLnN0eWxlLmxlZnQgPSAnMCc7XG4gICAgICAgICAgICAgICAgY2FudmFzLnN0eWxlLndpZHRoID0gJzEwMCUnO1xuICAgICAgICAgICAgICAgIGNhbnZhcy5zdHlsZS5oZWlnaHQgPSAnMTAwJSc7XG4gICAgICAgICAgICAgICAgY2FudmFzLnN0eWxlLnBvaW50ZXJFdmVudHMgPSAnbm9uZSc7XG4gICAgICAgICAgICAgICAgY2FudmFzLnN0eWxlLnpJbmRleCA9ICctMSc7XG4gICAgICAgICAgICAgICAgY2FudmFzLnN0eWxlLm9wYWNpdHkgPSAnMC4xJztcbiAgICAgICAgICAgICAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKGNhbnZhcyk7XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgY2FudmFzLndpZHRoID0gd2luZG93LmlubmVyV2lkdGg7XG4gICAgICAgICAgICAgICAgY2FudmFzLmhlaWdodCA9IHdpbmRvdy5pbm5lckhlaWdodDtcbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICBjb25zdCBjaGFycyA9ICcwMeOCouOCpOOCpuOCqOOCquOCq+OCreOCr+OCseOCs+OCteOCt+OCueOCu+OCveOCv+ODgeODhOODhuODiOODiuODi+ODjOODjeODjuODj+ODkuODleODmOODm+ODnuODn+ODoOODoeODouODpOODpuODqOODqeODquODq+ODrOODreODr+ODsuODsyc7XG4gICAgICAgICAgICAgICAgY29uc3QgY2hhckFycmF5ID0gY2hhcnMuc3BsaXQoJycpO1xuICAgICAgICAgICAgICAgIGNvbnN0IGZvbnRTaXplID0gMTQ7XG4gICAgICAgICAgICAgICAgY29uc3QgY29sdW1ucyA9IGNhbnZhcy53aWR0aCAvIGZvbnRTaXplO1xuICAgICAgICAgICAgICAgIGNvbnN0IGRyb3BzID0gW107XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBjb2x1bW5zOyBpKyspIHtcbiAgICAgICAgICAgICAgICAgIGRyb3BzW2ldID0gMTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgZnVuY3Rpb24gZHJhdygpIHtcbiAgICAgICAgICAgICAgICAgIGN0eC5maWxsU3R5bGUgPSAncmdiYSgwLCAwLCAwLCAwLjA1KSc7XG4gICAgICAgICAgICAgICAgICBjdHguZmlsbFJlY3QoMCwgMCwgY2FudmFzLndpZHRoLCBjYW52YXMuaGVpZ2h0KTtcbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgY3R4LmZpbGxTdHlsZSA9ICcjMDBmZjQxJztcbiAgICAgICAgICAgICAgICAgIGN0eC5mb250ID0gZm9udFNpemUgKyAncHggbW9ub3NwYWNlJztcbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBkcm9wcy5sZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCB0ZXh0ID0gY2hhckFycmF5W01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIGNoYXJBcnJheS5sZW5ndGgpXTtcbiAgICAgICAgICAgICAgICAgICAgY3R4LmZpbGxUZXh0KHRleHQsIGkgKiBmb250U2l6ZSwgZHJvcHNbaV0gKiBmb250U2l6ZSk7XG4gICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgICBpZiAoZHJvcHNbaV0gKiBmb250U2l6ZSA+IGNhbnZhcy5oZWlnaHQgJiYgTWF0aC5yYW5kb20oKSA+IDAuOTc1KSB7XG4gICAgICAgICAgICAgICAgICAgICAgZHJvcHNbaV0gPSAwO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGRyb3BzW2ldKys7XG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIHNldEludGVydmFsKGRyYXcsIDUwKTtcbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgY2FudmFzLndpZHRoID0gd2luZG93LmlubmVyV2lkdGg7XG4gICAgICAgICAgICAgICAgICBjYW52YXMuaGVpZ2h0ID0gd2luZG93LmlubmVySGVpZ2h0O1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAvLyBJbml0aWFsaXplIG1hdHJpeCBlZmZlY3QgYWZ0ZXIgcGFnZSBsb2FkXG4gICAgICAgICAgICAgIGlmIChkb2N1bWVudC5yZWFkeVN0YXRlID09PSAnbG9hZGluZycpIHtcbiAgICAgICAgICAgICAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCdET01Db250ZW50TG9hZGVkJywgY3JlYXRlTWF0cml4UmFpbik7XG4gICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgY3JlYXRlTWF0cml4UmFpbigpO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICBgLFxuICAgICAgICAgIH19XG4gICAgICAgIC8+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJrZXl3b3JkcyIsImF1dGhvcnMiLCJuYW1lIiwiY3JlYXRvciIsInB1Ymxpc2hlciIsInJvYm90cyIsIm9wZW5HcmFwaCIsInR5cGUiLCJsb2NhbGUiLCJ1cmwiLCJzaXRlTmFtZSIsInR3aXR0ZXIiLCJjYXJkIiwidmlld3BvcnQiLCJ0aGVtZUNvbG9yIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJjbGFzc05hbWUiLCJoZWFkIiwibGluayIsInJlbCIsImhyZWYiLCJib2R5IiwiZGl2Iiwic2NyaXB0IiwiZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUwiLCJfX2h0bWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdocs%2Fpage&page=%2Fdocs%2Fpage&appPaths=%2Fdocs%2Fpage&pagePath=private-next-app-dir%2Fdocs%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();