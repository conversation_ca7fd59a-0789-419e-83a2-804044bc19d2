(()=>{var e={};e.id=173,e.ids=[173],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39355:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>c}),a(14769),a(30829),a(35866);var t=a(23191),r=a(88716),i=a(37922),l=a.n(i),n=a(95231),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);a.d(s,d);let c=["",{children:["leaderboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,14769)),"D:\\Users\\Downloads\\kodeXGuard\\app\\leaderboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,35866,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\Users\\Downloads\\kodeXGuard\\app\\leaderboard\\page.tsx"],x="/leaderboard/page",m={require:a,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/leaderboard/page",pathname:"/leaderboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3882:(e,s,a)=>{Promise.resolve().then(a.bind(a,87619))},87619:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>N});var t=a(10326),r=a(17577),i=a(60463),l=a(2262),n=a(26092),d=a(89265),c=a(997),o=a(58907),x=a(24061),m=a(66697),h=a(39730),u=a(54014),p=a(53468),g=a(94244),j=a(58038),v=a(3634),b=a(7027);function N(){let[e]=(0,r.useState)({username:"admin",avatar:"",role:"super_admin",plan:"cybersecurity"}),[s,a]=(0,r.useState)("leaderboard"),[N,y]=(0,r.useState)("all"),[f,w]=(0,r.useState)("overall"),k={totalHunters:5847,activeToday:1234,totalCommunities:12,totalMembers:8956},A=e=>{switch(e){case 1:return n.Z;case 2:return d.Z;case 3:return c.Z;default:return o.Z}},P=e=>{switch(e){case 1:return"text-yellow-400";case 2:return"text-gray-300";case 3:return"text-orange-400";default:return"text-gray-400"}},S=e=>{let s=["bg-red-900/50 text-red-400","bg-blue-900/50 text-blue-400","bg-green-900/50 text-green-400","bg-purple-900/50 text-purple-400","bg-yellow-900/50 text-yellow-400"];return s[e.length%s.length]};return t.jsx(i.Z,{user:e,title:"Leaderboard & Komunitas",children:(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[t.jsx(o.Z,{className:"h-8 w-8 text-cyber-green"}),(0,t.jsxs)("h1",{className:"text-3xl font-bold font-cyber text-white",children:["Leaderboard & ",t.jsx("span",{className:"cyber-text",children:"Komunitas"})]})]}),t.jsx("p",{className:"text-gray-400 max-w-3xl",children:"Statistik nasional Bug Hunter Indonesia dengan skor berdasarkan laporan valid, eksploitasi sukses, dan API usage. Bergabung dengan komunitas WhatsApp & Telegram untuk kolaborasi."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[t.jsx(l.Rm,{title:"Total Hunters",value:k.totalHunters,icon:x.Z,color:"green",trend:{value:12,isPositive:!0}}),t.jsx(l.Rm,{title:"Active Today",value:k.activeToday,icon:m.Z,color:"blue",trend:{value:8,isPositive:!0}}),t.jsx(l.Rm,{title:"Communities",value:k.totalCommunities,icon:h.Z,color:"purple"}),t.jsx(l.Rm,{title:"Total Members",value:k.totalMembers,icon:u.Z,color:"gold",trend:{value:15,isPositive:!0}})]}),t.jsx("div",{className:"mb-8",children:t.jsx("div",{className:"flex space-x-1 bg-gray-800/50 p-1 rounded-lg",children:[{id:"leaderboard",name:"Leaderboard",icon:o.Z},{id:"community",name:"Community Groups",icon:x.Z}].map(e=>{let r=e.icon;return(0,t.jsxs)("button",{onClick:()=>a(e.id),className:`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 ${s===e.id?"bg-cyber-green text-black":"text-gray-300 hover:text-white hover:bg-gray-700/50"}`,children:[t.jsx(r,{className:"h-4 w-4"}),t.jsx("span",{children:e.name})]},e.id)})})}),"leaderboard"===s&&(0,t.jsxs)("div",{className:"grid lg:grid-cols-4 gap-8",children:[t.jsx("div",{className:"lg:col-span-3",children:t.jsx(l.Zb,{border:"green",glow:!0,children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[t.jsx("h2",{className:"text-xl font-bold text-white",children:"Bug Hunter Rankings"}),(0,t.jsxs)("div",{className:"flex space-x-3",children:[(0,t.jsxs)("select",{value:N,onChange:e=>y(e.target.value),className:"cyber-input text-sm",children:[t.jsx("option",{value:"all",children:"All Time"}),t.jsx("option",{value:"month",children:"This Month"}),t.jsx("option",{value:"week",children:"This Week"}),t.jsx("option",{value:"today",children:"Today"})]}),(0,t.jsxs)("select",{value:f,onChange:e=>w(e.target.value),className:"cyber-input text-sm",children:[t.jsx("option",{value:"overall",children:"Overall Score"}),t.jsx("option",{value:"scans",children:"Total Scans"}),t.jsx("option",{value:"vulnerabilities",children:"Vulnerabilities"}),t.jsx("option",{value:"reports",children:"Valid Reports"})]})]})]}),t.jsx("div",{className:"space-y-4",children:[{id:"1",username:"CyberNinja",avatar:"",rank:1,score:15420,totalScans:2847,vulnerabilitiesFound:156,validReports:89,apiUsage:45230,joinedAt:"2023-01-15",lastActive:"2 hours ago",badges:["Elite Hunter","CVE Discoverer","API Master"],country:"Indonesia"},{id:"2",username:"SecurityGuru",avatar:"",rank:2,score:12890,totalScans:2156,vulnerabilitiesFound:134,validReports:78,apiUsage:38940,joinedAt:"2023-02-20",lastActive:"1 hour ago",badges:["Bug Master","OSINT Expert"],country:"Indonesia"},{id:"3",username:"HackTheBox",avatar:"",rank:3,score:11250,totalScans:1923,vulnerabilitiesFound:112,validReports:67,apiUsage:32150,joinedAt:"2023-03-10",lastActive:"30 minutes ago",badges:["Scanner Pro","File Analyzer"],country:"Indonesia"},{id:"4",username:"admin",avatar:"",rank:4,score:9870,totalScans:1247,vulnerabilitiesFound:89,validReports:45,apiUsage:28560,joinedAt:"2023-01-01",lastActive:"Online",badges:["Super Admin","Platform Creator"],country:"Indonesia"},{id:"5",username:"PentestPro",avatar:"",rank:5,score:8640,totalScans:1089,vulnerabilitiesFound:76,validReports:42,apiUsage:24890,joinedAt:"2023-04-05",lastActive:"1 day ago",badges:["Vulnerability Hunter"],country:"Indonesia"}].map(s=>{let a=A(s.rank);return(0,t.jsxs)("div",{className:`bg-gray-800/50 rounded-lg p-4 border transition-all duration-200 ${s.username===e.username?"border-cyber-green bg-cyber-green/5":"border-gray-700 hover:border-gray-600"}`,children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(a,{className:`h-6 w-6 ${P(s.rank)}`}),(0,t.jsxs)("span",{className:"text-2xl font-bold text-white",children:["#",s.rank]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[s.avatar?t.jsx("img",{src:s.avatar,alt:s.username,className:"h-10 w-10 rounded-full"}):t.jsx("div",{className:"h-10 w-10 rounded-full bg-cyber-green/20 flex items-center justify-center",children:t.jsx("span",{className:"text-cyber-green font-bold",children:s.username.charAt(0).toUpperCase()})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("h3",{className:"font-bold text-white",children:s.username}),s.username===e.username&&t.jsx("span",{className:"px-2 py-1 bg-cyber-green/20 text-cyber-green rounded-full text-xs font-semibold",children:"You"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-400",children:[t.jsx("span",{children:s.country}),t.jsx("span",{children:"•"}),(0,t.jsxs)("span",{children:["Joined ",new Date(s.joinedAt).toLocaleDateString()]}),t.jsx("span",{children:"•"}),(0,t.jsxs)("span",{children:["Active ",s.lastActive]})]})]})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[t.jsx("div",{className:"text-2xl font-bold cyber-text",children:s.score.toLocaleString()}),t.jsx("div",{className:"text-sm text-gray-400",children:"points"})]})]}),(0,t.jsxs)("div",{className:"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(p.Z,{className:"h-4 w-4 text-blue-400"}),t.jsx("span",{className:"text-gray-400",children:"Scans:"}),t.jsx("span",{className:"text-white font-semibold",children:s.totalScans.toLocaleString()})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(g.Z,{className:"h-4 w-4 text-red-400"}),t.jsx("span",{className:"text-gray-400",children:"Vulns:"}),t.jsx("span",{className:"text-white font-semibold",children:s.vulnerabilitiesFound})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(j.Z,{className:"h-4 w-4 text-green-400"}),t.jsx("span",{className:"text-gray-400",children:"Reports:"}),t.jsx("span",{className:"text-white font-semibold",children:s.validReports})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(v.Z,{className:"h-4 w-4 text-purple-400"}),t.jsx("span",{className:"text-gray-400",children:"API:"}),t.jsx("span",{className:"text-white font-semibold",children:s.apiUsage.toLocaleString()})]})]}),s.badges.length>0&&t.jsx("div",{className:"mt-4 flex flex-wrap gap-2",children:s.badges.map((e,s)=>t.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-semibold ${S(e)}`,children:e},s))})]},s.id)})})]})})}),(0,t.jsxs)("div",{children:[t.jsx(l.Zb,{children:(0,t.jsxs)("div",{className:"p-6",children:[t.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Scoring System"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"p-3 bg-gray-800/50 rounded-lg",children:[t.jsx("div",{className:"font-medium text-white",children:"Valid Report"}),t.jsx("div",{className:"text-sm text-gray-400",children:"+100 points"})]}),(0,t.jsxs)("div",{className:"p-3 bg-gray-800/50 rounded-lg",children:[t.jsx("div",{className:"font-medium text-white",children:"Critical Vuln"}),t.jsx("div",{className:"text-sm text-gray-400",children:"+50 points"})]}),(0,t.jsxs)("div",{className:"p-3 bg-gray-800/50 rounded-lg",children:[t.jsx("div",{className:"font-medium text-white",children:"High Vuln"}),t.jsx("div",{className:"text-sm text-gray-400",children:"+30 points"})]}),(0,t.jsxs)("div",{className:"p-3 bg-gray-800/50 rounded-lg",children:[t.jsx("div",{className:"font-medium text-white",children:"Successful Scan"}),t.jsx("div",{className:"text-sm text-gray-400",children:"+5 points"})]}),(0,t.jsxs)("div",{className:"p-3 bg-gray-800/50 rounded-lg",children:[t.jsx("div",{className:"font-medium text-white",children:"API Usage"}),t.jsx("div",{className:"text-sm text-gray-400",children:"+1 per 100 calls"})]})]})]})}),t.jsx(l.Zb,{className:"mt-6",border:"gold",children:(0,t.jsxs)("div",{className:"p-6",children:[t.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Your Rank"}),(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"text-3xl font-bold cyber-text mb-2",children:"#4"}),(0,t.jsxs)("div",{className:"text-gray-400 mb-4",children:["out of ",k.totalHunters.toLocaleString()," hunters"]}),(0,t.jsxs)("div",{className:"text-sm text-gray-300",children:["You need ",t.jsx("span",{className:"text-cyber-green font-semibold",children:"1,380 points"})," to reach rank #3"]})]})]})})]})]}),"community"===s&&(0,t.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[t.jsx("div",{className:"lg:col-span-2",children:t.jsx(l.Zb,{border:"green",glow:!0,children:(0,t.jsxs)("div",{className:"p-6",children:[t.jsx("h2",{className:"text-xl font-bold text-white mb-6",children:"Community Groups"}),t.jsx("div",{className:"space-y-4",children:[{id:"1",name:"KodeXGuard Indonesia",type:"telegram",members:1247,isPrivate:!1,description:"Official KodeXGuard community for Indonesian bug hunters and cybersecurity enthusiasts",inviteLink:"https://t.me/kodexguard_id",lastActivity:"5 minutes ago"},{id:"2",name:"Elite Bug Hunters",type:"whatsapp",members:89,isPrivate:!0,description:"Exclusive group for top-tier bug hunters with 1000+ score",lastActivity:"1 hour ago"},{id:"3",name:"CVE Research Team",type:"telegram",members:234,isPrivate:!1,description:"Collaborative CVE research and 0-day discovery discussions",inviteLink:"https://t.me/kodexguard_cve",lastActivity:"2 hours ago"},{id:"4",name:"OSINT Investigators",type:"telegram",members:567,isPrivate:!1,description:"Open Source Intelligence gathering and investigation techniques",inviteLink:"https://t.me/kodexguard_osint",lastActivity:"30 minutes ago"},{id:"5",name:"Malware Analysis Lab",type:"whatsapp",members:156,isPrivate:!0,description:"Advanced malware analysis and reverse engineering discussions",lastActivity:"3 hours ago"}].map(e=>(0,t.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-4 border border-gray-700 hover:border-gray-600 transition-colors",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:["whatsapp"===e.type?t.jsx(h.Z,{className:"h-6 w-6 text-green-400"}):t.jsx(h.Z,{className:"h-6 w-6 text-blue-400"}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("h3",{className:"font-bold text-white",children:e.name}),e.isPrivate&&t.jsx("span",{className:"px-2 py-1 bg-orange-900/50 text-orange-400 rounded-full text-xs font-semibold",children:"Private"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-400",children:[t.jsx(x.Z,{className:"h-3 w-3"}),(0,t.jsxs)("span",{children:[e.members.toLocaleString()," members"]}),t.jsx("span",{children:"•"}),t.jsx("span",{className:"capitalize",children:e.type})]})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[t.jsx("div",{className:"text-sm text-gray-400",children:"Last activity"}),t.jsx("div",{className:"text-sm text-white",children:e.lastActivity})]})]}),t.jsx("p",{className:"text-gray-300 mb-4",children:e.description}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("div",{className:"text-sm text-gray-400",children:e.isPrivate?"Invitation required":"Open to join"}),e.inviteLink&&!e.isPrivate&&(0,t.jsxs)("a",{href:e.inviteLink,target:"_blank",rel:"noopener noreferrer",className:"cyber-btn text-sm",children:[t.jsx(b.Z,{className:"h-4 w-4 mr-2"}),"Join Group"]}),e.isPrivate&&t.jsx("button",{className:"cyber-btn text-sm",disabled:!0,children:"Request Access"})]})]},e.id))})]})})}),(0,t.jsxs)("div",{children:[t.jsx(l.Zb,{children:(0,t.jsxs)("div",{className:"p-6",children:[t.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Community Stats"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"Total Groups"}),t.jsx("span",{className:"text-white font-semibold",children:"12"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"WhatsApp Groups"}),t.jsx("span",{className:"text-white font-semibold",children:"5"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"Telegram Groups"}),t.jsx("span",{className:"text-white font-semibold",children:"7"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"Private Groups"}),t.jsx("span",{className:"text-white font-semibold",children:"3"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-400",children:"Active Members"}),t.jsx("span",{className:"text-cyber-green font-semibold",children:"2,847"})]})]})]})}),t.jsx(l.Zb,{className:"mt-6",children:(0,t.jsxs)("div",{className:"p-6",children:[t.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Group Guidelines"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm text-gray-300",children:[t.jsx("p",{children:"• Be respectful to all members"}),t.jsx("p",{children:"• Share knowledge and help others"}),t.jsx("p",{children:"• No spam or self-promotion"}),t.jsx("p",{children:"• Follow responsible disclosure"}),t.jsx("p",{children:"• Keep discussions on-topic"}),t.jsx("p",{children:"• Report violations to admins"})]})]})})]})]})]})})}},14769:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(68570).createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\leaderboard\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[216,592],()=>a(39355));module.exports=t})();