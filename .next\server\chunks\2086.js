exports.id=2086,exports.ids=[2086],exports.modules={89121:(e,t,r)=>{"use strict";var n=r(78893).Buffer,i=r(78893).SlowBuffer;function s(e,t){if(!n.isBuffer(e)||!n.isBuffer(t)||e.length!==t.length)return!1;for(var r=0,i=0;i<e.length;i++)r|=e[i]^t[i];return 0===r}e.exports=s,s.install=function(){n.prototype.equal=i.prototype.equal=function(e){return s(this,e)}};var o=n.prototype.equal,a=i.prototype.equal;s.restore=function(){n.prototype.equal=o,i.prototype.equal=a}},97145:(e,t,r)=>{"use strict";var n=r(18243).Buffer,i=r(41457);function s(e){if(n.isBuffer(e))return e;if("string"==typeof e)return n.from(e,"base64");throw TypeError("ECDSA signature must be a Base64 string or a Buffer")}function o(e,t,r){for(var n=0;t+n<r&&0===e[t+n];)++n;return e[t+n]>=128&&--n,n}e.exports={derToJose:function(e,t){e=s(e);var r=i(t),o=r+1,a=e.length,l=0;if(48!==e[l++])throw Error('Could not find expected "seq"');var u=e[l++];if(129===u&&(u=e[l++]),a-l<u)throw Error('"seq" specified length of "'+u+'", only "'+(a-l)+'" remaining');if(2!==e[l++])throw Error('Could not find expected "int" for "r"');var f=e[l++];if(a-l-2<f)throw Error('"r" specified length of "'+f+'", only "'+(a-l-2)+'" available');if(o<f)throw Error('"r" specified length of "'+f+'", max of "'+o+'" is acceptable');var c=l;if(l+=f,2!==e[l++])throw Error('Could not find expected "int" for "s"');var p=e[l++];if(a-l!==p)throw Error('"s" specified length of "'+p+'", expected "'+(a-l)+'"');if(o<p)throw Error('"s" specified length of "'+p+'", max of "'+o+'" is acceptable');var h=l;if((l+=p)!==a)throw Error('Expected to consume entire buffer, but "'+(a-l)+'" bytes remain');var m=r-f,E=r-p,d=n.allocUnsafe(m+f+E+p);for(l=0;l<m;++l)d[l]=0;e.copy(d,l,c+Math.max(-m,0),c+f),l=r;for(var y=l;l<y+E;++l)d[l]=0;return e.copy(d,l,h+Math.max(-E,0),h+p),d=(d=d.toString("base64")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")},joseToDer:function(e,t){e=s(e);var r=i(t),a=e.length;if(a!==2*r)throw TypeError('"'+t+'" signatures must be "'+2*r+'" bytes, saw "'+a+'"');var l=o(e,0,r),u=o(e,r,e.length),f=r-l,c=r-u,p=2+f+1+1+c,h=p<128,m=n.allocUnsafe((h?2:3)+p),E=0;return m[E++]=48,h?m[E++]=p:(m[E++]=129,m[E++]=255&p),m[E++]=2,m[E++]=f,l<0?(m[E++]=0,E+=e.copy(m,E,0,r)):E+=e.copy(m,E,l,r),m[E++]=2,m[E++]=c,u<0?(m[E++]=0,e.copy(m,E,r)):e.copy(m,E,r+u),m}}},41457:e=>{"use strict";function t(e){return(e/8|0)+(e%8==0?0:1)}var r={ES256:t(256),ES384:t(384),ES512:t(521)};e.exports=function(e){var t=r[e];if(t)return t;throw Error('Unknown algorithm "'+e+'"')}},42857:(e,t,r)=>{var n=r(80843);e.exports=function(e,t){t=t||{};var r=n.decode(e,t);if(!r)return null;var i=r.payload;if("string"==typeof i)try{var s=JSON.parse(i);null!==s&&"object"==typeof s&&(i=s)}catch(e){}return!0===t.complete?{header:r.header,payload:i,signature:r.signature}:i}},41482:(e,t,r)=>{e.exports={decode:r(42857),verify:r(88061),sign:r(73601),JsonWebTokenError:r(453),NotBeforeError:r(37565),TokenExpiredError:r(74658)}},453:e=>{var t=function(e,t){Error.call(this,e),Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor),this.name="JsonWebTokenError",this.message=e,t&&(this.inner=t)};t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,e.exports=t},37565:(e,t,r)=>{var n=r(453),i=function(e,t){n.call(this,e),this.name="NotBeforeError",this.date=t};i.prototype=Object.create(n.prototype),i.prototype.constructor=i,e.exports=i},74658:(e,t,r)=>{var n=r(453),i=function(e,t){n.call(this,e),this.name="TokenExpiredError",this.expiredAt=t};i.prototype=Object.create(n.prototype),i.prototype.constructor=i,e.exports=i},91440:(e,t,r)=>{let n=r(90799);e.exports=n.satisfies(process.version,">=15.7.0")},33051:(e,t,r)=>{var n=r(90799);e.exports=n.satisfies(process.version,"^6.12.0 || >=8.0.0")},41790:(e,t,r)=>{let n=r(90799);e.exports=n.satisfies(process.version,">=16.9.0")},73258:(e,t,r)=>{var n=r(13974);e.exports=function(e,t){var r=t||Math.floor(Date.now()/1e3);if("string"==typeof e){var i=n(e);if(void 0===i)return;return Math.floor(r+i/1e3)}if("number"==typeof e)return r+e}},59279:(e,t,r)=>{let n=r(91440),i=r(41790),s={ec:["ES256","ES384","ES512"],rsa:["RS256","PS256","RS384","PS384","RS512","PS512"],"rsa-pss":["PS256","PS384","PS512"]},o={ES256:"prime256v1",ES384:"secp384r1",ES512:"secp521r1"};e.exports=function(e,t){if(!e||!t)return;let r=t.asymmetricKeyType;if(!r)return;let a=s[r];if(!a)throw Error(`Unknown key type "${r}".`);if(!a.includes(e))throw Error(`"alg" parameter for "${r}" key type must be one of: ${a.join(", ")}.`);if(n)switch(r){case"ec":let l=t.asymmetricKeyDetails.namedCurve,u=o[e];if(l!==u)throw Error(`"alg" parameter "${e}" requires curve "${u}".`);break;case"rsa-pss":if(i){let r=parseInt(e.slice(-3),10),{hashAlgorithm:n,mgf1HashAlgorithm:i,saltLength:s}=t.asymmetricKeyDetails;if(n!==`sha${r}`||i!==n)throw Error(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of "alg" ${e}.`);if(void 0!==s&&s>r>>3)throw Error(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of "alg" ${e}.`)}}}},73601:(e,t,r)=>{let n=r(73258),i=r(33051),s=r(59279),o=r(80843),a=r(22086),l=r(21724),u=r(54591),f=r(59366),c=r(29080),p=r(71380),h=r(32144),{KeyObject:m,createSecretKey:E,createPrivateKey:d}=r(84770),y=["RS256","RS384","RS512","ES256","ES384","ES512","HS256","HS384","HS512","none"];i&&y.splice(3,0,"PS256","PS384","PS512");let g={expiresIn:{isValid:function(e){return u(e)||p(e)&&e},message:'"expiresIn" should be a number of seconds or string representing a timespan'},notBefore:{isValid:function(e){return u(e)||p(e)&&e},message:'"notBefore" should be a number of seconds or string representing a timespan'},audience:{isValid:function(e){return p(e)||Array.isArray(e)},message:'"audience" must be a string or array'},algorithm:{isValid:a.bind(null,y),message:'"algorithm" must be a valid string enum value'},header:{isValid:c,message:'"header" must be an object'},encoding:{isValid:p,message:'"encoding" must be a string'},issuer:{isValid:p,message:'"issuer" must be a string'},subject:{isValid:p,message:'"subject" must be a string'},jwtid:{isValid:p,message:'"jwtid" must be a string'},noTimestamp:{isValid:l,message:'"noTimestamp" must be a boolean'},keyid:{isValid:p,message:'"keyid" must be a string'},mutatePayload:{isValid:l,message:'"mutatePayload" must be a boolean'},allowInsecureKeySizes:{isValid:l,message:'"allowInsecureKeySizes" must be a boolean'},allowInvalidAsymmetricKeyTypes:{isValid:l,message:'"allowInvalidAsymmetricKeyTypes" must be a boolean'}},v={iat:{isValid:f,message:'"iat" should be a number of seconds'},exp:{isValid:f,message:'"exp" should be a number of seconds'},nbf:{isValid:f,message:'"nbf" should be a number of seconds'}};function b(e,t,r,n){if(!c(r))throw Error('Expected "'+n+'" to be a plain object.');Object.keys(r).forEach(function(i){let s=e[i];if(!s){if(!t)throw Error('"'+i+'" is not allowed in "'+n+'"');return}if(!s.isValid(r[i]))throw Error(s.message)})}let S={audience:"aud",issuer:"iss",subject:"sub",jwtid:"jti"},$=["expiresIn","notBefore","noTimestamp","audience","issuer","subject","jwtid"];e.exports=function(e,t,r,i){var a,l;"function"==typeof r?(i=r,r={}):r=r||{};let u="object"==typeof e&&!Buffer.isBuffer(e),f=Object.assign({alg:r.algorithm||"HS256",typ:u?"JWT":void 0,kid:r.keyid},r.header);function c(e){if(i)return i(e);throw e}if(!t&&"none"!==r.algorithm)return c(Error("secretOrPrivateKey must have a value"));if(null!=t&&!(t instanceof m))try{t=d(t)}catch(e){try{t=E("string"==typeof t?Buffer.from(t):t)}catch(e){return c(Error("secretOrPrivateKey is not valid key material"))}}if(f.alg.startsWith("HS")&&"secret"!==t.type)return c(Error(`secretOrPrivateKey must be a symmetric key when using ${f.alg}`));if(/^(?:RS|PS|ES)/.test(f.alg)){if("private"!==t.type)return c(Error(`secretOrPrivateKey must be an asymmetric key when using ${f.alg}`));if(!r.allowInsecureKeySizes&&!f.alg.startsWith("ES")&&void 0!==t.asymmetricKeyDetails&&t.asymmetricKeyDetails.modulusLength<2048)return c(Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${f.alg}`))}if(void 0===e)return c(Error("payload is required"));if(u){try{a=e,b(v,!0,a,"payload")}catch(e){return c(e)}r.mutatePayload||(e=Object.assign({},e))}else{let t=$.filter(function(e){return void 0!==r[e]});if(t.length>0)return c(Error("invalid "+t.join(",")+" option for "+typeof e+" payload"))}if(void 0!==e.exp&&void 0!==r.expiresIn)return c(Error('Bad "options.expiresIn" option the payload already has an "exp" property.'));if(void 0!==e.nbf&&void 0!==r.notBefore)return c(Error('Bad "options.notBefore" option the payload already has an "nbf" property.'));try{l=r,b(g,!1,l,"options")}catch(e){return c(e)}if(!r.allowInvalidAsymmetricKeyTypes)try{s(f.alg,t)}catch(e){return c(e)}let p=e.iat||Math.floor(Date.now()/1e3);if(r.noTimestamp?delete e.iat:u&&(e.iat=p),void 0!==r.notBefore){try{e.nbf=n(r.notBefore,p)}catch(e){return c(e)}if(void 0===e.nbf)return c(Error('"notBefore" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60'))}if(void 0!==r.expiresIn&&"object"==typeof e){try{e.exp=n(r.expiresIn,p)}catch(e){return c(e)}if(void 0===e.exp)return c(Error('"expiresIn" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60'))}Object.keys(S).forEach(function(t){let n=S[t];if(void 0!==r[t]){if(void 0!==e[n])return c(Error('Bad "options.'+t+'" option. The payload already has an "'+n+'" property.'));e[n]=r[t]}});let y=r.encoding||"utf8";if("function"==typeof i)i=i&&h(i),o.createSign({header:f,privateKey:t,payload:e,encoding:y}).once("error",i).once("done",function(e){if(!r.allowInsecureKeySizes&&/^(?:RS|PS)/.test(f.alg)&&e.length<256)return i(Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${f.alg}`));i(null,e)});else{let n=o.sign({header:f,payload:e,secret:t,encoding:y});if(!r.allowInsecureKeySizes&&/^(?:RS|PS)/.test(f.alg)&&n.length<256)throw Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${f.alg}`);return n}}},88061:(e,t,r)=>{let n=r(453),i=r(37565),s=r(74658),o=r(42857),a=r(73258),l=r(59279),u=r(33051),f=r(80843),{KeyObject:c,createSecretKey:p,createPublicKey:h}=r(84770),m=["RS256","RS384","RS512"],E=["ES256","ES384","ES512"],d=["RS256","RS384","RS512"],y=["HS256","HS384","HS512"];u&&(m.splice(m.length,0,"PS256","PS384","PS512"),d.splice(d.length,0,"PS256","PS384","PS512")),e.exports=function(e,t,r,u){let g,v,b;if("function"!=typeof r||u||(u=r,r={}),r||(r={}),r=Object.assign({},r),g=u||function(e,t){if(e)throw e;return t},r.clockTimestamp&&"number"!=typeof r.clockTimestamp)return g(new n("clockTimestamp must be a number"));if(void 0!==r.nonce&&("string"!=typeof r.nonce||""===r.nonce.trim()))return g(new n("nonce must be a non-empty string"));if(void 0!==r.allowInvalidAsymmetricKeyTypes&&"boolean"!=typeof r.allowInvalidAsymmetricKeyTypes)return g(new n("allowInvalidAsymmetricKeyTypes must be a boolean"));let S=r.clockTimestamp||Math.floor(Date.now()/1e3);if(!e)return g(new n("jwt must be provided"));if("string"!=typeof e)return g(new n("jwt must be a string"));let $=e.split(".");if(3!==$.length)return g(new n("jwt malformed"));try{v=o(e,{complete:!0})}catch(e){return g(e)}if(!v)return g(new n("invalid token"));let I=v.header;if("function"==typeof t){if(!u)return g(new n("verify must be called asynchronous if secret or public key is provided as a callback"));b=t}else b=function(e,r){return r(null,t)};return b(I,function(t,o){let u;if(t)return g(new n("error in secret or public key callback: "+t.message));let b=""!==$[2].trim();if(!b&&o)return g(new n("jwt signature is required"));if(b&&!o)return g(new n("secret or public key must be provided"));if(!b&&!r.algorithms)return g(new n('please specify "none" in "algorithms" to verify unsigned tokens'));if(null!=o&&!(o instanceof c))try{o=h(o)}catch(e){try{o=p("string"==typeof o?Buffer.from(o):o)}catch(e){return g(new n("secretOrPublicKey is not valid key material"))}}if(r.algorithms||("secret"===o.type?r.algorithms=y:["rsa","rsa-pss"].includes(o.asymmetricKeyType)?r.algorithms=d:"ec"===o.asymmetricKeyType?r.algorithms=E:r.algorithms=m),-1===r.algorithms.indexOf(v.header.alg))return g(new n("invalid algorithm"));if(I.alg.startsWith("HS")&&"secret"!==o.type)return g(new n(`secretOrPublicKey must be a symmetric key when using ${I.alg}`));if(/^(?:RS|PS|ES)/.test(I.alg)&&"public"!==o.type)return g(new n(`secretOrPublicKey must be an asymmetric key when using ${I.alg}`));if(!r.allowInvalidAsymmetricKeyTypes)try{l(I.alg,o)}catch(e){return g(e)}try{u=f.verify(e,v.header.alg,o)}catch(e){return g(e)}if(!u)return g(new n("invalid signature"));let w=v.payload;if(void 0!==w.nbf&&!r.ignoreNotBefore){if("number"!=typeof w.nbf)return g(new n("invalid nbf value"));if(w.nbf>S+(r.clockTolerance||0))return g(new i("jwt not active",new Date(1e3*w.nbf)))}if(void 0!==w.exp&&!r.ignoreExpiration){if("number"!=typeof w.exp)return g(new n("invalid exp value"));if(S>=w.exp+(r.clockTolerance||0))return g(new s("jwt expired",new Date(1e3*w.exp)))}if(r.audience){let e=Array.isArray(r.audience)?r.audience:[r.audience];if(!(Array.isArray(w.aud)?w.aud:[w.aud]).some(function(t){return e.some(function(e){return e instanceof RegExp?e.test(t):e===t})}))return g(new n("jwt audience invalid. expected: "+e.join(" or ")))}if(r.issuer&&("string"==typeof r.issuer&&w.iss!==r.issuer||Array.isArray(r.issuer)&&-1===r.issuer.indexOf(w.iss)))return g(new n("jwt issuer invalid. expected: "+r.issuer));if(r.subject&&w.sub!==r.subject)return g(new n("jwt subject invalid. expected: "+r.subject));if(r.jwtid&&w.jti!==r.jwtid)return g(new n("jwt jwtid invalid. expected: "+r.jwtid));if(r.nonce&&w.nonce!==r.nonce)return g(new n("jwt nonce invalid. expected: "+r.nonce));if(r.maxAge){if("number"!=typeof w.iat)return g(new n("iat required when maxAge is specified"));let e=a(r.maxAge,w.iat);if(void 0===e)return g(new n('"maxAge" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60'));if(S>=e+(r.clockTolerance||0))return g(new s("maxAge exceeded",new Date(1e3*e)))}return!0===r.complete?g(null,{header:I,payload:w,signature:v.signature}):g(null,w)})}},47917:(e,t,r)=>{var n,i=r(18243).Buffer,s=r(84770),o=r(97145),a=r(21764),l="secret must be a string or buffer",u="key must be a string or a buffer",f="function"==typeof s.createPublicKey;function c(e){if(!i.isBuffer(e)&&"string"!=typeof e&&(!f||"object"!=typeof e||"string"!=typeof e.type||"string"!=typeof e.asymmetricKeyType||"function"!=typeof e.export))throw E(u)}function p(e){if(!i.isBuffer(e)&&"string"!=typeof e&&"object"!=typeof e)throw E("key must be a string, a buffer or an object")}function h(e){return e.replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function m(e){var t=4-(e=e.toString()).length%4;if(4!==t)for(var r=0;r<t;++r)e+="=";return e.replace(/\-/g,"+").replace(/_/g,"/")}function E(e){var t=[].slice.call(arguments,1);return TypeError(a.format.bind(a,e).apply(null,t))}function d(e){var t;return t=e,i.isBuffer(t)||"string"==typeof t||(e=JSON.stringify(e)),e}function y(e){return function(t,r){(function(e){if(!i.isBuffer(e)&&"string"!=typeof e&&(!f||"object"!=typeof e||"secret"!==e.type||"function"!=typeof e.export))throw E(l)})(r),t=d(t);var n=s.createHmac("sha"+e,r);return h((n.update(t),n.digest("base64")))}}f&&(u+=" or a KeyObject",l+="or a KeyObject");var g="timingSafeEqual"in s?function(e,t){return e.byteLength===t.byteLength&&s.timingSafeEqual(e,t)}:function(e,t){return n||(n=r(89121)),n(e,t)};function v(e){return function(t,r,n){var s=y(e)(t,n);return g(i.from(r),i.from(s))}}function b(e){return function(t,r){p(r),t=d(t);var n=s.createSign("RSA-SHA"+e);return h((n.update(t),n.sign(r,"base64")))}}function S(e){return function(t,r,n){c(n),t=d(t),r=m(r);var i=s.createVerify("RSA-SHA"+e);return i.update(t),i.verify(n,r,"base64")}}function $(e){return function(t,r){p(r),t=d(t);var n=s.createSign("RSA-SHA"+e);return h((n.update(t),n.sign({key:r,padding:s.constants.RSA_PKCS1_PSS_PADDING,saltLength:s.constants.RSA_PSS_SALTLEN_DIGEST},"base64")))}}function I(e){return function(t,r,n){c(n),t=d(t),r=m(r);var i=s.createVerify("RSA-SHA"+e);return i.update(t),i.verify({key:n,padding:s.constants.RSA_PKCS1_PSS_PADDING,saltLength:s.constants.RSA_PSS_SALTLEN_DIGEST},r,"base64")}}function w(e){var t=b(e);return function(){var r=t.apply(null,arguments);return o.derToJose(r,"ES"+e)}}function R(e){var t=S(e);return function(r,n,i){return t(r,n=o.joseToDer(n,"ES"+e).toString("base64"),i)}}function A(){return function(){return""}}function O(){return function(e,t){return""===t}}e.exports=function(e){var t=e.match(/^(RS|PS|ES|HS)(256|384|512)$|^(none)$/i);if(!t)throw E('"%s" is not a valid algorithm.\n  Supported algorithms are:\n  "HS256", "HS384", "HS512", "RS256", "RS384", "RS512", "PS256", "PS384", "PS512", "ES256", "ES384", "ES512" and "none".',e);var r=(t[1]||t[3]).toLowerCase(),n=t[2];return{sign:({hs:y,rs:b,ps:$,es:w,none:A})[r](n),verify:({hs:v,rs:S,ps:I,es:R,none:O})[r](n)}}},80843:(e,t,r)=>{var n=r(61287),i=r(75640);t.ALGORITHMS=["HS256","HS384","HS512","RS256","RS384","RS512","PS256","PS384","PS512","ES256","ES384","ES512"],t.sign=n.sign,t.verify=i.verify,t.decode=i.decode,t.isValid=i.isValid,t.createSign=function(e){return new n(e)},t.createVerify=function(e){return new i(e)}},23348:(e,t,r)=>{var n=r(18243).Buffer,i=r(76162);function s(e){if(this.buffer=null,this.writable=!0,this.readable=!0,!e)return this.buffer=n.alloc(0),this;if("function"==typeof e.pipe)return this.buffer=n.alloc(0),e.pipe(this),this;if(e.length||"object"==typeof e)return this.buffer=e,this.writable=!1,process.nextTick((function(){this.emit("end",e),this.readable=!1,this.emit("close")}).bind(this)),this;throw TypeError("Unexpected data type ("+typeof e+")")}r(21764).inherits(s,i),s.prototype.write=function(e){this.buffer=n.concat([this.buffer,n.from(e)]),this.emit("data",e)},s.prototype.end=function(e){e&&this.write(e),this.emit("end",e),this.emit("close"),this.writable=!1,this.readable=!1},e.exports=s},61287:(e,t,r)=>{var n=r(18243).Buffer,i=r(23348),s=r(47917),o=r(76162),a=r(96107),l=r(21764);function u(e,t){return n.from(e,t).toString("base64").replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function f(e){var t,r,n,i=e.header,o=e.payload,f=e.secret||e.privateKey,c=e.encoding,p=s(i.alg),h=(t=(t=c)||"utf8",r=u(a(i),"binary"),n=u(a(o),t),l.format("%s.%s",r,n)),m=p.sign(h,f);return l.format("%s.%s",h,m)}function c(e){var t=new i(e.secret||e.privateKey||e.key);this.readable=!0,this.header=e.header,this.encoding=e.encoding,this.secret=this.privateKey=this.key=t,this.payload=new i(e.payload),this.secret.once("close",(function(){!this.payload.writable&&this.readable&&this.sign()}).bind(this)),this.payload.once("close",(function(){!this.secret.writable&&this.readable&&this.sign()}).bind(this))}l.inherits(c,o),c.prototype.sign=function(){try{var e=f({header:this.header,payload:this.payload.buffer,secret:this.secret.buffer,encoding:this.encoding});return this.emit("done",e),this.emit("data",e),this.emit("end"),this.readable=!1,e}catch(e){this.readable=!1,this.emit("error",e),this.emit("close")}},c.sign=f,e.exports=c},96107:(e,t,r)=>{var n=r(78893).Buffer;e.exports=function(e){return"string"==typeof e?e:"number"==typeof e||n.isBuffer(e)?e.toString():JSON.stringify(e)}},75640:(e,t,r)=>{var n=r(18243).Buffer,i=r(23348),s=r(47917),o=r(76162),a=r(96107),l=r(21764),u=/^[a-zA-Z0-9\-_]+?\.[a-zA-Z0-9\-_]+?\.([a-zA-Z0-9\-_]+)?$/;function f(e){var t=e.split(".",1)[0];return function(e){if("[object Object]"===Object.prototype.toString.call(e))return e;try{return JSON.parse(e)}catch(e){return}}(n.from(t,"base64").toString("binary"))}function c(e){return e.split(".")[2]}function p(e){return u.test(e)&&!!f(e)}function h(e,t,r){if(!t){var n=Error("Missing algorithm parameter for jws.verify");throw n.code="MISSING_ALGORITHM",n}var i=c(e=a(e)),o=e.split(".",2).join(".");return s(t).verify(o,i,r)}function m(e,t){if(t=t||{},!p(e=a(e)))return null;var r,i,s=f(e);if(!s)return null;var o=(r=r||"utf8",i=e.split(".")[1],n.from(i,"base64").toString(r));return("JWT"===s.typ||t.json)&&(o=JSON.parse(o,t.encoding)),{header:s,payload:o,signature:c(e)}}function E(e){var t=new i((e=e||{}).secret||e.publicKey||e.key);this.readable=!0,this.algorithm=e.algorithm,this.encoding=e.encoding,this.secret=this.publicKey=this.key=t,this.signature=new i(e.signature),this.secret.once("close",(function(){!this.signature.writable&&this.readable&&this.verify()}).bind(this)),this.signature.once("close",(function(){!this.secret.writable&&this.readable&&this.verify()}).bind(this))}l.inherits(E,o),E.prototype.verify=function(){try{var e=h(this.signature.buffer,this.algorithm,this.key.buffer),t=m(this.signature.buffer,this.encoding);return this.emit("done",e,t),this.emit("data",e),this.emit("end"),this.readable=!1,e}catch(e){this.readable=!1,this.emit("error",e),this.emit("close")}},E.decode=m,E.isValid=p,E.verify=h,e.exports=E},22086:e=>{var t,r,n=1/0,i=0/0,s=/^\s+|\s+$/g,o=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,l=/^0o[0-7]+$/i,u=/^(?:0|[1-9]\d*)$/,f=parseInt;function c(e){return e!=e}var p=Object.prototype,h=p.hasOwnProperty,m=p.toString,E=p.propertyIsEnumerable,d=(t=Object.keys,r=Object,function(e){return t(r(e))}),y=Math.max,g=Array.isArray;function v(e){var t,r;return null!=e&&"number"==typeof(t=e.length)&&t>-1&&t%1==0&&t<=9007199254740991&&!("[object Function]"==(r=b(e)?m.call(e):"")||"[object GeneratorFunction]"==r)}function b(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function S(e){return!!e&&"object"==typeof e}e.exports=function(e,t,r,$){e=v(e)?e:(I=e)?function(e,t){for(var r=-1,n=e?e.length:0,i=Array(n);++r<n;)i[r]=t(e[r],r,e);return i}(v(I)?function(e,t){var r,n=g(e)||S(e)&&v(e)&&h.call(e,"callee")&&(!E.call(e,"callee")||"[object Arguments]"==m.call(e))?function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}(e.length,String):[],i=n.length,s=!!i;for(var o in e)h.call(e,o)&&!(s&&("length"==o||(r=null==(r=i)?9007199254740991:r)&&("number"==typeof o||u.test(o))&&o>-1&&o%1==0&&o<r))&&n.push(o);return n}(I):function(e){if(t=e&&e.constructor,e!==("function"==typeof t&&t.prototype||p))return d(e);var t,r=[];for(var n in Object(e))h.call(e,n)&&"constructor"!=n&&r.push(n);return r}(I),function(e){return I[e]}):[],r=r&&!$?(A=(R=(w=r)?(w=function(e){if("number"==typeof e)return e;if("symbol"==typeof(t=e)||S(t)&&"[object Symbol]"==m.call(t))return i;if(b(e)){var t,r="function"==typeof e.valueOf?e.valueOf():e;e=b(r)?r+"":r}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(s,"");var n=a.test(e);return n||l.test(e)?f(e.slice(2),n?2:8):o.test(e)?i:+e}(w))===n||w===-n?(w<0?-1:1)*17976931348623157e292:w==w?w:0:0===w?w:0)%1,R==R?A?R-A:R:0):0;var I,w,R,A,O,T=e.length;return r<0&&(r=y(T+r,0)),"string"==typeof(O=e)||!g(O)&&S(O)&&"[object String]"==m.call(O)?r<=T&&e.indexOf(t,r)>-1:!!T&&function(e,t,r){if(t!=t)return function(e,t,r,n){for(var i=e.length,s=r+-1;++s<i;)if(t(e[s],s,e))return s;return -1}(e,c,r);for(var n=r-1,i=e.length;++n<i;)if(e[n]===t)return n;return -1}(e,t,r)>-1}},21724:e=>{var t=Object.prototype.toString;e.exports=function(e){return!0===e||!1===e||!!e&&"object"==typeof e&&"[object Boolean]"==t.call(e)}},54591:e=>{var t=1/0,r=0/0,n=/^\s+|\s+$/g,i=/^[-+]0x[0-9a-f]+$/i,s=/^0b[01]+$/i,o=/^0o[0-7]+$/i,a=parseInt,l=Object.prototype.toString;function u(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}e.exports=function(e){var f,c,p;return"number"==typeof e&&e==(p=(c=(f=e)?(f=function(e){if("number"==typeof e)return e;if("symbol"==typeof(t=e)||t&&"object"==typeof t&&"[object Symbol]"==l.call(t))return r;if(u(e)){var t,f="function"==typeof e.valueOf?e.valueOf():e;e=u(f)?f+"":f}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(n,"");var c=s.test(e);return c||o.test(e)?a(e.slice(2),c?2:8):i.test(e)?r:+e}(f))===t||f===-t?(f<0?-1:1)*17976931348623157e292:f==f?f:0:0===f?f:0)%1,c==c?p?c-p:c:0)}},59366:e=>{var t=Object.prototype.toString;e.exports=function(e){return"number"==typeof e||!!e&&"object"==typeof e&&"[object Number]"==t.call(e)}},29080:e=>{var t,r,n=Object.prototype,i=Function.prototype.toString,s=n.hasOwnProperty,o=i.call(Object),a=n.toString,l=(t=Object.getPrototypeOf,r=Object,function(e){return t(r(e))});e.exports=function(e){if(!(e&&"object"==typeof e)||"[object Object]"!=a.call(e)||function(e){var t=!1;if(null!=e&&"function"!=typeof e.toString)try{t=!!(e+"")}catch(e){}return t}(e))return!1;var t=l(e);if(null===t)return!0;var r=s.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&i.call(r)==o}},71380:e=>{var t=Object.prototype.toString,r=Array.isArray;e.exports=function(e){var n;return"string"==typeof e||!r(e)&&!!(n=e)&&"object"==typeof n&&"[object String]"==t.call(e)}},32144:e=>{var t=1/0,r=0/0,n=/^\s+|\s+$/g,i=/^[-+]0x[0-9a-f]+$/i,s=/^0b[01]+$/i,o=/^0o[0-7]+$/i,a=parseInt,l=Object.prototype.toString;function u(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}e.exports=function(e){return function(e,f){var c,p,h,m;if("function"!=typeof f)throw TypeError("Expected a function");return m=(h=(p=e)?(p=function(e){if("number"==typeof e)return e;if("symbol"==typeof(t=e)||t&&"object"==typeof t&&"[object Symbol]"==l.call(t))return r;if(u(e)){var t,f="function"==typeof e.valueOf?e.valueOf():e;e=u(f)?f+"":f}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(n,"");var c=s.test(e);return c||o.test(e)?a(e.slice(2),c?2:8):i.test(e)?r:+e}(p))===t||p===-t?(p<0?-1:1)*17976931348623157e292:p==p?p:0:0===p?p:0)%1,e=h==h?m?h-m:h:0,function(){return--e>0&&(c=f.apply(this,arguments)),e<=1&&(f=void 0),c}}(2,e)}},18243:(e,t,r)=>{var n=r(78893),i=n.Buffer;function s(e,t){for(var r in e)t[r]=e[r]}function o(e,t,r){return i(e,t,r)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?e.exports=n:(s(n,t),t.Buffer=o),o.prototype=Object.create(i.prototype),s(i,o),o.from=function(e,t,r){if("number"==typeof e)throw TypeError("Argument must not be a number");return i(e,t,r)},o.alloc=function(e,t,r){if("number"!=typeof e)throw TypeError("Argument must be a number");var n=i(e);return void 0!==t?"string"==typeof r?n.fill(t,r):n.fill(t):n.fill(0),n},o.allocUnsafe=function(e){if("number"!=typeof e)throw TypeError("Argument must be a number");return i(e)},o.allocUnsafeSlow=function(e){if("number"!=typeof e)throw TypeError("Argument must be a number");return n.SlowBuffer(e)}},12925:(e,t,r)=>{"use strict";let n=Symbol("SemVer ANY");class i{static get ANY(){return n}constructor(e,t){if(t=s(t),e instanceof i){if(!!t.loose===e.loose)return e;e=e.value}u("comparator",e=e.trim().split(/\s+/).join(" "),t),this.options=t,this.loose=!!t.loose,this.parse(e),this.semver===n?this.value="":this.value=this.operator+this.semver.version,u("comp",this)}parse(e){let t=this.options.loose?o[a.COMPARATORLOOSE]:o[a.COMPARATOR],r=e.match(t);if(!r)throw TypeError(`Invalid comparator: ${e}`);this.operator=void 0!==r[1]?r[1]:"","="===this.operator&&(this.operator=""),r[2]?this.semver=new f(r[2],this.options.loose):this.semver=n}toString(){return this.value}test(e){if(u("Comparator.test",e,this.options.loose),this.semver===n||e===n)return!0;if("string"==typeof e)try{e=new f(e,this.options)}catch(e){return!1}return l(e,this.operator,this.semver,this.options)}intersects(e,t){if(!(e instanceof i))throw TypeError("a Comparator is required");return""===this.operator?""===this.value||new c(e.value,t).test(this.value):""===e.operator?""===e.value||new c(this.value,t).test(e.semver):!((t=s(t)).includePrerelease&&("<0.0.0-0"===this.value||"<0.0.0-0"===e.value)||!t.includePrerelease&&(this.value.startsWith("<0.0.0")||e.value.startsWith("<0.0.0")))&&!!(this.operator.startsWith(">")&&e.operator.startsWith(">")||this.operator.startsWith("<")&&e.operator.startsWith("<")||this.semver.version===e.semver.version&&this.operator.includes("=")&&e.operator.includes("=")||l(this.semver,"<",e.semver,t)&&this.operator.startsWith(">")&&e.operator.startsWith("<")||l(this.semver,">",e.semver,t)&&this.operator.startsWith("<")&&e.operator.startsWith(">"))}}e.exports=i;let s=r(78949),{safeRe:o,t:a}=r(81520),l=r(43978),u=r(1410),f=r(28871),c=r(88889)},88889:(e,t,r)=>{"use strict";let n=/\s+/g;class i{constructor(e,t){if(t=o(t),e instanceof i){if(!!t.loose===e.loose&&!!t.includePrerelease===e.includePrerelease)return e;return new i(e.raw,t)}if(e instanceof a)return this.raw=e.value,this.set=[[e]],this.formatted=void 0,this;if(this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease,this.raw=e.trim().replace(n," "),this.set=this.raw.split("||").map(e=>this.parseRange(e.trim())).filter(e=>e.length),!this.set.length)throw TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){let e=this.set[0];if(this.set=this.set.filter(e=>!y(e[0])),0===this.set.length)this.set=[e];else if(this.set.length>1){for(let e of this.set)if(1===e.length&&g(e[0])){this.set=[e];break}}}this.formatted=void 0}get range(){if(void 0===this.formatted){this.formatted="";for(let e=0;e<this.set.length;e++){e>0&&(this.formatted+="||");let t=this.set[e];for(let e=0;e<t.length;e++)e>0&&(this.formatted+=" "),this.formatted+=t[e].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(e){let t=((this.options.includePrerelease&&E)|(this.options.loose&&d))+":"+e,r=s.get(t);if(r)return r;let n=this.options.loose,i=n?f[c.HYPHENRANGELOOSE]:f[c.HYPHENRANGE];l("hyphen replace",e=e.replace(i,N(this.options.includePrerelease))),l("comparator trim",e=e.replace(f[c.COMPARATORTRIM],p)),l("tilde trim",e=e.replace(f[c.TILDETRIM],h)),l("caret trim",e=e.replace(f[c.CARETTRIM],m));let o=e.split(" ").map(e=>b(e,this.options)).join(" ").split(/\s+/).map(e=>L(e,this.options));n&&(o=o.filter(e=>(l("loose invalid filter",e,this.options),!!e.match(f[c.COMPARATORLOOSE])))),l("range list",o);let u=new Map;for(let e of o.map(e=>new a(e,this.options))){if(y(e))return[e];u.set(e.value,e)}u.size>1&&u.has("")&&u.delete("");let g=[...u.values()];return s.set(t,g),g}intersects(e,t){if(!(e instanceof i))throw TypeError("a Range is required");return this.set.some(r=>v(r,t)&&e.set.some(e=>v(e,t)&&r.every(r=>e.every(e=>r.intersects(e,t)))))}test(e){if(!e)return!1;if("string"==typeof e)try{e=new u(e,this.options)}catch(e){return!1}for(let t=0;t<this.set.length;t++)if(x(this.set[t],e,this.options))return!0;return!1}}e.exports=i;let s=new(r(50241)),o=r(78949),a=r(12925),l=r(1410),u=r(28871),{safeRe:f,t:c,comparatorTrimReplace:p,tildeTrimReplace:h,caretTrimReplace:m}=r(81520),{FLAG_INCLUDE_PRERELEASE:E,FLAG_LOOSE:d}=r(73651),y=e=>"<0.0.0-0"===e.value,g=e=>""===e.value,v=(e,t)=>{let r=!0,n=e.slice(),i=n.pop();for(;r&&n.length;)r=n.every(e=>i.intersects(e,t)),i=n.pop();return r},b=(e,t)=>(l("comp",e,t),l("caret",e=w(e,t)),l("tildes",e=$(e,t)),l("xrange",e=A(e,t)),l("stars",e=T(e,t)),e),S=e=>!e||"x"===e.toLowerCase()||"*"===e,$=(e,t)=>e.trim().split(/\s+/).map(e=>I(e,t)).join(" "),I=(e,t)=>{let r=t.loose?f[c.TILDELOOSE]:f[c.TILDE];return e.replace(r,(t,r,n,i,s)=>{let o;return l("tilde",e,t,r,n,i,s),S(r)?o="":S(n)?o=`>=${r}.0.0 <${+r+1}.0.0-0`:S(i)?o=`>=${r}.${n}.0 <${r}.${+n+1}.0-0`:s?(l("replaceTilde pr",s),o=`>=${r}.${n}.${i}-${s} <${r}.${+n+1}.0-0`):o=`>=${r}.${n}.${i} <${r}.${+n+1}.0-0`,l("tilde return",o),o})},w=(e,t)=>e.trim().split(/\s+/).map(e=>R(e,t)).join(" "),R=(e,t)=>{l("caret",e,t);let r=t.loose?f[c.CARETLOOSE]:f[c.CARET],n=t.includePrerelease?"-0":"";return e.replace(r,(t,r,i,s,o)=>{let a;return l("caret",e,t,r,i,s,o),S(r)?a="":S(i)?a=`>=${r}.0.0${n} <${+r+1}.0.0-0`:S(s)?a="0"===r?`>=${r}.${i}.0${n} <${r}.${+i+1}.0-0`:`>=${r}.${i}.0${n} <${+r+1}.0.0-0`:o?(l("replaceCaret pr",o),a="0"===r?"0"===i?`>=${r}.${i}.${s}-${o} <${r}.${i}.${+s+1}-0`:`>=${r}.${i}.${s}-${o} <${r}.${+i+1}.0-0`:`>=${r}.${i}.${s}-${o} <${+r+1}.0.0-0`):(l("no pr"),a="0"===r?"0"===i?`>=${r}.${i}.${s}${n} <${r}.${i}.${+s+1}-0`:`>=${r}.${i}.${s}${n} <${r}.${+i+1}.0-0`:`>=${r}.${i}.${s} <${+r+1}.0.0-0`),l("caret return",a),a})},A=(e,t)=>(l("replaceXRanges",e,t),e.split(/\s+/).map(e=>O(e,t)).join(" ")),O=(e,t)=>{e=e.trim();let r=t.loose?f[c.XRANGELOOSE]:f[c.XRANGE];return e.replace(r,(r,n,i,s,o,a)=>{l("xRange",e,r,n,i,s,o,a);let u=S(i),f=u||S(s),c=f||S(o);return"="===n&&c&&(n=""),a=t.includePrerelease?"-0":"",u?r=">"===n||"<"===n?"<0.0.0-0":"*":n&&c?(f&&(s=0),o=0,">"===n?(n=">=",f?(i=+i+1,s=0):s=+s+1,o=0):"<="===n&&(n="<",f?i=+i+1:s=+s+1),"<"===n&&(a="-0"),r=`${n+i}.${s}.${o}${a}`):f?r=`>=${i}.0.0${a} <${+i+1}.0.0-0`:c&&(r=`>=${i}.${s}.0${a} <${i}.${+s+1}.0-0`),l("xRange return",r),r})},T=(e,t)=>(l("replaceStars",e,t),e.trim().replace(f[c.STAR],"")),L=(e,t)=>(l("replaceGTE0",e,t),e.trim().replace(f[t.includePrerelease?c.GTE0PRE:c.GTE0],"")),N=e=>(t,r,n,i,s,o,a,l,u,f,c,p)=>(r=S(n)?"":S(i)?`>=${n}.0.0${e?"-0":""}`:S(s)?`>=${n}.${i}.0${e?"-0":""}`:o?`>=${r}`:`>=${r}${e?"-0":""}`,l=S(u)?"":S(f)?`<${+u+1}.0.0-0`:S(c)?`<${u}.${+f+1}.0-0`:p?`<=${u}.${f}.${c}-${p}`:e?`<${u}.${f}.${+c+1}-0`:`<=${l}`,`${r} ${l}`.trim()),x=(e,t,r)=>{for(let r=0;r<e.length;r++)if(!e[r].test(t))return!1;if(t.prerelease.length&&!r.includePrerelease){for(let r=0;r<e.length;r++)if(l(e[r].semver),e[r].semver!==a.ANY&&e[r].semver.prerelease.length>0){let n=e[r].semver;if(n.major===t.major&&n.minor===t.minor&&n.patch===t.patch)return!0}return!1}return!0}},28871:(e,t,r)=>{"use strict";let n=r(1410),{MAX_LENGTH:i,MAX_SAFE_INTEGER:s}=r(73651),{safeRe:o,t:a}=r(81520),l=r(78949),{compareIdentifiers:u}=r(88178);class f{constructor(e,t){if(t=l(t),e instanceof f){if(!!t.loose===e.loose&&!!t.includePrerelease===e.includePrerelease)return e;e=e.version}else if("string"!=typeof e)throw TypeError(`Invalid version. Must be a string. Got type "${typeof e}".`);if(e.length>i)throw TypeError(`version is longer than ${i} characters`);n("SemVer",e,t),this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease;let r=e.trim().match(t.loose?o[a.LOOSE]:o[a.FULL]);if(!r)throw TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+r[1],this.minor=+r[2],this.patch=+r[3],this.major>s||this.major<0)throw TypeError("Invalid major version");if(this.minor>s||this.minor<0)throw TypeError("Invalid minor version");if(this.patch>s||this.patch<0)throw TypeError("Invalid patch version");r[4]?this.prerelease=r[4].split(".").map(e=>{if(/^[0-9]+$/.test(e)){let t=+e;if(t>=0&&t<s)return t}return e}):this.prerelease=[],this.build=r[5]?r[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if(n("SemVer.compare",this.version,this.options,e),!(e instanceof f)){if("string"==typeof e&&e===this.version)return 0;e=new f(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof f||(e=new f(e,this.options)),u(this.major,e.major)||u(this.minor,e.minor)||u(this.patch,e.patch)}comparePre(e){if(e instanceof f||(e=new f(e,this.options)),this.prerelease.length&&!e.prerelease.length)return -1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let t=0;do{let r=this.prerelease[t],i=e.prerelease[t];if(n("prerelease compare",t,r,i),void 0===r&&void 0===i)return 0;if(void 0===i)return 1;if(void 0===r)return -1;if(r===i)continue;else return u(r,i)}while(++t)}compareBuild(e){e instanceof f||(e=new f(e,this.options));let t=0;do{let r=this.build[t],i=e.build[t];if(n("build compare",t,r,i),void 0===r&&void 0===i)return 0;if(void 0===i)return 1;if(void 0===r)return -1;if(r===i)continue;else return u(r,i)}while(++t)}inc(e,t,r){if(e.startsWith("pre")){if(!t&&!1===r)throw Error("invalid increment argument: identifier is empty");if(t){let e=`-${t}`.match(this.options.loose?o[a.PRERELEASELOOSE]:o[a.PRERELEASE]);if(!e||e[1]!==t)throw Error(`invalid identifier: ${t}`)}}switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",t,r);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",t,r);break;case"prepatch":this.prerelease.length=0,this.inc("patch",t,r),this.inc("pre",t,r);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",t,r),this.inc("pre",t,r);break;case"release":if(0===this.prerelease.length)throw Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":(0!==this.minor||0!==this.patch||0===this.prerelease.length)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(0!==this.patch||0===this.prerelease.length)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":{let e=Number(r)?1:0;if(0===this.prerelease.length)this.prerelease=[e];else{let n=this.prerelease.length;for(;--n>=0;)"number"==typeof this.prerelease[n]&&(this.prerelease[n]++,n=-2);if(-1===n){if(t===this.prerelease.join(".")&&!1===r)throw Error("invalid increment argument: identifier already exists");this.prerelease.push(e)}}if(t){let n=[t,e];!1===r&&(n=[t]),0===u(this.prerelease[0],t)?isNaN(this.prerelease[1])&&(this.prerelease=n):this.prerelease=n}break}default:throw Error(`invalid increment argument: ${e}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}}e.exports=f},62485:(e,t,r)=>{"use strict";let n=r(48242);e.exports=(e,t)=>{let r=n(e.trim().replace(/^[=v]+/,""),t);return r?r.version:null}},43978:(e,t,r)=>{"use strict";let n=r(57411),i=r(28305),s=r(58368),o=r(76483),a=r(89673),l=r(94106);e.exports=(e,t,r,u)=>{switch(t){case"===":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e===r;case"!==":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e!==r;case"":case"=":case"==":return n(e,r,u);case"!=":return i(e,r,u);case">":return s(e,r,u);case">=":return o(e,r,u);case"<":return a(e,r,u);case"<=":return l(e,r,u);default:throw TypeError(`Invalid operator: ${t}`)}}},39150:(e,t,r)=>{"use strict";let n=r(28871),i=r(48242),{safeRe:s,t:o}=r(81520);e.exports=(e,t)=>{if(e instanceof n)return e;if("number"==typeof e&&(e=String(e)),"string"!=typeof e)return null;let r=null;if((t=t||{}).rtl){let n;let i=t.includePrerelease?s[o.COERCERTLFULL]:s[o.COERCERTL];for(;(n=i.exec(e))&&(!r||r.index+r[0].length!==e.length);)r&&n.index+n[0].length===r.index+r[0].length||(r=n),i.lastIndex=n.index+n[1].length+n[2].length;i.lastIndex=-1}else r=e.match(t.includePrerelease?s[o.COERCEFULL]:s[o.COERCE]);if(null===r)return null;let a=r[2],l=r[3]||"0",u=r[4]||"0",f=t.includePrerelease&&r[5]?`-${r[5]}`:"",c=t.includePrerelease&&r[6]?`+${r[6]}`:"";return i(`${a}.${l}.${u}${f}${c}`,t)}},84472:(e,t,r)=>{"use strict";let n=r(28871);e.exports=(e,t,r)=>{let i=new n(e,r),s=new n(t,r);return i.compare(s)||i.compareBuild(s)}},98136:(e,t,r)=>{"use strict";let n=r(51586);e.exports=(e,t)=>n(e,t,!0)},51586:(e,t,r)=>{"use strict";let n=r(28871);e.exports=(e,t,r)=>new n(e,r).compare(new n(t,r))},10906:(e,t,r)=>{"use strict";let n=r(48242);e.exports=(e,t)=>{let r=n(e,null,!0),i=n(t,null,!0),s=r.compare(i);if(0===s)return null;let o=s>0,a=o?r:i,l=o?i:r,u=!!a.prerelease.length;if(l.prerelease.length&&!u){if(!l.patch&&!l.minor)return"major";if(0===l.compareMain(a))return l.minor&&!l.patch?"minor":"patch"}let f=u?"pre":"";return r.major!==i.major?f+"major":r.minor!==i.minor?f+"minor":r.patch!==i.patch?f+"patch":"prerelease"}},57411:(e,t,r)=>{"use strict";let n=r(51586);e.exports=(e,t,r)=>0===n(e,t,r)},58368:(e,t,r)=>{"use strict";let n=r(51586);e.exports=(e,t,r)=>n(e,t,r)>0},76483:(e,t,r)=>{"use strict";let n=r(51586);e.exports=(e,t,r)=>n(e,t,r)>=0},44580:(e,t,r)=>{"use strict";let n=r(28871);e.exports=(e,t,r,i,s)=>{"string"==typeof r&&(s=i,i=r,r=void 0);try{return new n(e instanceof n?e.version:e,r).inc(t,i,s).version}catch(e){return null}}},89673:(e,t,r)=>{"use strict";let n=r(51586);e.exports=(e,t,r)=>0>n(e,t,r)},94106:(e,t,r)=>{"use strict";let n=r(51586);e.exports=(e,t,r)=>0>=n(e,t,r)},31148:(e,t,r)=>{"use strict";let n=r(28871);e.exports=(e,t)=>new n(e,t).major},95662:(e,t,r)=>{"use strict";let n=r(28871);e.exports=(e,t)=>new n(e,t).minor},28305:(e,t,r)=>{"use strict";let n=r(51586);e.exports=(e,t,r)=>0!==n(e,t,r)},48242:(e,t,r)=>{"use strict";let n=r(28871);e.exports=(e,t,r=!1)=>{if(e instanceof n)return e;try{return new n(e,t)}catch(e){if(!r)return null;throw e}}},34234:(e,t,r)=>{"use strict";let n=r(28871);e.exports=(e,t)=>new n(e,t).patch},92606:(e,t,r)=>{"use strict";let n=r(48242);e.exports=(e,t)=>{let r=n(e,t);return r&&r.prerelease.length?r.prerelease:null}},92490:(e,t,r)=>{"use strict";let n=r(51586);e.exports=(e,t,r)=>n(t,e,r)},11302:(e,t,r)=>{"use strict";let n=r(84472);e.exports=(e,t)=>e.sort((e,r)=>n(r,e,t))},24520:(e,t,r)=>{"use strict";let n=r(88889);e.exports=(e,t,r)=>{try{t=new n(t,r)}catch(e){return!1}return t.test(e)}},83975:(e,t,r)=>{"use strict";let n=r(84472);e.exports=(e,t)=>e.sort((e,r)=>n(e,r,t))},40628:(e,t,r)=>{"use strict";let n=r(48242);e.exports=(e,t)=>{let r=n(e,t);return r?r.version:null}},90799:(e,t,r)=>{"use strict";let n=r(81520),i=r(73651),s=r(28871),o=r(88178),a=r(48242),l=r(40628),u=r(62485),f=r(44580),c=r(10906),p=r(31148),h=r(95662),m=r(34234),E=r(92606),d=r(51586),y=r(92490),g=r(98136),v=r(84472),b=r(83975),S=r(11302),$=r(58368),I=r(89673),w=r(57411),R=r(28305),A=r(76483),O=r(94106),T=r(43978),L=r(39150),N=r(12925),x=r(88889),j=r(24520),P=r(19835),C=r(1446),k=r(75030),D=r(30091),M=r(16269),B=r(45400),G=r(45760),F=r(62884),U=r(53225),_=r(86861),K=r(64550);e.exports={parse:a,valid:l,clean:u,inc:f,diff:c,major:p,minor:h,patch:m,prerelease:E,compare:d,rcompare:y,compareLoose:g,compareBuild:v,sort:b,rsort:S,gt:$,lt:I,eq:w,neq:R,gte:A,lte:O,cmp:T,coerce:L,Comparator:N,Range:x,satisfies:j,toComparators:P,maxSatisfying:C,minSatisfying:k,minVersion:D,validRange:M,outside:B,gtr:G,ltr:F,intersects:U,simplifyRange:_,subset:K,SemVer:s,re:n.re,src:n.src,tokens:n.t,SEMVER_SPEC_VERSION:i.SEMVER_SPEC_VERSION,RELEASE_TYPES:i.RELEASE_TYPES,compareIdentifiers:o.compareIdentifiers,rcompareIdentifiers:o.rcompareIdentifiers}},73651:e=>{"use strict";let t=Number.MAX_SAFE_INTEGER||9007199254740991;e.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:16,MAX_SAFE_BUILD_LENGTH:250,MAX_SAFE_INTEGER:t,RELEASE_TYPES:["major","premajor","minor","preminor","patch","prepatch","prerelease"],SEMVER_SPEC_VERSION:"2.0.0",FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}},1410:e=>{"use strict";let t="object"==typeof process&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};e.exports=t},88178:e=>{"use strict";let t=/^[0-9]+$/,r=(e,r)=>{let n=t.test(e),i=t.test(r);return n&&i&&(e=+e,r=+r),e===r?0:n&&!i?-1:i&&!n?1:e<r?-1:1};e.exports={compareIdentifiers:r,rcompareIdentifiers:(e,t)=>r(t,e)}},50241:e=>{"use strict";class t{constructor(){this.max=1e3,this.map=new Map}get(e){let t=this.map.get(e);if(void 0!==t)return this.map.delete(e),this.map.set(e,t),t}delete(e){return this.map.delete(e)}set(e,t){if(!this.delete(e)&&void 0!==t){if(this.map.size>=this.max){let e=this.map.keys().next().value;this.delete(e)}this.map.set(e,t)}return this}}e.exports=t},78949:e=>{"use strict";let t=Object.freeze({loose:!0}),r=Object.freeze({});e.exports=e=>e?"object"!=typeof e?t:e:r},81520:(e,t,r)=>{"use strict";let{MAX_SAFE_COMPONENT_LENGTH:n,MAX_SAFE_BUILD_LENGTH:i,MAX_LENGTH:s}=r(73651),o=r(1410),a=(t=e.exports={}).re=[],l=t.safeRe=[],u=t.src=[],f=t.safeSrc=[],c=t.t={},p=0,h="[a-zA-Z0-9-]",m=[["\\s",1],["\\d",s],[h,i]],E=e=>{for(let[t,r]of m)e=e.split(`${t}*`).join(`${t}{0,${r}}`).split(`${t}+`).join(`${t}{1,${r}}`);return e},d=(e,t,r)=>{let n=E(t),i=p++;o(e,i,t),c[e]=i,u[i]=t,f[i]=n,a[i]=new RegExp(t,r?"g":void 0),l[i]=new RegExp(n,r?"g":void 0)};d("NUMERICIDENTIFIER","0|[1-9]\\d*"),d("NUMERICIDENTIFIERLOOSE","\\d+"),d("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${h}*`),d("MAINVERSION",`(${u[c.NUMERICIDENTIFIER]})\\.(${u[c.NUMERICIDENTIFIER]})\\.(${u[c.NUMERICIDENTIFIER]})`),d("MAINVERSIONLOOSE",`(${u[c.NUMERICIDENTIFIERLOOSE]})\\.(${u[c.NUMERICIDENTIFIERLOOSE]})\\.(${u[c.NUMERICIDENTIFIERLOOSE]})`),d("PRERELEASEIDENTIFIER",`(?:${u[c.NONNUMERICIDENTIFIER]}|${u[c.NUMERICIDENTIFIER]})`),d("PRERELEASEIDENTIFIERLOOSE",`(?:${u[c.NONNUMERICIDENTIFIER]}|${u[c.NUMERICIDENTIFIERLOOSE]})`),d("PRERELEASE",`(?:-(${u[c.PRERELEASEIDENTIFIER]}(?:\\.${u[c.PRERELEASEIDENTIFIER]})*))`),d("PRERELEASELOOSE",`(?:-?(${u[c.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${u[c.PRERELEASEIDENTIFIERLOOSE]})*))`),d("BUILDIDENTIFIER",`${h}+`),d("BUILD",`(?:\\+(${u[c.BUILDIDENTIFIER]}(?:\\.${u[c.BUILDIDENTIFIER]})*))`),d("FULLPLAIN",`v?${u[c.MAINVERSION]}${u[c.PRERELEASE]}?${u[c.BUILD]}?`),d("FULL",`^${u[c.FULLPLAIN]}$`),d("LOOSEPLAIN",`[v=\\s]*${u[c.MAINVERSIONLOOSE]}${u[c.PRERELEASELOOSE]}?${u[c.BUILD]}?`),d("LOOSE",`^${u[c.LOOSEPLAIN]}$`),d("GTLT","((?:<|>)?=?)"),d("XRANGEIDENTIFIERLOOSE",`${u[c.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),d("XRANGEIDENTIFIER",`${u[c.NUMERICIDENTIFIER]}|x|X|\\*`),d("XRANGEPLAIN",`[v=\\s]*(${u[c.XRANGEIDENTIFIER]})(?:\\.(${u[c.XRANGEIDENTIFIER]})(?:\\.(${u[c.XRANGEIDENTIFIER]})(?:${u[c.PRERELEASE]})?${u[c.BUILD]}?)?)?`),d("XRANGEPLAINLOOSE",`[v=\\s]*(${u[c.XRANGEIDENTIFIERLOOSE]})(?:\\.(${u[c.XRANGEIDENTIFIERLOOSE]})(?:\\.(${u[c.XRANGEIDENTIFIERLOOSE]})(?:${u[c.PRERELEASELOOSE]})?${u[c.BUILD]}?)?)?`),d("XRANGE",`^${u[c.GTLT]}\\s*${u[c.XRANGEPLAIN]}$`),d("XRANGELOOSE",`^${u[c.GTLT]}\\s*${u[c.XRANGEPLAINLOOSE]}$`),d("COERCEPLAIN",`(^|[^\\d])(\\d{1,${n}})(?:\\.(\\d{1,${n}}))?(?:\\.(\\d{1,${n}}))?`),d("COERCE",`${u[c.COERCEPLAIN]}(?:$|[^\\d])`),d("COERCEFULL",u[c.COERCEPLAIN]+`(?:${u[c.PRERELEASE]})?`+`(?:${u[c.BUILD]})?`+"(?:$|[^\\d])"),d("COERCERTL",u[c.COERCE],!0),d("COERCERTLFULL",u[c.COERCEFULL],!0),d("LONETILDE","(?:~>?)"),d("TILDETRIM",`(\\s*)${u[c.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",d("TILDE",`^${u[c.LONETILDE]}${u[c.XRANGEPLAIN]}$`),d("TILDELOOSE",`^${u[c.LONETILDE]}${u[c.XRANGEPLAINLOOSE]}$`),d("LONECARET","(?:\\^)"),d("CARETTRIM",`(\\s*)${u[c.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",d("CARET",`^${u[c.LONECARET]}${u[c.XRANGEPLAIN]}$`),d("CARETLOOSE",`^${u[c.LONECARET]}${u[c.XRANGEPLAINLOOSE]}$`),d("COMPARATORLOOSE",`^${u[c.GTLT]}\\s*(${u[c.LOOSEPLAIN]})$|^$`),d("COMPARATOR",`^${u[c.GTLT]}\\s*(${u[c.FULLPLAIN]})$|^$`),d("COMPARATORTRIM",`(\\s*)${u[c.GTLT]}\\s*(${u[c.LOOSEPLAIN]}|${u[c.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",d("HYPHENRANGE",`^\\s*(${u[c.XRANGEPLAIN]})\\s+-\\s+(${u[c.XRANGEPLAIN]})\\s*$`),d("HYPHENRANGELOOSE",`^\\s*(${u[c.XRANGEPLAINLOOSE]})\\s+-\\s+(${u[c.XRANGEPLAINLOOSE]})\\s*$`),d("STAR","(<|>)?=?\\s*\\*"),d("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),d("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")},45760:(e,t,r)=>{"use strict";let n=r(45400);e.exports=(e,t,r)=>n(e,t,">",r)},53225:(e,t,r)=>{"use strict";let n=r(88889);e.exports=(e,t,r)=>(e=new n(e,r),t=new n(t,r),e.intersects(t,r))},62884:(e,t,r)=>{"use strict";let n=r(45400);e.exports=(e,t,r)=>n(e,t,"<",r)},1446:(e,t,r)=>{"use strict";let n=r(28871),i=r(88889);e.exports=(e,t,r)=>{let s=null,o=null,a=null;try{a=new i(t,r)}catch(e){return null}return e.forEach(e=>{a.test(e)&&(!s||-1===o.compare(e))&&(o=new n(s=e,r))}),s}},75030:(e,t,r)=>{"use strict";let n=r(28871),i=r(88889);e.exports=(e,t,r)=>{let s=null,o=null,a=null;try{a=new i(t,r)}catch(e){return null}return e.forEach(e=>{a.test(e)&&(!s||1===o.compare(e))&&(o=new n(s=e,r))}),s}},30091:(e,t,r)=>{"use strict";let n=r(28871),i=r(88889),s=r(58368);e.exports=(e,t)=>{e=new i(e,t);let r=new n("0.0.0");if(e.test(r)||(r=new n("0.0.0-0"),e.test(r)))return r;r=null;for(let t=0;t<e.set.length;++t){let i=e.set[t],o=null;i.forEach(e=>{let t=new n(e.semver.version);switch(e.operator){case">":0===t.prerelease.length?t.patch++:t.prerelease.push(0),t.raw=t.format();case"":case">=":(!o||s(t,o))&&(o=t);break;case"<":case"<=":break;default:throw Error(`Unexpected operation: ${e.operator}`)}}),o&&(!r||s(r,o))&&(r=o)}return r&&e.test(r)?r:null}},45400:(e,t,r)=>{"use strict";let n=r(28871),i=r(12925),{ANY:s}=i,o=r(88889),a=r(24520),l=r(58368),u=r(89673),f=r(94106),c=r(76483);e.exports=(e,t,r,p)=>{let h,m,E,d,y;switch(e=new n(e,p),t=new o(t,p),r){case">":h=l,m=f,E=u,d=">",y=">=";break;case"<":h=u,m=c,E=l,d="<",y="<=";break;default:throw TypeError('Must provide a hilo val of "<" or ">"')}if(a(e,t,p))return!1;for(let r=0;r<t.set.length;++r){let n=t.set[r],o=null,a=null;if(n.forEach(e=>{e.semver===s&&(e=new i(">=0.0.0")),o=o||e,a=a||e,h(e.semver,o.semver,p)?o=e:E(e.semver,a.semver,p)&&(a=e)}),o.operator===d||o.operator===y||(!a.operator||a.operator===d)&&m(e,a.semver)||a.operator===y&&E(e,a.semver))return!1}return!0}},86861:(e,t,r)=>{"use strict";let n=r(24520),i=r(51586);e.exports=(e,t,r)=>{let s=[],o=null,a=null,l=e.sort((e,t)=>i(e,t,r));for(let e of l)n(e,t,r)?(a=e,o||(o=e)):(a&&s.push([o,a]),a=null,o=null);o&&s.push([o,null]);let u=[];for(let[e,t]of s)e===t?u.push(e):t||e!==l[0]?t?e===l[0]?u.push(`<=${t}`):u.push(`${e} - ${t}`):u.push(`>=${e}`):u.push("*");let f=u.join(" || "),c="string"==typeof t.raw?t.raw:String(t);return f.length<c.length?f:t}},64550:(e,t,r)=>{"use strict";let n=r(88889),i=r(12925),{ANY:s}=i,o=r(24520),a=r(51586),l=[new i(">=0.0.0-0")],u=[new i(">=0.0.0")],f=(e,t,r)=>{let n,i,f,h,m,E,d;if(e===t)return!0;if(1===e.length&&e[0].semver===s){if(1===t.length&&t[0].semver===s)return!0;e=r.includePrerelease?l:u}if(1===t.length&&t[0].semver===s){if(r.includePrerelease)return!0;t=u}let y=new Set;for(let t of e)">"===t.operator||">="===t.operator?n=c(n,t,r):"<"===t.operator||"<="===t.operator?i=p(i,t,r):y.add(t.semver);if(y.size>1||n&&i&&((f=a(n.semver,i.semver,r))>0||0===f&&(">="!==n.operator||"<="!==i.operator)))return null;for(let e of y){if(n&&!o(e,String(n),r)||i&&!o(e,String(i),r))return null;for(let n of t)if(!o(e,String(n),r))return!1;return!0}let g=!!i&&!r.includePrerelease&&!!i.semver.prerelease.length&&i.semver,v=!!n&&!r.includePrerelease&&!!n.semver.prerelease.length&&n.semver;for(let e of(g&&1===g.prerelease.length&&"<"===i.operator&&0===g.prerelease[0]&&(g=!1),t)){if(d=d||">"===e.operator||">="===e.operator,E=E||"<"===e.operator||"<="===e.operator,n){if(v&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===v.major&&e.semver.minor===v.minor&&e.semver.patch===v.patch&&(v=!1),">"===e.operator||">="===e.operator){if((h=c(n,e,r))===e&&h!==n)return!1}else if(">="===n.operator&&!o(n.semver,String(e),r))return!1}if(i){if(g&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===g.major&&e.semver.minor===g.minor&&e.semver.patch===g.patch&&(g=!1),"<"===e.operator||"<="===e.operator){if((m=p(i,e,r))===e&&m!==i)return!1}else if("<="===i.operator&&!o(i.semver,String(e),r))return!1}if(!e.operator&&(i||n)&&0!==f)return!1}return(!n||!E||!!i||0===f)&&(!i||!d||!!n||0===f)&&!v&&!g},c=(e,t,r)=>{if(!e)return t;let n=a(e.semver,t.semver,r);return n>0?e:n<0?t:">"===t.operator&&">="===e.operator?t:e},p=(e,t,r)=>{if(!e)return t;let n=a(e.semver,t.semver,r);return n<0?e:n>0?t:"<"===t.operator&&"<="===e.operator?t:e};e.exports=(e,t,r={})=>{if(e===t)return!0;e=new n(e,r),t=new n(t,r);let i=!1;e:for(let n of e.set){for(let e of t.set){let t=f(n,e,r);if(i=i||null!==t,t)continue e}if(i)return!1}return!0}},19835:(e,t,r)=>{"use strict";let n=r(88889);e.exports=(e,t)=>new n(e,t).set.map(e=>e.map(e=>e.value).join(" ").trim().split(" "))},16269:(e,t,r)=>{"use strict";let n=r(88889);e.exports=(e,t)=>{try{return new n(e,t).range||"*"}catch(e){return null}}},98691:(e,t,r)=>{"use strict";r.d(t,{ZP:()=>A});var n=r(84770),i=null;function s(e,t){if("number"!=typeof(e=e||y))throw Error("Illegal arguments: "+typeof e+", "+typeof t);e<4?e=4:e>31&&(e=31);var r=[];return r.push("$2b$"),e<10&&r.push("0"),r.push(e.toString()),r.push("$"),r.push(m(function(e){try{return crypto.getRandomValues(new Uint8Array(e))}catch{}try{return n.randomBytes(e)}catch{}if(!i)throw Error("Neither WebCryptoAPI nor a crypto module is available. Use bcrypt.setRandomFallback to set an alternative");return i(e)}(d),d)),r.join("")}function o(e,t,r){if("function"==typeof t&&(r=t,t=void 0),"function"==typeof e&&(r=e,e=void 0),void 0===e)e=y;else if("number"!=typeof e)throw Error("illegal arguments: "+typeof e);function n(t){f(function(){try{t(null,s(e))}catch(e){t(e)}})}if(!r)return new Promise(function(e,t){n(function(r,n){if(r){t(r);return}e(n)})});if("function"!=typeof r)throw Error("Illegal callback: "+typeof r);n(r)}function a(e,t){if(void 0===t&&(t=y),"number"==typeof t&&(t=s(t)),"string"!=typeof e||"string"!=typeof t)throw Error("Illegal arguments: "+typeof e+", "+typeof t);return R(e,t)}function l(e,t,r,n){function i(r){"string"==typeof e&&"number"==typeof t?o(t,function(t,i){R(e,i,r,n)}):"string"==typeof e&&"string"==typeof t?R(e,t,r,n):f(r.bind(this,Error("Illegal arguments: "+typeof e+", "+typeof t)))}if(!r)return new Promise(function(e,t){i(function(r,n){if(r){t(r);return}e(n)})});if("function"!=typeof r)throw Error("Illegal callback: "+typeof r);i(r)}function u(e,t){for(var r=e.length^t.length,n=0;n<e.length;++n)r|=e.charCodeAt(n)^t.charCodeAt(n);return 0===r}var f="undefined"!=typeof process&&process&&"function"==typeof process.nextTick?"function"==typeof setImmediate?setImmediate:process.nextTick:setTimeout;function c(e){for(var t=0,r=0,n=0;n<e.length;++n)(r=e.charCodeAt(n))<128?t+=1:r<2048?t+=2:(64512&r)==55296&&(64512&e.charCodeAt(n+1))==56320?(++n,t+=4):t+=3;return t}var p="./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=[-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,1,54,55,56,57,58,59,60,61,62,63,-1,-1,-1,-1,-1,-1,-1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,-1,-1,-1,-1,-1,-1,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,-1,-1,-1,-1,-1];function m(e,t){var r,n,i=0,s=[];if(t<=0||t>e.length)throw Error("Illegal len: "+t);for(;i<t;){if(r=255&e[i++],s.push(p[r>>2&63]),r=(3&r)<<4,i>=t||(r|=(n=255&e[i++])>>4&15,s.push(p[63&r]),r=(15&n)<<2,i>=t)){s.push(p[63&r]);break}r|=(n=255&e[i++])>>6&3,s.push(p[63&r]),s.push(p[63&n])}return s.join("")}function E(e,t){var r,n,i,s,o,a=0,l=e.length,u=0,f=[];if(t<=0)throw Error("Illegal len: "+t);for(;a<l-1&&u<t&&(r=(o=e.charCodeAt(a++))<h.length?h[o]:-1,n=(o=e.charCodeAt(a++))<h.length?h[o]:-1,-1!=r&&-1!=n)&&(s=r<<2>>>0|(48&n)>>4,f.push(String.fromCharCode(s)),!(++u>=t||a>=l||-1==(i=(o=e.charCodeAt(a++))<h.length?h[o]:-1)||(s=(15&n)<<4>>>0|(60&i)>>2,f.push(String.fromCharCode(s)),++u>=t||a>=l)));)s=(3&i)<<6>>>0|((o=e.charCodeAt(a++))<h.length?h[o]:-1),f.push(String.fromCharCode(s)),++u;var c=[];for(a=0;a<u;a++)c.push(f[a].charCodeAt(0));return c}var d=16,y=10,g=[608135816,2242054355,320440878,57701188,2752067618,698298832,137296536,3964562569,1160258022,953160567,3193202383,887688300,3232508343,3380367581,1065670069,3041331479,2450970073,2306472731],v=[3509652390,2564797868,805139163,3491422135,3101798381,1780907670,3128725573,4046225305,614570311,3012652279,134345442,2240740374,1667834072,1901547113,2757295779,4103290238,227898511,1921955416,1904987480,2182433518,2069144605,3260701109,2620446009,720527379,3318853667,677414384,3393288472,3101374703,2390351024,1614419982,1822297739,2954791486,3608508353,3174124327,2024746970,1432378464,3864339955,2857741204,1464375394,1676153920,1439316330,715854006,3033291828,289532110,2706671279,2087905683,3018724369,1668267050,732546397,1947742710,3462151702,2609353502,2950085171,1814351708,2050118529,680887927,999245976,1800124847,3300911131,1713906067,1641548236,4213287313,1216130144,1575780402,4018429277,3917837745,3693486850,3949271944,596196993,3549867205,258830323,2213823033,772490370,2760122372,1774776394,2652871518,566650946,4142492826,1728879713,2882767088,1783734482,3629395816,2517608232,2874225571,1861159788,326777828,3124490320,2130389656,2716951837,967770486,1724537150,2185432712,2364442137,1164943284,2105845187,998989502,3765401048,2244026483,1075463327,1455516326,1322494562,910128902,469688178,1117454909,936433444,3490320968,3675253459,1240580251,122909385,2157517691,634681816,4142456567,3825094682,3061402683,2540495037,79693498,3249098678,1084186820,1583128258,426386531,1761308591,1047286709,322548459,995290223,1845252383,2603652396,3431023940,2942221577,3202600964,3727903485,1712269319,422464435,3234572375,1170764815,3523960633,3117677531,1434042557,442511882,3600875718,1076654713,1738483198,4213154764,2393238008,3677496056,1014306527,4251020053,793779912,2902807211,842905082,4246964064,1395751752,1040244610,2656851899,3396308128,445077038,3742853595,3577915638,679411651,2892444358,2354009459,1767581616,3150600392,3791627101,3102740896,284835224,4246832056,1258075500,768725851,2589189241,3069724005,3532540348,1274779536,3789419226,2764799539,1660621633,3471099624,4011903706,913787905,3497959166,737222580,2514213453,2928710040,3937242737,1804850592,3499020752,2949064160,2386320175,2390070455,2415321851,4061277028,2290661394,2416832540,1336762016,1754252060,3520065937,3014181293,791618072,3188594551,3933548030,2332172193,3852520463,3043980520,413987798,3465142937,3030929376,4245938359,2093235073,3534596313,375366246,2157278981,2479649556,555357303,3870105701,2008414854,3344188149,4221384143,3956125452,2067696032,3594591187,2921233993,2428461,544322398,577241275,1471733935,610547355,4027169054,1432588573,1507829418,2025931657,3646575487,545086370,48609733,2200306550,1653985193,298326376,1316178497,3007786442,2064951626,458293330,2589141269,3591329599,3164325604,727753846,2179363840,146436021,1461446943,4069977195,705550613,3059967265,3887724982,4281599278,3313849956,1404054877,2845806497,146425753,1854211946,1266315497,3048417604,3681880366,3289982499,290971e4,1235738493,2632868024,2414719590,3970600049,1771706367,1449415276,3266420449,422970021,1963543593,2690192192,3826793022,1062508698,1531092325,1804592342,2583117782,2714934279,4024971509,1294809318,4028980673,1289560198,2221992742,1669523910,35572830,157838143,1052438473,1016535060,1802137761,1753167236,1386275462,3080475397,2857371447,1040679964,2145300060,2390574316,1461121720,2956646967,4031777805,4028374788,33600511,2920084762,1018524850,629373528,3691585981,3515945977,2091462646,2486323059,586499841,988145025,935516892,3367335476,2599673255,2839830854,265290510,3972581182,2759138881,3795373465,1005194799,847297441,406762289,1314163512,1332590856,1866599683,4127851711,750260880,613907577,1450815602,3165620655,3734664991,3650291728,3012275730,3704569646,1427272223,778793252,1343938022,2676280711,2052605720,1946737175,3164576444,3914038668,3967478842,3682934266,1661551462,3294938066,4011595847,840292616,3712170807,616741398,312560963,711312465,1351876610,322626781,1910503582,271666773,2175563734,1594956187,70604529,3617834859,1007753275,1495573769,4069517037,2549218298,2663038764,504708206,2263041392,3941167025,2249088522,1514023603,1998579484,1312622330,694541497,2582060303,2151582166,1382467621,776784248,2618340202,3323268794,2497899128,2784771155,503983604,4076293799,907881277,423175695,432175456,1378068232,4145222326,3954048622,3938656102,3820766613,2793130115,2977904593,26017576,3274890735,3194772133,1700274565,1756076034,4006520079,3677328699,720338349,1533947780,354530856,688349552,3973924725,1637815568,332179504,3949051286,53804574,2852348879,3044236432,1282449977,3583942155,3416972820,4006381244,1617046695,2628476075,3002303598,1686838959,431878346,2686675385,1700445008,1080580658,1009431731,832498133,3223435511,2605976345,2271191193,2516031870,1648197032,4164389018,2548247927,300782431,375919233,238389289,3353747414,2531188641,2019080857,1475708069,455242339,2609103871,448939670,3451063019,1395535956,2413381860,1841049896,1491858159,885456874,4264095073,4001119347,1565136089,3898914787,1108368660,540939232,1173283510,2745871338,3681308437,4207628240,3343053890,4016749493,1699691293,1103962373,3625875870,2256883143,3830138730,1031889488,3479347698,1535977030,4236805024,3251091107,2132092099,1774941330,1199868427,1452454533,157007616,2904115357,342012276,595725824,1480756522,206960106,497939518,591360097,863170706,2375253569,3596610801,1814182875,2094937945,3421402208,1082520231,3463918190,2785509508,435703966,3908032597,1641649973,2842273706,3305899714,1510255612,2148256476,2655287854,3276092548,4258621189,236887753,3681803219,274041037,1734335097,3815195456,3317970021,1899903192,1026095262,4050517792,356393447,2410691914,3873677099,3682840055,3913112168,2491498743,4132185628,2489919796,1091903735,1979897079,3170134830,3567386728,3557303409,857797738,1136121015,1342202287,507115054,2535736646,337727348,3213592640,1301675037,2528481711,1895095763,1721773893,3216771564,62756741,2142006736,835421444,2531993523,1442658625,3659876326,2882144922,676362277,1392781812,170690266,3921047035,1759253602,3611846912,1745797284,664899054,1329594018,3901205900,3045908486,2062866102,2865634940,3543621612,3464012697,1080764994,553557557,3656615353,3996768171,991055499,499776247,1265440854,648242737,3940784050,980351604,3713745714,1749149687,3396870395,4211799374,3640570775,1161844396,3125318951,1431517754,545492359,4268468663,3499529547,1437099964,2702547544,3433638243,2581715763,2787789398,1060185593,1593081372,2418618748,4260947970,69676912,2159744348,86519011,2512459080,3838209314,1220612927,3339683548,133810670,1090789135,1078426020,1569222167,845107691,3583754449,4072456591,1091646820,628848692,1613405280,3757631651,526609435,236106946,48312990,2942717905,3402727701,1797494240,859738849,992217954,4005476642,2243076622,3870952857,3732016268,765654824,3490871365,2511836413,1685915746,3888969200,1414112111,2273134842,3281911079,4080962846,172450625,2569994100,980381355,4109958455,2819808352,2716589560,2568741196,3681446669,3329971472,1835478071,660984891,3704678404,4045999559,3422617507,3040415634,1762651403,1719377915,3470491036,2693910283,3642056355,3138596744,1364962596,2073328063,1983633131,926494387,3423689081,2150032023,4096667949,1749200295,3328846651,309677260,2016342300,1779581495,3079819751,111262694,1274766160,443224088,298511866,1025883608,3806446537,1145181785,168956806,3641502830,3584813610,1689216846,3666258015,3200248200,1692713982,2646376535,4042768518,1618508792,1610833997,3523052358,4130873264,2001055236,3610705100,2202168115,4028541809,2961195399,1006657119,2006996926,3186142756,1430667929,3210227297,1314452623,4074634658,4101304120,2273951170,1399257539,3367210612,3027628629,1190975929,2062231137,2333990788,2221543033,2438960610,1181637006,548689776,2362791313,3372408396,3104550113,3145860560,296247880,1970579870,3078560182,3769228297,1714227617,3291629107,3898220290,166772364,1251581989,493813264,448347421,195405023,2709975567,677966185,3703036547,1463355134,2715995803,1338867538,1343315457,2802222074,2684532164,233230375,2599980071,2000651841,3277868038,1638401717,4028070440,3237316320,6314154,819756386,300326615,590932579,1405279636,3267499572,3150704214,2428286686,3959192993,3461946742,1862657033,1266418056,963775037,2089974820,2263052895,1917689273,448879540,3550394620,3981727096,150775221,3627908307,1303187396,508620638,2975983352,2726630617,1817252668,1876281319,1457606340,908771278,3720792119,3617206836,2455994898,1729034894,1080033504,976866871,3556439503,2881648439,1522871579,1555064734,1336096578,3548522304,2579274686,3574697629,3205460757,3593280638,3338716283,3079412587,564236357,2993598910,1781952180,1464380207,3163844217,3332601554,1699332808,1393555694,1183702653,3581086237,1288719814,691649499,2847557200,2895455976,3193889540,2717570544,1781354906,1676643554,2592534050,3230253752,1126444790,2770207658,2633158820,2210423226,2615765581,2414155088,3127139286,673620729,2805611233,1269405062,4015350505,3341807571,4149409754,1057255273,2012875353,2162469141,2276492801,2601117357,993977747,3918593370,2654263191,753973209,36408145,2530585658,25011837,3520020182,2088578344,530523599,2918365339,1524020338,1518925132,3760827505,3759777254,1202760957,3985898139,3906192525,674977740,4174734889,2031300136,2019492241,3983892565,4153806404,3822280332,352677332,2297720250,60907813,90501309,3286998549,1016092578,2535922412,2839152426,457141659,509813237,4120667899,652014361,1966332200,2975202805,55981186,2327461051,676427537,3255491064,2882294119,3433927263,1307055953,942726286,933058658,2468411793,3933900994,4215176142,1361170020,2001714738,2830558078,3274259782,1222529897,1679025792,2729314320,3714953764,1770335741,151462246,3013232138,1682292957,1483529935,471910574,1539241949,458788160,3436315007,1807016891,3718408830,978976581,1043663428,3165965781,1927990952,4200891579,2372276910,3208408903,3533431907,1412390302,2931980059,4132332400,1947078029,3881505623,4168226417,2941484381,1077988104,1320477388,886195818,18198404,3786409e3,2509781533,112762804,3463356488,1866414978,891333506,18488651,661792760,1628790961,3885187036,3141171499,876946877,2693282273,1372485963,791857591,2686433993,3759982718,3167212022,3472953795,2716379847,445679433,3561995674,3504004811,3574258232,54117162,3331405415,2381918588,3769707343,4154350007,1140177722,4074052095,668550556,3214352940,367459370,261225585,2610173221,4209349473,3468074219,3265815641,314222801,3066103646,3808782860,282218597,3406013506,3773591054,379116347,1285071038,846784868,2669647154,3771962079,3550491691,2305946142,453669953,1268987020,3317592352,3279303384,3744833421,2610507566,3859509063,266596637,3847019092,517658769,3462560207,3443424879,370717030,4247526661,2224018117,4143653529,4112773975,2788324899,2477274417,1456262402,2901442914,1517677493,1846949527,2295493580,3734397586,2176403920,1280348187,1908823572,3871786941,846861322,1172426758,3287448474,3383383037,1655181056,3139813346,901632758,1897031941,2986607138,3066810236,3447102507,1393639104,373351379,950779232,625454576,3124240540,4148612726,2007998917,544563296,2244738638,2330496472,2058025392,1291430526,424198748,50039436,29584100,3605783033,2429876329,2791104160,1057563949,3255363231,3075367218,3463963227,1469046755,985887462],b=[1332899944,1700884034,1701343084,1684370003,1668446532,1869963892];function S(e,t,r,n){var i=e[t],s=e[t+1];return i^=r[0],s^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[1],i^=(n[s>>>24]+n[256|s>>16&255]^n[512|s>>8&255])+n[768|255&s]^r[2],s^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[3],i^=(n[s>>>24]+n[256|s>>16&255]^n[512|s>>8&255])+n[768|255&s]^r[4],s^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[5],i^=(n[s>>>24]+n[256|s>>16&255]^n[512|s>>8&255])+n[768|255&s]^r[6],s^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[7],i^=(n[s>>>24]+n[256|s>>16&255]^n[512|s>>8&255])+n[768|255&s]^r[8],s^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[9],i^=(n[s>>>24]+n[256|s>>16&255]^n[512|s>>8&255])+n[768|255&s]^r[10],s^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[11],i^=(n[s>>>24]+n[256|s>>16&255]^n[512|s>>8&255])+n[768|255&s]^r[12],s^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[13],i^=(n[s>>>24]+n[256|s>>16&255]^n[512|s>>8&255])+n[768|255&s]^r[14],s^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[15],i^=(n[s>>>24]+n[256|s>>16&255]^n[512|s>>8&255])+n[768|255&s]^r[16],e[t]=s^r[17],e[t+1]=i,e}function $(e,t){for(var r=0,n=0;r<4;++r)n=n<<8|255&e[t],t=(t+1)%e.length;return{key:n,offp:t}}function I(e,t,r){for(var n,i=0,s=[0,0],o=t.length,a=r.length,l=0;l<o;l++)i=(n=$(e,i)).offp,t[l]=t[l]^n.key;for(l=0;l<o;l+=2)s=S(s,0,t,r),t[l]=s[0],t[l+1]=s[1];for(l=0;l<a;l+=2)s=S(s,0,t,r),r[l]=s[0],r[l+1]=s[1]}function w(e,t,r,n,i){var s,o,a=b.slice(),l=a.length;if(r<4||r>31){if(o=Error("Illegal number of rounds (4-31): "+r),n){f(n.bind(this,o));return}throw o}if(t.length!==d){if(o=Error("Illegal salt length: "+t.length+" != "+d),n){f(n.bind(this,o));return}throw o}r=1<<r>>>0;var u,c,p,h=0;function m(){if(i&&i(h/r),h<r)for(var s=Date.now();h<r&&(h+=1,I(e,u,c),I(t,u,c),!(Date.now()-s>100)););else{for(h=0;h<64;h++)for(p=0;p<l>>1;p++)S(a,p<<1,u,c);var o=[];for(h=0;h<l;h++)o.push((a[h]>>24&255)>>>0),o.push((a[h]>>16&255)>>>0),o.push((a[h]>>8&255)>>>0),o.push((255&a[h])>>>0);return n?void n(null,o):o}n&&f(m)}if("function"==typeof Int32Array?(u=new Int32Array(g),c=new Int32Array(v)):(u=g.slice(),c=v.slice()),function(e,t,r,n){for(var i,s=0,o=[0,0],a=r.length,l=n.length,u=0;u<a;u++)s=(i=$(t,s)).offp,r[u]=r[u]^i.key;for(u=0,s=0;u<a;u+=2)s=(i=$(e,s)).offp,o[0]^=i.key,s=(i=$(e,s)).offp,o[1]^=i.key,o=S(o,0,r,n),r[u]=o[0],r[u+1]=o[1];for(u=0;u<l;u+=2)s=(i=$(e,s)).offp,o[0]^=i.key,s=(i=$(e,s)).offp,o[1]^=i.key,o=S(o,0,r,n),n[u]=o[0],n[u+1]=o[1]}(t,e,u,c),void 0!==n)m();else for(;;)if(void 0!==(s=m()))return s||[]}function R(e,t,r,n){if("string"!=typeof e||"string"!=typeof t){if(i=Error("Invalid string / salt: Not a string"),r){f(r.bind(this,i));return}throw i}if("$"!==t.charAt(0)||"2"!==t.charAt(1)){if(i=Error("Invalid salt version: "+t.substring(0,2)),r){f(r.bind(this,i));return}throw i}if("$"===t.charAt(2))s="\0",o=3;else{if("a"!==(s=t.charAt(2))&&"b"!==s&&"y"!==s||"$"!==t.charAt(3)){if(i=Error("Invalid salt revision: "+t.substring(2,4)),r){f(r.bind(this,i));return}throw i}o=4}if(t.charAt(o+2)>"$"){if(i=Error("Missing salt rounds"),r){f(r.bind(this,i));return}throw i}var i,s,o,a=10*parseInt(t.substring(o,o+1),10)+parseInt(t.substring(o+1,o+2),10),l=t.substring(o+3,o+25),u=function(e){for(var t,r,n=0,i=Array(c(e)),s=0,o=e.length;s<o;++s)(t=e.charCodeAt(s))<128?i[n++]=t:(t<2048?i[n++]=t>>6|192:((64512&t)==55296&&(64512&(r=e.charCodeAt(s+1)))==56320?(t=65536+((1023&t)<<10)+(1023&r),++s,i[n++]=t>>18|240,i[n++]=t>>12&63|128):i[n++]=t>>12|224,i[n++]=t>>6&63|128),i[n++]=63&t|128);return i}(e+=s>="a"?"\0":""),p=E(l,d);function h(e){var t=[];return t.push("$2"),s>="a"&&t.push(s),t.push("$"),a<10&&t.push("0"),t.push(a.toString()),t.push("$"),t.push(m(p,p.length)),t.push(m(e,4*b.length-1)),t.join("")}if(void 0===r)return h(w(u,p,a));w(u,p,a,function(e,t){e?r(e,null):r(null,h(t))},n)}let A={setRandomFallback:function(e){i=e},genSaltSync:s,genSalt:o,hashSync:a,hash:l,compareSync:function(e,t){if("string"!=typeof e||"string"!=typeof t)throw Error("Illegal arguments: "+typeof e+", "+typeof t);return 60===t.length&&u(a(e,t.substring(0,t.length-31)),t)},compare:function(e,t,r,n){function i(r){if("string"!=typeof e||"string"!=typeof t){f(r.bind(this,Error("Illegal arguments: "+typeof e+", "+typeof t)));return}if(60!==t.length){f(r.bind(this,null,!1));return}l(e,t.substring(0,29),function(e,n){e?r(e):r(null,u(n,t))},n)}if(!r)return new Promise(function(e,t){i(function(r,n){if(r){t(r);return}e(n)})});if("function"!=typeof r)throw Error("Illegal callback: "+typeof r);i(r)},getRounds:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);return parseInt(e.split("$")[2],10)},getSalt:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);if(60!==e.length)throw Error("Illegal hash length: "+e.length+" != 60");return e.substring(0,29)},truncates:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);return c(e)>72},encodeBase64:function(e,t){return m(e,t)},decodeBase64:function(e,t){return E(e,t)}}}};