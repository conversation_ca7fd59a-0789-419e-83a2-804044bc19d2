'use client'

import { useState } from 'react'
import DashboardLayout from '@/components/DashboardLayout'
import { Card } from '@/components/Card'
import {
  Shield,
  Users,
  Activity,
  Database,
  Server,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Eye,
  UserCheck,
  Ban,
  Unlock,
  RefreshCw,
  Download,
  Search,
  Filter,
  MoreVertical,
  Crown
} from 'lucide-react'

export default function AdminPage() {
  const [user] = useState({
    username: 'admin',
    avatar: '',
    role: 'super_admin',
    plan: 'cybersecurity'
  })

  const [activeTab, setActiveTab] = useState('overview')

  // Mock data
  const stats = {
    totalUsers: 15847,
    activeUsers: 12456,
    premiumUsers: 3421,
    todayScans: 1234,
    systemHealth: 98.5,
    serverLoad: 45.2,
    storageUsed: 67.8
  }

  const tabs = [
    { id: 'overview', name: 'Overview', icon: Activity },
    { id: 'users', name: 'User Management', icon: Users },
    { id: 'system', name: 'System Health', icon: Server },
    { id: 'logs', name: 'System Logs', icon: Database }
  ]

  return (
    <DashboardLayout user={user} title="Admin Panel">
      <div>
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <Shield className="h-8 w-8 text-cyber-green" />
            <h1 className="text-3xl font-bold font-cyber text-white">
              <span className="cyber-text">Admin Panel</span>
            </h1>
          </div>
          <p className="text-gray-400 max-w-3xl">
            Kelola pengguna, monitor sistem, dan administrasi platform KodeXGuard.
          </p>
        </div>

        {/* Tab Navigation */}
        <div className="mb-8">
          <div className="flex space-x-1 bg-gray-800/50 p-1 rounded-lg">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                    activeTab === tab.id
                      ? 'bg-cyber-green text-black'
                      : 'text-gray-300 hover:text-white hover:bg-gray-700/50'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.name}</span>
                </button>
              )
            })}
          </div>
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card border="blue" glow>
                <div className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">Total Users</p>
                      <p className="text-2xl font-bold text-white">15,847</p>
                    </div>
                    <Users className="h-8 w-8 text-blue-400" />
                  </div>
                </div>
              </Card>

              <Card border="green" glow>
                <div className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">Active Users</p>
                      <p className="text-2xl font-bold text-white">12,456</p>
                    </div>
                    <UserCheck className="h-8 w-8 text-green-400" />
                  </div>
                </div>
              </Card>

              <Card border="gold" glow>
                <div className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">Premium Users</p>
                      <p className="text-2xl font-bold text-white">3,421</p>
                    </div>
                    <Crown className="h-8 w-8 text-nusantara-gold" />
                  </div>
                </div>
              </Card>

              <Card border="purple" glow>
                <div className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">Today's Scans</p>
                      <p className="text-2xl font-bold text-white">1,234</p>
                    </div>
                    <Activity className="h-8 w-8 text-purple-400" />
                  </div>
                </div>
              </Card>
            </div>

            {/* System Health */}
            <div className="grid lg:grid-cols-3 gap-6">
              <Card border="green" glow>
                <div className="p-6">
                  <h3 className="text-lg font-bold text-white mb-4">System Health</h3>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-gray-400">Overall Health</span>
                        <span className="text-cyber-green">{stats.systemHealth}%</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div className="bg-cyber-green h-2 rounded-full w-[98%]"></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-gray-400">Server Load</span>
                        <span className="text-yellow-400">{stats.serverLoad}%</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div className="bg-yellow-400 h-2 rounded-full w-[45%]"></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-gray-400">Storage Used</span>
                        <span className="text-orange-400">{stats.storageUsed}%</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div className="bg-orange-400 h-2 rounded-full w-[68%]"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>

              <Card border="blue" glow className="lg:col-span-2">
                <div className="p-6">
                  <h3 className="text-lg font-bold text-white mb-4">Recent Activity</h3>
                  <div className="space-y-3">
                    {systemLogs.slice(0, 5).map((log) => (
                      <div key={log.id} className="flex items-center space-x-3 p-3 bg-gray-800/50 rounded-lg">
                        {log.type === 'success' && <CheckCircle className="h-5 w-5 text-green-400" />}
                        {log.type === 'info' && <Activity className="h-5 w-5 text-blue-400" />}
                        {log.type === 'warning' && <AlertTriangle className="h-5 w-5 text-yellow-400" />}
                        {log.type === 'error' && <XCircle className="h-5 w-5 text-red-400" />}
                        <div className="flex-1">
                          <p className="text-white text-sm">{log.message}</p>
                          <p className="text-gray-400 text-xs">{log.timestamp}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </Card>
            </div>
          </div>
        )}

        {/* Users Tab */}
        {activeTab === 'users' && (
          <div className="space-y-6">
            {/* User Management Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search users..."
                    className="pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyber-green"
                  />
                </div>
                <button className="flex items-center space-x-2 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-gray-300 hover:text-white transition-colors">
                  <Filter className="h-4 w-4" />
                  <span>Filter</span>
                </button>
              </div>
              
              {selectedUsers.length > 0 && (
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-400">{selectedUsers.length} selected</span>
                  <button
                    onClick={() => handleBulkAction('activate')}
                    className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 transition-colors"
                  >
                    Activate
                  </button>
                  <button
                    onClick={() => handleBulkAction('suspend')}
                    className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors"
                  >
                    Suspend
                  </button>
                </div>
              )}
            </div>

            {/* Users Table */}
            <Card>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-700">
                      <th className="text-left p-4">
                        <input
                          type="checkbox"
                          className="rounded border-gray-600 text-cyber-green focus:ring-cyber-green"
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedUsers(recentUsers.map(u => u.id))
                            } else {
                              setSelectedUsers([])
                            }
                          }}
                        />
                      </th>
                      <th className="text-left p-4 text-gray-300 font-medium">User</th>
                      <th className="text-left p-4 text-gray-300 font-medium">Plan</th>
                      <th className="text-left p-4 text-gray-300 font-medium">Status</th>
                      <th className="text-left p-4 text-gray-300 font-medium">Last Active</th>
                      <th className="text-left p-4 text-gray-300 font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {recentUsers.map((user) => (
                      <tr key={user.id} className="border-b border-gray-800 hover:bg-gray-800/50">
                        <td className="p-4">
                          <input
                            type="checkbox"
                            checked={selectedUsers.includes(user.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedUsers([...selectedUsers, user.id])
                              } else {
                                setSelectedUsers(selectedUsers.filter(id => id !== user.id))
                              }
                            }}
                            className="rounded border-gray-600 text-cyber-green focus:ring-cyber-green"
                          />
                        </td>
                        <td className="p-4">
                          <div>
                            <div className="font-medium text-white">{user.username}</div>
                            <div className="text-sm text-gray-400">{user.email}</div>
                          </div>
                        </td>
                        <td className="p-4">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            user.plan === 'cybersecurity' ? 'bg-cyber-green/20 text-cyber-green' :
                            user.plan === 'bughunter' ? 'bg-yellow-500/20 text-yellow-400' :
                            user.plan === 'hobby' ? 'bg-blue-500/20 text-blue-400' :
                            'bg-gray-500/20 text-gray-400'
                          }`}>
                            {user.plan.toUpperCase()}
                          </span>
                        </td>
                        <td className="p-4">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            user.status === 'active' ? 'bg-green-500/20 text-green-400' :
                            user.status === 'suspended' ? 'bg-red-500/20 text-red-400' :
                            'bg-yellow-500/20 text-yellow-400'
                          }`}>
                            {user.status.toUpperCase()}
                          </span>
                        </td>
                        <td className="p-4 text-gray-400 text-sm">{user.lastActive}</td>
                        <td className="p-4">
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => handleUserAction('view', user.id)}
                              className="p-1 text-gray-400 hover:text-cyber-green transition-colors"
                              title="View User"
                            >
                              <Eye className="h-4 w-4" />
                            </button>
                            {user.status === 'active' ? (
                              <button
                                onClick={() => handleUserAction('suspend', user.id)}
                                className="p-1 text-gray-400 hover:text-red-400 transition-colors"
                                title="Suspend User"
                              >
                                <Ban className="h-4 w-4" />
                              </button>
                            ) : (
                              <button
                                onClick={() => handleUserAction('activate', user.id)}
                                className="p-1 text-gray-400 hover:text-green-400 transition-colors"
                                title="Activate User"
                              >
                                <Unlock className="h-4 w-4" />
                              </button>
                            )}
                            <button
                              className="p-1 text-gray-400 hover:text-white transition-colors"
                              title="More Actions"
                            >
                              <MoreVertical className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </Card>
          </div>
        )}

        {/* System Health Tab */}
        {activeTab === 'system' && (
          <div className="space-y-6">
            <AlertCard type="info" title="System Status" message="All systems are operational. Last check: 2 minutes ago." />
            
            <div className="grid lg:grid-cols-2 gap-6">
              <Card border="green" glow>
                <div className="p-6">
                  <h3 className="text-lg font-bold text-white mb-4">Server Metrics</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">CPU Usage</span>
                      <span className="text-cyber-green">45.2%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">Memory Usage</span>
                      <span className="text-yellow-400">67.8%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">Disk Usage</span>
                      <span className="text-orange-400">78.5%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">Network I/O</span>
                      <span className="text-blue-400">23.1 MB/s</span>
                    </div>
                  </div>
                </div>
              </Card>

              <Card border="blue" glow>
                <div className="p-6">
                  <h3 className="text-lg font-bold text-white mb-4">Database Status</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">Connection Pool</span>
                      <span className="text-green-400">Healthy</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">Query Performance</span>
                      <span className="text-cyber-green">Optimal</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">Backup Status</span>
                      <span className="text-green-400">Up to date</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">Replication Lag</span>
                      <span className="text-blue-400">< 1ms</span>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        )}

        {/* System Logs Tab */}
        {activeTab === 'logs' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-bold text-white">System Logs</h3>
              <div className="flex items-center space-x-2">
                <button className="cyber-btn flex items-center space-x-2">
                  <RefreshCw className="h-4 w-4" />
                  <span>Refresh</span>
                </button>
                <button className="cyber-btn flex items-center space-x-2">
                  <Download className="h-4 w-4" />
                  <span>Export</span>
                </button>
              </div>
            </div>

            <Card>
              <div className="p-6">
                <div className="space-y-3">
                  {systemLogs.map((log) => (
                    <div key={log.id} className="flex items-start space-x-3 p-3 bg-gray-800/30 rounded-lg">
                      <div className="flex-shrink-0 mt-1">
                        {log.type === 'success' && <CheckCircle className="h-5 w-5 text-green-400" />}
                        {log.type === 'info' && <Activity className="h-5 w-5 text-blue-400" />}
                        {log.type === 'warning' && <AlertTriangle className="h-5 w-5 text-yellow-400" />}
                        {log.type === 'error' && <XCircle className="h-5 w-5 text-red-400" />}
                      </div>
                      <div className="flex-1">
                        <p className="text-white text-sm">{log.message}</p>
                        <div className="flex items-center space-x-4 mt-1">
                          <span className="text-gray-400 text-xs">{log.timestamp}</span>
                          <span className="text-gray-500 text-xs">User: {log.user}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </Card>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
