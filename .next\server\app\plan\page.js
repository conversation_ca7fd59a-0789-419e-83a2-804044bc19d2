(()=>{var e={};e.id=927,e.ids=[927],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},95614:(e,r,n)=>{"use strict";n.r(r),n.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>p,routeModule:()=>m,tree:()=>l}),n(10259),n(30829),n(35866);var t=n(23191),a=n(88716),s=n(37922),o=n.n(s),d=n(95231),i={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>d[e]);n.d(r,i);let l=["",{children:["plan",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.t.bind(n,10259,23)),"D:\\Users\\Downloads\\kodeXGuard\\app\\plan\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,35866,23)),"next/dist/client/components/not-found-error"]}],p=["D:\\Users\\Downloads\\kodeXGuard\\app\\plan\\page.tsx"],x="/plan/page",u={require:n,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/plan/page",pathname:"/plan",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},10259:()=>{throw Error('Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError: \n  \x1b[38;2;255;30;30m\xd7\x1b[0m Unexpected token `DashboardLayout`. Expected jsx identifier\n     ╭─[\x1b[38;2;92;157;255;1;4mD:\\Users\\Downloads\\kodeXGuard\\app\\plan\\page.tsx\x1b[0m:269:1]\n \x1b[2m269\x1b[0m │   }\n \x1b[2m270\x1b[0m │ \n \x1b[2m271\x1b[0m │   return (\n \x1b[2m272\x1b[0m │     <DashboardLayout user={user} title="Plans & Billing">\n     \xb7 \x1b[38;2;246;87;248m     ───────────────\x1b[0m\n \x1b[2m273\x1b[0m │       <div>\n \x1b[2m274\x1b[0m │         {/* Header */}\n \x1b[2m275\x1b[0m │         <div className="mb-8 text-center">\n     ╰────\n\n\nCaused by:\n    Syntax Error')}};var r=require("../../webpack-runtime.js");r.C(e);var n=e=>r(r.s=e),t=r.X(0,[216,592],()=>n(95614));module.exports=t})();