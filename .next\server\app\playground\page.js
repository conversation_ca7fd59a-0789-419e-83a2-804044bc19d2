(()=>{var e={};e.id=383,e.ids=[383],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},23374:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>p,tree:()=>c}),t(96612),t(30829),t(35866);var a=t(23191),r=t(88716),i=t(37922),l=t.n(i),n=t(95231),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c=["",{children:["playground",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,96612)),"D:\\Users\\Downloads\\kodeXGuard\\app\\playground\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\Users\\Downloads\\kodeXGuard\\app\\playground\\page.tsx"],m="/playground/page",x={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/playground/page",pathname:"/playground",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},99038:(e,s,t)=>{Promise.resolve().then(t.bind(t,32708))},32708:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var a=t(10326),r=t(17577),i=t(60463),l=t(2262),n=t(92498),d=t(54014),c=t(88319),o=t(31215),m=t(3634),x=t(94893),p=t(6343),u=t(21405),h=t(43810),g=t(76828),y=t(33734),b=t(70003),j=t(98091);function v(){let[e]=(0,r.useState)({username:"admin",avatar:"",role:"super_admin",plan:"cybersecurity"}),[s,t]=(0,r.useState)(null),[v,N]=(0,r.useState)("GET"),[f,w]=(0,r.useState)(""),[q,S]=(0,r.useState)('{\n  "Authorization": "Bearer your-api-key",\n  "Content-Type": "application/json"\n}'),[P,T]=(0,r.useState)("{}"),[k,A]=(0,r.useState)(""),[E,I]=(0,r.useState)(!1),[C,D]=(0,r.useState)("playground"),[Z,O]=(0,r.useState)([]),R=[{id:"1",method:"POST",path:"/api/auth/login",description:"Authenticate user and get access token",category:"Authentication",requiresAuth:!1,parameters:[{name:"email",type:"string",required:!0,description:"User email address"},{name:"password",type:"string",required:!0,description:"User password"}],example:{request:{email:"<EMAIL>",password:"admin123"},response:{token:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",user:{id:"1",username:"admin",role:"super_admin"}}}},{id:"2",method:"GET",path:"/api/user/profile",description:"Get current user profile information",category:"User",requiresAuth:!0,parameters:[],example:{request:{},response:{id:"1",username:"admin",email:"<EMAIL>",plan:"cybersecurity",stats:{totalScans:1247}}}},{id:"3",method:"POST",path:"/api/scan/vulnerability",description:"Start a new vulnerability scan",category:"Scanning",requiresAuth:!0,parameters:[{name:"target",type:"string",required:!0,description:"Target URL to scan"},{name:"scanTypes",type:"array",required:!0,description:"Types of vulnerabilities to scan for"},{name:"maxDepth",type:"number",required:!1,description:"Maximum crawl depth (default: 3)"}],example:{request:{target:"https://example.com",scanTypes:["sqli","xss","lfi"],maxDepth:3},response:{scanId:"scan_123456",status:"started",estimatedTime:"5-10 minutes"}}},{id:"4",method:"GET",path:"/api/scan/{scanId}/results",description:"Get vulnerability scan results",category:"Scanning",requiresAuth:!0,parameters:[{name:"scanId",type:"string",required:!0,description:"Scan ID from scan initiation"}],example:{request:{},response:{scanId:"scan_123456",status:"completed",vulnerabilities:[{type:"sqli",severity:"high",url:"https://example.com/login"}]}}},{id:"5",method:"POST",path:"/api/osint/search",description:"Perform OSINT investigation",category:"OSINT",requiresAuth:!0,parameters:[{name:"query",type:"string",required:!0,description:"Search query (email, phone, domain, etc.)"},{name:"type",type:"string",required:!0,description:"Search type (email, phone, nik, domain, etc.)"},{name:"deepSearch",type:"boolean",required:!1,description:"Enable deep search (default: false)"}],example:{request:{query:"<EMAIL>",type:"email",deepSearch:!0},response:{results:[{source:"Dukcapil",data:{name:"John Doe",verified:!0},confidence:95}]}}},{id:"6",method:"POST",path:"/api/file/analyze",description:"Analyze uploaded file for threats",category:"File Analysis",requiresAuth:!0,parameters:[{name:"file",type:"file",required:!0,description:"File to analyze (multipart/form-data)"},{name:"analysisType",type:"string",required:!1,description:"Type of analysis (malware, webshell, secret, general)"}],example:{request:{file:"suspicious.php",analysisType:"webshell"},response:{analysisId:"analysis_789",threatLevel:"suspicious",results:{webshellDetected:!0,type:"PHP Webshell"}}}},{id:"7",method:"GET",path:"/api/cve/search",description:"Search CVE database",category:"CVE Intelligence",requiresAuth:!0,parameters:[{name:"query",type:"string",required:!1,description:"Search query"},{name:"severity",type:"string",required:!1,description:"Filter by severity (critical, high, medium, low)"},{name:"year",type:"string",required:!1,description:"Filter by year"},{name:"limit",type:"number",required:!1,description:"Number of results (default: 20)"}],example:{request:{query:"SQL injection",severity:"critical",limit:10},response:{total:156,cves:[{cveId:"CVE-2024-0001",severity:"critical",cvssScore:9.8,description:"SQL injection vulnerability..."}]}}}],G=async()=>{I(!0),A("");try{await new Promise(e=>setTimeout(e,1500));let e={status:200,statusText:"OK",headers:{"Content-Type":"application/json","X-RateLimit-Remaining":"99","X-Response-Time":"142ms"},data:s?.example.response||{message:"Success",timestamp:new Date().toISOString()}};A(JSON.stringify(e,null,2))}catch(e){A(JSON.stringify({status:500,error:"Internal Server Error",message:"An error occurred while processing your request"},null,2))}finally{I(!1)}},_=e=>{N(e.method),w(e.url),S(JSON.stringify(e.headers,null,2)),T(e.body),D("playground")},U=e=>{O(s=>s.map(s=>s.id===e?{...s,favorite:!s.favorite}:s))},J=e=>{O(s=>s.filter(s=>s.id!==e))},L=e=>{navigator.clipboard.writeText(e)},X=[...new Set(R.map(e=>e.category))];return a.jsx(i.Z,{user:e,title:"API Playground",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[a.jsx(n.Z,{className:"h-8 w-8 text-cyber-green"}),(0,a.jsxs)("h1",{className:"text-3xl font-bold font-cyber text-white",children:["API ",a.jsx("span",{className:"cyber-text",children:"Playground"})]})]}),a.jsx("p",{className:"text-gray-400 max-w-3xl",children:"Interactive API testing environment dengan Swagger documentation, request builder, dan endpoint management. Test semua endpoint KodeXGuard dengan real-time response."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[a.jsx(l.Rm,{title:"API Endpoints",value:R.length,icon:d.Z,color:"green"}),a.jsx(l.Rm,{title:"Categories",value:X.length,icon:c.Z,color:"blue"}),a.jsx(l.Rm,{title:"Saved Requests",value:Z.length,icon:o.Z,color:"purple"}),a.jsx(l.Rm,{title:"API Calls Today",value:"1,247",icon:m.Z,color:"gold"})]}),a.jsx("div",{className:"mb-8",children:a.jsx("div",{className:"flex space-x-1 bg-gray-800/50 p-1 rounded-lg",children:[{id:"playground",name:"API Playground",icon:x.Z},{id:"docs",name:"Documentation",icon:p.Z},{id:"saved",name:"Saved Requests",icon:o.Z}].map(e=>{let s=e.icon;return(0,a.jsxs)("button",{onClick:()=>D(e.id),className:`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${C===e.id?"bg-cyber-green text-black":"text-gray-300 hover:text-white hover:bg-gray-700/50"}`,children:[a.jsx(s,{className:"h-4 w-4"}),a.jsx("span",{children:e.name})]},e.id)})})}),"playground"===C&&(0,a.jsxs)("div",{className:"grid lg:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-3",children:[a.jsx(l.Zb,{border:"green",glow:!0,children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h2",{className:"text-xl font-bold text-white mb-6",children:"Request Builder"}),(0,a.jsxs)("div",{className:"grid md:grid-cols-4 gap-4 mb-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Method"}),(0,a.jsxs)("select",{value:v,onChange:e=>N(e.target.value),className:"cyber-input w-full",children:[a.jsx("option",{value:"GET",children:"GET"}),a.jsx("option",{value:"POST",children:"POST"}),a.jsx("option",{value:"PUT",children:"PUT"}),a.jsx("option",{value:"DELETE",children:"DELETE"})]})]}),(0,a.jsxs)("div",{className:"md:col-span-3",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"URL"}),a.jsx("input",{type:"text",value:f,onChange:e=>w(e.target.value),placeholder:"https://api.kodexguard.com/v1/endpoint",className:"cyber-input w-full"})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Headers"}),a.jsx("textarea",{value:q,onChange:e=>S(e.target.value),className:"cyber-input w-full h-24 font-mono text-sm",placeholder:'{"Authorization": "Bearer your-api-key"}'})]}),("POST"===v||"PUT"===v)&&(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Request Body"}),a.jsx("textarea",{value:P,onChange:e=>T(e.target.value),className:"cyber-input w-full h-32 font-mono text-sm",placeholder:'{"key": "value"}'})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[a.jsx("button",{onClick:G,disabled:E||!f,className:"cyber-btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:E?(0,a.jsxs)(a.Fragment,{children:[a.jsx(u.Z,{className:"h-4 w-4 mr-2 animate-spin"}),"Executing..."]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(x.Z,{className:"h-4 w-4 mr-2"}),"Send Request"]})}),(0,a.jsxs)("button",{onClick:()=>{let e={id:Date.now().toString(),name:`${v} ${f}`,method:v,url:f,headers:JSON.parse(q),body:P,createdAt:new Date().toISOString(),favorite:!1};O(s=>[...s,e])},disabled:!f,className:"cyber-btn disabled:opacity-50 disabled:cursor-not-allowed",children:[a.jsx(o.Z,{className:"h-4 w-4 mr-2"}),"Save"]}),(0,a.jsxs)("button",{onClick:()=>L(f),className:"cyber-btn",children:[a.jsx(h.Z,{className:"h-4 w-4 mr-2"}),"Copy URL"]})]})]})}),k&&a.jsx(l.Zb,{className:"mt-6",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[a.jsx("h3",{className:"text-lg font-bold text-white",children:"Response"}),(0,a.jsxs)("button",{onClick:()=>L(k),className:"cyber-btn text-sm",children:[a.jsx(h.Z,{className:"h-4 w-4 mr-2"}),"Copy"]})]}),a.jsx("div",{className:"bg-gray-900/50 rounded-lg p-4",children:a.jsx("pre",{className:"text-sm text-cyber-green whitespace-pre-wrap overflow-x-auto",children:k})})]})})]}),a.jsx("div",{children:a.jsx(l.Zb,{children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"API Endpoints"}),a.jsx("div",{className:"space-y-2",children:X.map(e=>(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"text-sm font-semibold text-gray-400 mb-2",children:e}),R.filter(s=>s.category===e).map(e=>(0,a.jsxs)("button",{onClick:()=>{t(e),N(e.method),w(`https://api.kodexguard.com/v1${e.path}`),T(JSON.stringify(e.example.request,null,2))},className:`w-full text-left p-3 rounded-lg border transition-all duration-200 ${s?.id===e.id?"border-cyber-green bg-cyber-green/10":"border-gray-700 hover:border-gray-600"}`,children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[a.jsx("span",{className:`px-2 py-1 rounded text-xs font-semibold ${"GET"===e.method?"bg-blue-900/50 text-blue-400":"POST"===e.method?"bg-green-900/50 text-green-400":"PUT"===e.method?"bg-yellow-900/50 text-yellow-400":"bg-red-900/50 text-red-400"}`,children:e.method}),e.requiresAuth&&a.jsx(g.Z,{className:"h-3 w-3 text-gray-400"})]}),a.jsx("div",{className:"text-sm font-medium text-white",children:e.path}),a.jsx("div",{className:"text-xs text-gray-400 mt-1",children:e.description})]},e.id))]},e))})]})})})]}),"docs"===C&&s&&a.jsx(l.Zb,{children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[a.jsx("span",{className:`px-3 py-1 rounded font-semibold ${"GET"===s.method?"bg-blue-900/50 text-blue-400":"POST"===s.method?"bg-green-900/50 text-green-400":"PUT"===s.method?"bg-yellow-900/50 text-yellow-400":"bg-red-900/50 text-red-400"}`,children:s.method}),a.jsx("h2",{className:"text-xl font-bold text-white",children:s.path}),s.requiresAuth&&a.jsx("span",{className:"px-2 py-1 bg-orange-900/50 text-orange-400 rounded text-sm",children:"\uD83D\uDD12 Auth Required"})]}),a.jsx("p",{className:"text-gray-300 mb-6",children:s.description}),s.parameters.length>0&&(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Parameters"}),a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full text-sm",children:[a.jsx("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-gray-700",children:[a.jsx("th",{className:"text-left py-2 text-gray-400",children:"Name"}),a.jsx("th",{className:"text-left py-2 text-gray-400",children:"Type"}),a.jsx("th",{className:"text-left py-2 text-gray-400",children:"Required"}),a.jsx("th",{className:"text-left py-2 text-gray-400",children:"Description"})]})}),a.jsx("tbody",{children:s.parameters.map((e,s)=>(0,a.jsxs)("tr",{className:"border-b border-gray-800",children:[a.jsx("td",{className:"py-2 text-white font-mono",children:e.name}),a.jsx("td",{className:"py-2 text-cyber-green",children:e.type}),a.jsx("td",{className:"py-2",children:e.required?a.jsx("span",{className:"text-red-400",children:"Yes"}):a.jsx("span",{className:"text-gray-400",children:"No"})}),a.jsx("td",{className:"py-2 text-gray-300",children:e.description})]},s))})]})})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Example Request"}),a.jsx("div",{className:"bg-gray-900/50 rounded-lg p-4",children:a.jsx("pre",{className:"text-sm text-gray-300 whitespace-pre-wrap",children:JSON.stringify(s.example.request,null,2)})})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Example Response"}),a.jsx("div",{className:"bg-gray-900/50 rounded-lg p-4",children:a.jsx("pre",{className:"text-sm text-cyber-green whitespace-pre-wrap",children:JSON.stringify(s.example.response,null,2)})})]})]})]})}),"saved"===C&&a.jsx(l.Zb,{children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h2",{className:"text-xl font-bold text-white mb-6",children:"Saved Requests"}),0===Z.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[a.jsx(o.Z,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"No Saved Requests"}),a.jsx("p",{className:"text-gray-400",children:"Save your API requests to access them later"})]}):a.jsx("div",{className:"space-y-4",children:Z.map(e=>(0,a.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-4 border border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("span",{className:`px-2 py-1 rounded text-xs font-semibold ${"GET"===e.method?"bg-blue-900/50 text-blue-400":"POST"===e.method?"bg-green-900/50 text-green-400":"PUT"===e.method?"bg-yellow-900/50 text-yellow-400":"bg-red-900/50 text-red-400"}`,children:e.method}),a.jsx("h3",{className:"font-semibold text-white",children:e.name}),e.favorite&&a.jsx(y.Z,{className:"h-4 w-4 text-yellow-400 fill-current"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx("button",{onClick:()=>U(e.id),className:`text-gray-400 hover:text-yellow-400 transition-colors ${e.favorite?"text-yellow-400":""}`,children:a.jsx(y.Z,{className:"h-4 w-4"})}),a.jsx("button",{onClick:()=>_(e),className:"text-gray-400 hover:text-cyber-green transition-colors",children:a.jsx(b.Z,{className:"h-4 w-4"})}),a.jsx("button",{onClick:()=>J(e.id),className:"text-gray-400 hover:text-red-400 transition-colors",children:a.jsx(j.Z,{className:"h-4 w-4"})})]})]}),a.jsx("div",{className:"text-sm text-gray-300 mb-2",children:e.url}),(0,a.jsxs)("div",{className:"text-xs text-gray-400",children:["Saved: ",new Date(e.createdAt).toLocaleString()]})]},e.id))})]})})]})})}},96612:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\playground\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[216,592],()=>t(23374));module.exports=a})();