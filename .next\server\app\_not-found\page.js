(()=>{var e={};e.id=7409,e.ids=[7409],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},69853:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>o.a,__next_app__:()=>f,originalPathname:()=>c,pages:()=>u,routeModule:()=>h,tree:()=>d}),n(7352),n(35866),n(30829);var r=n(23191),a=n(88716),i=n(37922),o=n.n(i),s=n(95231),l={};for(let e in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);n.d(t,l);let d=["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.t.bind(n,35866,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,35866,23)),"next/dist/client/components/not-found-error"]}],u=[],c="/_not-found/page",f={require:n,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},60546:(e,t,n)=>{Promise.resolve().then(n.bind(n,67382))},27807:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,12994,23)),Promise.resolve().then(n.t.bind(n,96114,23)),Promise.resolve().then(n.t.bind(n,9727,23)),Promise.resolve().then(n.t.bind(n,79671,23)),Promise.resolve().then(n.t.bind(n,41868,23)),Promise.resolve().then(n.t.bind(n,84759,23))},67382:(e,t,n)=>{"use strict";n.d(t,{AuthProvider:()=>o,a:()=>s});var r=n(10326),a=n(17577);let i=(0,a.createContext)(void 0);function o({children:e}){let[t,n]=(0,a.useState)(null),[o,s]=(0,a.useState)(null),[l,d]=(0,a.useState)(!0),u=async(e,t)=>{try{d(!0);let r=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})}),a=await r.json();if(a.success&&a.token)return s(a.token),n(a.user),!0;return!1}catch(e){return console.error("Login error:",e),!1}finally{d(!1)}},c=async()=>{try{let e=await fetch("/api/auth/refresh",{method:"POST",headers:{Authorization:`Bearer ${o}`}}),t=await e.json();t.success&&t.token&&s(t.token)}catch(e){console.error("Token refresh error:",e),f()}},f=()=>{n(null),s(null)};return r.jsx(i.Provider,{value:{user:t,token:o,login:u,logout:f,isLoading:l,isAuthenticated:!!t&&!!o,refreshToken:c},children:e})}function s(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},30829:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>d,metadata:()=>l});var r=n(19510),a=n(77366),i=n.n(a);n(7633);var o=n(68570);let s=(0,o.createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\contexts\AuthContext.tsx#AuthProvider`);(0,o.createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\contexts\AuthContext.tsx#useAuth`);let l={title:"KodeXGuard - Platform Cybersecurity & Bug Hunting",description:"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting dengan dukungan Bot WhatsApp/Telegram",keywords:"cybersecurity, bug hunting, OSINT, vulnerability scanner, CVE, penetration testing, security tools",authors:[{name:"KodeXGuard Team"}],creator:"KodeXGuard",publisher:"KodeXGuard",robots:"index, follow",openGraph:{type:"website",locale:"id_ID",url:"https://kodexguard.com",title:"KodeXGuard - Platform Cybersecurity & Bug Hunting",description:"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting",siteName:"KodeXGuard"},twitter:{card:"summary_large_image",title:"KodeXGuard - Platform Cybersecurity & Bug Hunting",description:"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting"},viewport:"width=device-width, initial-scale=1",themeColor:"#00ff41"};function d({children:e}){return(0,r.jsxs)("html",{lang:"id",className:"dark",children:[(0,r.jsxs)("head",{children:[r.jsx("link",{rel:"icon",href:"/favicon.ico"}),r.jsx("link",{rel:"apple-touch-icon",href:"/apple-touch-icon.png"}),r.jsx("link",{rel:"manifest",href:"/manifest.json"})]}),(0,r.jsxs)("body",{className:`${i().className} min-h-screen bg-gradient-cyber text-white antialiased`,children:[r.jsx("div",{className:"matrix-bg",children:r.jsx("div",{className:"absolute inset-0 bg-gradient-to-b from-transparent via-cyber-dark/20 to-cyber-dark"})}),r.jsx("div",{className:"relative z-10",children:r.jsx(s,{children:e})}),r.jsx("script",{dangerouslySetInnerHTML:{__html:`
              // Matrix Rain Effect
              function createMatrixRain() {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.style.position = 'fixed';
                canvas.style.top = '0';
                canvas.style.left = '0';
                canvas.style.width = '100%';
                canvas.style.height = '100%';
                canvas.style.pointerEvents = 'none';
                canvas.style.zIndex = '-1';
                canvas.style.opacity = '0.1';
                document.body.appendChild(canvas);
                
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
                
                const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';
                const charArray = chars.split('');
                const fontSize = 14;
                const columns = canvas.width / fontSize;
                const drops = [];
                
                for (let i = 0; i < columns; i++) {
                  drops[i] = 1;
                }
                
                function draw() {
                  ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
                  ctx.fillRect(0, 0, canvas.width, canvas.height);
                  
                  ctx.fillStyle = '#00ff41';
                  ctx.font = fontSize + 'px monospace';
                  
                  for (let i = 0; i < drops.length; i++) {
                    const text = charArray[Math.floor(Math.random() * charArray.length)];
                    ctx.fillText(text, i * fontSize, drops[i] * fontSize);
                    
                    if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
                      drops[i] = 0;
                    }
                    drops[i]++;
                  }
                }
                
                setInterval(draw, 50);
                
                window.addEventListener('resize', () => {
                  canvas.width = window.innerWidth;
                  canvas.height = window.innerHeight;
                });
              }
              
              // Initialize matrix effect after page load
              if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', createMatrixRain);
              } else {
                createMatrixRain();
              }
            `}})]})]})}},16399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{isNotFoundError:function(){return a},notFound:function(){return r}});let n="NEXT_NOT_FOUND";function r(){let e=Error(n);throw e.digest=n,e}function a(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===n}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7352:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{PARALLEL_ROUTE_DEFAULT_PATH:function(){return a},default:function(){return i}});let r=n(16399),a="next/dist/client/components/parallel-route-default.js";function i(){(0,r.notFound)()}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7633:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[9276,82],()=>n(69853));module.exports=r})();