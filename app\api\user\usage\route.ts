import { NextRequest, NextResponse } from 'next/server'

interface UsageData {
  current: {
    scans: {
      total: number
      limit: number | null
      percentage: number
      resetDate: string
    }
    osintSearches: {
      total: number
      limit: number | null
      percentage: number
      resetDate: string
    }
    apiCalls: {
      total: number
      limit: number | null
      percentage: number
      resetDate: string
    }
    storage: {
      used: number // in GB
      limit: number
      percentage: number
    }
  }
  history: {
    daily: Array<{
      date: string
      scans: number
      osintSearches: number
      apiCalls: number
    }>
    weekly: Array<{
      week: string
      scans: number
      osintSearches: number
      apiCalls: number
    }>
    monthly: Array<{
      month: string
      scans: number
      osintSearches: number
      apiCalls: number
      storageUsed: number
    }>
  }
  breakdown: {
    scanTypes: Array<{
      type: string
      count: number
      percentage: number
    }>
    osintTypes: Array<{
      type: string
      count: number
      percentage: number
    }>
    apiEndpoints: Array<{
      endpoint: string
      calls: number
      percentage: number
    }>
  }
  predictions: {
    monthlyProjection: {
      scans: number
      osintSearches: number
      apiCalls: number
    }
    quotaExhaustion: {
      scans?: string // date when quota will be exhausted
      osintSearches?: string
      apiCalls?: string
    }
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Simple token validation for testing
    if (!token.startsWith('kxg_')) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    // Get query parameters
    const url = new URL(request.url)
    const period = url.searchParams.get('period') || 'current'
    const granularity = url.searchParams.get('granularity') || 'daily'

    // Mock usage data
    const usageData: UsageData = {
      current: {
        scans: {
          total: 45,
          limit: null, // unlimited for cybersecurity plan
          percentage: 0, // unlimited
          resetDate: new Date(Date.now() + 86400000 * 5).toISOString()
        },
        osintSearches: {
          total: 123,
          limit: null, // unlimited
          percentage: 0, // unlimited
          resetDate: new Date(Date.now() + 86400000 * 5).toISOString()
        },
        apiCalls: {
          total: 28560,
          limit: 100000,
          percentage: 28.56,
          resetDate: new Date(Date.now() + 86400000 * 5).toISOString()
        },
        storage: {
          used: 156.7,
          limit: 1000,
          percentage: 15.67
        }
      },
      history: {
        daily: Array.from({ length: 30 }, (_, i) => ({
          date: new Date(Date.now() - 86400000 * (29 - i)).toISOString().split('T')[0],
          scans: Math.floor(Math.random() * 5) + 1,
          osintSearches: Math.floor(Math.random() * 10) + 2,
          apiCalls: Math.floor(Math.random() * 2000) + 500
        })),
        weekly: Array.from({ length: 12 }, (_, i) => ({
          week: `Week ${i + 1}`,
          scans: Math.floor(Math.random() * 20) + 5,
          osintSearches: Math.floor(Math.random() * 50) + 10,
          apiCalls: Math.floor(Math.random() * 10000) + 2000
        })),
        monthly: Array.from({ length: 6 }, (_, i) => ({
          month: new Date(Date.now() - 86400000 * 30 * (5 - i)).toISOString().slice(0, 7),
          scans: Math.floor(Math.random() * 80) + 20,
          osintSearches: Math.floor(Math.random() * 200) + 50,
          apiCalls: Math.floor(Math.random() * 40000) + 10000,
          storageUsed: Math.floor(Math.random() * 50) + 10
        }))
      },
      breakdown: {
        scanTypes: [
          { type: 'SQL Injection', count: 18, percentage: 40 },
          { type: 'XSS', count: 12, percentage: 26.7 },
          { type: 'CSRF', count: 8, percentage: 17.8 },
          { type: 'Directory Traversal', count: 4, percentage: 8.9 },
          { type: 'Others', count: 3, percentage: 6.7 }
        ],
        osintTypes: [
          { type: 'Email', count: 45, percentage: 36.6 },
          { type: 'Phone', count: 32, percentage: 26.0 },
          { type: 'Name', count: 28, percentage: 22.8 },
          { type: 'Domain', count: 12, percentage: 9.8 },
          { type: 'Others', count: 6, percentage: 4.9 }
        ],
        apiEndpoints: [
          { endpoint: '/api/scan/vulnerability', calls: 8520, percentage: 29.8 },
          { endpoint: '/api/osint/search', calls: 7340, percentage: 25.7 },
          { endpoint: '/api/notifications', calls: 4680, percentage: 16.4 },
          { endpoint: '/api/dashboard/stats', calls: 3920, percentage: 13.7 },
          { endpoint: 'Others', calls: 4100, percentage: 14.4 }
        ]
      },
      predictions: {
        monthlyProjection: {
          scans: 67,
          osintSearches: 184,
          apiCalls: 42840
        },
        quotaExhaustion: {
          // No exhaustion for unlimited features
          apiCalls: new Date(Date.now() + 86400000 * 45).toISOString() // 45 days
        }
      }
    }

    return NextResponse.json({
      success: true,
      data: usageData,
      meta: {
        period,
        granularity,
        generatedAt: new Date().toISOString()
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('User usage API error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}
