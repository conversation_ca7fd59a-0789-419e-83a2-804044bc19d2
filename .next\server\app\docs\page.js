(()=>{var e={};e.id=726,e.ids=[726],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},82220:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>r.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c}),a(6819),a(30829),a(35866);var s=a(23191),i=a(88716),n=a(37922),r=a.n(n),o=a(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);a.d(t,l);let c=["",{children:["docs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,6819)),"D:\\Users\\Downloads\\kodeXGuard\\app\\docs\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,35866,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\Users\\Downloads\\kodeXGuard\\app\\docs\\page.tsx"],u="/docs/page",p={require:a,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/docs/page",pathname:"/docs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9614:(e,t,a)=>{Promise.resolve().then(a.bind(a,45857))},45857:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>b});var s=a(10326),i=a(17577),n=a(60463),r=a(2262),o=a(94893),l=a(92498),c=a(88307),d=a(58038),u=a(36283),p=a(88319),m=a(50732),h=a(32130),x=a(43810),g=a(6343),y=a(39183);function b(){let[e]=(0,i.useState)({username:"admin",avatar:"",role:"super_admin",plan:"cybersecurity"}),[t,a]=(0,i.useState)("getting-started"),[b,S]=(0,i.useState)(""),f=[{id:"getting-started",title:"Getting Started",description:"Quick start guide untuk menggunakan KodeXGuard",icon:o.Z,content:["# Getting Started with KodeXGuard","","## Welcome to KodeXGuard","KodeXGuard adalah platform cybersecurity mandiri Indonesia yang menyediakan tools untuk OSINT investigation, vulnerability scanning, file analysis, dan bot automation.","","## Quick Start","1. **Register Account**: Daftar akun baru di `/register`","2. **Choose Plan**: Pilih plan yang sesuai kebutuhan","3. **Get API Key**: Generate API key di halaman profile","4. **Start Scanning**: Mulai gunakan tools yang tersedia","","## Demo Credentials","```","Email: <EMAIL>","Password: admin123","```","","## Available Tools","- **OSINT Investigator**: Pencarian NIK, NPWP, email, domain","- **Vulnerability Scanner**: Deteksi SQLi, XSS, LFI, RCE","- **File Analyzer**: Analisis malware, webshell, secrets","- **CVE Intelligence**: Database CVE terupdate","- **Security Tools**: Hash, encode, payload generator","- **Bot Center**: WhatsApp & Telegram automation"]},{id:"api-reference",title:"API Reference",description:"Dokumentasi lengkap API endpoints KodeXGuard",icon:l.Z,content:["# API Reference","","## Base URL","```","https://api.kodexguard.com/v1","```","","## Authentication","Semua API endpoint memerlukan authentication menggunakan Bearer token:","","```bash",'curl -H "Authorization: Bearer YOUR_API_KEY" \\',"     https://api.kodexguard.com/v1/endpoint","```","","## Rate Limiting","Rate limit berdasarkan plan:","- **Gratis**: 100 calls/day","- **Pelajar**: 1,000 calls/day","- **Hobby**: 5,000 calls/day","- **Bug Hunter**: 10,000 calls/day","- **Cybersecurity**: 100,000 calls/day","","## Response Format","Semua response dalam format JSON:","","```json","{",'  "success": true,','  "data": {...},','  "message": "Success",','  "timestamp": "2024-01-15T10:30:00Z"',"}","```","","## Error Handling","Error response format:","","```json","{",'  "success": false,','  "error": "Error message",','  "code": "ERROR_CODE",','  "timestamp": "2024-01-15T10:30:00Z"',"}","```"]},{id:"osint-api",title:"OSINT API",description:"API untuk OSINT investigation dan data gathering",icon:c.Z,content:["# OSINT API","","## Search Endpoint","**POST** `/api/osint/search`","","### Request Body","```json","{",'  "query": "<EMAIL>",','  "type": "email",','  "deepSearch": true,','  "sources": ["dukcapil", "kemkes", "github"]',"}","```","","### Parameters","- `query` (string, required): Search query","- `type` (string, required): Search type (email, phone, nik, npwp, domain, imei, name, address)","- `deepSearch` (boolean, optional): Enable deep search","- `sources` (array, optional): Specific data sources","","### Response","```json","{",'  "success": true,','  "data": {','    "results": [',"      {",'        "source": "Dukcapil Database",','        "data": {','          "name": "John Doe",','          "email": "<EMAIL>",','          "verified": true',"        },",'        "confidence": 95,','        "timestamp": "2024-01-15T10:30:00Z"',"      }","    ],",'    "totalResults": 1,','    "searchTime": "1.2s"',"  }","}","```","","## Track Location","**POST** `/api/osint/track`","","Track location berdasarkan nomor HP atau IMEI.","","### Example","```bash","curl -X POST https://api.kodexguard.com/v1/osint/track \\",'  -H "Authorization: Bearer YOUR_API_KEY" \\','  -H "Content-Type: application/json" \\','  -d \'{"target": "+62812345678", "type": "phone"}\'',"```"]},{id:"scanner-api",title:"Scanner API",description:"API untuk vulnerability scanning dan security testing",icon:d.Z,content:["# Vulnerability Scanner API","","## Start Scan","**POST** `/api/scan/vulnerability`","","### Request Body","```json","{",'  "target": "https://example.com",','  "scanTypes": ["sqli", "xss", "lfi", "rce"],','  "maxDepth": 3,','  "timeout": 30,','  "followRedirects": true',"}","```","","### Parameters","- `target` (string, required): Target URL to scan","- `scanTypes` (array, required): Vulnerability types to scan","- `maxDepth` (number, optional): Maximum crawl depth (default: 3)","- `timeout` (number, optional): Request timeout in seconds (default: 30)","- `followRedirects` (boolean, optional): Follow HTTP redirects (default: true)","","### Supported Scan Types","- `sqli`: SQL Injection","- `xss`: Cross-Site Scripting","- `lfi`: Local File Inclusion","- `rfi`: Remote File Inclusion","- `rce`: Remote Code Execution","- `csrf`: Cross-Site Request Forgery","- `ssrf`: Server-Side Request Forgery","- `xxe`: XML External Entity","- `idor`: Insecure Direct Object Reference","- `path_traversal`: Path Traversal","","### Response","```json","{",'  "success": true,','  "data": {','    "scanId": "scan_*********",','    "status": "started",','    "estimatedTime": "5-10 minutes",','    "target": "https://example.com"',"  }","}","```","","## Get Scan Results","**GET** `/api/scan/{scanId}/results`","","### Response","```json","{",'  "success": true,','  "data": {','    "scanId": "scan_*********",','    "status": "completed",','    "target": "https://example.com",','    "vulnerabilities": [',"      {",'        "type": "sqli",','        "severity": "critical",','        "cvssScore": 9.8,','        "url": "https://example.com/login.php",','        "parameter": "username",',"        \"payload\": \"admin' OR '1'='1\",",'        "evidence": "MySQL error detected",','        "recommendation": "Use parameterized queries"',"      }","    ],",'    "summary": {','      "total": 1,','      "critical": 1,','      "high": 0,','      "medium": 0,','      "low": 0',"    }","  }","}","```"]},{id:"file-api",title:"File Analysis API",description:"API untuk analisis file dan deteksi malware",icon:u.Z,content:["# File Analysis API","","## Analyze File","**POST** `/api/file/analyze`","","Upload dan analisis file untuk deteksi malware, webshell, dan secrets.","","### Request (multipart/form-data)","```bash","curl -X POST https://api.kodexguard.com/v1/file/analyze \\",'  -H "Authorization: Bearer YOUR_API_KEY" \\','  -F "file=@suspicious.php" \\','  -F "analysisType=webshell"',"```","","### Parameters","- `file` (file, required): File to analyze","- `analysisType` (string, optional): Analysis type (malware, webshell, secret, general)","","### Supported File Types","- **Scripts**: .php, .js, .py, .asp, .jsp","- **Documents**: .txt, .pdf, .doc, .docx","- **Archives**: .zip, .rar, .tar, .gz","- **Executables**: .exe, .dll, .apk","","### Response","```json","{",'  "success": true,','  "data": {','    "analysisId": "analysis_789012345",','    "filename": "suspicious.php",','    "fileSize": 2048,','    "mimeType": "text/x-php",','    "hash": "sha256:abc123...",','    "threatLevel": "suspicious",','    "results": {','      "webshellDetection": {','        "detected": true,','        "type": "PHP Webshell",','        "obfuscated": false,','        "confidence": 95',"      },",'      "secretDetection": {','        "detected": true,','        "secrets": [',"          {",'            "type": "API Key",','            "value": "sk-*********0abcdef",','            "confidence": 87',"          }","        ]","      }","    }","  }","}","```","","## Get Analysis Results","**GET** `/api/file/analysis/{analysisId}`","","Retrieve detailed analysis results by analysis ID."]},{id:"cve-api",title:"CVE API",description:"API untuk CVE intelligence dan vulnerability database",icon:p.Z,content:["# CVE Intelligence API","","## Search CVE Database","**GET** `/api/cve/search`","","### Query Parameters","- `query` (string, optional): Search query","- `severity` (string, optional): Filter by severity (critical, high, medium, low)","- `year` (string, optional): Filter by year (2024, 2023, etc.)","- `limit` (number, optional): Number of results (default: 20, max: 100)","- `offset` (number, optional): Pagination offset (default: 0)","","### Example","```bash",'curl "https://api.kodexguard.com/v1/cve/search?query=SQL%20injection&severity=critical&limit=10" \\','  -H "Authorization: Bearer YOUR_API_KEY"',"```","","### Response","```json","{",'  "success": true,','  "data": {','    "total": 156,','    "cves": [',"      {",'        "cveId": "CVE-2024-0001",','        "description": "SQL injection vulnerability...",','        "severity": "critical",','        "cvssScore": 9.8,','        "cvssVector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H",','        "publishedDate": "2024-01-15",','        "modifiedDate": "2024-01-16",','        "affectedProducts": ["WebApp 1.0", "WebApp 2.0"],','        "references": ["https://nvd.nist.gov/vuln/detail/CVE-2024-0001"],','        "exploits": [',"          {",'            "title": "SQL Injection Exploit",','            "url": "https://exploit-db.com/exploits/12345",','            "type": "Public Exploit"',"          }","        ]","      }","    ]","  }","}","```","","## Get CVE Details","**GET** `/api/cve/{cveId}`","","Get detailed information about specific CVE.","","### Example","```bash","curl https://api.kodexguard.com/v1/cve/CVE-2024-0001 \\",'  -H "Authorization: Bearer YOUR_API_KEY"',"```"]},{id:"bot-integration",title:"Bot Integration",description:"Integrasi WhatsApp dan Telegram bot untuk automation",icon:m.Z,content:["# Bot Integration Guide","","## WhatsApp Bot Setup","","### Prerequisites","- Plan Bug Hunter atau Cybersecurity","- Super Admin access untuk setup","- WhatsApp account untuk bot","","### Setup Steps","1. **Login sebagai Super Admin**","2. **Buka Bot Center** (`/bot`)","3. **Add New Bot** dengan type WhatsApp","4. **Scan QR Code** dengan WhatsApp","5. **Bot Ready** untuk menerima commands","","### Available Commands","```","/scan <url>     - Start vulnerability scan","/status         - Check plan status","/help           - Show available commands","/results <id>   - Get scan results","```","","### Example Usage","```","User: /scan https://example.com","Bot:  Starting vulnerability scan for https://example.com...","      Scan ID: scan_*********","      Estimated time: 5-10 minutes","","User: /results scan_*********","Bot:  Scan Results:","      ✅ Completed","      \uD83D\uDD34 1 Critical vulnerability found","      \uD83D\uDCCA CVSS Score: 9.8","      \uD83D\uDCC4 Full report: https://kodexguard.com/scan/*********","```","","## Telegram Bot Setup","","### Setup Steps","1. **Create Bot** dengan @BotFather","2. **Get Bot Token**","3. **Add Bot** di KodeXGuard Bot Center","4. **Configure Webhook** (otomatis)","","### Bot Configuration","```json","{",'  "botToken": "YOUR_TELEGRAM_BOT_TOKEN",','  "webhookUrl": "https://api.kodexguard.com/webhook/telegram",','  "allowedUsers": ["@username1", "@username2"],','  "commands": [','    {"command": "scan", "description": "Start vulnerability scan"},','    {"command": "status", "description": "Check plan status"},','    {"command": "help", "description": "Show help"}',"  ]","}","```","","## Bot API Integration","","### Send Message via API","**POST** `/api/bot/send`","","```json","{",'  "botId": "bot_123",','  "recipient": "+62812345678",','  "message": "Scan completed! Check results at: https://..."',"}","```","","### Get Bot Status","**GET** `/api/bot/status`","","Returns status of all configured bots."]},{id:"sdk-examples",title:"SDK & Examples",description:"Code examples dan SDK untuk berbagai bahasa pemrograman",icon:h.Z,content:["# SDK & Code Examples","","## JavaScript/Node.js","","### Installation","```bash","npm install kodexguard-sdk","```","","### Basic Usage","```javascript","const KodeXGuard = require('kodexguard-sdk');","","const client = new KodeXGuard({","  apiKey: 'your-api-key',","  baseUrl: 'https://api.kodexguard.com/v1'","});","","// OSINT Search","const osintResult = await client.osint.search({","  query: '<EMAIL>',","  type: 'email',","  deepSearch: true","});","","// Vulnerability Scan","const scan = await client.scanner.start({","  target: 'https://example.com',","  scanTypes: ['sqli', 'xss', 'lfi']","});","","// Get Results","const results = await client.scanner.getResults(scan.scanId);","```","","## Python","","### Installation","```bash","pip install kodexguard-python","```","","### Basic Usage","```python","from kodexguard import KodeXGuardClient","","client = KodeXGuardClient(",'    api_key="your-api-key",','    base_url="https://api.kodexguard.com/v1"',")","","# OSINT Search","osint_result = client.osint.search(",'    query="<EMAIL>",','    type="email",',"    deep_search=True",")","","# Vulnerability Scan","scan = client.scanner.start(",'    target="https://example.com",','    scan_types=["sqli", "xss", "lfi"]',")","","# Get Results",'results = client.scanner.get_results(scan["scanId"])',"```","","## PHP","","### Installation","```bash","composer require kodexguard/php-sdk","```","","### Basic Usage","```php","<?php","require_once 'vendor/autoload.php';","","use KodeXGuard\\Client;","","$client = new Client([","    'api_key' => 'your-api-key',","    'base_url' => 'https://api.kodexguard.com/v1'","]);","","// OSINT Search","$osintResult = $client->osint()->search([","    'query' => '<EMAIL>',","    'type' => 'email',","    'deepSearch' => true","]);","","// Vulnerability Scan","$scan = $client->scanner()->start([","    'target' => 'https://example.com',","    'scanTypes' => ['sqli', 'xss', 'lfi']","]);","?>","```","","## cURL Examples","","### OSINT Search","```bash","curl -X POST https://api.kodexguard.com/v1/osint/search \\",'  -H "Authorization: Bearer YOUR_API_KEY" \\','  -H "Content-Type: application/json" \\','  -d \'{"query": "<EMAIL>", "type": "email"}\'',"```","","### Start Vulnerability Scan","```bash","curl -X POST https://api.kodexguard.com/v1/scan/vulnerability \\",'  -H "Authorization: Bearer YOUR_API_KEY" \\','  -H "Content-Type: application/json" \\','  -d \'{"target": "https://example.com", "scanTypes": ["sqli", "xss"]}\'',"```"]}],v=f.filter(e=>e.title.toLowerCase().includes(b.toLowerCase())||e.description.toLowerCase().includes(b.toLowerCase())||e.content.some(e=>e.toLowerCase().includes(b.toLowerCase()))),k=e=>{navigator.clipboard.writeText(e)},P=e=>e.map((t,a)=>{if(t.startsWith("# "))return s.jsx("h1",{className:"text-2xl font-bold text-white mb-4",children:t.substring(2)},a);if(t.startsWith("## "))return s.jsx("h2",{className:"text-xl font-semibold text-white mb-3 mt-6",children:t.substring(3)},a);if(t.startsWith("### "))return s.jsx("h3",{className:"text-lg font-semibold text-cyber-green mb-2 mt-4",children:t.substring(4)},a);if(t.startsWith("```")){let t=e.findIndex((e,t)=>t>a&&e.startsWith("```"));if(t>a){let i=e.slice(a+1,t).join("\n");return(0,s.jsxs)("div",{className:"relative bg-gray-900/50 rounded-lg p-4 mb-4",children:[s.jsx("button",{onClick:()=>k(i),className:"absolute top-2 right-2 text-gray-400 hover:text-white transition-colors",children:s.jsx(x.Z,{className:"h-4 w-4"})}),s.jsx("pre",{className:"text-sm text-cyber-green overflow-x-auto",children:s.jsx("code",{children:i})})]},a)}return null}return t.startsWith("- ")?s.jsx("li",{className:"text-gray-300 mb-1 ml-4",children:t.substring(2)},a):""===t?s.jsx("br",{},a):s.jsx("p",{className:"text-gray-300 mb-2",children:t},a)}).filter(Boolean);return s.jsx(n.Z,{user:e,title:"Documentation",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[s.jsx(g.Z,{className:"h-8 w-8 text-cyber-green"}),s.jsx("h1",{className:"text-3xl font-bold font-cyber text-white",children:s.jsx("span",{className:"cyber-text",children:"Documentation"})})]}),s.jsx("p",{className:"text-gray-400 max-w-3xl",children:"Dokumentasi lengkap KodeXGuard API, SDK, dan panduan penggunaan untuk developer dan security researcher."})]}),s.jsx("div",{className:"mb-8",children:(0,s.jsxs)("div",{className:"relative max-w-md",children:[s.jsx(c.Z,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),s.jsx("input",{type:"text",value:b,onChange:e=>S(e.target.value),placeholder:"Search documentation...",className:"cyber-input pl-10 w-full"})]})}),(0,s.jsxs)("div",{className:"grid lg:grid-cols-4 gap-8",children:[s.jsx("div",{children:s.jsx(r.Zb,{children:(0,s.jsxs)("div",{className:"p-6",children:[s.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Documentation"}),s.jsx("nav",{className:"space-y-2",children:v.map(e=>{let i=e.icon;return(0,s.jsxs)("button",{onClick:()=>a(e.id),className:`w-full text-left flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 ${t===e.id?"bg-cyber-green/10 text-cyber-green border border-cyber-green":"text-gray-300 hover:text-white hover:bg-gray-800/50"}`,children:[s.jsx(i,{className:"h-4 w-4 flex-shrink-0"}),(0,s.jsxs)("div",{className:"flex-1",children:[s.jsx("div",{className:"font-medium",children:e.title}),s.jsx("div",{className:"text-xs text-gray-400",children:e.description})]}),s.jsx(y.Z,{className:"h-4 w-4"})]},e.id)})})]})})}),s.jsx("div",{className:"lg:col-span-3",children:s.jsx(r.Zb,{children:s.jsx("div",{className:"p-8",children:(()=>{let e=f.find(e=>e.id===t);return e?s.jsx("div",{className:"prose prose-invert max-w-none",children:P(e.content)}):null})()})})})]})]})})}},6819:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(68570).createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\docs\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[216,592],()=>a(82220));module.exports=s})();