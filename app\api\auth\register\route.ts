import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { username, email, password, plan = 'gratis' } = body

    // Validate input
    if (!username || !email || !password) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Username, email and password are required',
          code: 'MISSING_FIELDS'
        },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid email format',
          code: 'INVALID_EMAIL'
        },
        { status: 400 }
      )
    }

    // Validate password strength
    if (password.length < 6) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Password must be at least 6 characters long',
          code: 'WEAK_PASSWORD'
        },
        { status: 400 }
      )
    }

    // Check if email already exists (mock check)
    const existingEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ]

    if (existingEmails.includes(email)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Email already registered',
          code: 'EMAIL_EXISTS'
        },
        { status: 409 }
      )
    }

    // Generate user ID and token
    const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const token = `kxg_token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Mock user data
    const userData = {
      id: userId,
      username,
      email,
      role: 'user',
      plan,
      isActive: true,
      emailVerified: false,
      createdAt: new Date().toISOString(),
      lastLogin: new Date().toISOString(),
      stats: {
        totalScans: 0,
        vulnerabilitiesFound: 0,
        filesAnalyzed: 0,
        apiCalls: 0,
        score: 0,
        rank: 999999
      }
    }

    const response = NextResponse.json({
      success: true,
      data: {
        token,
        user: userData,
        expiresIn: '24h'
      },
      message: 'Registration successful',
      timestamp: new Date().toISOString()
    })

    // Set HTTP-only cookie for token
    response.cookies.set('auth-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 // 24 hours
    })

    return response

  } catch (error) {
    console.error('Registration error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
