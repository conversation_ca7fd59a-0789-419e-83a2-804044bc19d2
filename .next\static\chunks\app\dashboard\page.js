/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/dashboard/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*****************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(app-pages-browser)/./app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1VzZXJzJTVDJTVDRG93bmxvYWRzJTVDJTVDa29kZVhHdWFyZCU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEtBQStGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/NGQ4NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFVzZXJzXFxcXERvd25sb2Fkc1xcXFxrb2RlWEd1YXJkXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/createLucideIcon.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ createLucideIcon; },\n/* harmony export */   toKebabCase: function() { return /* binding */ toKebabCase; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _defaultAttributes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./defaultAttributes.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \n\nconst toKebabCase = (string)=>string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase().trim();\nconst createLucideIcon = (iconName, iconNode)=>{\n    const Component = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((param, ref)=>{\n        let { color = \"currentColor\", size = 24, strokeWidth = 2, absoluteStrokeWidth, className = \"\", children, ...rest } = param;\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(\"svg\", {\n            ref,\n            ..._defaultAttributes_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            width: size,\n            height: size,\n            stroke: color,\n            strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n            className: [\n                \"lucide\",\n                \"lucide-\".concat(toKebabCase(iconName)),\n                className\n            ].join(\" \"),\n            ...rest\n        }, [\n            ...iconNode.map((param)=>{\n                let [tag, attrs] = param;\n                return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(tag, attrs);\n            }),\n            ...Array.isArray(children) ? children : [\n                children\n            ]\n        ]);\n    });\n    Component.displayName = \"\".concat(iconName);\n    return Component;\n};\n //# sourceMappingURL=createLucideIcon.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/defaultAttributes.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ defaultAttributes; }\n/* harmony export */ });\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ var defaultAttributes = {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: 24,\n    height: 24,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: 2,\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n};\n //# sourceMappingURL=defaultAttributes.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vZGVmYXVsdEF0dHJpYnV0ZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0lBQUEsSUFBZUEsb0JBQUE7SUFDYkMsT0FBTztJQUNQQyxPQUFPO0lBQ1BDLFFBQVE7SUFDUkMsU0FBUztJQUNUQyxNQUFNO0lBQ05DLFFBQVE7SUFDUkMsYUFBYTtJQUNiQyxlQUFlO0lBQ2ZDLGdCQUFnQjtBQUNsQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vc3JjL2RlZmF1bHRBdHRyaWJ1dGVzLnRzPzM3MGMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1xuICB4bWxuczogJ2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyxcbiAgd2lkdGg6IDI0LFxuICBoZWlnaHQ6IDI0LFxuICB2aWV3Qm94OiAnMCAwIDI0IDI0JyxcbiAgZmlsbDogJ25vbmUnLFxuICBzdHJva2U6ICdjdXJyZW50Q29sb3InLFxuICBzdHJva2VXaWR0aDogMixcbiAgc3Ryb2tlTGluZWNhcDogJ3JvdW5kJyxcbiAgc3Ryb2tlTGluZWpvaW46ICdyb3VuZCcsXG59O1xuIl0sIm5hbWVzIjpbImRlZmF1bHRBdHRyaWJ1dGVzIiwieG1sbnMiLCJ3aWR0aCIsImhlaWdodCIsInZpZXdCb3giLCJmaWxsIiwic3Ryb2tlIiwic3Ryb2tlV2lkdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/activity.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Activity; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Activity = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Activity\", [\n    [\n        \"path\",\n        {\n            d: \"M22 12h-4l-3 9L9 3l-3 9H2\",\n            key: \"d5dnw9\"\n        }\n    ]\n]);\n //# sourceMappingURL=activity.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvYWN0aXZpdHkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFhTSxNQUFBQSxXQUFXQyxnRUFBZ0JBLENBQUMsWUFBWTtJQUM1QztRQUFDO1FBQVE7WUFBRUMsR0FBRztZQUE2QkMsS0FBSztRQUFBO0tBQVU7Q0FDM0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uLy4uL3NyYy9pY29ucy9hY3Rpdml0eS50cz85YzNjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgQWN0aXZpdHlcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKTk1qSWdNVEpvTFRSc0xUTWdPVXc1SUROc0xUTWdPVWd5SWlBdlBnbzhMM04yWno0SykgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvYWN0aXZpdHlcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBBY3Rpdml0eSA9IGNyZWF0ZUx1Y2lkZUljb24oJ0FjdGl2aXR5JywgW1xuICBbJ3BhdGgnLCB7IGQ6ICdNMjIgMTJoLTRsLTMgOUw5IDNsLTMgOUgyJywga2V5OiAnZDVkbnc5JyB9XSxcbl0pO1xuXG5leHBvcnQgZGVmYXVsdCBBY3Rpdml0eTtcbiJdLCJuYW1lcyI6WyJBY3Rpdml0eSIsImNyZWF0ZUx1Y2lkZUljb24iLCJkIiwia2V5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js":
/*!********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/alert-triangle.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AlertTriangle; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst AlertTriangle = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"AlertTriangle\", [\n    [\n        \"path\",\n        {\n            d: \"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z\",\n            key: \"c3ski4\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 9v4\",\n            key: \"juzpu7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 17h.01\",\n            key: \"p32p05\"\n        }\n    ]\n]);\n //# sourceMappingURL=alert-triangle.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js":
/*!*********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/bot.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Bot; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Bot = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Bot\", [\n    [\n        \"path\",\n        {\n            d: \"M12 8V4H8\",\n            key: \"hb8ula\"\n        }\n    ],\n    [\n        \"rect\",\n        {\n            width: \"16\",\n            height: \"12\",\n            x: \"4\",\n            y: \"8\",\n            rx: \"2\",\n            key: \"enze0r\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M2 14h2\",\n            key: \"vft8re\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M20 14h2\",\n            key: \"4cs60a\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M15 13v2\",\n            key: \"1xurst\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M9 13v2\",\n            key: \"rq6x2g\"\n        }\n    ]\n]);\n //# sourceMappingURL=bot.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bug.js":
/*!*********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/bug.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Bug; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Bug = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Bug\", [\n    [\n        \"path\",\n        {\n            d: \"m8 2 1.88 1.88\",\n            key: \"fmnt4t\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M14.12 3.88 16 2\",\n            key: \"qol33r\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M9 7.13v-1a3.003 3.003 0 1 1 6 0v1\",\n            key: \"d7y7pr\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6\",\n            key: \"xs1cw7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 20v-9\",\n            key: \"1qisl0\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M6.53 9C4.6 8.8 3 7.1 3 5\",\n            key: \"32zzws\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M6 13H2\",\n            key: \"82j7cp\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3 21c0-2.1 1.7-3.9 3.8-4\",\n            key: \"4p0ekp\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M20.97 5c0 2.1-1.6 3.8-3.5 4\",\n            key: \"18gb23\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M22 13h-4\",\n            key: \"1jl80f\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M17.2 17c2.1.1 3.8 1.9 3.8 4\",\n            key: \"k3fwyw\"\n        }\n    ]\n]);\n //# sourceMappingURL=bug.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bug.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/check-circle.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CheckCircle; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst CheckCircle = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"CheckCircle\", [\n    [\n        \"path\",\n        {\n            d: \"M22 11.08V12a10 10 0 1 1-5.93-9.14\",\n            key: \"g774vq\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m9 11 3 3L22 4\",\n            key: \"1pflzl\"\n        }\n    ]\n]);\n //# sourceMappingURL=check-circle.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/clock.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Clock; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Clock = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Clock\", [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"12 6 12 12 16 14\",\n            key: \"68esgv\"\n        }\n    ]\n]);\n //# sourceMappingURL=clock.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2xvY2suanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFhTSxNQUFBQSxRQUFRQyxnRUFBZ0JBLENBQUMsU0FBUztJQUN0QztRQUFDO1FBQVU7WUFBRUMsSUFBSTtZQUFNQyxJQUFJO1lBQU1DLEdBQUc7WUFBTUMsS0FBSztRQUFBO0tBQVU7SUFDekQ7UUFBQztRQUFZO1lBQUVDLFFBQVE7WUFBb0JELEtBQUs7UUFBQTtLQUFVO0NBQzNEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi8uLi9zcmMvaWNvbnMvY2xvY2sudHM/ZjViYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIENsb2NrXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThZMmx5WTJ4bElHTjRQU0l4TWlJZ1kzazlJakV5SWlCeVBTSXhNQ0lnTHo0S0lDQThjRzlzZVd4cGJtVWdjRzlwYm5SelBTSXhNaUEySURFeUlERXlJREUySURFMElpQXZQZ284TDNOMlp6NEspIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2Nsb2NrXG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgQ2xvY2sgPSBjcmVhdGVMdWNpZGVJY29uKCdDbG9jaycsIFtcbiAgWydjaXJjbGUnLCB7IGN4OiAnMTInLCBjeTogJzEyJywgcjogJzEwJywga2V5OiAnMW1nbGF5JyB9XSxcbiAgWydwb2x5bGluZScsIHsgcG9pbnRzOiAnMTIgNiAxMiAxMiAxNiAxNCcsIGtleTogJzY4ZXNndicgfV0sXG5dKTtcblxuZXhwb3J0IGRlZmF1bHQgQ2xvY2s7XG4iXSwibmFtZXMiOlsiQ2xvY2siLCJjcmVhdGVMdWNpZGVJY29uIiwiY3giLCJjeSIsInIiLCJrZXkiLCJwb2ludHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/code.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Code; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Code = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Code\", [\n    [\n        \"polyline\",\n        {\n            points: \"16 18 22 12 16 6\",\n            key: \"z7tu5w\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"8 6 2 12 8 18\",\n            key: \"1eg1df\"\n        }\n    ]\n]);\n //# sourceMappingURL=code.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY29kZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQWFNLE1BQUFBLE9BQU9DLGdFQUFnQkEsQ0FBQyxRQUFRO0lBQ3BDO1FBQUM7UUFBWTtZQUFFQyxRQUFRO1lBQW9CQyxLQUFLO1FBQUE7S0FBVTtJQUMxRDtRQUFDO1FBQVk7WUFBRUQsUUFBUTtZQUFpQkMsS0FBSztRQUFBO0tBQVU7Q0FDeEQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uLy4uL3NyYy9pY29ucy9jb2RlLnRzPzFlODUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBDb2RlXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjRzlzZVd4cGJtVWdjRzlwYm5SelBTSXhOaUF4T0NBeU1pQXhNaUF4TmlBMklpQXZQZ29nSUR4d2IyeDViR2x1WlNCd2IybHVkSE05SWpnZ05pQXlJREV5SURnZ01UZ2lJQzgrQ2p3dmMzWm5QZ289KSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9jb2RlXG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgQ29kZSA9IGNyZWF0ZUx1Y2lkZUljb24oJ0NvZGUnLCBbXG4gIFsncG9seWxpbmUnLCB7IHBvaW50czogJzE2IDE4IDIyIDEyIDE2IDYnLCBrZXk6ICd6N3R1NXcnIH1dLFxuICBbJ3BvbHlsaW5lJywgeyBwb2ludHM6ICc4IDYgMiAxMiA4IDE4Jywga2V5OiAnMWVnMWRmJyB9XSxcbl0pO1xuXG5leHBvcnQgZGVmYXVsdCBDb2RlO1xuIl0sIm5hbWVzIjpbIkNvZGUiLCJjcmVhdGVMdWNpZGVJY29uIiwicG9pbnRzIiwia2V5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-search.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/file-search.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FileSearch; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst FileSearch = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"FileSearch\", [\n    [\n        \"path\",\n        {\n            d: \"M4 22h14a2 2 0 0 0 2-2V7.5L14.5 2H6a2 2 0 0 0-2 2v3\",\n            key: \"am10z3\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"14 2 14 8 20 8\",\n            key: \"1ew0cm\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M5 17a3 3 0 1 0 0-6 3 3 0 0 0 0 6z\",\n            key: \"ychnub\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m9 18-1.5-1.5\",\n            key: \"1j6qii\"\n        }\n    ]\n]);\n //# sourceMappingURL=file-search.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-search.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/search.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Search; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Search = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Search\", [\n    [\n        \"circle\",\n        {\n            cx: \"11\",\n            cy: \"11\",\n            r: \"8\",\n            key: \"4ej97u\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m21 21-4.3-4.3\",\n            key: \"1qie3q\"\n        }\n    ]\n]);\n //# sourceMappingURL=search.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/shield.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Shield; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Shield = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Shield\", [\n    [\n        \"path\",\n        {\n            d: \"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10\",\n            key: \"1irkt0\"\n        }\n    ]\n]);\n //# sourceMappingURL=shield.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvc2hpZWxkLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBYU0sTUFBQUEsU0FBU0MsZ0VBQWdCQSxDQUFDLFVBQVU7SUFDeEM7UUFBQztRQUFRO1lBQUVDLEdBQUc7WUFBOENDLEtBQUs7UUFBQTtLQUFVO0NBQzVFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi8uLi9zcmMvaWNvbnMvc2hpZWxkLnRzPzk1YTYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBTaGllbGRcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKTk1USWdNakp6T0MwMElEZ3RNVEJXTld3dE9DMHpMVGdnTTNZM1l6QWdOaUE0SURFd0lEZ2dNVEFpSUM4K0Nqd3ZjM1puUGdvPSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvc2hpZWxkXG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgU2hpZWxkID0gY3JlYXRlTHVjaWRlSWNvbignU2hpZWxkJywgW1xuICBbJ3BhdGgnLCB7IGQ6ICdNMTIgMjJzOC00IDgtMTBWNWwtOC0zLTggM3Y3YzAgNiA4IDEwIDggMTAnLCBrZXk6ICcxaXJrdDAnIH1dLFxuXSk7XG5cbmV4cG9ydCBkZWZhdWx0IFNoaWVsZDtcbiJdLCJuYW1lcyI6WyJTaGllbGQiLCJjcmVhdGVMdWNpZGVJY29uIiwiZCIsImtleSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/trending-up.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TrendingUp; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst TrendingUp = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"TrendingUp\", [\n    [\n        \"polyline\",\n        {\n            points: \"22 7 13.5 15.5 8.5 10.5 2 17\",\n            key: \"126l90\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"16 7 22 7 22 13\",\n            key: \"kwv8wd\"\n        }\n    ]\n]);\n //# sourceMappingURL=trending-up.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/api/app-dynamic.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* reexport default from dynamic */ _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0___default.a; }\n/* harmony export */ });\n/* harmony import */ var _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../shared/lib/app-dynamic */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-dynamic.js\");\n/* harmony import */ var _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n//# sourceMappingURL=app-dynamic.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL2FwcC1keW5hbWljLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUEwQztBQUNVOztBQUVwRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2FwaS9hcHAtZHluYW1pYy5qcz9mMzlkIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuLi9zaGFyZWQvbGliL2FwcC1keW5hbWljXCI7XG5leHBvcnQgeyBkZWZhdWx0IH0gZnJvbSBcIi4uL3NoYXJlZC9saWIvYXBwLWR5bmFtaWNcIjtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLWR5bmFtaWMuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./components/DashboardLayout.tsx\");\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Card */ \"(app-pages-browser)/./components/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Bug,CheckCircle,Clock,Code,FileSearch,Search,Shield,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Bug,CheckCircle,Clock,Code,FileSearch,Search,Shield,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Bug,CheckCircle,Clock,Code,FileSearch,Search,Shield,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Bug,CheckCircle,Clock,Code,FileSearch,Search,Shield,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Bug,CheckCircle,Clock,Code,FileSearch,Search,Shield,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Bug,CheckCircle,Clock,Code,FileSearch,Search,Shield,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Bug,CheckCircle,Clock,Code,FileSearch,Search,Shield,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Bug,CheckCircle,Clock,Code,FileSearch,Search,Shield,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Bug,CheckCircle,Clock,Code,FileSearch,Search,Shield,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Bug,CheckCircle,Clock,Code,FileSearch,Search,Shield,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bot,Bug,CheckCircle,Clock,Code,FileSearch,Search,Shield,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const [user] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: \"admin\",\n        avatar: \"\",\n        role: \"super_admin\",\n        plan: \"cybersecurity\"\n    });\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalScans: 0,\n        vulnerabilitiesFound: 0,\n        filesAnalyzed: 0,\n        apiCalls: 0,\n        loading: true\n    });\n    const [recentActivity, setRecentActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            type: \"scan\",\n            title: \"Vulnerability scan completed\",\n            target: \"example.com\",\n            severity: \"high\",\n            time: \"2 minutes ago\"\n        },\n        {\n            id: 2,\n            type: \"osint\",\n            title: \"OSINT search completed\",\n            target: \"<EMAIL>\",\n            severity: \"medium\",\n            time: \"15 minutes ago\"\n        },\n        {\n            id: 3,\n            type: \"file\",\n            title: \"File analysis completed\",\n            target: \"suspicious.php\",\n            severity: \"critical\",\n            time: \"1 hour ago\"\n        }\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simulate loading stats\n        setTimeout(()=>{\n            setStats({\n                totalScans: 1247,\n                vulnerabilitiesFound: 89,\n                filesAnalyzed: 456,\n                apiCalls: 12890,\n                loading: false\n            });\n        }, 1000);\n    }, []);\n    const features = [\n        {\n            title: \"OSINT Investigator\",\n            description: \"Investigasi mendalam dengan pencarian NIK, NPWP, nomor HP, email, dan domain\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: \"green\",\n            href: \"/osint\"\n        },\n        {\n            title: \"Vulnerability Scanner\",\n            description: \"Deteksi SQLi, XSS, LFI, RCE dengan scoring CVSS dan mapping CVE\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: \"red\",\n            href: \"/scanner\"\n        },\n        {\n            title: \"File Analyzer\",\n            description: \"Analisis webshell, malware, dan deteksi secret dalam berbagai format file\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: \"blue\",\n            href: \"/file-analyzer\"\n        },\n        {\n            title: \"CVE Intelligence\",\n            description: \"Database CVE terupdate dengan Google dorking dan payload generator\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            color: \"purple\",\n            href: \"/cve\"\n        },\n        {\n            title: \"Bot Integration\",\n            description: \"WhatsApp & Telegram bot untuk monitoring dan notifikasi real-time\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            color: \"gold\",\n            href: \"/bot\"\n        },\n        {\n            title: \"API Playground\",\n            description: \"Testing environment dengan Swagger docs dan request builder\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            color: \"green\",\n            href: \"/playground\"\n        }\n    ];\n    const getSeverityColor = (severity)=>{\n        switch(severity){\n            case \"critical\":\n                return \"text-red-400\";\n            case \"high\":\n                return \"text-orange-400\";\n            case \"medium\":\n                return \"text-yellow-400\";\n            case \"low\":\n                return \"text-blue-400\";\n            default:\n                return \"text-gray-400\";\n        }\n    };\n    const getSeverityIcon = (severity)=>{\n        switch(severity){\n            case \"critical\":\n                return _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\n            case \"high\":\n                return _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\n            case \"medium\":\n                return _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"];\n            default:\n                return _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"];\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        user: user,\n        title: \"Dashboard\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold font-cyber text-white mb-2\",\n                            children: [\n                                \"Welcome back, \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"cyber-text\",\n                                    children: user.username\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400\",\n                            children: \"Monitor your cybersecurity activities and manage your tools from here.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.AlertCard, {\n                        type: \"success\",\n                        title: \"System Status: Online\",\n                        message: \"All systems are operational. Last updated: 2 minutes ago\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.StatCard, {\n                            title: \"Total Scans\",\n                            value: stats.totalScans,\n                            icon: _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                            color: \"green\",\n                            trend: {\n                                value: 12,\n                                isPositive: true\n                            },\n                            loading: stats.loading\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.StatCard, {\n                            title: \"Vulnerabilities Found\",\n                            value: stats.vulnerabilitiesFound,\n                            icon: _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                            color: \"red\",\n                            trend: {\n                                value: 8,\n                                isPositive: true\n                            },\n                            loading: stats.loading\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.StatCard, {\n                            title: \"Files Analyzed\",\n                            value: stats.filesAnalyzed,\n                            icon: _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                            color: \"blue\",\n                            trend: {\n                                value: 15,\n                                isPositive: true\n                            },\n                            loading: stats.loading\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.StatCard, {\n                            title: \"API Calls\",\n                            value: stats.apiCalls,\n                            icon: _barrel_optimize_names_Activity_AlertTriangle_Bot_Bug_CheckCircle_Clock_Code_FileSearch_Search_Shield_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                            color: \"purple\",\n                            trend: {\n                                value: 23,\n                                isPositive: true\n                            },\n                            loading: stats.loading\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold text-white mb-6\",\n                                    children: \"Quick Access\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-2 gap-6\",\n                                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.FeatureCard, {\n                                            title: feature.title,\n                                            description: feature.description,\n                                            icon: feature.icon,\n                                            color: feature.color,\n                                            onClick: ()=>window.location.href = feature.href\n                                        }, index, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold text-white mb-6\",\n                                    children: \"Recent Activity\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: recentActivity.map((activity)=>{\n                                                    const SeverityIcon = getSeverityIcon(activity.severity);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-3 p-3 rounded-lg bg-gray-800/50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SeverityIcon, {\n                                                                className: \"h-5 w-5 mt-0.5 \".concat(getSeverityColor(activity.severity))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium text-white\",\n                                                                        children: activity.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 229,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-400 truncate\",\n                                                                        children: activity.target\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 230,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: activity.time\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 231,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 text-xs font-semibold rounded-full \".concat(activity.severity === \"critical\" ? \"bg-red-900/50 text-red-400\" : activity.severity === \"high\" ? \"bg-orange-900/50 text-orange-400\" : activity.severity === \"medium\" ? \"bg-yellow-900/50 text-yellow-400\" : \"bg-blue-900/50 text-blue-400\"),\n                                                                children: activity.severity\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, activity.id, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 25\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 pt-4 border-t border-gray-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"w-full text-sm text-cyber-green hover:text-cyber-blue transition-colors\",\n                                                    children: \"View All Activity →\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"mt-6\",\n                                    border: \"gold\",\n                                    glow: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white mb-2\",\n                                                children: \"Current Plan\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl font-bold nusantara-gold\",\n                                                        children: user.plan.charAt(0).toUpperCase() + user.plan.slice(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-3 py-1 bg-nusantara-gold/20 text-nusantara-gold rounded-full text-sm font-semibold\",\n                                                        children: \"Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 text-sm text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Daily Scans\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white\",\n                                                                children: \"Unlimited\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"API Calls\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white\",\n                                                                children: \"100,000/day\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"File Upload\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white\",\n                                                                children: \"1GB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-full mt-4 cyber-btn text-sm\",\n                                                children: \"Manage Plan\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 143,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"xFrc30q1NsnmxF3jWNw7t91Zq5E=\");\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/Card.tsx":
/*!*****************************!*\
  !*** ./components/Card.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlertCard: function() { return /* binding */ AlertCard; },\n/* harmony export */   Card: function() { return /* binding */ Card; },\n/* harmony export */   FeatureCard: function() { return /* binding */ FeatureCard; },\n/* harmony export */   LoadingCard: function() { return /* binding */ LoadingCard; },\n/* harmony export */   StatCard: function() { return /* binding */ StatCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Card,StatCard,FeatureCard,LoadingCard,AlertCard auto */ \nfunction Card(param) {\n    let { children, className = \"\", hover = false, glow = false, border = \"default\" } = param;\n    const borderColors = {\n        default: \"border-gray-700\",\n        green: \"border-cyber-green\",\n        blue: \"border-cyber-blue\",\n        red: \"border-cyber-red\",\n        gold: \"border-nusantara-gold\"\n    };\n    const glowColors = {\n        default: \"\",\n        green: \"shadow-lg shadow-cyber-green/20\",\n        blue: \"shadow-lg shadow-cyber-blue/20\",\n        red: \"shadow-lg shadow-cyber-red/20\",\n        gold: \"shadow-lg shadow-nusantara-gold/20\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\\n        bg-gray-900/50 backdrop-blur-sm rounded-lg border \".concat(borderColors[border], \"\\n        \").concat(hover ? \"hover:border-cyber-green hover:shadow-lg hover:shadow-cyber-green/20 transition-all duration-300 cursor-pointer\" : \"\", \"\\n        \").concat(glow ? glowColors[border] : \"\", \"\\n        \").concat(className, \"\\n      \"),\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n_c = Card;\nfunction StatCard(param) {\n    let { title, value, icon: Icon, color = \"green\", trend, loading = false } = param;\n    const colors = {\n        green: \"text-cyber-green\",\n        blue: \"text-cyber-blue\",\n        red: \"text-cyber-red\",\n        purple: \"text-cyber-purple\",\n        gold: \"text-nusantara-gold\"\n    };\n    const bgColors = {\n        green: \"bg-cyber-green/10\",\n        blue: \"bg-cyber-blue/10\",\n        red: \"bg-cyber-red/10\",\n        purple: \"bg-cyber-purple/10\",\n        gold: \"bg-nusantara-gold/10\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        border: color,\n        glow: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-400\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-baseline space-x-2\",\n                                children: [\n                                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 w-20 bg-gray-700 animate-pulse rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold \".concat(colors[color]),\n                                        children: typeof value === \"number\" ? value.toLocaleString() : value\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, this),\n                                    trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium \".concat(trend.isPositive ? \"text-green-400\" : \"text-red-400\"),\n                                        children: [\n                                            trend.isPositive ? \"+\" : \"\",\n                                            trend.value,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 rounded-lg \".concat(bgColors[color]),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            className: \"h-6 w-6 \".concat(colors[color])\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\n_c1 = StatCard;\nfunction FeatureCard(param) {\n    let { title, description, icon: Icon, color = \"green\", onClick, disabled = false, badge } = param;\n    const colors = {\n        green: \"text-cyber-green\",\n        blue: \"text-cyber-blue\",\n        red: \"text-cyber-red\",\n        purple: \"text-cyber-purple\",\n        gold: \"text-nusantara-gold\"\n    };\n    const bgColors = {\n        green: \"bg-cyber-green/10\",\n        blue: \"bg-cyber-blue/10\",\n        red: \"bg-cyber-red/10\",\n        purple: \"bg-cyber-purple/10\",\n        gold: \"bg-nusantara-gold/10\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        hover: !disabled && !!onClick,\n        border: color,\n        className: \"relative \".concat(disabled ? \"opacity-50 cursor-not-allowed\" : \"\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 h-full\",\n            onClick: disabled ? undefined : onClick,\n            children: [\n                badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-3 right-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"px-2 py-1 text-xs font-semibold rounded-full \".concat(bgColors[color], \" \").concat(colors[color]),\n                        children: badge\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"inline-flex p-3 rounded-lg \".concat(bgColors[color], \" mb-4\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: \"h-6 w-6 \".concat(colors[color])\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-white mb-2\",\n                    children: title\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-400 text-sm leading-relaxed\",\n                    children: description\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this),\n                onClick && !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 pt-4 border-t border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium \".concat(colors[color], \" hover:underline\"),\n                        children: \"Mulai Sekarang →\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\n_c2 = FeatureCard;\nfunction LoadingCard(param) {\n    let { className = \"\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 animate-pulse\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 bg-gray-700 rounded-lg\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-gray-700 rounded w-3/4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-3 bg-gray-700 rounded w-1/2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                lineNumber: 184,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 183,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 182,\n        columnNumber: 5\n    }, this);\n}\n_c3 = LoadingCard;\nfunction AlertCard(param) {\n    let { type = \"info\", title, message, onClose } = param;\n    const styles = {\n        info: {\n            border: \"border-cyber-blue\",\n            bg: \"bg-cyber-blue/10\",\n            text: \"text-cyber-blue\",\n            icon: \"\\uD83D\\uDCA1\"\n        },\n        success: {\n            border: \"border-cyber-green\",\n            bg: \"bg-cyber-green/10\",\n            text: \"text-cyber-green\",\n            icon: \"✅\"\n        },\n        warning: {\n            border: \"border-nusantara-gold\",\n            bg: \"bg-nusantara-gold/10\",\n            text: \"text-nusantara-gold\",\n            icon: \"⚠️\"\n        },\n        error: {\n            border: \"border-cyber-red\",\n            bg: \"bg-cyber-red/10\",\n            text: \"text-cyber-red\",\n            icon: \"❌\"\n        }\n    };\n    const style = styles[type];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border \".concat(style.border, \" \").concat(style.bg, \" rounded-lg p-4\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-lg\",\n                    children: style.icon\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"font-semibold \".concat(style.text),\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300 text-sm mt-1\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, this),\n                onClose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onClose,\n                    className: \"text-gray-400 hover:text-white transition-colors\",\n                    children: \"\\xd7\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 238,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 237,\n        columnNumber: 5\n    }, this);\n}\n_c4 = AlertCard;\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"Card\");\n$RefreshReg$(_c1, \"StatCard\");\n$RefreshReg$(_c2, \"FeatureCard\");\n$RefreshReg$(_c3, \"LoadingCard\");\n$RefreshReg$(_c4, \"AlertCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/Card.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/DashboardLayout.tsx":
/*!****************************************!*\
  !*** ./components/DashboardLayout.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _lib_preloader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/preloader */ \"(app-pages-browser)/./lib/preloader.ts\");\n/* harmony import */ var _lib_performance__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/performance */ \"(app-pages-browser)/./lib/performance.ts\");\n/* harmony import */ var _lib_cache__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/cache */ \"(app-pages-browser)/./lib/cache.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// Dynamic imports for better code splitting\nconst Sidebar = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_Sidebar_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./Sidebar */ \"(app-pages-browser)/./components/Sidebar.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\DashboardLayout.tsx -> \" + \"./Sidebar\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-64 bg-gray-900 animate-pulse\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n            lineNumber: 11,\n            columnNumber: 18\n        }, undefined),\n    ssr: true\n});\n_c = Sidebar;\nconst Navbar = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_Navbar_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./Navbar */ \"(app-pages-browser)/./components/Navbar.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\DashboardLayout.tsx -> \" + \"./Navbar\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-16 bg-gray-900 animate-pulse\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n            lineNumber: 16,\n            columnNumber: 18\n        }, undefined),\n    ssr: true\n});\n_c1 = Navbar;\nfunction DashboardLayout(param) {\n    let { children, user, title, showSearch = true } = param;\n    _s();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Performance optimizations on mount\n        const initOptimizations = async ()=>{\n            try {\n                // Mark route change complete for performance tracking\n                (0,_lib_performance__WEBPACK_IMPORTED_MODULE_4__.markRouteChangeComplete)();\n                // Preload user journey based on role and plan\n                await _lib_preloader__WEBPACK_IMPORTED_MODULE_3__.preloader.preloadUserJourney(user.role, user.plan);\n                // Preload critical data for current user\n                await _lib_cache__WEBPACK_IMPORTED_MODULE_5__.cache.preloadCriticalData(user.username);\n                // Observe links for intersection-based preloading\n                setTimeout(()=>{\n                    (0,_lib_preloader__WEBPACK_IMPORTED_MODULE_3__.observeLinks)();\n                }, 1000);\n                // Preload critical assets\n                await _lib_preloader__WEBPACK_IMPORTED_MODULE_3__.preloader.preloadCriticalAssets();\n            } catch (error) {\n                console.warn(\"Optimization initialization failed:\", error);\n            }\n        };\n        initOptimizations();\n        // Cleanup on unmount\n        return ()=>{\n            _lib_performance__WEBPACK_IMPORTED_MODULE_4__.performanceMonitor.cleanup();\n        };\n    }, [\n        user.role,\n        user.plan,\n        user.username\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-cyber\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-16 bg-gray-900 animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 27\n                }, void 0),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Navbar, {\n                    user: user,\n                    title: title,\n                    showSearch: showSearch,\n                    isLandingPage: false\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex pt-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-64 bg-gray-900 animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 29\n                        }, void 0),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Sidebar, {\n                            user: user\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 min-w-0 lg:ml-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-8 bg-gray-800 rounded animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-64 bg-gray-800 rounded animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 17\n                                    }, void 0),\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\DashboardLayout.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardLayout, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c2 = DashboardLayout;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Sidebar\");\n$RefreshReg$(_c1, \"Navbar\");\n$RefreshReg$(_c2, \"DashboardLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/DashboardLayout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/cache.ts":
/*!**********************!*\
  !*** ./lib/cache.ts ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CacheKeys: function() { return /* binding */ CacheKeys; },\n/* harmony export */   cache: function() { return /* binding */ cache; }\n/* harmony export */ });\n// Advanced Caching System for KodeXGuard\n// Faster than McMaster-Carr with multi-layer caching\nclass AdvancedCache {\n    async initDiskCache() {\n        if (true) {\n            // Browser environment - use IndexedDB\n            try {\n                const { openDB } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_idb_build_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! idb */ \"(app-pages-browser)/./node_modules/idb/build/index.js\"));\n                this.diskCache = await openDB(\"kodexguard-cache\", 1, {\n                    upgrade (db) {\n                        if (!db.objectStoreNames.contains(\"cache\")) {\n                            db.createObjectStore(\"cache\", {\n                                keyPath: \"key\"\n                            });\n                        }\n                    }\n                });\n            } catch (error) {\n                console.warn(\"IndexedDB not available, using memory cache only\");\n            }\n        }\n    }\n    // Memory Cache Operations\n    isExpired(item) {\n        return Date.now() - item.timestamp > item.ttl;\n    }\n    generateKey(prefix, identifier) {\n        return \"\".concat(prefix, \":\").concat(identifier);\n    }\n    // Get from cache with fallback chain: Memory -> Disk -> Source\n    async get(key, fallback, ttl) {\n        // Try memory cache first\n        const memoryItem = this.memoryCache.get(key);\n        if (memoryItem && !this.isExpired(memoryItem)) {\n            this.stats.hits++;\n            return memoryItem.data;\n        }\n        // Try disk cache\n        if (this.diskCache) {\n            try {\n                const diskItem = await this.diskCache.get(\"cache\", key);\n                if (diskItem && !this.isExpired(diskItem)) {\n                    // Promote to memory cache\n                    this.memoryCache.set(key, diskItem);\n                    this.stats.hits++;\n                    return diskItem.data;\n                }\n            } catch (error) {\n                console.warn(\"Disk cache read error:\", error);\n            }\n        }\n        // Cache miss - use fallback if provided\n        if (fallback) {\n            try {\n                const data = await fallback();\n                if (data !== null && data !== undefined) {\n                    await this.set(key, data, ttl || this.TTL.API);\n                }\n                this.stats.misses++;\n                return data;\n            } catch (error) {\n                console.error(\"Cache fallback error:\", error);\n                this.stats.misses++;\n                return null;\n            }\n        }\n        this.stats.misses++;\n        return null;\n    }\n    // Set cache with automatic tier management\n    async set(key, data) {\n        let ttl = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : this.TTL.API;\n        const item = {\n            data,\n            timestamp: Date.now(),\n            ttl,\n            version: \"1.0\"\n        };\n        // Always set in memory cache\n        this.memoryCache.set(key, item);\n        this.stats.sets++;\n        this.stats.size = this.memoryCache.size;\n        // Set in disk cache for persistence\n        if (this.diskCache && ttl > this.TTL.REALTIME) {\n            try {\n                await this.diskCache.put(\"cache\", {\n                    key,\n                    ...item\n                });\n            } catch (error) {\n                console.warn(\"Disk cache write error:\", error);\n            }\n        }\n        // Memory management - remove oldest items if cache is too large\n        if (this.memoryCache.size > 1000) {\n            this.evictOldest();\n        }\n    }\n    // Delete from all cache tiers\n    async delete(key) {\n        this.memoryCache.delete(key);\n        this.stats.deletes++;\n        this.stats.size = this.memoryCache.size;\n        if (this.diskCache) {\n            try {\n                await this.diskCache.delete(\"cache\", key);\n            } catch (error) {\n                console.warn(\"Disk cache delete error:\", error);\n            }\n        }\n    }\n    // Clear all caches\n    async clear() {\n        this.memoryCache.clear();\n        this.stats = {\n            hits: 0,\n            misses: 0,\n            sets: 0,\n            deletes: 0,\n            size: 0\n        };\n        if (this.diskCache) {\n            try {\n                await this.diskCache.clear(\"cache\");\n            } catch (error) {\n                console.warn(\"Disk cache clear error:\", error);\n            }\n        }\n    }\n    // Cache management\n    evictOldest() {\n        const entries = Array.from(this.memoryCache.entries());\n        entries.sort((a, b)=>a[1].timestamp - b[1].timestamp);\n        // Remove oldest 10% of entries\n        const toRemove = Math.floor(entries.length * 0.1);\n        for(let i = 0; i < toRemove; i++){\n            this.memoryCache.delete(entries[i][0]);\n        }\n    }\n    startCleanupInterval() {\n        if (true) {\n            setInterval(()=>{\n                this.cleanup();\n            }, 5 * 60 * 1000) // Cleanup every 5 minutes\n            ;\n        }\n    }\n    cleanup() {\n        const now = Date.now();\n        for (const [key, item] of this.memoryCache.entries()){\n            if (this.isExpired(item)) {\n                this.memoryCache.delete(key);\n            }\n        }\n        this.stats.size = this.memoryCache.size;\n    }\n    // Specialized cache methods for different data types\n    async cacheUserData(userId, data) {\n        await this.set(\"user:\".concat(userId), data, this.TTL.USER_DATA);\n    }\n    async getUserData(userId) {\n        return await this.get(\"user:\".concat(userId));\n    }\n    async cacheSearchResults(query, results) {\n        const key = \"search:\".concat(btoa(query));\n        await this.set(key, results, this.TTL.SEARCH);\n    }\n    async getSearchResults(query) {\n        const key = \"search:\".concat(btoa(query));\n        return await this.get(key);\n    }\n    async cacheDashboardData(userId, data) {\n        await this.set(\"dashboard:\".concat(userId), data, this.TTL.DASHBOARD);\n    }\n    async getDashboardData(userId) {\n        return await this.get(\"dashboard:\".concat(userId));\n    }\n    async cacheApiResponse(endpoint, params, response) {\n        const key = \"api:\".concat(endpoint, \":\").concat(JSON.stringify(params));\n        await this.set(key, response, this.TTL.API);\n    }\n    async getApiResponse(endpoint, params) {\n        const key = \"api:\".concat(endpoint, \":\").concat(JSON.stringify(params));\n        return await this.get(key);\n    }\n    // Cache statistics\n    getStats() {\n        return {\n            ...this.stats\n        };\n    }\n    getHitRate() {\n        const total = this.stats.hits + this.stats.misses;\n        return total > 0 ? this.stats.hits / total * 100 : 0;\n    }\n    // Preload critical data\n    async preloadCriticalData(userId) {\n        const criticalEndpoints = [\n            \"/api/user/profile\",\n            \"/api/dashboard/stats\",\n            \"/api/notifications\",\n            \"/api/user/plan\"\n        ];\n        const preloadPromises = criticalEndpoints.map(async (endpoint)=>{\n            try {\n                const response = await fetch(endpoint);\n                if (response.ok) {\n                    const data = await response.json();\n                    await this.cacheApiResponse(endpoint, {\n                        userId\n                    }, data);\n                }\n            } catch (error) {\n                console.warn(\"Preload failed for \".concat(endpoint, \":\"), error);\n            }\n        });\n        await Promise.allSettled(preloadPromises);\n    }\n    constructor(){\n        this.memoryCache = new Map();\n        this.diskCache = null;\n        this.stats = {\n            hits: 0,\n            misses: 0,\n            sets: 0,\n            deletes: 0,\n            size: 0\n        };\n        // Cache TTL configurations (in milliseconds)\n        this.TTL = {\n            STATIC: 24 * 60 * 60 * 1000,\n            API: 5 * 60 * 1000,\n            USER_DATA: 15 * 60 * 1000,\n            SEARCH: 10 * 60 * 1000,\n            DASHBOARD: 2 * 60 * 1000,\n            REALTIME: 30 * 1000\n        };\n        this.initDiskCache();\n        this.startCleanupInterval();\n    }\n}\n// Singleton instance\nconst cache = new AdvancedCache();\n// Cache utilities\nconst CacheKeys = {\n    USER_PROFILE: (id)=>\"user:profile:\".concat(id),\n    DASHBOARD_STATS: (id)=>\"dashboard:stats:\".concat(id),\n    SEARCH_RESULTS: (query)=>\"search:\".concat(btoa(query)),\n    API_RESPONSE: (endpoint, params)=>\"api:\".concat(endpoint, \":\").concat(JSON.stringify(params)),\n    STATIC_DATA: (key)=>\"static:\".concat(key),\n    REALTIME_DATA: (key)=>\"realtime:\".concat(key)\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (cache);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi9jYWNoZS50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBLHlDQUF5QztBQUN6QyxxREFBcUQ7QUFpQnJELE1BQU1BO0lBMEJKLE1BQWNDLGdCQUFnQjtRQUM1QixJQUFJLElBQWtCLEVBQWE7WUFDakMsc0NBQXNDO1lBQ3RDLElBQUk7Z0JBQ0YsTUFBTSxFQUFFQyxNQUFNLEVBQUUsR0FBRyxNQUFNLG1OQUFPO2dCQUNoQyxJQUFJLENBQUNDLFNBQVMsR0FBRyxNQUFNRCxPQUFPLG9CQUFvQixHQUFHO29CQUNuREUsU0FBUUMsRUFBRTt3QkFDUixJQUFJLENBQUNBLEdBQUdDLGdCQUFnQixDQUFDQyxRQUFRLENBQUMsVUFBVTs0QkFDMUNGLEdBQUdHLGlCQUFpQixDQUFDLFNBQVM7Z0NBQUVDLFNBQVM7NEJBQU07d0JBQ2pEO29CQUNGO2dCQUNGO1lBQ0YsRUFBRSxPQUFPQyxPQUFPO2dCQUNkQyxRQUFRQyxJQUFJLENBQUM7WUFDZjtRQUNGO0lBQ0Y7SUFFQSwwQkFBMEI7SUFDbEJDLFVBQVVDLElBQW9CLEVBQVc7UUFDL0MsT0FBT0MsS0FBS0MsR0FBRyxLQUFLRixLQUFLRyxTQUFTLEdBQUdILEtBQUtJLEdBQUc7SUFDL0M7SUFFUUMsWUFBWUMsTUFBYyxFQUFFQyxVQUFrQixFQUFVO1FBQzlELE9BQU8sR0FBYUEsT0FBVkQsUUFBTyxLQUFjLE9BQVhDO0lBQ3RCO0lBRUEsK0RBQStEO0lBQy9ELE1BQU1DLElBQU9DLEdBQVcsRUFBRUMsUUFBMkIsRUFBRU4sR0FBWSxFQUFxQjtRQUN0Rix5QkFBeUI7UUFDekIsTUFBTU8sYUFBYSxJQUFJLENBQUNDLFdBQVcsQ0FBQ0osR0FBRyxDQUFDQztRQUN4QyxJQUFJRSxjQUFjLENBQUMsSUFBSSxDQUFDWixTQUFTLENBQUNZLGFBQWE7WUFDN0MsSUFBSSxDQUFDRSxLQUFLLENBQUNDLElBQUk7WUFDZixPQUFPSCxXQUFXSSxJQUFJO1FBQ3hCO1FBRUEsaUJBQWlCO1FBQ2pCLElBQUksSUFBSSxDQUFDMUIsU0FBUyxFQUFFO1lBQ2xCLElBQUk7Z0JBQ0YsTUFBTTJCLFdBQVcsTUFBTSxJQUFJLENBQUMzQixTQUFTLENBQUNtQixHQUFHLENBQUMsU0FBU0M7Z0JBQ25ELElBQUlPLFlBQVksQ0FBQyxJQUFJLENBQUNqQixTQUFTLENBQUNpQixXQUFXO29CQUN6QywwQkFBMEI7b0JBQzFCLElBQUksQ0FBQ0osV0FBVyxDQUFDSyxHQUFHLENBQUNSLEtBQUtPO29CQUMxQixJQUFJLENBQUNILEtBQUssQ0FBQ0MsSUFBSTtvQkFDZixPQUFPRSxTQUFTRCxJQUFJO2dCQUN0QjtZQUNGLEVBQUUsT0FBT25CLE9BQU87Z0JBQ2RDLFFBQVFDLElBQUksQ0FBQywwQkFBMEJGO1lBQ3pDO1FBQ0Y7UUFFQSx3Q0FBd0M7UUFDeEMsSUFBSWMsVUFBVTtZQUNaLElBQUk7Z0JBQ0YsTUFBTUssT0FBTyxNQUFNTDtnQkFDbkIsSUFBSUssU0FBUyxRQUFRQSxTQUFTRyxXQUFXO29CQUN2QyxNQUFNLElBQUksQ0FBQ0QsR0FBRyxDQUFDUixLQUFLTSxNQUFNWCxPQUFPLElBQUksQ0FBQ2UsR0FBRyxDQUFDQyxHQUFHO2dCQUMvQztnQkFDQSxJQUFJLENBQUNQLEtBQUssQ0FBQ1EsTUFBTTtnQkFDakIsT0FBT047WUFDVCxFQUFFLE9BQU9uQixPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMseUJBQXlCQTtnQkFDdkMsSUFBSSxDQUFDaUIsS0FBSyxDQUFDUSxNQUFNO2dCQUNqQixPQUFPO1lBQ1Q7UUFDRjtRQUVBLElBQUksQ0FBQ1IsS0FBSyxDQUFDUSxNQUFNO1FBQ2pCLE9BQU87SUFDVDtJQUVBLDJDQUEyQztJQUMzQyxNQUFNSixJQUFPUixHQUFXLEVBQUVNLElBQU8sRUFBNkM7WUFBM0NYLE1BQUFBLGlFQUFjLElBQUksQ0FBQ2UsR0FBRyxDQUFDQyxHQUFHO1FBQzNELE1BQU1wQixPQUFxQjtZQUN6QmU7WUFDQVosV0FBV0YsS0FBS0MsR0FBRztZQUNuQkU7WUFDQWtCLFNBQVM7UUFDWDtRQUVBLDZCQUE2QjtRQUM3QixJQUFJLENBQUNWLFdBQVcsQ0FBQ0ssR0FBRyxDQUFDUixLQUFLVDtRQUMxQixJQUFJLENBQUNhLEtBQUssQ0FBQ1UsSUFBSTtRQUNmLElBQUksQ0FBQ1YsS0FBSyxDQUFDVyxJQUFJLEdBQUcsSUFBSSxDQUFDWixXQUFXLENBQUNZLElBQUk7UUFFdkMsb0NBQW9DO1FBQ3BDLElBQUksSUFBSSxDQUFDbkMsU0FBUyxJQUFJZSxNQUFNLElBQUksQ0FBQ2UsR0FBRyxDQUFDTSxRQUFRLEVBQUU7WUFDN0MsSUFBSTtnQkFDRixNQUFNLElBQUksQ0FBQ3BDLFNBQVMsQ0FBQ3FDLEdBQUcsQ0FBQyxTQUFTO29CQUFFakI7b0JBQUssR0FBR1QsSUFBSTtnQkFBQztZQUNuRCxFQUFFLE9BQU9KLE9BQU87Z0JBQ2RDLFFBQVFDLElBQUksQ0FBQywyQkFBMkJGO1lBQzFDO1FBQ0Y7UUFFQSxnRUFBZ0U7UUFDaEUsSUFBSSxJQUFJLENBQUNnQixXQUFXLENBQUNZLElBQUksR0FBRyxNQUFNO1lBQ2hDLElBQUksQ0FBQ0csV0FBVztRQUNsQjtJQUNGO0lBRUEsOEJBQThCO0lBQzlCLE1BQU1DLE9BQU9uQixHQUFXLEVBQWlCO1FBQ3ZDLElBQUksQ0FBQ0csV0FBVyxDQUFDZ0IsTUFBTSxDQUFDbkI7UUFDeEIsSUFBSSxDQUFDSSxLQUFLLENBQUNnQixPQUFPO1FBQ2xCLElBQUksQ0FBQ2hCLEtBQUssQ0FBQ1csSUFBSSxHQUFHLElBQUksQ0FBQ1osV0FBVyxDQUFDWSxJQUFJO1FBRXZDLElBQUksSUFBSSxDQUFDbkMsU0FBUyxFQUFFO1lBQ2xCLElBQUk7Z0JBQ0YsTUFBTSxJQUFJLENBQUNBLFNBQVMsQ0FBQ3VDLE1BQU0sQ0FBQyxTQUFTbkI7WUFDdkMsRUFBRSxPQUFPYixPQUFPO2dCQUNkQyxRQUFRQyxJQUFJLENBQUMsNEJBQTRCRjtZQUMzQztRQUNGO0lBQ0Y7SUFFQSxtQkFBbUI7SUFDbkIsTUFBTWtDLFFBQXVCO1FBQzNCLElBQUksQ0FBQ2xCLFdBQVcsQ0FBQ2tCLEtBQUs7UUFDdEIsSUFBSSxDQUFDakIsS0FBSyxHQUFHO1lBQUVDLE1BQU07WUFBR08sUUFBUTtZQUFHRSxNQUFNO1lBQUdNLFNBQVM7WUFBR0wsTUFBTTtRQUFFO1FBRWhFLElBQUksSUFBSSxDQUFDbkMsU0FBUyxFQUFFO1lBQ2xCLElBQUk7Z0JBQ0YsTUFBTSxJQUFJLENBQUNBLFNBQVMsQ0FBQ3lDLEtBQUssQ0FBQztZQUM3QixFQUFFLE9BQU9sQyxPQUFPO2dCQUNkQyxRQUFRQyxJQUFJLENBQUMsMkJBQTJCRjtZQUMxQztRQUNGO0lBQ0Y7SUFFQSxtQkFBbUI7SUFDWCtCLGNBQW9CO1FBQzFCLE1BQU1JLFVBQVVDLE1BQU1DLElBQUksQ0FBQyxJQUFJLENBQUNyQixXQUFXLENBQUNtQixPQUFPO1FBQ25EQSxRQUFRRyxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUQsQ0FBQyxDQUFDLEVBQUUsQ0FBQ2hDLFNBQVMsR0FBR2lDLENBQUMsQ0FBQyxFQUFFLENBQUNqQyxTQUFTO1FBRXRELCtCQUErQjtRQUMvQixNQUFNa0MsV0FBV0MsS0FBS0MsS0FBSyxDQUFDUixRQUFRUyxNQUFNLEdBQUc7UUFDN0MsSUFBSyxJQUFJQyxJQUFJLEdBQUdBLElBQUlKLFVBQVVJLElBQUs7WUFDakMsSUFBSSxDQUFDN0IsV0FBVyxDQUFDZ0IsTUFBTSxDQUFDRyxPQUFPLENBQUNVLEVBQUUsQ0FBQyxFQUFFO1FBQ3ZDO0lBQ0Y7SUFFUUMsdUJBQTZCO1FBQ25DLElBQUksSUFBa0IsRUFBYTtZQUNqQ0MsWUFBWTtnQkFDVixJQUFJLENBQUNDLE9BQU87WUFDZCxHQUFHLElBQUksS0FBSyxNQUFNLDBCQUEwQjs7UUFDOUM7SUFDRjtJQUVRQSxVQUFnQjtRQUN0QixNQUFNMUMsTUFBTUQsS0FBS0MsR0FBRztRQUNwQixLQUFLLE1BQU0sQ0FBQ08sS0FBS1QsS0FBSyxJQUFJLElBQUksQ0FBQ1ksV0FBVyxDQUFDbUIsT0FBTyxHQUFJO1lBQ3BELElBQUksSUFBSSxDQUFDaEMsU0FBUyxDQUFDQyxPQUFPO2dCQUN4QixJQUFJLENBQUNZLFdBQVcsQ0FBQ2dCLE1BQU0sQ0FBQ25CO1lBQzFCO1FBQ0Y7UUFDQSxJQUFJLENBQUNJLEtBQUssQ0FBQ1csSUFBSSxHQUFHLElBQUksQ0FBQ1osV0FBVyxDQUFDWSxJQUFJO0lBQ3pDO0lBRUEscURBQXFEO0lBQ3JELE1BQU1xQixjQUFjQyxNQUFjLEVBQUUvQixJQUFTLEVBQWlCO1FBQzVELE1BQU0sSUFBSSxDQUFDRSxHQUFHLENBQUMsUUFBZSxPQUFQNkIsU0FBVS9CLE1BQU0sSUFBSSxDQUFDSSxHQUFHLENBQUM0QixTQUFTO0lBQzNEO0lBRUEsTUFBTUMsWUFBWUYsTUFBYyxFQUFnQjtRQUM5QyxPQUFPLE1BQU0sSUFBSSxDQUFDdEMsR0FBRyxDQUFDLFFBQWUsT0FBUHNDO0lBQ2hDO0lBRUEsTUFBTUcsbUJBQW1CQyxLQUFhLEVBQUVDLE9BQVksRUFBaUI7UUFDbkUsTUFBTTFDLE1BQU0sVUFBc0IsT0FBWjJDLEtBQUtGO1FBQzNCLE1BQU0sSUFBSSxDQUFDakMsR0FBRyxDQUFDUixLQUFLMEMsU0FBUyxJQUFJLENBQUNoQyxHQUFHLENBQUNrQyxNQUFNO0lBQzlDO0lBRUEsTUFBTUMsaUJBQWlCSixLQUFhLEVBQWdCO1FBQ2xELE1BQU16QyxNQUFNLFVBQXNCLE9BQVoyQyxLQUFLRjtRQUMzQixPQUFPLE1BQU0sSUFBSSxDQUFDMUMsR0FBRyxDQUFDQztJQUN4QjtJQUVBLE1BQU04QyxtQkFBbUJULE1BQWMsRUFBRS9CLElBQVMsRUFBaUI7UUFDakUsTUFBTSxJQUFJLENBQUNFLEdBQUcsQ0FBQyxhQUFvQixPQUFQNkIsU0FBVS9CLE1BQU0sSUFBSSxDQUFDSSxHQUFHLENBQUNxQyxTQUFTO0lBQ2hFO0lBRUEsTUFBTUMsaUJBQWlCWCxNQUFjLEVBQWdCO1FBQ25ELE9BQU8sTUFBTSxJQUFJLENBQUN0QyxHQUFHLENBQUMsYUFBb0IsT0FBUHNDO0lBQ3JDO0lBRUEsTUFBTVksaUJBQWlCQyxRQUFnQixFQUFFQyxNQUFXLEVBQUVDLFFBQWEsRUFBaUI7UUFDbEYsTUFBTXBELE1BQU0sT0FBbUJxRCxPQUFaSCxVQUFTLEtBQTBCLE9BQXZCRyxLQUFLQyxTQUFTLENBQUNIO1FBQzlDLE1BQU0sSUFBSSxDQUFDM0MsR0FBRyxDQUFDUixLQUFLb0QsVUFBVSxJQUFJLENBQUMxQyxHQUFHLENBQUNDLEdBQUc7SUFDNUM7SUFFQSxNQUFNNEMsZUFBZUwsUUFBZ0IsRUFBRUMsTUFBVyxFQUFnQjtRQUNoRSxNQUFNbkQsTUFBTSxPQUFtQnFELE9BQVpILFVBQVMsS0FBMEIsT0FBdkJHLEtBQUtDLFNBQVMsQ0FBQ0g7UUFDOUMsT0FBTyxNQUFNLElBQUksQ0FBQ3BELEdBQUcsQ0FBQ0M7SUFDeEI7SUFFQSxtQkFBbUI7SUFDbkJ3RCxXQUF1QjtRQUNyQixPQUFPO1lBQUUsR0FBRyxJQUFJLENBQUNwRCxLQUFLO1FBQUM7SUFDekI7SUFFQXFELGFBQXFCO1FBQ25CLE1BQU1DLFFBQVEsSUFBSSxDQUFDdEQsS0FBSyxDQUFDQyxJQUFJLEdBQUcsSUFBSSxDQUFDRCxLQUFLLENBQUNRLE1BQU07UUFDakQsT0FBTzhDLFFBQVEsSUFBSSxJQUFLLENBQUN0RCxLQUFLLENBQUNDLElBQUksR0FBR3FELFFBQVMsTUFBTTtJQUN2RDtJQUVBLHdCQUF3QjtJQUN4QixNQUFNQyxvQkFBb0J0QixNQUFjLEVBQWlCO1FBQ3ZELE1BQU11QixvQkFBb0I7WUFDeEI7WUFDQTtZQUNBO1lBQ0E7U0FDRDtRQUVELE1BQU1DLGtCQUFrQkQsa0JBQWtCRSxHQUFHLENBQUMsT0FBT1o7WUFDbkQsSUFBSTtnQkFDRixNQUFNRSxXQUFXLE1BQU1XLE1BQU1iO2dCQUM3QixJQUFJRSxTQUFTWSxFQUFFLEVBQUU7b0JBQ2YsTUFBTTFELE9BQU8sTUFBTThDLFNBQVNhLElBQUk7b0JBQ2hDLE1BQU0sSUFBSSxDQUFDaEIsZ0JBQWdCLENBQUNDLFVBQVU7d0JBQUViO29CQUFPLEdBQUcvQjtnQkFDcEQ7WUFDRixFQUFFLE9BQU9uQixPQUFPO2dCQUNkQyxRQUFRQyxJQUFJLENBQUMsc0JBQStCLE9BQVQ2RCxVQUFTLE1BQUkvRDtZQUNsRDtRQUNGO1FBRUEsTUFBTStFLFFBQVFDLFVBQVUsQ0FBQ047SUFDM0I7SUF6T0FPLGFBQWM7YUFwQk5qRSxjQUFjLElBQUlrRTthQUNsQnpGLFlBQWlCO2FBQ2pCd0IsUUFBb0I7WUFDMUJDLE1BQU07WUFDTk8sUUFBUTtZQUNSRSxNQUFNO1lBQ05NLFNBQVM7WUFDVEwsTUFBTTtRQUNSO1FBRUEsNkNBQTZDO2FBQzVCTCxNQUFNO1lBQ3JCNEQsUUFBUSxLQUFLLEtBQUssS0FBSztZQUN2QjNELEtBQUssSUFBSSxLQUFLO1lBQ2QyQixXQUFXLEtBQUssS0FBSztZQUNyQk0sUUFBUSxLQUFLLEtBQUs7WUFDbEJHLFdBQVcsSUFBSSxLQUFLO1lBQ3BCL0IsVUFBVSxLQUFLO1FBQ2pCO1FBR0UsSUFBSSxDQUFDdEMsYUFBYTtRQUNsQixJQUFJLENBQUN1RCxvQkFBb0I7SUFDM0I7QUF1T0Y7QUFFQSxxQkFBcUI7QUFDZCxNQUFNc0MsUUFBUSxJQUFJOUYsZ0JBQWU7QUFFeEMsa0JBQWtCO0FBQ1gsTUFBTStGLFlBQVk7SUFDdkJDLGNBQWMsQ0FBQ0MsS0FBZSxnQkFBbUIsT0FBSEE7SUFDOUNDLGlCQUFpQixDQUFDRCxLQUFlLG1CQUFzQixPQUFIQTtJQUNwREUsZ0JBQWdCLENBQUNuQyxRQUFrQixVQUFzQixPQUFaRSxLQUFLRjtJQUNsRG9DLGNBQWMsQ0FBQzNCLFVBQWtCQyxTQUFnQixPQUFtQkUsT0FBWkgsVUFBUyxLQUEwQixPQUF2QkcsS0FBS0MsU0FBUyxDQUFDSDtJQUNuRjJCLGFBQWEsQ0FBQzlFLE1BQWdCLFVBQWMsT0FBSkE7SUFDeEMrRSxlQUFlLENBQUMvRSxNQUFnQixZQUFnQixPQUFKQTtBQUM5QyxFQUFVO0FBRVYsK0RBQWV1RSxLQUFLQSxFQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2xpYi9jYWNoZS50cz83Y2JlIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEFkdmFuY2VkIENhY2hpbmcgU3lzdGVtIGZvciBLb2RlWEd1YXJkXG4vLyBGYXN0ZXIgdGhhbiBNY01hc3Rlci1DYXJyIHdpdGggbXVsdGktbGF5ZXIgY2FjaGluZ1xuXG5pbnRlcmZhY2UgQ2FjaGVJdGVtPFQ+IHtcbiAgZGF0YTogVFxuICB0aW1lc3RhbXA6IG51bWJlclxuICB0dGw6IG51bWJlclxuICB2ZXJzaW9uOiBzdHJpbmdcbn1cblxuaW50ZXJmYWNlIENhY2hlU3RhdHMge1xuICBoaXRzOiBudW1iZXJcbiAgbWlzc2VzOiBudW1iZXJcbiAgc2V0czogbnVtYmVyXG4gIGRlbGV0ZXM6IG51bWJlclxuICBzaXplOiBudW1iZXJcbn1cblxuY2xhc3MgQWR2YW5jZWRDYWNoZSB7XG4gIHByaXZhdGUgbWVtb3J5Q2FjaGUgPSBuZXcgTWFwPHN0cmluZywgQ2FjaGVJdGVtPGFueT4+KClcbiAgcHJpdmF0ZSBkaXNrQ2FjaGU6IGFueSA9IG51bGxcbiAgcHJpdmF0ZSBzdGF0czogQ2FjaGVTdGF0cyA9IHtcbiAgICBoaXRzOiAwLFxuICAgIG1pc3NlczogMCxcbiAgICBzZXRzOiAwLFxuICAgIGRlbGV0ZXM6IDAsXG4gICAgc2l6ZTogMFxuICB9XG4gIFxuICAvLyBDYWNoZSBUVEwgY29uZmlndXJhdGlvbnMgKGluIG1pbGxpc2Vjb25kcylcbiAgcHJpdmF0ZSByZWFkb25seSBUVEwgPSB7XG4gICAgU1RBVElDOiAyNCAqIDYwICogNjAgKiAxMDAwLCAgICAgIC8vIDI0IGhvdXJzIGZvciBzdGF0aWMgY29udGVudFxuICAgIEFQSTogNSAqIDYwICogMTAwMCwgICAgICAgICAgICAgICAvLyA1IG1pbnV0ZXMgZm9yIEFQSSByZXNwb25zZXNcbiAgICBVU0VSX0RBVEE6IDE1ICogNjAgKiAxMDAwLCAgICAgICAgLy8gMTUgbWludXRlcyBmb3IgdXNlciBkYXRhXG4gICAgU0VBUkNIOiAxMCAqIDYwICogMTAwMCwgICAgICAgICAgIC8vIDEwIG1pbnV0ZXMgZm9yIHNlYXJjaCByZXN1bHRzXG4gICAgREFTSEJPQVJEOiAyICogNjAgKiAxMDAwLCAgICAgICAgIC8vIDIgbWludXRlcyBmb3IgZGFzaGJvYXJkIGRhdGFcbiAgICBSRUFMVElNRTogMzAgKiAxMDAwLCAgICAgICAgICAgICAgLy8gMzAgc2Vjb25kcyBmb3IgcmVhbC10aW1lIGRhdGFcbiAgfVxuXG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIHRoaXMuaW5pdERpc2tDYWNoZSgpXG4gICAgdGhpcy5zdGFydENsZWFudXBJbnRlcnZhbCgpXG4gIH1cblxuICBwcml2YXRlIGFzeW5jIGluaXREaXNrQ2FjaGUoKSB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAvLyBCcm93c2VyIGVudmlyb25tZW50IC0gdXNlIEluZGV4ZWREQlxuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgeyBvcGVuREIgfSA9IGF3YWl0IGltcG9ydCgnaWRiJylcbiAgICAgICAgdGhpcy5kaXNrQ2FjaGUgPSBhd2FpdCBvcGVuREIoJ2tvZGV4Z3VhcmQtY2FjaGUnLCAxLCB7XG4gICAgICAgICAgdXBncmFkZShkYikge1xuICAgICAgICAgICAgaWYgKCFkYi5vYmplY3RTdG9yZU5hbWVzLmNvbnRhaW5zKCdjYWNoZScpKSB7XG4gICAgICAgICAgICAgIGRiLmNyZWF0ZU9iamVjdFN0b3JlKCdjYWNoZScsIHsga2V5UGF0aDogJ2tleScgfSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9LFxuICAgICAgICB9KVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS53YXJuKCdJbmRleGVkREIgbm90IGF2YWlsYWJsZSwgdXNpbmcgbWVtb3J5IGNhY2hlIG9ubHknKVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8vIE1lbW9yeSBDYWNoZSBPcGVyYXRpb25zXG4gIHByaXZhdGUgaXNFeHBpcmVkKGl0ZW06IENhY2hlSXRlbTxhbnk+KTogYm9vbGVhbiB7XG4gICAgcmV0dXJuIERhdGUubm93KCkgLSBpdGVtLnRpbWVzdGFtcCA+IGl0ZW0udHRsXG4gIH1cblxuICBwcml2YXRlIGdlbmVyYXRlS2V5KHByZWZpeDogc3RyaW5nLCBpZGVudGlmaWVyOiBzdHJpbmcpOiBzdHJpbmcge1xuICAgIHJldHVybiBgJHtwcmVmaXh9OiR7aWRlbnRpZmllcn1gXG4gIH1cblxuICAvLyBHZXQgZnJvbSBjYWNoZSB3aXRoIGZhbGxiYWNrIGNoYWluOiBNZW1vcnkgLT4gRGlzayAtPiBTb3VyY2VcbiAgYXN5bmMgZ2V0PFQ+KGtleTogc3RyaW5nLCBmYWxsYmFjaz86ICgpID0+IFByb21pc2U8VD4sIHR0bD86IG51bWJlcik6IFByb21pc2U8VCB8IG51bGw+IHtcbiAgICAvLyBUcnkgbWVtb3J5IGNhY2hlIGZpcnN0XG4gICAgY29uc3QgbWVtb3J5SXRlbSA9IHRoaXMubWVtb3J5Q2FjaGUuZ2V0KGtleSlcbiAgICBpZiAobWVtb3J5SXRlbSAmJiAhdGhpcy5pc0V4cGlyZWQobWVtb3J5SXRlbSkpIHtcbiAgICAgIHRoaXMuc3RhdHMuaGl0cysrXG4gICAgICByZXR1cm4gbWVtb3J5SXRlbS5kYXRhXG4gICAgfVxuXG4gICAgLy8gVHJ5IGRpc2sgY2FjaGVcbiAgICBpZiAodGhpcy5kaXNrQ2FjaGUpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IGRpc2tJdGVtID0gYXdhaXQgdGhpcy5kaXNrQ2FjaGUuZ2V0KCdjYWNoZScsIGtleSlcbiAgICAgICAgaWYgKGRpc2tJdGVtICYmICF0aGlzLmlzRXhwaXJlZChkaXNrSXRlbSkpIHtcbiAgICAgICAgICAvLyBQcm9tb3RlIHRvIG1lbW9yeSBjYWNoZVxuICAgICAgICAgIHRoaXMubWVtb3J5Q2FjaGUuc2V0KGtleSwgZGlza0l0ZW0pXG4gICAgICAgICAgdGhpcy5zdGF0cy5oaXRzKytcbiAgICAgICAgICByZXR1cm4gZGlza0l0ZW0uZGF0YVxuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLndhcm4oJ0Rpc2sgY2FjaGUgcmVhZCBlcnJvcjonLCBlcnJvcilcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBDYWNoZSBtaXNzIC0gdXNlIGZhbGxiYWNrIGlmIHByb3ZpZGVkXG4gICAgaWYgKGZhbGxiYWNrKSB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgZmFsbGJhY2soKVxuICAgICAgICBpZiAoZGF0YSAhPT0gbnVsbCAmJiBkYXRhICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICBhd2FpdCB0aGlzLnNldChrZXksIGRhdGEsIHR0bCB8fCB0aGlzLlRUTC5BUEkpXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5zdGF0cy5taXNzZXMrK1xuICAgICAgICByZXR1cm4gZGF0YVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignQ2FjaGUgZmFsbGJhY2sgZXJyb3I6JywgZXJyb3IpXG4gICAgICAgIHRoaXMuc3RhdHMubWlzc2VzKytcbiAgICAgICAgcmV0dXJuIG51bGxcbiAgICAgIH1cbiAgICB9XG5cbiAgICB0aGlzLnN0YXRzLm1pc3NlcysrXG4gICAgcmV0dXJuIG51bGxcbiAgfVxuXG4gIC8vIFNldCBjYWNoZSB3aXRoIGF1dG9tYXRpYyB0aWVyIG1hbmFnZW1lbnRcbiAgYXN5bmMgc2V0PFQ+KGtleTogc3RyaW5nLCBkYXRhOiBULCB0dGw6IG51bWJlciA9IHRoaXMuVFRMLkFQSSk6IFByb21pc2U8dm9pZD4ge1xuICAgIGNvbnN0IGl0ZW06IENhY2hlSXRlbTxUPiA9IHtcbiAgICAgIGRhdGEsXG4gICAgICB0aW1lc3RhbXA6IERhdGUubm93KCksXG4gICAgICB0dGwsXG4gICAgICB2ZXJzaW9uOiAnMS4wJ1xuICAgIH1cblxuICAgIC8vIEFsd2F5cyBzZXQgaW4gbWVtb3J5IGNhY2hlXG4gICAgdGhpcy5tZW1vcnlDYWNoZS5zZXQoa2V5LCBpdGVtKVxuICAgIHRoaXMuc3RhdHMuc2V0cysrXG4gICAgdGhpcy5zdGF0cy5zaXplID0gdGhpcy5tZW1vcnlDYWNoZS5zaXplXG5cbiAgICAvLyBTZXQgaW4gZGlzayBjYWNoZSBmb3IgcGVyc2lzdGVuY2VcbiAgICBpZiAodGhpcy5kaXNrQ2FjaGUgJiYgdHRsID4gdGhpcy5UVEwuUkVBTFRJTUUpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGF3YWl0IHRoaXMuZGlza0NhY2hlLnB1dCgnY2FjaGUnLCB7IGtleSwgLi4uaXRlbSB9KVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS53YXJuKCdEaXNrIGNhY2hlIHdyaXRlIGVycm9yOicsIGVycm9yKVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIE1lbW9yeSBtYW5hZ2VtZW50IC0gcmVtb3ZlIG9sZGVzdCBpdGVtcyBpZiBjYWNoZSBpcyB0b28gbGFyZ2VcbiAgICBpZiAodGhpcy5tZW1vcnlDYWNoZS5zaXplID4gMTAwMCkge1xuICAgICAgdGhpcy5ldmljdE9sZGVzdCgpXG4gICAgfVxuICB9XG5cbiAgLy8gRGVsZXRlIGZyb20gYWxsIGNhY2hlIHRpZXJzXG4gIGFzeW5jIGRlbGV0ZShrZXk6IHN0cmluZyk6IFByb21pc2U8dm9pZD4ge1xuICAgIHRoaXMubWVtb3J5Q2FjaGUuZGVsZXRlKGtleSlcbiAgICB0aGlzLnN0YXRzLmRlbGV0ZXMrK1xuICAgIHRoaXMuc3RhdHMuc2l6ZSA9IHRoaXMubWVtb3J5Q2FjaGUuc2l6ZVxuXG4gICAgaWYgKHRoaXMuZGlza0NhY2hlKSB7XG4gICAgICB0cnkge1xuICAgICAgICBhd2FpdCB0aGlzLmRpc2tDYWNoZS5kZWxldGUoJ2NhY2hlJywga2V5KVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS53YXJuKCdEaXNrIGNhY2hlIGRlbGV0ZSBlcnJvcjonLCBlcnJvcilcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvLyBDbGVhciBhbGwgY2FjaGVzXG4gIGFzeW5jIGNsZWFyKCk6IFByb21pc2U8dm9pZD4ge1xuICAgIHRoaXMubWVtb3J5Q2FjaGUuY2xlYXIoKVxuICAgIHRoaXMuc3RhdHMgPSB7IGhpdHM6IDAsIG1pc3NlczogMCwgc2V0czogMCwgZGVsZXRlczogMCwgc2l6ZTogMCB9XG5cbiAgICBpZiAodGhpcy5kaXNrQ2FjaGUpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGF3YWl0IHRoaXMuZGlza0NhY2hlLmNsZWFyKCdjYWNoZScpXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLndhcm4oJ0Rpc2sgY2FjaGUgY2xlYXIgZXJyb3I6JywgZXJyb3IpXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLy8gQ2FjaGUgbWFuYWdlbWVudFxuICBwcml2YXRlIGV2aWN0T2xkZXN0KCk6IHZvaWQge1xuICAgIGNvbnN0IGVudHJpZXMgPSBBcnJheS5mcm9tKHRoaXMubWVtb3J5Q2FjaGUuZW50cmllcygpKVxuICAgIGVudHJpZXMuc29ydCgoYSwgYikgPT4gYVsxXS50aW1lc3RhbXAgLSBiWzFdLnRpbWVzdGFtcClcbiAgICBcbiAgICAvLyBSZW1vdmUgb2xkZXN0IDEwJSBvZiBlbnRyaWVzXG4gICAgY29uc3QgdG9SZW1vdmUgPSBNYXRoLmZsb29yKGVudHJpZXMubGVuZ3RoICogMC4xKVxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdG9SZW1vdmU7IGkrKykge1xuICAgICAgdGhpcy5tZW1vcnlDYWNoZS5kZWxldGUoZW50cmllc1tpXVswXSlcbiAgICB9XG4gIH1cblxuICBwcml2YXRlIHN0YXJ0Q2xlYW51cEludGVydmFsKCk6IHZvaWQge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgICB0aGlzLmNsZWFudXAoKVxuICAgICAgfSwgNSAqIDYwICogMTAwMCkgLy8gQ2xlYW51cCBldmVyeSA1IG1pbnV0ZXNcbiAgICB9XG4gIH1cblxuICBwcml2YXRlIGNsZWFudXAoKTogdm9pZCB7XG4gICAgY29uc3Qgbm93ID0gRGF0ZS5ub3coKVxuICAgIGZvciAoY29uc3QgW2tleSwgaXRlbV0gb2YgdGhpcy5tZW1vcnlDYWNoZS5lbnRyaWVzKCkpIHtcbiAgICAgIGlmICh0aGlzLmlzRXhwaXJlZChpdGVtKSkge1xuICAgICAgICB0aGlzLm1lbW9yeUNhY2hlLmRlbGV0ZShrZXkpXG4gICAgICB9XG4gICAgfVxuICAgIHRoaXMuc3RhdHMuc2l6ZSA9IHRoaXMubWVtb3J5Q2FjaGUuc2l6ZVxuICB9XG5cbiAgLy8gU3BlY2lhbGl6ZWQgY2FjaGUgbWV0aG9kcyBmb3IgZGlmZmVyZW50IGRhdGEgdHlwZXNcbiAgYXN5bmMgY2FjaGVVc2VyRGF0YSh1c2VySWQ6IHN0cmluZywgZGF0YTogYW55KTogUHJvbWlzZTx2b2lkPiB7XG4gICAgYXdhaXQgdGhpcy5zZXQoYHVzZXI6JHt1c2VySWR9YCwgZGF0YSwgdGhpcy5UVEwuVVNFUl9EQVRBKVxuICB9XG5cbiAgYXN5bmMgZ2V0VXNlckRhdGEodXNlcklkOiBzdHJpbmcpOiBQcm9taXNlPGFueT4ge1xuICAgIHJldHVybiBhd2FpdCB0aGlzLmdldChgdXNlcjoke3VzZXJJZH1gKVxuICB9XG5cbiAgYXN5bmMgY2FjaGVTZWFyY2hSZXN1bHRzKHF1ZXJ5OiBzdHJpbmcsIHJlc3VsdHM6IGFueSk6IFByb21pc2U8dm9pZD4ge1xuICAgIGNvbnN0IGtleSA9IGBzZWFyY2g6JHtidG9hKHF1ZXJ5KX1gXG4gICAgYXdhaXQgdGhpcy5zZXQoa2V5LCByZXN1bHRzLCB0aGlzLlRUTC5TRUFSQ0gpXG4gIH1cblxuICBhc3luYyBnZXRTZWFyY2hSZXN1bHRzKHF1ZXJ5OiBzdHJpbmcpOiBQcm9taXNlPGFueT4ge1xuICAgIGNvbnN0IGtleSA9IGBzZWFyY2g6JHtidG9hKHF1ZXJ5KX1gXG4gICAgcmV0dXJuIGF3YWl0IHRoaXMuZ2V0KGtleSlcbiAgfVxuXG4gIGFzeW5jIGNhY2hlRGFzaGJvYXJkRGF0YSh1c2VySWQ6IHN0cmluZywgZGF0YTogYW55KTogUHJvbWlzZTx2b2lkPiB7XG4gICAgYXdhaXQgdGhpcy5zZXQoYGRhc2hib2FyZDoke3VzZXJJZH1gLCBkYXRhLCB0aGlzLlRUTC5EQVNIQk9BUkQpXG4gIH1cblxuICBhc3luYyBnZXREYXNoYm9hcmREYXRhKHVzZXJJZDogc3RyaW5nKTogUHJvbWlzZTxhbnk+IHtcbiAgICByZXR1cm4gYXdhaXQgdGhpcy5nZXQoYGRhc2hib2FyZDoke3VzZXJJZH1gKVxuICB9XG5cbiAgYXN5bmMgY2FjaGVBcGlSZXNwb25zZShlbmRwb2ludDogc3RyaW5nLCBwYXJhbXM6IGFueSwgcmVzcG9uc2U6IGFueSk6IFByb21pc2U8dm9pZD4ge1xuICAgIGNvbnN0IGtleSA9IGBhcGk6JHtlbmRwb2ludH06JHtKU09OLnN0cmluZ2lmeShwYXJhbXMpfWBcbiAgICBhd2FpdCB0aGlzLnNldChrZXksIHJlc3BvbnNlLCB0aGlzLlRUTC5BUEkpXG4gIH1cblxuICBhc3luYyBnZXRBcGlSZXNwb25zZShlbmRwb2ludDogc3RyaW5nLCBwYXJhbXM6IGFueSk6IFByb21pc2U8YW55PiB7XG4gICAgY29uc3Qga2V5ID0gYGFwaToke2VuZHBvaW50fToke0pTT04uc3RyaW5naWZ5KHBhcmFtcyl9YFxuICAgIHJldHVybiBhd2FpdCB0aGlzLmdldChrZXkpXG4gIH1cblxuICAvLyBDYWNoZSBzdGF0aXN0aWNzXG4gIGdldFN0YXRzKCk6IENhY2hlU3RhdHMge1xuICAgIHJldHVybiB7IC4uLnRoaXMuc3RhdHMgfVxuICB9XG5cbiAgZ2V0SGl0UmF0ZSgpOiBudW1iZXIge1xuICAgIGNvbnN0IHRvdGFsID0gdGhpcy5zdGF0cy5oaXRzICsgdGhpcy5zdGF0cy5taXNzZXNcbiAgICByZXR1cm4gdG90YWwgPiAwID8gKHRoaXMuc3RhdHMuaGl0cyAvIHRvdGFsKSAqIDEwMCA6IDBcbiAgfVxuXG4gIC8vIFByZWxvYWQgY3JpdGljYWwgZGF0YVxuICBhc3luYyBwcmVsb2FkQ3JpdGljYWxEYXRhKHVzZXJJZDogc3RyaW5nKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgY29uc3QgY3JpdGljYWxFbmRwb2ludHMgPSBbXG4gICAgICAnL2FwaS91c2VyL3Byb2ZpbGUnLFxuICAgICAgJy9hcGkvZGFzaGJvYXJkL3N0YXRzJyxcbiAgICAgICcvYXBpL25vdGlmaWNhdGlvbnMnLFxuICAgICAgJy9hcGkvdXNlci9wbGFuJ1xuICAgIF1cblxuICAgIGNvbnN0IHByZWxvYWRQcm9taXNlcyA9IGNyaXRpY2FsRW5kcG9pbnRzLm1hcChhc3luYyAoZW5kcG9pbnQpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goZW5kcG9pbnQpXG4gICAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKClcbiAgICAgICAgICBhd2FpdCB0aGlzLmNhY2hlQXBpUmVzcG9uc2UoZW5kcG9pbnQsIHsgdXNlcklkIH0sIGRhdGEpXG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUud2FybihgUHJlbG9hZCBmYWlsZWQgZm9yICR7ZW5kcG9pbnR9OmAsIGVycm9yKVxuICAgICAgfVxuICAgIH0pXG5cbiAgICBhd2FpdCBQcm9taXNlLmFsbFNldHRsZWQocHJlbG9hZFByb21pc2VzKVxuICB9XG59XG5cbi8vIFNpbmdsZXRvbiBpbnN0YW5jZVxuZXhwb3J0IGNvbnN0IGNhY2hlID0gbmV3IEFkdmFuY2VkQ2FjaGUoKVxuXG4vLyBDYWNoZSB1dGlsaXRpZXNcbmV4cG9ydCBjb25zdCBDYWNoZUtleXMgPSB7XG4gIFVTRVJfUFJPRklMRTogKGlkOiBzdHJpbmcpID0+IGB1c2VyOnByb2ZpbGU6JHtpZH1gLFxuICBEQVNIQk9BUkRfU1RBVFM6IChpZDogc3RyaW5nKSA9PiBgZGFzaGJvYXJkOnN0YXRzOiR7aWR9YCxcbiAgU0VBUkNIX1JFU1VMVFM6IChxdWVyeTogc3RyaW5nKSA9PiBgc2VhcmNoOiR7YnRvYShxdWVyeSl9YCxcbiAgQVBJX1JFU1BPTlNFOiAoZW5kcG9pbnQ6IHN0cmluZywgcGFyYW1zOiBhbnkpID0+IGBhcGk6JHtlbmRwb2ludH06JHtKU09OLnN0cmluZ2lmeShwYXJhbXMpfWAsXG4gIFNUQVRJQ19EQVRBOiAoa2V5OiBzdHJpbmcpID0+IGBzdGF0aWM6JHtrZXl9YCxcbiAgUkVBTFRJTUVfREFUQTogKGtleTogc3RyaW5nKSA9PiBgcmVhbHRpbWU6JHtrZXl9YCxcbn0gYXMgY29uc3RcblxuZXhwb3J0IGRlZmF1bHQgY2FjaGVcbiJdLCJuYW1lcyI6WyJBZHZhbmNlZENhY2hlIiwiaW5pdERpc2tDYWNoZSIsIm9wZW5EQiIsImRpc2tDYWNoZSIsInVwZ3JhZGUiLCJkYiIsIm9iamVjdFN0b3JlTmFtZXMiLCJjb250YWlucyIsImNyZWF0ZU9iamVjdFN0b3JlIiwia2V5UGF0aCIsImVycm9yIiwiY29uc29sZSIsIndhcm4iLCJpc0V4cGlyZWQiLCJpdGVtIiwiRGF0ZSIsIm5vdyIsInRpbWVzdGFtcCIsInR0bCIsImdlbmVyYXRlS2V5IiwicHJlZml4IiwiaWRlbnRpZmllciIsImdldCIsImtleSIsImZhbGxiYWNrIiwibWVtb3J5SXRlbSIsIm1lbW9yeUNhY2hlIiwic3RhdHMiLCJoaXRzIiwiZGF0YSIsImRpc2tJdGVtIiwic2V0IiwidW5kZWZpbmVkIiwiVFRMIiwiQVBJIiwibWlzc2VzIiwidmVyc2lvbiIsInNldHMiLCJzaXplIiwiUkVBTFRJTUUiLCJwdXQiLCJldmljdE9sZGVzdCIsImRlbGV0ZSIsImRlbGV0ZXMiLCJjbGVhciIsImVudHJpZXMiLCJBcnJheSIsImZyb20iLCJzb3J0IiwiYSIsImIiLCJ0b1JlbW92ZSIsIk1hdGgiLCJmbG9vciIsImxlbmd0aCIsImkiLCJzdGFydENsZWFudXBJbnRlcnZhbCIsInNldEludGVydmFsIiwiY2xlYW51cCIsImNhY2hlVXNlckRhdGEiLCJ1c2VySWQiLCJVU0VSX0RBVEEiLCJnZXRVc2VyRGF0YSIsImNhY2hlU2VhcmNoUmVzdWx0cyIsInF1ZXJ5IiwicmVzdWx0cyIsImJ0b2EiLCJTRUFSQ0giLCJnZXRTZWFyY2hSZXN1bHRzIiwiY2FjaGVEYXNoYm9hcmREYXRhIiwiREFTSEJPQVJEIiwiZ2V0RGFzaGJvYXJkRGF0YSIsImNhY2hlQXBpUmVzcG9uc2UiLCJlbmRwb2ludCIsInBhcmFtcyIsInJlc3BvbnNlIiwiSlNPTiIsInN0cmluZ2lmeSIsImdldEFwaVJlc3BvbnNlIiwiZ2V0U3RhdHMiLCJnZXRIaXRSYXRlIiwidG90YWwiLCJwcmVsb2FkQ3JpdGljYWxEYXRhIiwiY3JpdGljYWxFbmRwb2ludHMiLCJwcmVsb2FkUHJvbWlzZXMiLCJtYXAiLCJmZXRjaCIsIm9rIiwianNvbiIsIlByb21pc2UiLCJhbGxTZXR0bGVkIiwiY29uc3RydWN0b3IiLCJNYXAiLCJTVEFUSUMiLCJjYWNoZSIsIkNhY2hlS2V5cyIsIlVTRVJfUFJPRklMRSIsImlkIiwiREFTSEJPQVJEX1NUQVRTIiwiU0VBUkNIX1JFU1VMVFMiLCJBUElfUkVTUE9OU0UiLCJTVEFUSUNfREFUQSIsIlJFQUxUSU1FX0RBVEEiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/cache.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/performance.ts":
/*!****************************!*\
  !*** ./lib/performance.ts ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getPerformanceMetrics: function() { return /* binding */ getPerformanceMetrics; },\n/* harmony export */   getPerformanceScore: function() { return /* binding */ getPerformanceScore; },\n/* harmony export */   markRouteChangeComplete: function() { return /* binding */ markRouteChangeComplete; },\n/* harmony export */   performanceMonitor: function() { return /* binding */ performanceMonitor; }\n/* harmony export */ });\n// Advanced Performance Monitoring & Analytics\n// Real-time performance tracking and optimization\nclass PerformanceMonitor {\n    // Initialize performance observers\n    initPerformanceObservers() {\n        if (false) {}\n        // Core Web Vitals Observer\n        this.initWebVitalsObserver();\n        // Navigation Observer\n        this.initNavigationObserver();\n        // Resource Observer\n        this.initResourceObserver();\n        // Long Task Observer\n        this.initLongTaskObserver();\n    }\n    // Web Vitals Observer (FCP, LCP, FID, CLS)\n    initWebVitalsObserver() {\n        if (!(\"PerformanceObserver\" in window)) return;\n        // First Contentful Paint\n        const fcpObserver = new PerformanceObserver((list)=>{\n            const entries = list.getEntries();\n            const fcp = entries[entries.length - 1];\n            this.metrics.firstContentfulPaint = fcp.startTime;\n            this.checkBudget(\"firstContentfulPaint\", fcp.startTime);\n        });\n        fcpObserver.observe({\n            entryTypes: [\n                \"paint\"\n            ]\n        });\n        this.observers.set(\"fcp\", fcpObserver);\n        // Largest Contentful Paint\n        const lcpObserver = new PerformanceObserver((list)=>{\n            const entries = list.getEntries();\n            const lcp = entries[entries.length - 1];\n            this.metrics.largestContentfulPaint = lcp.startTime;\n            this.checkBudget(\"largestContentfulPaint\", lcp.startTime);\n        });\n        lcpObserver.observe({\n            entryTypes: [\n                \"largest-contentful-paint\"\n            ]\n        });\n        this.observers.set(\"lcp\", lcpObserver);\n        // First Input Delay\n        const fidObserver = new PerformanceObserver((list)=>{\n            const entries = list.getEntries();\n            entries.forEach((entry)=>{\n                this.metrics.firstInputDelay = entry.processingStart - entry.startTime;\n                this.checkBudget(\"firstInputDelay\", this.metrics.firstInputDelay);\n            });\n        });\n        fidObserver.observe({\n            entryTypes: [\n                \"first-input\"\n            ]\n        });\n        this.observers.set(\"fid\", fidObserver);\n        // Cumulative Layout Shift\n        let clsValue = 0;\n        const clsObserver = new PerformanceObserver((list)=>{\n            const entries = list.getEntries();\n            entries.forEach((entry)=>{\n                if (!entry.hadRecentInput) {\n                    clsValue += entry.value;\n                }\n            });\n            this.metrics.cumulativeLayoutShift = clsValue;\n            this.checkBudget(\"cumulativeLayoutShift\", clsValue);\n        });\n        clsObserver.observe({\n            entryTypes: [\n                \"layout-shift\"\n            ]\n        });\n        this.observers.set(\"cls\", clsObserver);\n    }\n    // Navigation Observer\n    initNavigationObserver() {\n        if (!(\"PerformanceObserver\" in window)) return;\n        const navObserver = new PerformanceObserver((list)=>{\n            const entries = list.getEntries();\n            entries.forEach((entry)=>{\n                this.metrics.pageLoadTime = entry.loadEventEnd - entry.fetchStart;\n                this.metrics.timeToInteractive = entry.domInteractive - entry.fetchStart;\n                this.checkBudget(\"pageLoadTime\", this.metrics.pageLoadTime);\n                this.checkBudget(\"timeToInteractive\", this.metrics.timeToInteractive);\n            });\n        });\n        navObserver.observe({\n            entryTypes: [\n                \"navigation\"\n            ]\n        });\n        this.observers.set(\"navigation\", navObserver);\n    }\n    // Resource Observer\n    initResourceObserver() {\n        if (!(\"PerformanceObserver\" in window)) return;\n        const resourceObserver = new PerformanceObserver((list)=>{\n            const entries = list.getEntries();\n            entries.forEach((entry)=>{\n                // Track slow resources\n                if (entry.duration > 1000) {\n                    console.warn(\"Slow resource detected: \".concat(entry.name, \" (\").concat(entry.duration, \"ms)\"));\n                }\n                // Track API response times\n                if (entry.name.includes(\"/api/\")) {\n                    this.metrics.apiResponseTime = entry.duration;\n                }\n            });\n        });\n        resourceObserver.observe({\n            entryTypes: [\n                \"resource\"\n            ]\n        });\n        this.observers.set(\"resource\", resourceObserver);\n    }\n    // Long Task Observer\n    initLongTaskObserver() {\n        if (!(\"PerformanceObserver\" in window)) return;\n        let totalBlockingTime = 0;\n        const longTaskObserver = new PerformanceObserver((list)=>{\n            const entries = list.getEntries();\n            entries.forEach((entry)=>{\n                // Tasks longer than 50ms are considered blocking\n                if (entry.duration > 50) {\n                    totalBlockingTime += entry.duration - 50;\n                }\n            });\n            this.metrics.totalBlockingTime = totalBlockingTime;\n        });\n        longTaskObserver.observe({\n            entryTypes: [\n                \"longtask\"\n            ]\n        });\n        this.observers.set(\"longtask\", longTaskObserver);\n    }\n    // Track page load performance\n    trackPageLoad() {\n        if (false) {}\n        window.addEventListener(\"load\", ()=>{\n            // Use setTimeout to ensure all metrics are captured\n            setTimeout(()=>{\n                this.calculatePerformanceScore();\n                this.reportMetrics();\n            }, 1000);\n        });\n    }\n    // Track route changes (for SPA navigation)\n    trackRouteChanges() {\n        var _this = this;\n        if (false) {}\n        // Track route change start\n        const originalPushState = history.pushState;\n        const originalReplaceState = history.replaceState;\n        history.pushState = function() {\n            for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n                args[_key] = arguments[_key];\n            }\n            _this.routeStartTime = performance.now();\n            return originalPushState.apply(history, args);\n        };\n        history.replaceState = function() {\n            for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n                args[_key] = arguments[_key];\n            }\n            _this.routeStartTime = performance.now();\n            return originalReplaceState.apply(history, args);\n        };\n        // Track route change end\n        window.addEventListener(\"popstate\", ()=>{\n            this.routeStartTime = performance.now();\n        });\n    }\n    // Mark route change complete\n    markRouteChangeComplete() {\n        if (this.routeStartTime > 0) {\n            this.metrics.routeChangeTime = performance.now() - this.routeStartTime;\n            this.routeStartTime = 0;\n            // Log slow route changes\n            if (this.metrics.routeChangeTime > 500) {\n                console.warn(\"Slow route change: \".concat(this.metrics.routeChangeTime, \"ms\"));\n            }\n        }\n    }\n    // Check performance budget\n    checkBudget(metric, value) {\n        const budgetValue = this.budget[metric];\n        if (value > budgetValue) {\n            console.warn(\"Performance budget exceeded for \".concat(metric, \": \").concat(value, \"ms > \").concat(budgetValue, \"ms\"));\n            // Trigger optimization suggestions\n            this.suggestOptimizations(metric, value, budgetValue);\n        }\n    }\n    // Suggest optimizations based on metrics\n    suggestOptimizations(metric, actual, budget) {\n        const suggestions = {\n            pageLoadTime: [\n                \"Enable compression (gzip/brotli)\",\n                \"Optimize images and use WebP format\",\n                \"Implement code splitting\",\n                \"Use CDN for static assets\"\n            ],\n            firstContentfulPaint: [\n                \"Optimize critical CSS\",\n                \"Preload key resources\",\n                \"Reduce server response time\",\n                \"Eliminate render-blocking resources\"\n            ],\n            largestContentfulPaint: [\n                \"Optimize images above the fold\",\n                \"Preload LCP element\",\n                \"Reduce server response time\",\n                \"Remove unused CSS\"\n            ],\n            firstInputDelay: [\n                \"Reduce JavaScript execution time\",\n                \"Break up long tasks\",\n                \"Use web workers for heavy computations\",\n                \"Optimize third-party scripts\"\n            ],\n            cumulativeLayoutShift: [\n                \"Set size attributes on images and videos\",\n                \"Reserve space for ads and embeds\",\n                \"Avoid inserting content above existing content\",\n                \"Use CSS aspect-ratio for responsive images\"\n            ]\n        };\n        const metricSuggestions = suggestions[metric] || [];\n        console.group(\"\\uD83D\\uDE80 Performance Optimization Suggestions for \".concat(metric));\n        console.log(\"Current: \".concat(actual, \"ms, Budget: \").concat(budget, \"ms\"));\n        metricSuggestions.forEach((suggestion)=>console.log(\"• \".concat(suggestion)));\n        console.groupEnd();\n    }\n    // Calculate overall performance score\n    calculatePerformanceScore() {\n        const weights = {\n            firstContentfulPaint: 0.15,\n            largestContentfulPaint: 0.25,\n            firstInputDelay: 0.25,\n            cumulativeLayoutShift: 0.25,\n            totalBlockingTime: 0.1\n        };\n        let score = 100;\n        Object.entries(weights).forEach((param)=>{\n            let [metric, weight] = param;\n            const value = this.metrics[metric];\n            const budget = this.budget[metric];\n            if (value && budget) {\n                const ratio = value / budget;\n                if (ratio > 1) {\n                    score -= (ratio - 1) * weight * 100;\n                }\n            }\n        });\n        return Math.max(0, Math.min(100, score));\n    }\n    // Report metrics to analytics\n    reportMetrics() {\n        const score = this.calculatePerformanceScore();\n        console.group(\"\\uD83D\\uDCCA Performance Metrics Report\");\n        console.log(\"Overall Score:\", \"\".concat(score.toFixed(1), \"/100\"));\n        console.log(\"Metrics:\", this.metrics);\n        console.log(\"Cache Hit Rate:\", this.getCacheHitRate());\n        console.groupEnd();\n        // Send to analytics service (implement as needed)\n        this.sendToAnalytics({\n            ...this.metrics,\n            performanceScore: score,\n            timestamp: Date.now(),\n            userAgent: navigator.userAgent,\n            url: window.location.href\n        });\n    }\n    // Get cache hit rate from cache system\n    getCacheHitRate() {\n        // This would integrate with your cache system\n        return 0 // Placeholder\n        ;\n    }\n    // Send metrics to analytics service\n    sendToAnalytics(data) {\n        // Implement analytics reporting\n        // Could be Google Analytics, custom analytics, etc.\n        if (true) {\n            console.log(\"\\uD83D\\uDCC8 Analytics Data:\", data);\n        }\n    }\n    // Get current metrics\n    getMetrics() {\n        return {\n            ...this.metrics\n        };\n    }\n    // Get performance score\n    getPerformanceScore() {\n        return this.calculatePerformanceScore();\n    }\n    // Update performance budget\n    updateBudget(newBudget) {\n        this.budget = {\n            ...this.budget,\n            ...newBudget\n        };\n    }\n    // Cleanup observers\n    cleanup() {\n        this.observers.forEach((observer)=>observer.disconnect());\n        this.observers.clear();\n    }\n    constructor(){\n        this.metrics = {};\n        this.budget = {\n            pageLoadTime: 2000,\n            firstContentfulPaint: 1000,\n            largestContentfulPaint: 2500,\n            firstInputDelay: 100,\n            cumulativeLayoutShift: 0.1,\n            timeToInteractive: 3000 // 3 seconds\n        };\n        this.observers = new Map();\n        this.routeStartTime = 0;\n        this.initPerformanceObservers();\n        this.trackPageLoad();\n        this.trackRouteChanges();\n    }\n}\n// Singleton instance\nconst performanceMonitor = new PerformanceMonitor();\n// Utility functions\nconst markRouteChangeComplete = ()=>{\n    performanceMonitor.markRouteChangeComplete();\n};\nconst getPerformanceMetrics = ()=>{\n    return performanceMonitor.getMetrics();\n};\nconst getPerformanceScore = ()=>{\n    return performanceMonitor.getPerformanceScore();\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (performanceMonitor);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/performance.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/preloader.ts":
/*!**************************!*\
  !*** ./lib/preloader.ts ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   observeLinks: function() { return /* binding */ observeLinks; },\n/* harmony export */   preloadRoute: function() { return /* binding */ preloadRoute; },\n/* harmony export */   preloadUserJourney: function() { return /* binding */ preloadUserJourney; },\n/* harmony export */   preloader: function() { return /* binding */ preloader; }\n/* harmony export */ });\n/* harmony import */ var _cache__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cache */ \"(app-pages-browser)/./lib/cache.ts\");\n// Advanced Pre-rendering & Route Prefetching System\n// McMaster-Carr level performance optimization\n\nclass AdvancedPreloader {\n    // Initialize intersection observer for link prefetching\n    initIntersectionObserver() {\n        if (false) {}\n        this.observer = new IntersectionObserver((entries)=>{\n            entries.forEach((entry)=>{\n                if (entry.isIntersecting) {\n                    const link = entry.target;\n                    const href = link.getAttribute(\"href\");\n                    if (href && !this.preloadedRoutes.has(href)) {\n                        this.preloadRoute(href, \"low\");\n                    }\n                }\n            });\n        }, {\n            rootMargin: \"100px\",\n            threshold: 0.1\n        });\n    }\n    // Initialize route preloading based on current page\n    initRoutePreloading() {\n        if (false) {}\n        // Preload critical routes immediately\n        this.preloadCriticalRoutes();\n        // Set up hover preloading\n        this.setupHoverPreloading();\n        // Preload based on user behavior patterns\n        this.setupBehaviorBasedPreloading();\n    }\n    // Preload critical routes immediately\n    async preloadCriticalRoutes() {\n        const currentPath = window.location.pathname;\n        const strategy = this.ROUTE_STRATEGIES.find((s)=>s.route === currentPath);\n        if (strategy) {\n            // Preload dependencies of current route\n            for (const dep of strategy.dependencies){\n                await this.preloadRoute(dep, \"high\");\n            }\n            // Preload data endpoints\n            for (const endpoint of strategy.dataEndpoints){\n                await this.preloadData(endpoint);\n            }\n        }\n        // Always preload dashboard if not current page\n        if (currentPath !== \"/dashboard\") {\n            await this.preloadRoute(\"/dashboard\", \"medium\");\n        }\n    }\n    // Setup hover-based preloading\n    setupHoverPreloading() {\n        if (false) {}\n        document.addEventListener(\"mouseover\", (event)=>{\n            const target = event.target;\n            const link = target.closest(\"a[href]\");\n            if (link && link.href) {\n                const href = new URL(link.href).pathname;\n                if (!this.preloadedRoutes.has(href)) {\n                    // Debounce hover preloading\n                    setTimeout(()=>{\n                        this.preloadRoute(href, \"medium\");\n                    }, 100);\n                }\n            }\n        });\n    }\n    // Setup behavior-based preloading\n    setupBehaviorBasedPreloading() {\n        if (false) {}\n        // Preload based on user's typical navigation patterns\n        const userBehavior = this.getUserBehaviorPattern();\n        // Preload likely next routes based on current page\n        const currentPath = window.location.pathname;\n        const likelyNext = this.predictNextRoutes(currentPath, userBehavior);\n        likelyNext.forEach((route, index)=>{\n            setTimeout(()=>{\n                this.preloadRoute(route, \"low\");\n            }, index * 1000) // Stagger preloading\n            ;\n        });\n    }\n    // Predict next routes based on current page and user behavior\n    predictNextRoutes(currentPath, behavior) {\n        const predictions = {\n            \"/dashboard\": [\n                \"/osint\",\n                \"/scanner\",\n                \"/profile\"\n            ],\n            \"/osint\": [\n                \"/file-analyzer\",\n                \"/cve\",\n                \"/dashboard\"\n            ],\n            \"/scanner\": [\n                \"/cve\",\n                \"/tools\",\n                \"/dashboard\"\n            ],\n            \"/profile\": [\n                \"/settings\",\n                \"/plan\",\n                \"/dashboard\"\n            ],\n            \"/\": [\n                \"/dashboard\",\n                \"/login\",\n                \"/register\"\n            ]\n        };\n        return predictions[currentPath] || [];\n    }\n    // Get user behavior pattern from localStorage\n    getUserBehaviorPattern() {\n        if (false) {}\n        try {\n            const stored = localStorage.getItem(\"kodexguard_user_behavior\");\n            return stored ? JSON.parse(stored) : {};\n        } catch (e) {\n            return {};\n        }\n    }\n    // Main preload route method\n    async preloadRoute(route) {\n        let priority = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"medium\";\n        if (this.preloadedRoutes.has(route)) return;\n        try {\n            var _window_next;\n            // Use Next.js router prefetch\n            if ( true && ((_window_next = window.next) === null || _window_next === void 0 ? void 0 : _window_next.router)) {\n                await window.next.router.prefetch(route);\n            }\n            // Preload route data\n            const strategy = this.ROUTE_STRATEGIES.find((s)=>s.route === route);\n            if (strategy) {\n                // Preload data endpoints\n                const dataPromises = strategy.dataEndpoints.map((endpoint)=>this.preloadData(endpoint));\n                await Promise.allSettled(dataPromises);\n            }\n            this.preloadedRoutes.add(route);\n            console.log(\"✅ Preloaded route: \".concat(route));\n        } catch (error) {\n            console.warn(\"❌ Failed to preload route \".concat(route, \":\"), error);\n        }\n    }\n    // Preload data for endpoints\n    async preloadData(endpoint) {\n        try {\n            // Check if already cached\n            const cached = await _cache__WEBPACK_IMPORTED_MODULE_0__.cache.get(endpoint);\n            if (cached) return;\n            // Fetch and cache data\n            const response = await fetch(endpoint, {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                await _cache__WEBPACK_IMPORTED_MODULE_0__.cache.set(endpoint, data);\n                console.log(\"\\uD83D\\uDCE6 Preloaded data: \".concat(endpoint));\n            }\n        } catch (error) {\n            console.warn(\"❌ Failed to preload data \".concat(endpoint, \":\"), error);\n        }\n    }\n    // Preload critical assets\n    async preloadCriticalAssets() {\n        const criticalAssets = [\n            \"/images/logo.svg\",\n            \"/images/cyber-bg.jpg\"\n        ];\n        const preloadPromises = criticalAssets.map((asset)=>{\n            return new Promise((resolve)=>{\n                const link = document.createElement(\"link\");\n                link.rel = \"preload\";\n                link.href = asset;\n                link.as = asset.endsWith(\".svg\") ? \"image\" : \"image\";\n                link.onload = ()=>resolve();\n                link.onerror = ()=>resolve() // Don't fail on asset errors\n                ;\n                document.head.appendChild(link);\n            });\n        });\n        await Promise.allSettled(preloadPromises);\n    }\n    // Observe links for intersection-based preloading\n    observeLinks() {\n        if (!this.observer) return;\n        const links = document.querySelectorAll('a[href^=\"/\"]');\n        links.forEach((link)=>{\n            this.observer.observe(link);\n        });\n    }\n    // Preload entire user journey\n    async preloadUserJourney(userRole, userPlan) {\n        const journeyRoutes = this.getUserJourneyRoutes(userRole, userPlan);\n        for (const [index, route] of journeyRoutes.entries()){\n            // Stagger preloading to avoid overwhelming the browser\n            setTimeout(()=>{\n                this.preloadRoute(route, index < 3 ? \"high\" : \"low\");\n            }, index * 500);\n        }\n    }\n    // Get user journey routes based on role and plan\n    getUserJourneyRoutes(userRole, userPlan) {\n        const baseJourney = [\n            \"/dashboard\",\n            \"/profile\",\n            \"/settings\"\n        ];\n        if (userPlan === \"cybersecurity\" || userPlan === \"bughunter\") {\n            return [\n                ...baseJourney,\n                \"/osint\",\n                \"/scanner\",\n                \"/file-analyzer\",\n                \"/cve\",\n                \"/playground\",\n                \"/bot\"\n            ];\n        }\n        return [\n            ...baseJourney,\n            \"/osint\",\n            \"/file-analyzer\",\n            \"/cve\",\n            \"/tools\"\n        ];\n    }\n    // Get preloading statistics\n    getStats() {\n        return {\n            preloadedRoutes: Array.from(this.preloadedRoutes),\n            queueSize: this.preloadQueue.size,\n            isPreloading: this.isPreloading,\n            cacheStats: _cache__WEBPACK_IMPORTED_MODULE_0__.cache.getStats()\n        };\n    }\n    // Clear preloaded routes\n    clearPreloaded() {\n        this.preloadedRoutes.clear();\n        this.preloadQueue.clear();\n    }\n    constructor(){\n        this.preloadQueue = new Map();\n        this.preloadedRoutes = new Set();\n        this.isPreloading = false;\n        this.observer = null;\n        // Route strategies for intelligent preloading\n        this.ROUTE_STRATEGIES = [\n            {\n                route: \"/dashboard\",\n                dependencies: [\n                    \"/osint\",\n                    \"/scanner\",\n                    \"/profile\"\n                ],\n                dataEndpoints: [\n                    \"/api/dashboard/stats\",\n                    \"/api/user/profile\",\n                    \"/api/notifications\"\n                ],\n                assets: [],\n                priority: 1\n            },\n            {\n                route: \"/osint\",\n                dependencies: [\n                    \"/file-analyzer\",\n                    \"/cve\"\n                ],\n                dataEndpoints: [\n                    \"/api/osint/recent\",\n                    \"/api/osint/templates\"\n                ],\n                assets: [],\n                priority: 2\n            },\n            {\n                route: \"/scanner\",\n                dependencies: [\n                    \"/cve\",\n                    \"/tools\"\n                ],\n                dataEndpoints: [\n                    \"/api/scanner/history\",\n                    \"/api/scanner/templates\"\n                ],\n                assets: [],\n                priority: 2\n            },\n            {\n                route: \"/profile\",\n                dependencies: [\n                    \"/settings\",\n                    \"/plan\"\n                ],\n                dataEndpoints: [\n                    \"/api/user/profile\",\n                    \"/api/user/api-keys\",\n                    \"/api/user/usage\"\n                ],\n                assets: [],\n                priority: 3\n            },\n            {\n                route: \"/settings\",\n                dependencies: [],\n                dataEndpoints: [\n                    \"/api/user/settings\",\n                    \"/api/user/preferences\"\n                ],\n                assets: [],\n                priority: 4\n            }\n        ];\n        this.initIntersectionObserver();\n        this.initRoutePreloading();\n    }\n}\n// Singleton instance\nconst preloader = new AdvancedPreloader();\n// Utility functions\nconst preloadRoute = (route, priority)=>{\n    return preloader.preloadRoute(route, priority);\n};\nconst preloadUserJourney = (userRole, userPlan)=>{\n    return preloader.preloadUserJourney(userRole, userPlan);\n};\nconst observeLinks = ()=>{\n    preloader.observeLinks();\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (preloader);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/preloader.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/app-dynamic.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/app-dynamic.js ***!
  \**********************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return dynamic;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _loadable = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./lazy-dynamic/loadable */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js\"));\nfunction dynamic(dynamicOptions, options) {\n    var _mergedOptions_loadableGenerated;\n    let loadableOptions = {\n        // A loading component is not required, so we default it\n        loading: (param)=>{\n            let { error, isLoading, pastDelay } = param;\n            if (!pastDelay) return null;\n            if (true) {\n                if (isLoading) {\n                    return null;\n                }\n                if (error) {\n                    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                        children: [\n                            error.message,\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"br\", {}),\n                            error.stack\n                        ]\n                    });\n                }\n            }\n            return null;\n        }\n    };\n    if (typeof dynamicOptions === \"function\") {\n        loadableOptions.loader = dynamicOptions;\n    }\n    const mergedOptions = {\n        ...loadableOptions,\n        ...options\n    };\n    return (0, _loadable.default)({\n        ...mergedOptions,\n        modules: (_mergedOptions_loadableGenerated = mergedOptions.loadableGenerated) == null ? void 0 : _mergedOptions_loadableGenerated.modules\n    });\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-dynamic.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9hcHAtZHluYW1pYy5qcyIsIm1hcHBpbmdzIjoiOzs7OzJDQWlDQTs7O2VBQXdCQTs7Ozs7NEVBakNOOytFQUNHO0FBZ0NOLFNBQVNBLFFBQ3RCQyxjQUE2QyxFQUM3Q0MsT0FBMkI7UUFtQ2hCQztJQWpDWCxJQUFJQyxrQkFBc0M7UUFDeEMsd0RBQXdEO1FBQ3hEQyxTQUFTLENBQUFDO2dCQUFDLEVBQUVDLEtBQUssRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQUUsR0FBQUg7WUFDdkMsSUFBSSxDQUFDRyxXQUFXLE9BQU87WUFDdkIsSUFBSUMsSUFBeUIsRUFBYztnQkFDekMsSUFBSUYsV0FBVztvQkFDYixPQUFPO2dCQUNUO2dCQUNBLElBQUlELE9BQU87b0JBQ1QsT0FDRSxXQURGLEdBQ0UsSUFBQUksWUFBQUMsSUFBQSxFQUFDQyxLQUFBQTs7NEJBQ0VOLE1BQU1PLE9BQU87MENBQ2QsSUFBQUgsWUFBQUksR0FBQSxFQUFDQyxNQUFBQSxDQUFBQTs0QkFDQVQsTUFBTVUsS0FBSzs7O2dCQUdsQjtZQUNGO1lBQ0EsT0FBTztRQUNUO0lBQ0Y7SUFFQSxJQUFJLE9BQU9oQixtQkFBbUIsWUFBWTtRQUN4Q0csZ0JBQWdCYyxNQUFNLEdBQUdqQjtJQUMzQjtJQUVBLE1BQU1FLGdCQUFnQjtRQUNwQixHQUFHQyxlQUFlO1FBQ2xCLEdBQUdGLE9BQU87SUFDWjtJQUVBLE9BQU9pQixDQUFBQSxHQUFBQSxVQUFBQSxPQUFRLEVBQUM7UUFDZCxHQUFHaEIsYUFBYTtRQUNoQmlCLFNBQU8sQ0FBRWpCLG1DQUFBQSxjQUFja0IsaUJBQWlCLHFCQUEvQmxCLGlDQUFpQ2lCLE9BQU87SUFDbkQ7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vLi4vc3JjL3NoYXJlZC9saWIvYXBwLWR5bmFtaWMudHN4P2M1NjUiXSwibmFtZXMiOlsiZHluYW1pYyIsImR5bmFtaWNPcHRpb25zIiwib3B0aW9ucyIsIm1lcmdlZE9wdGlvbnMiLCJsb2FkYWJsZU9wdGlvbnMiLCJsb2FkaW5nIiwicGFyYW0iLCJlcnJvciIsImlzTG9hZGluZyIsInBhc3REZWxheSIsInByb2Nlc3MiLCJfanN4cnVudGltZSIsImpzeHMiLCJwIiwibWVzc2FnZSIsImpzeCIsImJyIiwic3RhY2siLCJsb2FkZXIiLCJMb2FkYWJsZSIsIm1vZHVsZXMiLCJsb2FkYWJsZUdlbmVyYXRlZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/app-dynamic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js ***!
  \**********************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"BailoutToCSR\", ({\n    enumerable: true,\n    get: function() {\n        return BailoutToCSR;\n    }\n}));\nconst _bailouttocsr = __webpack_require__(/*! ./bailout-to-csr */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js\");\nfunction BailoutToCSR(param) {\n    let { reason, children } = param;\n    if (typeof window === \"undefined\") {\n        throw new _bailouttocsr.BailoutToCSRError(reason);\n    }\n    return children;\n} //# sourceMappingURL=dynamic-bailout-to-csr.js.map\n_c = BailoutToCSR;\nvar _c;\n$RefreshReg$(_c, \"BailoutToCSR\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9sYXp5LWR5bmFtaWMvZHluYW1pYy1iYWlsb3V0LXRvLWNzci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBY08sTUFBQUEsZ0JBQXNCQyxtQkFBQUEsQ0FBdUM7U0FBdkNDLGFBQVVDLEtBQVE7SUFDN0MsSUFBSSxFQUFBQyxNQUFPQyxFQUFBQSxRQUFXLEtBQUFDO1FBQ3BCLE9BQU1ELFdBQUlFLGFBQUFBO1FBQ1osVUFBQVAsY0FBQU8saUJBQUEsQ0FBQUg7SUFFQTtJQUNGLE9BQUFEOztLQU42QkQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uLy4uL3NyYy9zaGFyZWQvbGliL2xhenktZHluYW1pYy9keW5hbWljLWJhaWxvdXQtdG8tY3NyLnRzeD9lMzQwIl0sIm5hbWVzIjpbIl9iYWlsb3V0dG9jc3IiLCJyZXF1aXJlIiwiQmFpbG91dFRvQ1NSIiwiY2hpbGRyZW4iLCJyZWFzb24iLCJ3aW5kb3ciLCJwYXJhbSIsIkJhaWxvdXRUb0NTUkVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js":
/*!********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js ***!
  \********************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _dynamicbailouttocsr = __webpack_require__(/*! ./dynamic-bailout-to-csr */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js\");\nconst _preloadcss = __webpack_require__(/*! ./preload-css */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js\");\n// Normalize loader to return the module as form { default: Component } for `React.lazy`.\n// Also for backward compatible since next/dynamic allows to resolve a component directly with loader\n// Client component reference proxy need to be converted to a module.\nfunction convertModule(mod) {\n    // Check \"default\" prop before accessing it, as it could be client reference proxy that could break it reference.\n    // Cases:\n    // mod: { default: Component }\n    // mod: Component\n    // mod: { $$typeof, default: proxy(Component) }\n    // mod: proxy(Component)\n    const hasDefault = mod && \"default\" in mod;\n    return {\n        default: hasDefault ? mod.default : mod\n    };\n}\nconst defaultOptions = {\n    loader: ()=>Promise.resolve(convertModule(()=>null)),\n    loading: null,\n    ssr: true\n};\nfunction Loadable(options) {\n    const opts = {\n        ...defaultOptions,\n        ...options\n    };\n    const Lazy = /*#__PURE__*/ (0, _react.lazy)(()=>opts.loader().then(convertModule));\n    const Loading = opts.loading;\n    function LoadableComponent(props) {\n        const fallbackElement = Loading ? /*#__PURE__*/ (0, _jsxruntime.jsx)(Loading, {\n            isLoading: true,\n            pastDelay: true,\n            error: null\n        }) : null;\n        const children = opts.ssr ? /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n            children: [\n                typeof window === \"undefined\" ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_preloadcss.PreloadCss, {\n                    moduleIds: opts.modules\n                }) : null,\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(Lazy, {\n                    ...props\n                })\n            ]\n        }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_dynamicbailouttocsr.BailoutToCSR, {\n            reason: \"next/dynamic\",\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Lazy, {\n                ...props\n            })\n        });\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.Suspense, {\n            fallback: fallbackElement,\n            children: children\n        });\n    }\n    LoadableComponent.displayName = \"LoadableComponent\";\n    return LoadableComponent;\n}\n_c = Loadable;\nconst _default = Loadable; //# sourceMappingURL=loadable.js.map\nvar _c;\n$RefreshReg$(_c, \"Loadable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js ***!
  \***********************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"PreloadCss\", ({\n    enumerable: true,\n    get: function() {\n        return PreloadCss;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _requestasyncstorageexternal = __webpack_require__(/*! ../../../client/components/request-async-storage.external */ \"(shared)/./node_modules/next/dist/client/components/request-async-storage.external.js\");\nfunction PreloadCss(param) {\n    let { moduleIds } = param;\n    // Early return in client compilation and only load requestStore on server side\n    if (typeof window !== \"undefined\") {\n        return null;\n    }\n    const requestStore = (0, _requestasyncstorageexternal.getExpectedRequestStore)(\"next/dynamic css\");\n    const allFiles = [];\n    // Search the current dynamic call unique key id in react loadable manifest,\n    // and find the corresponding CSS files to preload\n    if (requestStore.reactLoadableManifest && moduleIds) {\n        const manifest = requestStore.reactLoadableManifest;\n        for (const key of moduleIds){\n            if (!manifest[key]) continue;\n            const cssFiles = manifest[key].files.filter((file)=>file.endsWith(\".css\"));\n            allFiles.push(...cssFiles);\n        }\n    }\n    if (allFiles.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: allFiles.map((file)=>{\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                // @ts-ignore\n                precedence: \"dynamic\",\n                rel: \"stylesheet\",\n                href: requestStore.assetPrefix + \"/_next/\" + encodeURI(file),\n                as: \"style\"\n            }, file);\n        })\n    });\n} //# sourceMappingURL=preload-css.js.map\n_c = PreloadCss;\nvar _c;\n$RefreshReg$(_c, \"PreloadCss\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9sYXp5LWR5bmFtaWMvcHJlbG9hZC1jc3MuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTyxNQUFBQSwrQkFBc0VDLG1CQUFBQSxDQUFBO1NBQWxEQyxXQUFXQyxLQUFYO0lBQ3pCLE1BQUFDLFNBQUEsS0FBQUQ7SUFDQSwrRUFBbUM7UUFDakMsT0FBT0UsV0FBQTtRQUNUO0lBRUE7SUFDQSxNQUFNQyxlQUFhLElBQUFOLDZCQUFBTyx1QkFBQTtJQUVuQixNQUFBRCxXQUFBO0lBQ0EsNEVBQWtEO0lBQ2xELGtEQUEwQ0Y7UUFDeENJLGFBQU1DLHFCQUF3QkMsSUFBQUEsV0FBQUE7UUFDOUIsTUFBS0QsV0FBTUUsYUFBa0JELHFCQUFBO2FBQzNCLE1BQUtELE9BQVNFLFVBQU07WUFDcEIsS0FBQUYsUUFBTUcsQ0FBQUEsSUFBV0gsRUFBQUE7WUFHakJILE1BQUFBLFdBQWlCTSxRQUFBQSxDQUFBQSxJQUFBQSxDQUFBQSxLQUFBQSxDQUFBQSxNQUFBQSxDQUFBQSxDQUFBQSxPQUFBQSxLQUFBQSxRQUFBQSxDQUFBQTtZQUNuQk4sU0FBQU8sSUFBQSxJQUFBRDtRQUNGO0lBRUE7UUFDRU4sU0FBT1EsTUFBQTtRQUNUO0lBRUE7V0FFS1IsV0FBQUEsR0FBQUEsQ0FBQUEsR0FBU1MsWUFBS0MsR0FBQUEsRUFBQUEsWUFBQUEsUUFBQUEsRUFBQUE7a0JBQ2JWLFNBQUFTLEdBQUEsRUFBQUM7bUJBR2lCLGtCQUFBQyxZQUFBQyxHQUFBO2dCQUNiQyxhQUFZO2dCQUNaQyxZQUFJO2dCQUNKQyxLQUFBQTtnQkFDQUMsTUFBR2QsYUFBQWUsV0FBQSxlQUFBQyxVQUFBUjtnQkFMRUEsSUFBQUE7WUFRWCxHQUFBQTs7SUFHTjs7S0ExQzJCZCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vLi4vc3JjL3NoYXJlZC9saWIvbGF6eS1keW5hbWljL3ByZWxvYWQtY3NzLnRzeD9iYWQ1Il0sIm5hbWVzIjpbIl9yZXF1ZXN0YXN5bmNzdG9yYWdlZXh0ZXJuYWwiLCJyZXF1aXJlIiwiUHJlbG9hZENzcyIsInBhcmFtIiwibW9kdWxlSWRzIiwid2luZG93IiwiYWxsRmlsZXMiLCJnZXRFeHBlY3RlZFJlcXVlc3RTdG9yZSIsInJlcXVlc3RTdG9yZSIsIm1hbmlmZXN0IiwicmVhY3RMb2FkYWJsZU1hbmlmZXN0Iiwia2V5IiwiY3NzRmlsZXMiLCJwdXNoIiwibGVuZ3RoIiwibWFwIiwiZmlsZSIsIl9qc3hydW50aW1lIiwianN4IiwicHJlY2VkZW5jZSIsInJlbCIsImhyZWYiLCJhcyIsImFzc2V0UHJlZml4IiwiZW5jb2RlVVJJIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\nvar React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider'); // TODO: Delete with enableRenderableContext\n\nvar REACT_CONSUMER_TYPE = Symbol.for('react.consumer');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar REACT_CACHE_TYPE = Symbol.for('react.cache');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\nvar enableRenderableContext = false;\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false;\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n}\n\nvar REACT_CLIENT_REFERENCE$2 = Symbol.for('react.client.reference'); // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  if (typeof type === 'function') {\n    if (type.$$typeof === REACT_CLIENT_REFERENCE$2) {\n      // TODO: Create a convention for naming client references with debug info.\n      return null;\n    }\n\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n    case REACT_CACHE_TYPE:\n      {\n        return 'Cache';\n      }\n\n  }\n\n  if (typeof type === 'object') {\n    {\n      if (typeof type.tag === 'number') {\n        error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n      }\n    }\n\n    switch (type.$$typeof) {\n      case REACT_PROVIDER_TYPE:\n        {\n          var provider = type;\n          return getContextName(provider._context) + '.Provider';\n        }\n\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n\n        {\n          return getContextName(context) + '.Consumer';\n        }\n\n      case REACT_CONSUMER_TYPE:\n        {\n          return null;\n        }\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n    }\n  }\n\n  return null;\n}\n\n// $FlowFixMe[method-unbinding]\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar assign = Object.assign;\n\n/*\n * The `'' + value` pattern (used in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object'; // $FlowFixMe[incompatible-return]\n\n    return type;\n  }\n} // $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar REACT_CLIENT_REFERENCE$1 = Symbol.for('react.client.reference');\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || enableRenderableContext  || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_CLIENT_REFERENCE$1 || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n/**\n * Leverages native browser/VM stack frames to get proper details (e.g.\n * filename, line + col number) for a single component in a component stack. We\n * do this by:\n *   (1) throwing and catching an error in the function - this will be our\n *       control error.\n *   (2) calling the component which will eventually throw an error that we'll\n *       catch - this will be our sample error.\n *   (3) diffing the control and sample error stacks to find the stack frame\n *       which represents our component.\n */\n\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if (!fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe[incompatible-type] It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n  /**\n   * Finding a common stack frame between sample and control errors can be\n   * tricky given the different types and levels of stack trace truncation from\n   * different JS VMs. So instead we'll attempt to control what that common\n   * frame should be through this object method:\n   * Having both the sample and control errors be in the function under the\n   * `DescribeNativeComponentFrameRoot` property, + setting the `name` and\n   * `displayName` properties of the function ensures that a stack\n   * frame exists that has the method name `DescribeNativeComponentFrameRoot` in\n   * it for both control and sample stacks.\n   */\n\n\n  var RunInRootFrame = {\n    DetermineComponentFrameRoot: function () {\n      var control;\n\n      try {\n        // This should throw.\n        if (construct) {\n          // Something should be setting the props in the constructor.\n          var Fake = function () {\n            throw Error();\n          }; // $FlowFixMe[prop-missing]\n\n\n          Object.defineProperty(Fake.prototype, 'props', {\n            set: function () {\n              // We use a throwing setter instead of frozen or non-writable props\n              // because that won't throw in a non-strict mode function.\n              throw Error();\n            }\n          });\n\n          if (typeof Reflect === 'object' && Reflect.construct) {\n            // We construct a different control for this case to include any extra\n            // frames added by the construct call.\n            try {\n              Reflect.construct(Fake, []);\n            } catch (x) {\n              control = x;\n            }\n\n            Reflect.construct(fn, [], Fake);\n          } else {\n            try {\n              Fake.call();\n            } catch (x) {\n              control = x;\n            } // $FlowFixMe[prop-missing] found when upgrading Flow\n\n\n            fn.call(Fake.prototype);\n          }\n        } else {\n          try {\n            throw Error();\n          } catch (x) {\n            control = x;\n          } // TODO(luna): This will currently only throw if the function component\n          // tries to access React/ReactDOM/props. We should probably make this throw\n          // in simple components too\n\n\n          var maybePromise = fn(); // If the function component returns a promise, it's likely an async\n          // component, which we don't yet support. Attach a noop catch handler to\n          // silence the error.\n          // TODO: Implement component stacks for async client components?\n\n          if (maybePromise && typeof maybePromise.catch === 'function') {\n            maybePromise.catch(function () {});\n          }\n        }\n      } catch (sample) {\n        // This is inlined manually because closure doesn't do it for us.\n        if (sample && control && typeof sample.stack === 'string') {\n          return [sample.stack, control.stack];\n        }\n      }\n\n      return [null, null];\n    }\n  }; // $FlowFixMe[prop-missing]\n\n  RunInRootFrame.DetermineComponentFrameRoot.displayName = 'DetermineComponentFrameRoot';\n  var namePropDescriptor = Object.getOwnPropertyDescriptor(RunInRootFrame.DetermineComponentFrameRoot, 'name'); // Before ES6, the `name` property was not configurable.\n\n  if (namePropDescriptor && namePropDescriptor.configurable) {\n    // V8 utilizes a function's `name` property when generating a stack trace.\n    Object.defineProperty(RunInRootFrame.DetermineComponentFrameRoot, // Configurable properties can be updated even if its writable descriptor\n    // is set to `false`.\n    // $FlowFixMe[cannot-write]\n    'name', {\n      value: 'DetermineComponentFrameRoot'\n    });\n  }\n\n  try {\n    var _RunInRootFrame$Deter = RunInRootFrame.DetermineComponentFrameRoot(),\n        sampleStack = _RunInRootFrame$Deter[0],\n        controlStack = _RunInRootFrame$Deter[1];\n\n    if (sampleStack && controlStack) {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sampleStack.split('\\n');\n      var controlLines = controlStack.split('\\n');\n      var s = 0;\n      var c = 0;\n\n      while (s < sampleLines.length && !sampleLines[s].includes('DetermineComponentFrameRoot')) {\n        s++;\n      }\n\n      while (c < controlLines.length && !controlLines[c].includes('DetermineComponentFrameRoot')) {\n        c++;\n      } // We couldn't find our intentionally injected common root frame, attempt\n      // to find another common root frame by search from the bottom of the\n      // control stack...\n\n\n      if (s === sampleLines.length || c === controlLines.length) {\n        s = sampleLines.length - 1;\n        c = controlLines.length - 1;\n\n        while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n          // We expect at least one stack frame to be shared.\n          // Typically this will be the root most one. However, stack frames may be\n          // cut off due to maximum stack limits. In this case, one maybe cut off\n          // earlier than the other. We assume that the sample is longer or the same\n          // and there for cut off earlier. So we should find the root most frame in\n          // the sample somewhere in the control.\n          c--;\n        }\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                if (true) {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\nvar REACT_CLIENT_REFERENCE = Symbol.for('react.client.reference');\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    {\n      var warnAboutAccessingRef = function () {\n        if (!specialPropRefWarningShown) {\n          specialPropRefWarningShown = true;\n\n          error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n        }\n      };\n\n      warnAboutAccessingRef.isReactWarning = true;\n      Object.defineProperty(props, 'ref', {\n        get: warnAboutAccessingRef,\n        configurable: true\n      });\n    }\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nfunction ReactElement(type, key, _ref, self, source, owner, props) {\n  var ref;\n\n  {\n    ref = _ref;\n  }\n\n  var element;\n\n  {\n    // In prod, `ref` is a regular property. It will be removed in a\n    // future release.\n    element = {\n      // This tag allows us to uniquely identify this as a React Element\n      $$typeof: REACT_ELEMENT_TYPE,\n      // Built-in properties that belong on the element\n      type: type,\n      key: key,\n      ref: ref,\n      props: props,\n      // Record the component responsible for creating this element.\n      _owner: owner\n    };\n  }\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // debugInfo contains Server Component debug information.\n\n    Object.defineProperty(element, '_debugInfo', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: null\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n}\nvar didWarnAboutKeySpread = {};\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV$1(type, config, maybeKey, isStaticChildren, source, self) {\n  {\n    if (!isValidElementType(type)) {\n      // This is an invalid element type.\n      //\n      // We warn in this case but don't throw. We expect the element creation to\n      // succeed and there will likely be errors in render.\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    } else {\n      // This is a valid element type.\n      // Skip key warning if the type isn't valid since our key validation logic\n      // doesn't expect a non-string/function type and can throw confusing\n      // errors. We don't want exception behavior to differ between dev and\n      // prod. (Rendering will throw with a helpful message and as soon as the\n      // type is fixed, the key warnings will appear.)\n      var children = config.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    } // Warn about key spread regardless of whether the type is valid.\n\n\n    if (hasOwnProperty.call(config, 'key')) {\n      var componentName = getComponentNameFromType(type);\n      var keys = Object.keys(config).filter(function (k) {\n        return k !== 'key';\n      });\n      var beforeExample = keys.length > 0 ? '{key: someKey, ' + keys.join(': ..., ') + ': ...}' : '{key: someKey}';\n\n      if (!didWarnAboutKeySpread[componentName + beforeExample]) {\n        var afterExample = keys.length > 0 ? '{' + keys.join(': ..., ') + ': ...}' : '{}';\n\n        error('A props object containing a \"key\" prop is being spread into JSX:\\n' + '  let props = %s;\\n' + '  <%s {...props} />\\n' + 'React keys must be passed directly to JSX without using spread:\\n' + '  let props = %s;\\n' + '  <%s key={someKey} {...props} />', beforeExample, componentName, afterExample, componentName);\n\n        didWarnAboutKeySpread[componentName + beforeExample] = true;\n      }\n    }\n\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      {\n        ref = config.ref;\n      }\n\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && // Skip over reserved prop names\n      propName !== 'key' && (propName !== 'ref')) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    var element = ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    }\n\n    return element;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object' || !node) {\n      return;\n    }\n\n    if (node.$$typeof === REACT_CLIENT_REFERENCE) ; else if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nvar ownerHasKeyUseWarning = {};\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement(null);\n  }\n}\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = getComponentNameFromType(parentType);\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  // TODO: Move this to render phase instead of at element creation.\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement(null);\n    }\n  }\n}\n\nvar jsxDEV = jsxDEV$1 ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV;\n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanM/N2ViZCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24ubWluLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);