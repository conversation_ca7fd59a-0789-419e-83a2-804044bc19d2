{"name": "kodexguard", "version": "1.0.0", "private": true, "description": "KodeXGuard - Platform Cybersecurity & Bug Hunting", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "db:init": "bun run lib/init-database.ts", "db:reset": "bun run lib/init-database.ts", "db:seed": "bun run lib/init-database.ts"}, "dependencies": {"@elastic/elasticsearch": "^8.11.0", "@hookform/resolvers": "^3.3.2", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.0", "axios": "^1.6.0", "bcryptjs": "^2.4.3", "clsx": "^2.0.0", "crypto-js": "^4.2.0", "date-fns": "^2.30.0", "idb": "^8.0.3", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.294.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "next": "^14.0.0", "node-telegram-bot-api": "^0.64.0", "postcss": "^8.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.47.0", "redis": "^4.6.10", "sharp": "^0.32.6", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "venom-bot": "^5.0.0", "workbox-sw": "^7.3.0", "workbox-webpack-plugin": "^7.3.0", "zod": "^3.22.4"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/multer": "^1.4.11", "@types/crypto-js": "^4.2.1", "@types/node-telegram-bot-api": "^0.64.7", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0"}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}}