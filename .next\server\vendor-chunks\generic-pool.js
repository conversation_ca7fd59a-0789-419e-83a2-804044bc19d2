/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/generic-pool";
exports.ids = ["vendor-chunks/generic-pool"];
exports.modules = {

/***/ "(rsc)/./node_modules/generic-pool/index.js":
/*!********************************************!*\
  !*** ./node_modules/generic-pool/index.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const Pool = __webpack_require__(/*! ./lib/Pool */ \"(rsc)/./node_modules/generic-pool/lib/Pool.js\");\nconst Deque = __webpack_require__(/*! ./lib/Deque */ \"(rsc)/./node_modules/generic-pool/lib/Deque.js\");\nconst PriorityQueue = __webpack_require__(/*! ./lib/PriorityQueue */ \"(rsc)/./node_modules/generic-pool/lib/PriorityQueue.js\");\nconst DefaultEvictor = __webpack_require__(/*! ./lib/DefaultEvictor */ \"(rsc)/./node_modules/generic-pool/lib/DefaultEvictor.js\");\nmodule.exports = {\n  Pool: Pool,\n  Deque: Deque,\n  PriorityQueue: PriorityQueue,\n  DefaultEvictor: DefaultEvictor,\n  createPool: function(factory, config) {\n    return new Pool(DefaultEvictor, Deque, PriorityQueue, factory, config);\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBLGFBQWEsbUJBQU8sQ0FBQyxpRUFBWTtBQUNqQyxjQUFjLG1CQUFPLENBQUMsbUVBQWE7QUFDbkMsc0JBQXNCLG1CQUFPLENBQUMsbUZBQXFCO0FBQ25ELHVCQUF1QixtQkFBTyxDQUFDLHFGQUFzQjtBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb2RleGd1YXJkLy4vbm9kZV9tb2R1bGVzL2dlbmVyaWMtcG9vbC9pbmRleC5qcz9kZGNmIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IFBvb2wgPSByZXF1aXJlKFwiLi9saWIvUG9vbFwiKTtcbmNvbnN0IERlcXVlID0gcmVxdWlyZShcIi4vbGliL0RlcXVlXCIpO1xuY29uc3QgUHJpb3JpdHlRdWV1ZSA9IHJlcXVpcmUoXCIuL2xpYi9Qcmlvcml0eVF1ZXVlXCIpO1xuY29uc3QgRGVmYXVsdEV2aWN0b3IgPSByZXF1aXJlKFwiLi9saWIvRGVmYXVsdEV2aWN0b3JcIik7XG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgUG9vbDogUG9vbCxcbiAgRGVxdWU6IERlcXVlLFxuICBQcmlvcml0eVF1ZXVlOiBQcmlvcml0eVF1ZXVlLFxuICBEZWZhdWx0RXZpY3RvcjogRGVmYXVsdEV2aWN0b3IsXG4gIGNyZWF0ZVBvb2w6IGZ1bmN0aW9uKGZhY3RvcnksIGNvbmZpZykge1xuICAgIHJldHVybiBuZXcgUG9vbChEZWZhdWx0RXZpY3RvciwgRGVxdWUsIFByaW9yaXR5UXVldWUsIGZhY3RvcnksIGNvbmZpZyk7XG4gIH1cbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/DefaultEvictor.js":
/*!*********************************************************!*\
  !*** ./node_modules/generic-pool/lib/DefaultEvictor.js ***!
  \*********************************************************/
/***/ ((module) => {

"use strict";
eval("\n\nclass DefaultEvictor {\n  evict(config, pooledResource, availableObjectsCount) {\n    const idleTime = Date.now() - pooledResource.lastIdleTime;\n\n    if (\n      config.softIdleTimeoutMillis > 0 &&\n      config.softIdleTimeoutMillis < idleTime &&\n      config.min < availableObjectsCount\n    ) {\n      return true;\n    }\n\n    if (config.idleTimeoutMillis < idleTime) {\n      return true;\n    }\n\n    return false;\n  }\n}\n\nmodule.exports = DefaultEvictor;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2xpYi9EZWZhdWx0RXZpY3Rvci5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL2tvZGV4Z3VhcmQvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2xpYi9EZWZhdWx0RXZpY3Rvci5qcz8yMDU2Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5jbGFzcyBEZWZhdWx0RXZpY3RvciB7XG4gIGV2aWN0KGNvbmZpZywgcG9vbGVkUmVzb3VyY2UsIGF2YWlsYWJsZU9iamVjdHNDb3VudCkge1xuICAgIGNvbnN0IGlkbGVUaW1lID0gRGF0ZS5ub3coKSAtIHBvb2xlZFJlc291cmNlLmxhc3RJZGxlVGltZTtcblxuICAgIGlmIChcbiAgICAgIGNvbmZpZy5zb2Z0SWRsZVRpbWVvdXRNaWxsaXMgPiAwICYmXG4gICAgICBjb25maWcuc29mdElkbGVUaW1lb3V0TWlsbGlzIDwgaWRsZVRpbWUgJiZcbiAgICAgIGNvbmZpZy5taW4gPCBhdmFpbGFibGVPYmplY3RzQ291bnRcbiAgICApIHtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cblxuICAgIGlmIChjb25maWcuaWRsZVRpbWVvdXRNaWxsaXMgPCBpZGxlVGltZSkge1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuXG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gRGVmYXVsdEV2aWN0b3I7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/DefaultEvictor.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/Deferred.js":
/*!***************************************************!*\
  !*** ./node_modules/generic-pool/lib/Deferred.js ***!
  \***************************************************/
/***/ ((module) => {

"use strict";
eval("\n\n/**\n * This is apparently a bit like a Jquery deferred, hence the name\n */\n\nclass Deferred {\n  constructor(Promise) {\n    this._state = Deferred.PENDING;\n    this._resolve = undefined;\n    this._reject = undefined;\n\n    this._promise = new Promise((resolve, reject) => {\n      this._resolve = resolve;\n      this._reject = reject;\n    });\n  }\n\n  get state() {\n    return this._state;\n  }\n\n  get promise() {\n    return this._promise;\n  }\n\n  reject(reason) {\n    if (this._state !== Deferred.PENDING) {\n      return;\n    }\n    this._state = Deferred.REJECTED;\n    this._reject(reason);\n  }\n\n  resolve(value) {\n    if (this._state !== Deferred.PENDING) {\n      return;\n    }\n    this._state = Deferred.FULFILLED;\n    this._resolve(value);\n  }\n}\n\n// TODO: should these really live here? or be a seperate 'state' enum\nDeferred.PENDING = \"PENDING\";\nDeferred.FULFILLED = \"FULFILLED\";\nDeferred.REJECTED = \"REJECTED\";\n\nmodule.exports = Deferred;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2xpYi9EZWZlcnJlZC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va29kZXhndWFyZC8uL25vZGVfbW9kdWxlcy9nZW5lcmljLXBvb2wvbGliL0RlZmVycmVkLmpzPzk5MTAiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbi8qKlxuICogVGhpcyBpcyBhcHBhcmVudGx5IGEgYml0IGxpa2UgYSBKcXVlcnkgZGVmZXJyZWQsIGhlbmNlIHRoZSBuYW1lXG4gKi9cblxuY2xhc3MgRGVmZXJyZWQge1xuICBjb25zdHJ1Y3RvcihQcm9taXNlKSB7XG4gICAgdGhpcy5fc3RhdGUgPSBEZWZlcnJlZC5QRU5ESU5HO1xuICAgIHRoaXMuX3Jlc29sdmUgPSB1bmRlZmluZWQ7XG4gICAgdGhpcy5fcmVqZWN0ID0gdW5kZWZpbmVkO1xuXG4gICAgdGhpcy5fcHJvbWlzZSA9IG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHtcbiAgICAgIHRoaXMuX3Jlc29sdmUgPSByZXNvbHZlO1xuICAgICAgdGhpcy5fcmVqZWN0ID0gcmVqZWN0O1xuICAgIH0pO1xuICB9XG5cbiAgZ2V0IHN0YXRlKCkge1xuICAgIHJldHVybiB0aGlzLl9zdGF0ZTtcbiAgfVxuXG4gIGdldCBwcm9taXNlKCkge1xuICAgIHJldHVybiB0aGlzLl9wcm9taXNlO1xuICB9XG5cbiAgcmVqZWN0KHJlYXNvbikge1xuICAgIGlmICh0aGlzLl9zdGF0ZSAhPT0gRGVmZXJyZWQuUEVORElORykge1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICB0aGlzLl9zdGF0ZSA9IERlZmVycmVkLlJFSkVDVEVEO1xuICAgIHRoaXMuX3JlamVjdChyZWFzb24pO1xuICB9XG5cbiAgcmVzb2x2ZSh2YWx1ZSkge1xuICAgIGlmICh0aGlzLl9zdGF0ZSAhPT0gRGVmZXJyZWQuUEVORElORykge1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICB0aGlzLl9zdGF0ZSA9IERlZmVycmVkLkZVTEZJTExFRDtcbiAgICB0aGlzLl9yZXNvbHZlKHZhbHVlKTtcbiAgfVxufVxuXG4vLyBUT0RPOiBzaG91bGQgdGhlc2UgcmVhbGx5IGxpdmUgaGVyZT8gb3IgYmUgYSBzZXBlcmF0ZSAnc3RhdGUnIGVudW1cbkRlZmVycmVkLlBFTkRJTkcgPSBcIlBFTkRJTkdcIjtcbkRlZmVycmVkLkZVTEZJTExFRCA9IFwiRlVMRklMTEVEXCI7XG5EZWZlcnJlZC5SRUpFQ1RFRCA9IFwiUkVKRUNURURcIjtcblxubW9kdWxlLmV4cG9ydHMgPSBEZWZlcnJlZDtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/Deferred.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/Deque.js":
/*!************************************************!*\
  !*** ./node_modules/generic-pool/lib/Deque.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst DoublyLinkedList = __webpack_require__(/*! ./DoublyLinkedList */ \"(rsc)/./node_modules/generic-pool/lib/DoublyLinkedList.js\");\nconst DequeIterator = __webpack_require__(/*! ./DequeIterator */ \"(rsc)/./node_modules/generic-pool/lib/DequeIterator.js\");\n/**\n * DoublyLinkedList backed double ended queue\n * implements just enough to keep the Pool\n */\nclass Deque {\n  constructor() {\n    this._list = new DoublyLinkedList();\n  }\n\n  /**\n   * removes and returns the first element from the queue\n   * @return {any} [description]\n   */\n  shift() {\n    if (this.length === 0) {\n      return undefined;\n    }\n\n    const node = this._list.head;\n    this._list.remove(node);\n\n    return node.data;\n  }\n\n  /**\n   * adds one elemts to the beginning of the queue\n   * @param  {any} element [description]\n   * @return {any}         [description]\n   */\n  unshift(element) {\n    const node = DoublyLinkedList.createNode(element);\n\n    this._list.insertBeginning(node);\n  }\n\n  /**\n   * adds one to the end of the queue\n   * @param  {any} element [description]\n   * @return {any}         [description]\n   */\n  push(element) {\n    const node = DoublyLinkedList.createNode(element);\n\n    this._list.insertEnd(node);\n  }\n\n  /**\n   * removes and returns the last element from the queue\n   */\n  pop() {\n    if (this.length === 0) {\n      return undefined;\n    }\n\n    const node = this._list.tail;\n    this._list.remove(node);\n\n    return node.data;\n  }\n\n  [Symbol.iterator]() {\n    return new DequeIterator(this._list);\n  }\n\n  iterator() {\n    return new DequeIterator(this._list);\n  }\n\n  reverseIterator() {\n    return new DequeIterator(this._list, true);\n  }\n\n  /**\n   * get a reference to the item at the head of the queue\n   * @return {any} [description]\n   */\n  get head() {\n    if (this.length === 0) {\n      return undefined;\n    }\n    const node = this._list.head;\n    return node.data;\n  }\n\n  /**\n   * get a reference to the item at the tail of the queue\n   * @return {any} [description]\n   */\n  get tail() {\n    if (this.length === 0) {\n      return undefined;\n    }\n    const node = this._list.tail;\n    return node.data;\n  }\n\n  get length() {\n    return this._list.length;\n  }\n}\n\nmodule.exports = Deque;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/Deque.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/DequeIterator.js":
/*!********************************************************!*\
  !*** ./node_modules/generic-pool/lib/DequeIterator.js ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst DoublyLinkedListIterator = __webpack_require__(/*! ./DoublyLinkedListIterator */ \"(rsc)/./node_modules/generic-pool/lib/DoublyLinkedListIterator.js\");\n/**\n * Thin wrapper around an underlying DDL iterator\n */\nclass DequeIterator extends DoublyLinkedListIterator {\n  next() {\n    const result = super.next();\n\n    // unwrap the node...\n    if (result.value) {\n      result.value = result.value.data;\n    }\n\n    return result;\n  }\n}\n\nmodule.exports = DequeIterator;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2xpYi9EZXF1ZUl0ZXJhdG9yLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLGlDQUFpQyxtQkFBTyxDQUFDLHFHQUE0QjtBQUNyRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va29kZXhndWFyZC8uL25vZGVfbW9kdWxlcy9nZW5lcmljLXBvb2wvbGliL0RlcXVlSXRlcmF0b3IuanM/ZjgwOCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuY29uc3QgRG91Ymx5TGlua2VkTGlzdEl0ZXJhdG9yID0gcmVxdWlyZShcIi4vRG91Ymx5TGlua2VkTGlzdEl0ZXJhdG9yXCIpO1xuLyoqXG4gKiBUaGluIHdyYXBwZXIgYXJvdW5kIGFuIHVuZGVybHlpbmcgRERMIGl0ZXJhdG9yXG4gKi9cbmNsYXNzIERlcXVlSXRlcmF0b3IgZXh0ZW5kcyBEb3VibHlMaW5rZWRMaXN0SXRlcmF0b3Ige1xuICBuZXh0KCkge1xuICAgIGNvbnN0IHJlc3VsdCA9IHN1cGVyLm5leHQoKTtcblxuICAgIC8vIHVud3JhcCB0aGUgbm9kZS4uLlxuICAgIGlmIChyZXN1bHQudmFsdWUpIHtcbiAgICAgIHJlc3VsdC52YWx1ZSA9IHJlc3VsdC52YWx1ZS5kYXRhO1xuICAgIH1cblxuICAgIHJldHVybiByZXN1bHQ7XG4gIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSBEZXF1ZUl0ZXJhdG9yO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/DequeIterator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/DoublyLinkedList.js":
/*!***********************************************************!*\
  !*** ./node_modules/generic-pool/lib/DoublyLinkedList.js ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
eval("\n\n/**\n * A Doubly Linked List, because there aren't enough in the world...\n * this is pretty much a direct JS port of the one wikipedia\n * https://en.wikipedia.org/wiki/Doubly_linked_list\n *\n * For most usage 'insertBeginning' and 'insertEnd' should be enough\n *\n * nodes are expected to something like a POJSO like\n * {\n *   prev: null,\n *   next: null,\n *   something: 'whatever you like'\n * }\n */\nclass DoublyLinkedList {\n  constructor() {\n    this.head = null;\n    this.tail = null;\n    this.length = 0;\n  }\n\n  insertBeginning(node) {\n    if (this.head === null) {\n      this.head = node;\n      this.tail = node;\n      node.prev = null;\n      node.next = null;\n      this.length++;\n    } else {\n      this.insertBefore(this.head, node);\n    }\n  }\n\n  insertEnd(node) {\n    if (this.tail === null) {\n      this.insertBeginning(node);\n    } else {\n      this.insertAfter(this.tail, node);\n    }\n  }\n\n  insertAfter(node, newNode) {\n    newNode.prev = node;\n    newNode.next = node.next;\n    if (node.next === null) {\n      this.tail = newNode;\n    } else {\n      node.next.prev = newNode;\n    }\n    node.next = newNode;\n    this.length++;\n  }\n\n  insertBefore(node, newNode) {\n    newNode.prev = node.prev;\n    newNode.next = node;\n    if (node.prev === null) {\n      this.head = newNode;\n    } else {\n      node.prev.next = newNode;\n    }\n    node.prev = newNode;\n    this.length++;\n  }\n\n  remove(node) {\n    if (node.prev === null) {\n      this.head = node.next;\n    } else {\n      node.prev.next = node.next;\n    }\n    if (node.next === null) {\n      this.tail = node.prev;\n    } else {\n      node.next.prev = node.prev;\n    }\n    node.prev = null;\n    node.next = null;\n    this.length--;\n  }\n\n  // FIXME: this should not live here and has become a dumping ground...\n  static createNode(data) {\n    return {\n      prev: null,\n      next: null,\n      data: data\n    };\n  }\n}\n\nmodule.exports = DoublyLinkedList;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2xpYi9Eb3VibHlMaW5rZWRMaXN0LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL2tvZGV4Z3VhcmQvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2xpYi9Eb3VibHlMaW5rZWRMaXN0LmpzPzMzN2EiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbi8qKlxuICogQSBEb3VibHkgTGlua2VkIExpc3QsIGJlY2F1c2UgdGhlcmUgYXJlbid0IGVub3VnaCBpbiB0aGUgd29ybGQuLi5cbiAqIHRoaXMgaXMgcHJldHR5IG11Y2ggYSBkaXJlY3QgSlMgcG9ydCBvZiB0aGUgb25lIHdpa2lwZWRpYVxuICogaHR0cHM6Ly9lbi53aWtpcGVkaWEub3JnL3dpa2kvRG91Ymx5X2xpbmtlZF9saXN0XG4gKlxuICogRm9yIG1vc3QgdXNhZ2UgJ2luc2VydEJlZ2lubmluZycgYW5kICdpbnNlcnRFbmQnIHNob3VsZCBiZSBlbm91Z2hcbiAqXG4gKiBub2RlcyBhcmUgZXhwZWN0ZWQgdG8gc29tZXRoaW5nIGxpa2UgYSBQT0pTTyBsaWtlXG4gKiB7XG4gKiAgIHByZXY6IG51bGwsXG4gKiAgIG5leHQ6IG51bGwsXG4gKiAgIHNvbWV0aGluZzogJ3doYXRldmVyIHlvdSBsaWtlJ1xuICogfVxuICovXG5jbGFzcyBEb3VibHlMaW5rZWRMaXN0IHtcbiAgY29uc3RydWN0b3IoKSB7XG4gICAgdGhpcy5oZWFkID0gbnVsbDtcbiAgICB0aGlzLnRhaWwgPSBudWxsO1xuICAgIHRoaXMubGVuZ3RoID0gMDtcbiAgfVxuXG4gIGluc2VydEJlZ2lubmluZyhub2RlKSB7XG4gICAgaWYgKHRoaXMuaGVhZCA9PT0gbnVsbCkge1xuICAgICAgdGhpcy5oZWFkID0gbm9kZTtcbiAgICAgIHRoaXMudGFpbCA9IG5vZGU7XG4gICAgICBub2RlLnByZXYgPSBudWxsO1xuICAgICAgbm9kZS5uZXh0ID0gbnVsbDtcbiAgICAgIHRoaXMubGVuZ3RoKys7XG4gICAgfSBlbHNlIHtcbiAgICAgIHRoaXMuaW5zZXJ0QmVmb3JlKHRoaXMuaGVhZCwgbm9kZSk7XG4gICAgfVxuICB9XG5cbiAgaW5zZXJ0RW5kKG5vZGUpIHtcbiAgICBpZiAodGhpcy50YWlsID09PSBudWxsKSB7XG4gICAgICB0aGlzLmluc2VydEJlZ2lubmluZyhub2RlKTtcbiAgICB9IGVsc2Uge1xuICAgICAgdGhpcy5pbnNlcnRBZnRlcih0aGlzLnRhaWwsIG5vZGUpO1xuICAgIH1cbiAgfVxuXG4gIGluc2VydEFmdGVyKG5vZGUsIG5ld05vZGUpIHtcbiAgICBuZXdOb2RlLnByZXYgPSBub2RlO1xuICAgIG5ld05vZGUubmV4dCA9IG5vZGUubmV4dDtcbiAgICBpZiAobm9kZS5uZXh0ID09PSBudWxsKSB7XG4gICAgICB0aGlzLnRhaWwgPSBuZXdOb2RlO1xuICAgIH0gZWxzZSB7XG4gICAgICBub2RlLm5leHQucHJldiA9IG5ld05vZGU7XG4gICAgfVxuICAgIG5vZGUubmV4dCA9IG5ld05vZGU7XG4gICAgdGhpcy5sZW5ndGgrKztcbiAgfVxuXG4gIGluc2VydEJlZm9yZShub2RlLCBuZXdOb2RlKSB7XG4gICAgbmV3Tm9kZS5wcmV2ID0gbm9kZS5wcmV2O1xuICAgIG5ld05vZGUubmV4dCA9IG5vZGU7XG4gICAgaWYgKG5vZGUucHJldiA9PT0gbnVsbCkge1xuICAgICAgdGhpcy5oZWFkID0gbmV3Tm9kZTtcbiAgICB9IGVsc2Uge1xuICAgICAgbm9kZS5wcmV2Lm5leHQgPSBuZXdOb2RlO1xuICAgIH1cbiAgICBub2RlLnByZXYgPSBuZXdOb2RlO1xuICAgIHRoaXMubGVuZ3RoKys7XG4gIH1cblxuICByZW1vdmUobm9kZSkge1xuICAgIGlmIChub2RlLnByZXYgPT09IG51bGwpIHtcbiAgICAgIHRoaXMuaGVhZCA9IG5vZGUubmV4dDtcbiAgICB9IGVsc2Uge1xuICAgICAgbm9kZS5wcmV2Lm5leHQgPSBub2RlLm5leHQ7XG4gICAgfVxuICAgIGlmIChub2RlLm5leHQgPT09IG51bGwpIHtcbiAgICAgIHRoaXMudGFpbCA9IG5vZGUucHJldjtcbiAgICB9IGVsc2Uge1xuICAgICAgbm9kZS5uZXh0LnByZXYgPSBub2RlLnByZXY7XG4gICAgfVxuICAgIG5vZGUucHJldiA9IG51bGw7XG4gICAgbm9kZS5uZXh0ID0gbnVsbDtcbiAgICB0aGlzLmxlbmd0aC0tO1xuICB9XG5cbiAgLy8gRklYTUU6IHRoaXMgc2hvdWxkIG5vdCBsaXZlIGhlcmUgYW5kIGhhcyBiZWNvbWUgYSBkdW1waW5nIGdyb3VuZC4uLlxuICBzdGF0aWMgY3JlYXRlTm9kZShkYXRhKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIHByZXY6IG51bGwsXG4gICAgICBuZXh0OiBudWxsLFxuICAgICAgZGF0YTogZGF0YVxuICAgIH07XG4gIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSBEb3VibHlMaW5rZWRMaXN0O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/DoublyLinkedList.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/DoublyLinkedListIterator.js":
/*!*******************************************************************!*\
  !*** ./node_modules/generic-pool/lib/DoublyLinkedListIterator.js ***!
  \*******************************************************************/
/***/ ((module) => {

"use strict";
eval("\n\n/**\n * Creates an interator for a DoublyLinkedList starting at the given node\n * It's internal cursor will remains relative to the last \"iterated\" node as that\n * node moves through the list until it either iterates to the end of the list,\n * or the the node it's tracking is removed from the list. Until the first 'next'\n * call it tracks the head/tail of the linked list. This means that one can create\n * an iterator on an empty list, then add nodes, and then the iterator will follow\n * those nodes. Because the DoublyLinkedList nodes don't track their owning \"list\" and\n * it's highly inefficient to walk the list for every iteration, the iterator won't know\n * if the node has been detached from one List and added to another list, or if the iterator\n *\n * The created object is an es6 compatible iterator\n */\nclass DoublyLinkedListIterator {\n  /**\n   * @param  {Object} doublyLinkedList     a node that is part of a doublyLinkedList\n   * @param  {Boolean} [reverse=false]     is this a reverse iterator? default: false\n   */\n  constructor(doublyLinkedList, reverse) {\n    this._list = doublyLinkedList;\n    // NOTE: these key names are tied to the DoublyLinkedListIterator\n    this._direction = reverse === true ? \"prev\" : \"next\";\n    this._startPosition = reverse === true ? \"tail\" : \"head\";\n    this._started = false;\n    this._cursor = null;\n    this._done = false;\n  }\n\n  _start() {\n    this._cursor = this._list[this._startPosition];\n    this._started = true;\n  }\n\n  _advanceCursor() {\n    if (this._started === false) {\n      this._started = true;\n      this._cursor = this._list[this._startPosition];\n      return;\n    }\n    this._cursor = this._cursor[this._direction];\n  }\n\n  reset() {\n    this._done = false;\n    this._started = false;\n    this._cursor = null;\n  }\n\n  remove() {\n    if (\n      this._started === false ||\n      this._done === true ||\n      this._isCursorDetached()\n    ) {\n      return false;\n    }\n    this._list.remove(this._cursor);\n  }\n\n  next() {\n    if (this._done === true) {\n      return { done: true };\n    }\n\n    this._advanceCursor();\n\n    // if there is no node at the cursor or the node at the cursor is no longer part of\n    // a doubly linked list then we are done/finished/kaput\n    if (this._cursor === null || this._isCursorDetached()) {\n      this._done = true;\n      return { done: true };\n    }\n\n    return {\n      value: this._cursor,\n      done: false\n    };\n  }\n\n  /**\n   * Is the node detached from a list?\n   * NOTE: you can trick/bypass/confuse this check by removing a node from one DoublyLinkedList\n   * and adding it to another.\n   * TODO: We can make this smarter by checking the direction of travel and only checking\n   * the required next/prev/head/tail rather than all of them\n   * @return {Boolean}      [description]\n   */\n  _isCursorDetached() {\n    return (\n      this._cursor.prev === null &&\n      this._cursor.next === null &&\n      this._list.tail !== this._cursor &&\n      this._list.head !== this._cursor\n    );\n  }\n}\n\nmodule.exports = DoublyLinkedListIterator;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/DoublyLinkedListIterator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/Pool.js":
/*!***********************************************!*\
  !*** ./node_modules/generic-pool/lib/Pool.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst EventEmitter = (__webpack_require__(/*! events */ \"events\").EventEmitter);\n\nconst factoryValidator = __webpack_require__(/*! ./factoryValidator */ \"(rsc)/./node_modules/generic-pool/lib/factoryValidator.js\");\nconst PoolOptions = __webpack_require__(/*! ./PoolOptions */ \"(rsc)/./node_modules/generic-pool/lib/PoolOptions.js\");\nconst ResourceRequest = __webpack_require__(/*! ./ResourceRequest */ \"(rsc)/./node_modules/generic-pool/lib/ResourceRequest.js\");\nconst ResourceLoan = __webpack_require__(/*! ./ResourceLoan */ \"(rsc)/./node_modules/generic-pool/lib/ResourceLoan.js\");\nconst PooledResource = __webpack_require__(/*! ./PooledResource */ \"(rsc)/./node_modules/generic-pool/lib/PooledResource.js\");\nconst DefaultEvictor = __webpack_require__(/*! ./DefaultEvictor */ \"(rsc)/./node_modules/generic-pool/lib/DefaultEvictor.js\");\nconst Deque = __webpack_require__(/*! ./Deque */ \"(rsc)/./node_modules/generic-pool/lib/Deque.js\");\nconst Deferred = __webpack_require__(/*! ./Deferred */ \"(rsc)/./node_modules/generic-pool/lib/Deferred.js\");\nconst PriorityQueue = __webpack_require__(/*! ./PriorityQueue */ \"(rsc)/./node_modules/generic-pool/lib/PriorityQueue.js\");\nconst DequeIterator = __webpack_require__(/*! ./DequeIterator */ \"(rsc)/./node_modules/generic-pool/lib/DequeIterator.js\");\n\nconst reflector = (__webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/generic-pool/lib/utils.js\").reflector);\n\n/**\n * TODO: move me\n */\nconst FACTORY_CREATE_ERROR = \"factoryCreateError\";\nconst FACTORY_DESTROY_ERROR = \"factoryDestroyError\";\n\nclass Pool extends EventEmitter {\n  /**\n   * Generate an Object pool with a specified `factory` and `config`.\n   *\n   * @param {typeof DefaultEvictor} Evictor\n   * @param {typeof Deque} Deque\n   * @param {typeof PriorityQueue} PriorityQueue\n   * @param {Object} factory\n   *   Factory to be used for generating and destroying the items.\n   * @param {Function} factory.create\n   *   Should create the item to be acquired,\n   *   and call it's first callback argument with the generated item as it's argument.\n   * @param {Function} factory.destroy\n   *   Should gently close any resources that the item is using.\n   *   Called before the items is destroyed.\n   * @param {Function} factory.validate\n   *   Test if a resource is still valid .Should return a promise that resolves to a boolean, true if resource is still valid and false\n   *   If it should be removed from pool.\n   * @param {Object} options\n   */\n  constructor(Evictor, Deque, PriorityQueue, factory, options) {\n    super();\n\n    factoryValidator(factory);\n\n    this._config = new PoolOptions(options);\n\n    // TODO: fix up this ugly glue-ing\n    this._Promise = this._config.Promise;\n\n    this._factory = factory;\n    this._draining = false;\n    this._started = false;\n    /**\n     * Holds waiting clients\n     * @type {PriorityQueue}\n     */\n    this._waitingClientsQueue = new PriorityQueue(this._config.priorityRange);\n\n    /**\n     * Collection of promises for resource creation calls made by the pool to factory.create\n     * @type {Set}\n     */\n    this._factoryCreateOperations = new Set();\n\n    /**\n     * Collection of promises for resource destruction calls made by the pool to factory.destroy\n     * @type {Set}\n     */\n    this._factoryDestroyOperations = new Set();\n\n    /**\n     * A queue/stack of pooledResources awaiting acquisition\n     * TODO: replace with LinkedList backed array\n     * @type {Deque}\n     */\n    this._availableObjects = new Deque();\n\n    /**\n     * Collection of references for any resource that are undergoing validation before being acquired\n     * @type {Set}\n     */\n    this._testOnBorrowResources = new Set();\n\n    /**\n     * Collection of references for any resource that are undergoing validation before being returned\n     * @type {Set}\n     */\n    this._testOnReturnResources = new Set();\n\n    /**\n     * Collection of promises for any validations currently in process\n     * @type {Set}\n     */\n    this._validationOperations = new Set();\n\n    /**\n     * All objects associated with this pool in any state (except destroyed)\n     * @type {Set}\n     */\n    this._allObjects = new Set();\n\n    /**\n     * Loans keyed by the borrowed resource\n     * @type {Map}\n     */\n    this._resourceLoans = new Map();\n\n    /**\n     * Infinitely looping iterator over available object\n     * @type {DequeIterator}\n     */\n    this._evictionIterator = this._availableObjects.iterator();\n\n    this._evictor = new Evictor();\n\n    /**\n     * handle for setTimeout for next eviction run\n     * @type {(number|null)}\n     */\n    this._scheduledEviction = null;\n\n    // create initial resources (if factory.min > 0)\n    if (this._config.autostart === true) {\n      this.start();\n    }\n  }\n\n  _destroy(pooledResource) {\n    // FIXME: do we need another state for \"in destruction\"?\n    pooledResource.invalidate();\n    this._allObjects.delete(pooledResource);\n    // NOTE: this maybe very bad promise usage?\n    const destroyPromise = this._factory.destroy(pooledResource.obj);\n    const wrappedDestroyPromise = this._config.destroyTimeoutMillis\n      ? this._Promise.resolve(this._applyDestroyTimeout(destroyPromise))\n      : this._Promise.resolve(destroyPromise);\n\n    this._trackOperation(\n      wrappedDestroyPromise,\n      this._factoryDestroyOperations\n    ).catch(reason => {\n      this.emit(FACTORY_DESTROY_ERROR, reason);\n    });\n\n    // TODO: maybe ensuring minimum pool size should live outside here\n    this._ensureMinimum();\n  }\n\n  _applyDestroyTimeout(promise) {\n    const timeoutPromise = new this._Promise((resolve, reject) => {\n      setTimeout(() => {\n        reject(new Error(\"destroy timed out\"));\n      }, this._config.destroyTimeoutMillis).unref();\n    });\n    return this._Promise.race([timeoutPromise, promise]);\n  }\n\n  /**\n   * Attempt to move an available resource into test and then onto a waiting client\n   * @return {Boolean} could we move an available resource into test\n   */\n  _testOnBorrow() {\n    if (this._availableObjects.length < 1) {\n      return false;\n    }\n\n    const pooledResource = this._availableObjects.shift();\n    // Mark the resource as in test\n    pooledResource.test();\n    this._testOnBorrowResources.add(pooledResource);\n    const validationPromise = this._factory.validate(pooledResource.obj);\n    const wrappedValidationPromise = this._Promise.resolve(validationPromise);\n\n    this._trackOperation(\n      wrappedValidationPromise,\n      this._validationOperations\n    ).then(isValid => {\n      this._testOnBorrowResources.delete(pooledResource);\n\n      if (isValid === false) {\n        pooledResource.invalidate();\n        this._destroy(pooledResource);\n        this._dispense();\n        return;\n      }\n      this._dispatchPooledResourceToNextWaitingClient(pooledResource);\n    });\n\n    return true;\n  }\n\n  /**\n   * Attempt to move an available resource to a waiting client\n   * @return {Boolean} [description]\n   */\n  _dispatchResource() {\n    if (this._availableObjects.length < 1) {\n      return false;\n    }\n\n    const pooledResource = this._availableObjects.shift();\n    this._dispatchPooledResourceToNextWaitingClient(pooledResource);\n    return false;\n  }\n\n  /**\n   * Attempt to resolve an outstanding resource request using an available resource from\n   * the pool, or creating new ones\n   *\n   * @private\n   */\n  _dispense() {\n    /**\n     * Local variables for ease of reading/writing\n     * these don't (shouldn't) change across the execution of this fn\n     */\n    const numWaitingClients = this._waitingClientsQueue.length;\n\n    // If there aren't any waiting requests then there is nothing to do\n    // so lets short-circuit\n    if (numWaitingClients < 1) {\n      return;\n    }\n\n    const resourceShortfall =\n      numWaitingClients - this._potentiallyAllocableResourceCount;\n\n    const actualNumberOfResourcesToCreate = Math.min(\n      this.spareResourceCapacity,\n      resourceShortfall\n    );\n    for (let i = 0; actualNumberOfResourcesToCreate > i; i++) {\n      this._createResource();\n    }\n\n    // If we are doing test-on-borrow see how many more resources need to be moved into test\n    // to help satisfy waitingClients\n    if (this._config.testOnBorrow === true) {\n      // how many available resources do we need to shift into test\n      const desiredNumberOfResourcesToMoveIntoTest =\n        numWaitingClients - this._testOnBorrowResources.size;\n      const actualNumberOfResourcesToMoveIntoTest = Math.min(\n        this._availableObjects.length,\n        desiredNumberOfResourcesToMoveIntoTest\n      );\n      for (let i = 0; actualNumberOfResourcesToMoveIntoTest > i; i++) {\n        this._testOnBorrow();\n      }\n    }\n\n    // if we aren't testing-on-borrow then lets try to allocate what we can\n    if (this._config.testOnBorrow === false) {\n      const actualNumberOfResourcesToDispatch = Math.min(\n        this._availableObjects.length,\n        numWaitingClients\n      );\n      for (let i = 0; actualNumberOfResourcesToDispatch > i; i++) {\n        this._dispatchResource();\n      }\n    }\n  }\n\n  /**\n   * Dispatches a pooledResource to the next waiting client (if any) else\n   * puts the PooledResource back on the available list\n   * @param  {PooledResource} pooledResource [description]\n   * @return {Boolean}                [description]\n   */\n  _dispatchPooledResourceToNextWaitingClient(pooledResource) {\n    const clientResourceRequest = this._waitingClientsQueue.dequeue();\n    if (\n      clientResourceRequest === undefined ||\n      clientResourceRequest.state !== Deferred.PENDING\n    ) {\n      // While we were away either all the waiting clients timed out\n      // or were somehow fulfilled. put our pooledResource back.\n      this._addPooledResourceToAvailableObjects(pooledResource);\n      // TODO: do need to trigger anything before we leave?\n      return false;\n    }\n    const loan = new ResourceLoan(pooledResource, this._Promise);\n    this._resourceLoans.set(pooledResource.obj, loan);\n    pooledResource.allocate();\n    clientResourceRequest.resolve(pooledResource.obj);\n    return true;\n  }\n\n  /**\n   * tracks on operation using given set\n   * handles adding/removing from the set and resolve/rejects the value/reason\n   * @param  {Promise} operation\n   * @param  {Set} set       Set holding operations\n   * @return {Promise}       Promise that resolves once operation has been removed from set\n   */\n  _trackOperation(operation, set) {\n    set.add(operation);\n\n    return operation.then(\n      v => {\n        set.delete(operation);\n        return this._Promise.resolve(v);\n      },\n      e => {\n        set.delete(operation);\n        return this._Promise.reject(e);\n      }\n    );\n  }\n\n  /**\n   * @private\n   */\n  _createResource() {\n    // An attempt to create a resource\n    const factoryPromise = this._factory.create();\n    const wrappedFactoryPromise = this._Promise\n      .resolve(factoryPromise)\n      .then(resource => {\n        const pooledResource = new PooledResource(resource);\n        this._allObjects.add(pooledResource);\n        this._addPooledResourceToAvailableObjects(pooledResource);\n      });\n\n    this._trackOperation(wrappedFactoryPromise, this._factoryCreateOperations)\n      .then(() => {\n        this._dispense();\n        // Stop bluebird complaining about this side-effect only handler\n        // - a promise was created in a handler but was not returned from it\n        // https://goo.gl/rRqMUw\n        return null;\n      })\n      .catch(reason => {\n        this.emit(FACTORY_CREATE_ERROR, reason);\n        this._dispense();\n      });\n  }\n\n  /**\n   * @private\n   */\n  _ensureMinimum() {\n    if (this._draining === true) {\n      return;\n    }\n    const minShortfall = this._config.min - this._count;\n    for (let i = 0; i < minShortfall; i++) {\n      this._createResource();\n    }\n  }\n\n  _evict() {\n    const testsToRun = Math.min(\n      this._config.numTestsPerEvictionRun,\n      this._availableObjects.length\n    );\n    const evictionConfig = {\n      softIdleTimeoutMillis: this._config.softIdleTimeoutMillis,\n      idleTimeoutMillis: this._config.idleTimeoutMillis,\n      min: this._config.min\n    };\n    for (let testsHaveRun = 0; testsHaveRun < testsToRun; ) {\n      const iterationResult = this._evictionIterator.next();\n\n      // Safety check incase we could get stuck in infinite loop because we\n      // somehow emptied the array after checking its length.\n      if (iterationResult.done === true && this._availableObjects.length < 1) {\n        this._evictionIterator.reset();\n        return;\n      }\n      // If this happens it should just mean we reached the end of the\n      // list and can reset the cursor.\n      if (iterationResult.done === true && this._availableObjects.length > 0) {\n        this._evictionIterator.reset();\n        continue;\n      }\n\n      const resource = iterationResult.value;\n\n      const shouldEvict = this._evictor.evict(\n        evictionConfig,\n        resource,\n        this._availableObjects.length\n      );\n      testsHaveRun++;\n\n      if (shouldEvict === true) {\n        // take it out of the _availableObjects list\n        this._evictionIterator.remove();\n        this._destroy(resource);\n      }\n    }\n  }\n\n  _scheduleEvictorRun() {\n    // Start eviction if set\n    if (this._config.evictionRunIntervalMillis > 0) {\n      // @ts-ignore\n      this._scheduledEviction = setTimeout(() => {\n        this._evict();\n        this._scheduleEvictorRun();\n      }, this._config.evictionRunIntervalMillis).unref();\n    }\n  }\n\n  _descheduleEvictorRun() {\n    if (this._scheduledEviction) {\n      clearTimeout(this._scheduledEviction);\n    }\n    this._scheduledEviction = null;\n  }\n\n  start() {\n    if (this._draining === true) {\n      return;\n    }\n    if (this._started === true) {\n      return;\n    }\n    this._started = true;\n    this._scheduleEvictorRun();\n    this._ensureMinimum();\n  }\n\n  /**\n   * Request a new resource. The callback will be called,\n   * when a new resource is available, passing the resource to the callback.\n   * TODO: should we add a seperate \"acquireWithPriority\" function\n   *\n   * @param {Number} [priority=0]\n   *   Optional.  Integer between 0 and (priorityRange - 1).  Specifies the priority\n   *   of the caller if there are no available resources.  Lower numbers mean higher\n   *   priority.\n   *\n   * @returns {Promise}\n   */\n  acquire(priority) {\n    if (this._started === false && this._config.autostart === false) {\n      this.start();\n    }\n\n    if (this._draining) {\n      return this._Promise.reject(\n        new Error(\"pool is draining and cannot accept work\")\n      );\n    }\n\n    // TODO: should we defer this check till after this event loop incase \"the situation\" changes in the meantime\n    if (\n      this.spareResourceCapacity < 1 &&\n      this._availableObjects.length < 1 &&\n      this._config.maxWaitingClients !== undefined &&\n      this._waitingClientsQueue.length >= this._config.maxWaitingClients\n    ) {\n      return this._Promise.reject(\n        new Error(\"max waitingClients count exceeded\")\n      );\n    }\n\n    const resourceRequest = new ResourceRequest(\n      this._config.acquireTimeoutMillis,\n      this._Promise\n    );\n    this._waitingClientsQueue.enqueue(resourceRequest, priority);\n    this._dispense();\n\n    return resourceRequest.promise;\n  }\n\n  /**\n   * [use method, aquires a resource, passes the resource to a user supplied function and releases it]\n   * @param  {Function} fn [a function that accepts a resource and returns a promise that resolves/rejects once it has finished using the resource]\n   * @return {Promise}      [resolves once the resource is released to the pool]\n   */\n  use(fn, priority) {\n    return this.acquire(priority).then(resource => {\n      return fn(resource).then(\n        result => {\n          this.release(resource);\n          return result;\n        },\n        err => {\n          this.destroy(resource);\n          throw err;\n        }\n      );\n    });\n  }\n\n  /**\n   * Check if resource is currently on loan from the pool\n   *\n   * @param {Function} resource\n   *    Resource for checking.\n   *\n   * @returns {Boolean}\n   *  True if resource belongs to this pool and false otherwise\n   */\n  isBorrowedResource(resource) {\n    return this._resourceLoans.has(resource);\n  }\n\n  /**\n   * Return the resource to the pool when it is no longer required.\n   *\n   * @param {Object} resource\n   *   The acquired object to be put back to the pool.\n   */\n  release(resource) {\n    // check for an outstanding loan\n    const loan = this._resourceLoans.get(resource);\n\n    if (loan === undefined) {\n      return this._Promise.reject(\n        new Error(\"Resource not currently part of this pool\")\n      );\n    }\n\n    this._resourceLoans.delete(resource);\n    loan.resolve();\n    const pooledResource = loan.pooledResource;\n\n    pooledResource.deallocate();\n    this._addPooledResourceToAvailableObjects(pooledResource);\n\n    this._dispense();\n    return this._Promise.resolve();\n  }\n\n  /**\n   * Request the resource to be destroyed. The factory's destroy handler\n   * will also be called.\n   *\n   * This should be called within an acquire() block as an alternative to release().\n   *\n   * @param {Object} resource\n   *   The acquired resource to be destoyed.\n   */\n  destroy(resource) {\n    // check for an outstanding loan\n    const loan = this._resourceLoans.get(resource);\n\n    if (loan === undefined) {\n      return this._Promise.reject(\n        new Error(\"Resource not currently part of this pool\")\n      );\n    }\n\n    this._resourceLoans.delete(resource);\n    loan.resolve();\n    const pooledResource = loan.pooledResource;\n\n    pooledResource.deallocate();\n    this._destroy(pooledResource);\n\n    this._dispense();\n    return this._Promise.resolve();\n  }\n\n  _addPooledResourceToAvailableObjects(pooledResource) {\n    pooledResource.idle();\n    if (this._config.fifo === true) {\n      this._availableObjects.push(pooledResource);\n    } else {\n      this._availableObjects.unshift(pooledResource);\n    }\n  }\n\n  /**\n   * Disallow any new acquire calls and let the request backlog dissapate.\n   * The Pool will no longer attempt to maintain a \"min\" number of resources\n   * and will only make new resources on demand.\n   * Resolves once all resource requests are fulfilled and all resources are returned to pool and available...\n   * Should probably be called \"drain work\"\n   * @returns {Promise}\n   */\n  drain() {\n    this._draining = true;\n    return this.__allResourceRequestsSettled()\n      .then(() => {\n        return this.__allResourcesReturned();\n      })\n      .then(() => {\n        this._descheduleEvictorRun();\n      });\n  }\n\n  __allResourceRequestsSettled() {\n    if (this._waitingClientsQueue.length > 0) {\n      // wait for last waiting client to be settled\n      // FIXME: what if they can \"resolve\" out of order....?\n      return reflector(this._waitingClientsQueue.tail.promise);\n    }\n    return this._Promise.resolve();\n  }\n\n  // FIXME: this is a horrific mess\n  __allResourcesReturned() {\n    const ps = Array.from(this._resourceLoans.values())\n      .map(loan => loan.promise)\n      .map(reflector);\n    return this._Promise.all(ps);\n  }\n\n  /**\n   * Forcibly destroys all available resources regardless of timeout.  Intended to be\n   * invoked as part of a drain.  Does not prevent the creation of new\n   * resources as a result of subsequent calls to acquire.\n   *\n   * Note that if factory.min > 0 and the pool isn't \"draining\", the pool will destroy all idle resources\n   * in the pool, but replace them with newly created resources up to the\n   * specified factory.min value.  If this is not desired, set factory.min\n   * to zero before calling clear()\n   *\n   */\n  clear() {\n    const reflectedCreatePromises = Array.from(\n      this._factoryCreateOperations\n    ).map(reflector);\n\n    // wait for outstanding factory.create to complete\n    return this._Promise.all(reflectedCreatePromises).then(() => {\n      // Destroy existing resources\n      // @ts-ignore\n      for (const resource of this._availableObjects) {\n        this._destroy(resource);\n      }\n      const reflectedDestroyPromises = Array.from(\n        this._factoryDestroyOperations\n      ).map(reflector);\n      return reflector(this._Promise.all(reflectedDestroyPromises));\n    });\n  }\n\n  /**\n   * Waits until the pool is ready.\n   * We define ready by checking if the current resource number is at least\n   * the minimum number defined.\n   * @returns {Promise} that resolves when the minimum number is ready.\n   */\n  ready() {\n    return new this._Promise(resolve => {\n      const isReady = () => {\n        if (this.available >= this.min) {\n          resolve();\n        } else {\n          setTimeout(isReady, 100);\n        }\n      };\n\n      isReady();\n    });\n  }\n\n  /**\n   * How many resources are available to allocated\n   * (includes resources that have not been tested and may faul validation)\n   * NOTE: internal for now as the name is awful and might not be useful to anyone\n   * @return {Number} number of resources the pool has to allocate\n   */\n  get _potentiallyAllocableResourceCount() {\n    return (\n      this._availableObjects.length +\n      this._testOnBorrowResources.size +\n      this._testOnReturnResources.size +\n      this._factoryCreateOperations.size\n    );\n  }\n\n  /**\n   * The combined count of the currently created objects and those in the\n   * process of being created\n   * Does NOT include resources in the process of being destroyed\n   * sort of legacy...\n   * @return {Number}\n   */\n  get _count() {\n    return this._allObjects.size + this._factoryCreateOperations.size;\n  }\n\n  /**\n   * How many more resources does the pool have room for\n   * @return {Number} number of resources the pool could create before hitting any limits\n   */\n  get spareResourceCapacity() {\n    return (\n      this._config.max -\n      (this._allObjects.size + this._factoryCreateOperations.size)\n    );\n  }\n\n  /**\n   * see _count above\n   * @return {Number} [description]\n   */\n  get size() {\n    return this._count;\n  }\n\n  /**\n   * number of available resources\n   * @return {Number} [description]\n   */\n  get available() {\n    return this._availableObjects.length;\n  }\n\n  /**\n   * number of resources that are currently acquired\n   * @return {Number} [description]\n   */\n  get borrowed() {\n    return this._resourceLoans.size;\n  }\n\n  /**\n   * number of waiting acquire calls\n   * @return {Number} [description]\n   */\n  get pending() {\n    return this._waitingClientsQueue.length;\n  }\n\n  /**\n   * maximum size of the pool\n   * @return {Number} [description]\n   */\n  get max() {\n    return this._config.max;\n  }\n\n  /**\n   * minimum size of the pool\n   * @return {Number} [description]\n   */\n  get min() {\n    return this._config.min;\n  }\n}\n\nmodule.exports = Pool;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/Pool.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/PoolDefaults.js":
/*!*******************************************************!*\
  !*** ./node_modules/generic-pool/lib/PoolDefaults.js ***!
  \*******************************************************/
/***/ ((module) => {

"use strict";
eval("\n/**\n * Create the default settings used by the pool\n *\n * @class\n */\nclass PoolDefaults {\n  constructor() {\n    this.fifo = true;\n    this.priorityRange = 1;\n\n    this.testOnBorrow = false;\n    this.testOnReturn = false;\n\n    this.autostart = true;\n\n    this.evictionRunIntervalMillis = 0;\n    this.numTestsPerEvictionRun = 3;\n    this.softIdleTimeoutMillis = -1;\n    this.idleTimeoutMillis = 30000;\n\n    // FIXME: no defaults!\n    this.acquireTimeoutMillis = null;\n    this.destroyTimeoutMillis = null;\n    this.maxWaitingClients = null;\n\n    this.min = null;\n    this.max = null;\n    // FIXME: this seems odd?\n    this.Promise = Promise;\n  }\n}\n\nmodule.exports = PoolDefaults;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2xpYi9Qb29sRGVmYXVsdHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb2RleGd1YXJkLy4vbm9kZV9tb2R1bGVzL2dlbmVyaWMtcG9vbC9saWIvUG9vbERlZmF1bHRzLmpzPzYwMTMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKipcbiAqIENyZWF0ZSB0aGUgZGVmYXVsdCBzZXR0aW5ncyB1c2VkIGJ5IHRoZSBwb29sXG4gKlxuICogQGNsYXNzXG4gKi9cbmNsYXNzIFBvb2xEZWZhdWx0cyB7XG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIHRoaXMuZmlmbyA9IHRydWU7XG4gICAgdGhpcy5wcmlvcml0eVJhbmdlID0gMTtcblxuICAgIHRoaXMudGVzdE9uQm9ycm93ID0gZmFsc2U7XG4gICAgdGhpcy50ZXN0T25SZXR1cm4gPSBmYWxzZTtcblxuICAgIHRoaXMuYXV0b3N0YXJ0ID0gdHJ1ZTtcblxuICAgIHRoaXMuZXZpY3Rpb25SdW5JbnRlcnZhbE1pbGxpcyA9IDA7XG4gICAgdGhpcy5udW1UZXN0c1BlckV2aWN0aW9uUnVuID0gMztcbiAgICB0aGlzLnNvZnRJZGxlVGltZW91dE1pbGxpcyA9IC0xO1xuICAgIHRoaXMuaWRsZVRpbWVvdXRNaWxsaXMgPSAzMDAwMDtcblxuICAgIC8vIEZJWE1FOiBubyBkZWZhdWx0cyFcbiAgICB0aGlzLmFjcXVpcmVUaW1lb3V0TWlsbGlzID0gbnVsbDtcbiAgICB0aGlzLmRlc3Ryb3lUaW1lb3V0TWlsbGlzID0gbnVsbDtcbiAgICB0aGlzLm1heFdhaXRpbmdDbGllbnRzID0gbnVsbDtcblxuICAgIHRoaXMubWluID0gbnVsbDtcbiAgICB0aGlzLm1heCA9IG51bGw7XG4gICAgLy8gRklYTUU6IHRoaXMgc2VlbXMgb2RkP1xuICAgIHRoaXMuUHJvbWlzZSA9IFByb21pc2U7XG4gIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSBQb29sRGVmYXVsdHM7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/PoolDefaults.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/PoolOptions.js":
/*!******************************************************!*\
  !*** ./node_modules/generic-pool/lib/PoolOptions.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst PoolDefaults = __webpack_require__(/*! ./PoolDefaults */ \"(rsc)/./node_modules/generic-pool/lib/PoolDefaults.js\");\n\nclass PoolOptions {\n  /**\n   * @param {Object} opts\n   *   configuration for the pool\n   * @param {Number} [opts.max=null]\n   *   Maximum number of items that can exist at the same time.  Default: 1.\n   *   Any further acquire requests will be pushed to the waiting list.\n   * @param {Number} [opts.min=null]\n   *   Minimum number of items in pool (including in-use). Default: 0.\n   *   When the pool is created, or a resource destroyed, this minimum will\n   *   be checked. If the pool resource count is below the minimum, a new\n   *   resource will be created and added to the pool.\n   * @param {Number} [opts.maxWaitingClients=null]\n   *   maximum number of queued requests allowed after which acquire calls will be rejected\n   * @param {Boolean} [opts.testOnBorrow=false]\n   *   should the pool validate resources before giving them to clients. Requires that\n   *   `factory.validate` is specified.\n   * @param {Boolean} [opts.testOnReturn=false]\n   *   should the pool validate resources before returning them to the pool.\n   * @param {Number} [opts.acquireTimeoutMillis=null]\n   *   Delay in milliseconds after which the an `acquire` call will fail. optional.\n   *   Default: undefined. Should be positive and non-zero\n   * @param {Number} [opts.destroyTimeoutMillis=null]\n   *   Delay in milliseconds after which the an `destroy` call will fail, causing it to emit a factoryDestroyError event. optional.\n   *   Default: undefined. Should be positive and non-zero\n   * @param {Number} [opts.priorityRange=1]\n   *   The range from 1 to be treated as a valid priority\n   * @param {Boolean} [opts.fifo=true]\n   *   Sets whether the pool has LIFO (last in, first out) behaviour with respect to idle objects.\n   *   if false then pool has FIFO behaviour\n   * @param {Boolean} [opts.autostart=true]\n   *   Should the pool start creating resources etc once the constructor is called\n   * @param {Number} [opts.evictionRunIntervalMillis=0]\n   *   How often to run eviction checks.  Default: 0 (does not run).\n   * @param {Number} [opts.numTestsPerEvictionRun=3]\n   *   Number of resources to check each eviction run.  Default: 3.\n   * @param {Number} [opts.softIdleTimeoutMillis=-1]\n   *   amount of time an object may sit idle in the pool before it is eligible\n   *   for eviction by the idle object evictor (if any), with the extra condition\n   *   that at least \"min idle\" object instances remain in the pool. Default -1 (nothing can get evicted)\n   * @param {Number} [opts.idleTimeoutMillis=30000]\n   *   the minimum amount of time that an object may sit idle in the pool before it is eligible for eviction\n   *   due to idle time. Supercedes \"softIdleTimeoutMillis\" Default: 30000\n   * @param {typeof Promise} [opts.Promise=Promise]\n   *   What promise implementation should the pool use, defaults to native promises.\n   */\n  constructor(opts) {\n    const poolDefaults = new PoolDefaults();\n\n    opts = opts || {};\n\n    this.fifo = typeof opts.fifo === \"boolean\" ? opts.fifo : poolDefaults.fifo;\n    this.priorityRange = opts.priorityRange || poolDefaults.priorityRange;\n\n    this.testOnBorrow =\n      typeof opts.testOnBorrow === \"boolean\"\n        ? opts.testOnBorrow\n        : poolDefaults.testOnBorrow;\n    this.testOnReturn =\n      typeof opts.testOnReturn === \"boolean\"\n        ? opts.testOnReturn\n        : poolDefaults.testOnReturn;\n\n    this.autostart =\n      typeof opts.autostart === \"boolean\"\n        ? opts.autostart\n        : poolDefaults.autostart;\n\n    if (opts.acquireTimeoutMillis) {\n      // @ts-ignore\n      this.acquireTimeoutMillis = parseInt(opts.acquireTimeoutMillis, 10);\n    }\n\n    if (opts.destroyTimeoutMillis) {\n      // @ts-ignore\n      this.destroyTimeoutMillis = parseInt(opts.destroyTimeoutMillis, 10);\n    }\n\n    if (opts.maxWaitingClients !== undefined) {\n      // @ts-ignore\n      this.maxWaitingClients = parseInt(opts.maxWaitingClients, 10);\n    }\n\n    // @ts-ignore\n    this.max = parseInt(opts.max, 10);\n    // @ts-ignore\n    this.min = parseInt(opts.min, 10);\n\n    this.max = Math.max(isNaN(this.max) ? 1 : this.max, 1);\n    this.min = Math.min(isNaN(this.min) ? 0 : this.min, this.max);\n\n    this.evictionRunIntervalMillis =\n      opts.evictionRunIntervalMillis || poolDefaults.evictionRunIntervalMillis;\n    this.numTestsPerEvictionRun =\n      opts.numTestsPerEvictionRun || poolDefaults.numTestsPerEvictionRun;\n    this.softIdleTimeoutMillis =\n      opts.softIdleTimeoutMillis || poolDefaults.softIdleTimeoutMillis;\n    this.idleTimeoutMillis =\n      opts.idleTimeoutMillis || poolDefaults.idleTimeoutMillis;\n\n    this.Promise = opts.Promise != null ? opts.Promise : poolDefaults.Promise;\n  }\n}\n\nmodule.exports = PoolOptions;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/PoolOptions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/PooledResource.js":
/*!*********************************************************!*\
  !*** ./node_modules/generic-pool/lib/PooledResource.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst PooledResourceStateEnum = __webpack_require__(/*! ./PooledResourceStateEnum */ \"(rsc)/./node_modules/generic-pool/lib/PooledResourceStateEnum.js\");\n\n/**\n * @class\n * @private\n */\nclass PooledResource {\n  constructor(resource) {\n    this.creationTime = Date.now();\n    this.lastReturnTime = null;\n    this.lastBorrowTime = null;\n    this.lastIdleTime = null;\n    this.obj = resource;\n    this.state = PooledResourceStateEnum.IDLE;\n  }\n\n  // mark the resource as \"allocated\"\n  allocate() {\n    this.lastBorrowTime = Date.now();\n    this.state = PooledResourceStateEnum.ALLOCATED;\n  }\n\n  // mark the resource as \"deallocated\"\n  deallocate() {\n    this.lastReturnTime = Date.now();\n    this.state = PooledResourceStateEnum.IDLE;\n  }\n\n  invalidate() {\n    this.state = PooledResourceStateEnum.INVALID;\n  }\n\n  test() {\n    this.state = PooledResourceStateEnum.VALIDATION;\n  }\n\n  idle() {\n    this.lastIdleTime = Date.now();\n    this.state = PooledResourceStateEnum.IDLE;\n  }\n\n  returning() {\n    this.state = PooledResourceStateEnum.RETURNING;\n  }\n}\n\nmodule.exports = PooledResource;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/PooledResource.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/PooledResourceStateEnum.js":
/*!******************************************************************!*\
  !*** ./node_modules/generic-pool/lib/PooledResourceStateEnum.js ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
eval("\n\nconst PooledResourceStateEnum = {\n  ALLOCATED: \"ALLOCATED\", // In use\n  IDLE: \"IDLE\", // In the queue, not in use.\n  INVALID: \"INVALID\", // Failed validation\n  RETURNING: \"RETURNING\", // Resource is in process of returning\n  VALIDATION: \"VALIDATION\" // Currently being tested\n};\n\nmodule.exports = PooledResourceStateEnum;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2xpYi9Qb29sZWRSZXNvdXJjZVN0YXRlRW51bS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL2tvZGV4Z3VhcmQvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2xpYi9Qb29sZWRSZXNvdXJjZVN0YXRlRW51bS5qcz9lZjczIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5jb25zdCBQb29sZWRSZXNvdXJjZVN0YXRlRW51bSA9IHtcbiAgQUxMT0NBVEVEOiBcIkFMTE9DQVRFRFwiLCAvLyBJbiB1c2VcbiAgSURMRTogXCJJRExFXCIsIC8vIEluIHRoZSBxdWV1ZSwgbm90IGluIHVzZS5cbiAgSU5WQUxJRDogXCJJTlZBTElEXCIsIC8vIEZhaWxlZCB2YWxpZGF0aW9uXG4gIFJFVFVSTklORzogXCJSRVRVUk5JTkdcIiwgLy8gUmVzb3VyY2UgaXMgaW4gcHJvY2VzcyBvZiByZXR1cm5pbmdcbiAgVkFMSURBVElPTjogXCJWQUxJREFUSU9OXCIgLy8gQ3VycmVudGx5IGJlaW5nIHRlc3RlZFxufTtcblxubW9kdWxlLmV4cG9ydHMgPSBQb29sZWRSZXNvdXJjZVN0YXRlRW51bTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/PooledResourceStateEnum.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/PriorityQueue.js":
/*!********************************************************!*\
  !*** ./node_modules/generic-pool/lib/PriorityQueue.js ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst Queue = __webpack_require__(/*! ./Queue */ \"(rsc)/./node_modules/generic-pool/lib/Queue.js\");\n\n/**\n * @class\n * @private\n */\nclass PriorityQueue {\n  constructor(size) {\n    this._size = Math.max(+size | 0, 1);\n    /** @type {Queue[]} */\n    this._slots = [];\n    // initialize arrays to hold queue elements\n    for (let i = 0; i < this._size; i++) {\n      this._slots.push(new Queue());\n    }\n  }\n\n  get length() {\n    let _length = 0;\n    for (let i = 0, slots = this._slots.length; i < slots; i++) {\n      _length += this._slots[i].length;\n    }\n    return _length;\n  }\n\n  enqueue(obj, priority) {\n    // Convert to integer with a default value of 0.\n    priority = (priority && +priority | 0) || 0;\n\n    if (priority) {\n      if (priority < 0 || priority >= this._size) {\n        priority = this._size - 1;\n        // put obj at the end of the line\n      }\n    }\n    this._slots[priority].push(obj);\n  }\n\n  dequeue() {\n    for (let i = 0, sl = this._slots.length; i < sl; i += 1) {\n      if (this._slots[i].length) {\n        return this._slots[i].shift();\n      }\n    }\n    return;\n  }\n\n  get head() {\n    for (let i = 0, sl = this._slots.length; i < sl; i += 1) {\n      if (this._slots[i].length > 0) {\n        return this._slots[i].head;\n      }\n    }\n    return;\n  }\n\n  get tail() {\n    for (let i = this._slots.length - 1; i >= 0; i--) {\n      if (this._slots[i].length > 0) {\n        return this._slots[i].tail;\n      }\n    }\n    return;\n  }\n}\n\nmodule.exports = PriorityQueue;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/PriorityQueue.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/Queue.js":
/*!************************************************!*\
  !*** ./node_modules/generic-pool/lib/Queue.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst DoublyLinkedList = __webpack_require__(/*! ./DoublyLinkedList */ \"(rsc)/./node_modules/generic-pool/lib/DoublyLinkedList.js\");\nconst Deque = __webpack_require__(/*! ./Deque */ \"(rsc)/./node_modules/generic-pool/lib/Deque.js\");\n\n/**\n * Sort of a internal queue for holding the waiting\n * resource requets for a given \"priority\".\n * Also handles managing timeouts rejections on items (is this the best place for this?)\n * This is the last point where we know which queue a resourceRequest is in\n *\n */\nclass Queue extends Deque {\n  /**\n   * Adds the obj to the end of the list for this slot\n   * we completely override the parent method because we need access to the\n   * node for our rejection handler\n   * @param {any} resourceRequest [description]\n   */\n  push(resourceRequest) {\n    const node = DoublyLinkedList.createNode(resourceRequest);\n    resourceRequest.promise.catch(this._createTimeoutRejectionHandler(node));\n    this._list.insertEnd(node);\n  }\n\n  _createTimeoutRejectionHandler(node) {\n    return reason => {\n      if (reason.name === \"TimeoutError\") {\n        this._list.remove(node);\n      }\n    };\n  }\n}\n\nmodule.exports = Queue;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/Queue.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/ResourceLoan.js":
/*!*******************************************************!*\
  !*** ./node_modules/generic-pool/lib/ResourceLoan.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst Deferred = __webpack_require__(/*! ./Deferred */ \"(rsc)/./node_modules/generic-pool/lib/Deferred.js\");\n\n/**\n * Plan is to maybe add tracking via Error objects\n * and other fun stuff!\n */\n\nclass ResourceLoan extends Deferred {\n  /**\n   *\n   * @param  {any} pooledResource the PooledResource this loan belongs to\n   * @return {any}                [description]\n   */\n  constructor(pooledResource, Promise) {\n    super(Promise);\n    this._creationTimestamp = Date.now();\n    this.pooledResource = pooledResource;\n  }\n\n  reject() {\n    /**\n     * Loans can only be resolved at the moment\n     */\n  }\n}\n\nmodule.exports = ResourceLoan;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2xpYi9SZXNvdXJjZUxvYW4uanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsaUJBQWlCLG1CQUFPLENBQUMscUVBQVk7O0FBRXJDO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsS0FBSztBQUNuQixjQUFjLG9CQUFvQjtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va29kZXhndWFyZC8uL25vZGVfbW9kdWxlcy9nZW5lcmljLXBvb2wvbGliL1Jlc291cmNlTG9hbi5qcz9kY2Q3Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5jb25zdCBEZWZlcnJlZCA9IHJlcXVpcmUoXCIuL0RlZmVycmVkXCIpO1xuXG4vKipcbiAqIFBsYW4gaXMgdG8gbWF5YmUgYWRkIHRyYWNraW5nIHZpYSBFcnJvciBvYmplY3RzXG4gKiBhbmQgb3RoZXIgZnVuIHN0dWZmIVxuICovXG5cbmNsYXNzIFJlc291cmNlTG9hbiBleHRlbmRzIERlZmVycmVkIHtcbiAgLyoqXG4gICAqXG4gICAqIEBwYXJhbSAge2FueX0gcG9vbGVkUmVzb3VyY2UgdGhlIFBvb2xlZFJlc291cmNlIHRoaXMgbG9hbiBiZWxvbmdzIHRvXG4gICAqIEByZXR1cm4ge2FueX0gICAgICAgICAgICAgICAgW2Rlc2NyaXB0aW9uXVxuICAgKi9cbiAgY29uc3RydWN0b3IocG9vbGVkUmVzb3VyY2UsIFByb21pc2UpIHtcbiAgICBzdXBlcihQcm9taXNlKTtcbiAgICB0aGlzLl9jcmVhdGlvblRpbWVzdGFtcCA9IERhdGUubm93KCk7XG4gICAgdGhpcy5wb29sZWRSZXNvdXJjZSA9IHBvb2xlZFJlc291cmNlO1xuICB9XG5cbiAgcmVqZWN0KCkge1xuICAgIC8qKlxuICAgICAqIExvYW5zIGNhbiBvbmx5IGJlIHJlc29sdmVkIGF0IHRoZSBtb21lbnRcbiAgICAgKi9cbiAgfVxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IFJlc291cmNlTG9hbjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/ResourceLoan.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/ResourceRequest.js":
/*!**********************************************************!*\
  !*** ./node_modules/generic-pool/lib/ResourceRequest.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst Deferred = __webpack_require__(/*! ./Deferred */ \"(rsc)/./node_modules/generic-pool/lib/Deferred.js\");\nconst errors = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/generic-pool/lib/errors.js\");\n\nfunction fbind(fn, ctx) {\n  return function bound() {\n    return fn.apply(ctx, arguments);\n  };\n}\n\n/**\n * Wraps a users request for a resource\n * Basically a promise mashed in with a timeout\n * @private\n */\nclass ResourceRequest extends Deferred {\n  /**\n   * [constructor description]\n   * @param  {Number} ttl     timeout\n   */\n  constructor(ttl, Promise) {\n    super(Promise);\n    this._creationTimestamp = Date.now();\n    this._timeout = null;\n\n    if (ttl !== undefined) {\n      this.setTimeout(ttl);\n    }\n  }\n\n  setTimeout(delay) {\n    if (this._state !== ResourceRequest.PENDING) {\n      return;\n    }\n    const ttl = parseInt(delay, 10);\n\n    if (isNaN(ttl) || ttl <= 0) {\n      throw new Error(\"delay must be a positive int\");\n    }\n\n    const age = Date.now() - this._creationTimestamp;\n\n    if (this._timeout) {\n      this.removeTimeout();\n    }\n\n    this._timeout = setTimeout(\n      fbind(this._fireTimeout, this),\n      Math.max(ttl - age, 0)\n    );\n  }\n\n  removeTimeout() {\n    if (this._timeout) {\n      clearTimeout(this._timeout);\n    }\n    this._timeout = null;\n  }\n\n  _fireTimeout() {\n    this.reject(new errors.TimeoutError(\"ResourceRequest timed out\"));\n  }\n\n  reject(reason) {\n    this.removeTimeout();\n    super.reject(reason);\n  }\n\n  resolve(value) {\n    this.removeTimeout();\n    super.resolve(value);\n  }\n}\n\nmodule.exports = ResourceRequest;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/ResourceRequest.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/errors.js":
/*!*************************************************!*\
  !*** ./node_modules/generic-pool/lib/errors.js ***!
  \*************************************************/
/***/ ((module) => {

"use strict";
eval("\n\nclass ExtendableError extends Error {\n  constructor(message) {\n    super(message);\n    // @ts-ignore\n    this.name = this.constructor.name;\n    this.message = message;\n    if (typeof Error.captureStackTrace === \"function\") {\n      Error.captureStackTrace(this, this.constructor);\n    } else {\n      this.stack = new Error(message).stack;\n    }\n  }\n}\n\n/* eslint-disable no-useless-constructor */\nclass TimeoutError extends ExtendableError {\n  constructor(m) {\n    super(m);\n  }\n}\n/* eslint-enable no-useless-constructor */\n\nmodule.exports = {\n  TimeoutError: TimeoutError\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2xpYi9lcnJvcnMuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb2RleGd1YXJkLy4vbm9kZV9tb2R1bGVzL2dlbmVyaWMtcG9vbC9saWIvZXJyb3JzLmpzPzdiN2YiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbmNsYXNzIEV4dGVuZGFibGVFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgY29uc3RydWN0b3IobWVzc2FnZSkge1xuICAgIHN1cGVyKG1lc3NhZ2UpO1xuICAgIC8vIEB0cy1pZ25vcmVcbiAgICB0aGlzLm5hbWUgPSB0aGlzLmNvbnN0cnVjdG9yLm5hbWU7XG4gICAgdGhpcy5tZXNzYWdlID0gbWVzc2FnZTtcbiAgICBpZiAodHlwZW9mIEVycm9yLmNhcHR1cmVTdGFja1RyYWNlID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICAgIEVycm9yLmNhcHR1cmVTdGFja1RyYWNlKHRoaXMsIHRoaXMuY29uc3RydWN0b3IpO1xuICAgIH0gZWxzZSB7XG4gICAgICB0aGlzLnN0YWNrID0gbmV3IEVycm9yKG1lc3NhZ2UpLnN0YWNrO1xuICAgIH1cbiAgfVxufVxuXG4vKiBlc2xpbnQtZGlzYWJsZSBuby11c2VsZXNzLWNvbnN0cnVjdG9yICovXG5jbGFzcyBUaW1lb3V0RXJyb3IgZXh0ZW5kcyBFeHRlbmRhYmxlRXJyb3Ige1xuICBjb25zdHJ1Y3RvcihtKSB7XG4gICAgc3VwZXIobSk7XG4gIH1cbn1cbi8qIGVzbGludC1lbmFibGUgbm8tdXNlbGVzcy1jb25zdHJ1Y3RvciAqL1xuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgVGltZW91dEVycm9yOiBUaW1lb3V0RXJyb3Jcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/errors.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/factoryValidator.js":
/*!***********************************************************!*\
  !*** ./node_modules/generic-pool/lib/factoryValidator.js ***!
  \***********************************************************/
/***/ ((module) => {

eval("module.exports = function(factory) {\n  if (typeof factory.create !== \"function\") {\n    throw new TypeError(\"factory.create must be a function\");\n  }\n\n  if (typeof factory.destroy !== \"function\") {\n    throw new TypeError(\"factory.destroy must be a function\");\n  }\n\n  if (\n    typeof factory.validate !== \"undefined\" &&\n    typeof factory.validate !== \"function\"\n  ) {\n    throw new TypeError(\"factory.validate must be a function\");\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2xpYi9mYWN0b3J5VmFsaWRhdG9yLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2tvZGV4Z3VhcmQvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2xpYi9mYWN0b3J5VmFsaWRhdG9yLmpzPzQ0NDUiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbihmYWN0b3J5KSB7XG4gIGlmICh0eXBlb2YgZmFjdG9yeS5jcmVhdGUgIT09IFwiZnVuY3Rpb25cIikge1xuICAgIHRocm93IG5ldyBUeXBlRXJyb3IoXCJmYWN0b3J5LmNyZWF0ZSBtdXN0IGJlIGEgZnVuY3Rpb25cIik7XG4gIH1cblxuICBpZiAodHlwZW9mIGZhY3RvcnkuZGVzdHJveSAhPT0gXCJmdW5jdGlvblwiKSB7XG4gICAgdGhyb3cgbmV3IFR5cGVFcnJvcihcImZhY3RvcnkuZGVzdHJveSBtdXN0IGJlIGEgZnVuY3Rpb25cIik7XG4gIH1cblxuICBpZiAoXG4gICAgdHlwZW9mIGZhY3RvcnkudmFsaWRhdGUgIT09IFwidW5kZWZpbmVkXCIgJiZcbiAgICB0eXBlb2YgZmFjdG9yeS52YWxpZGF0ZSAhPT0gXCJmdW5jdGlvblwiXG4gICkge1xuICAgIHRocm93IG5ldyBUeXBlRXJyb3IoXCJmYWN0b3J5LnZhbGlkYXRlIG11c3QgYmUgYSBmdW5jdGlvblwiKTtcbiAgfVxufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/factoryValidator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/generic-pool/lib/utils.js":
/*!************************************************!*\
  !*** ./node_modules/generic-pool/lib/utils.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nfunction noop() {}\n\n/**\n * Reflects a promise but does not expose any\n * underlying value or rejection from that promise.\n * @param  {Promise} promise [description]\n * @return {Promise}         [description]\n */\nexports.reflector = function(promise) {\n  return promise.then(noop, noop);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2xpYi91dGlscy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLFNBQVM7QUFDckIsWUFBWSxpQkFBaUI7QUFDN0I7QUFDQSxpQkFBaUI7QUFDakI7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2tvZGV4Z3VhcmQvLi9ub2RlX21vZHVsZXMvZ2VuZXJpYy1wb29sL2xpYi91dGlscy5qcz8yNjVkIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5mdW5jdGlvbiBub29wKCkge31cblxuLyoqXG4gKiBSZWZsZWN0cyBhIHByb21pc2UgYnV0IGRvZXMgbm90IGV4cG9zZSBhbnlcbiAqIHVuZGVybHlpbmcgdmFsdWUgb3IgcmVqZWN0aW9uIGZyb20gdGhhdCBwcm9taXNlLlxuICogQHBhcmFtICB7UHJvbWlzZX0gcHJvbWlzZSBbZGVzY3JpcHRpb25dXG4gKiBAcmV0dXJuIHtQcm9taXNlfSAgICAgICAgIFtkZXNjcmlwdGlvbl1cbiAqL1xuZXhwb3J0cy5yZWZsZWN0b3IgPSBmdW5jdGlvbihwcm9taXNlKSB7XG4gIHJldHVybiBwcm9taXNlLnRoZW4obm9vcCwgbm9vcCk7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/generic-pool/lib/utils.js\n");

/***/ })

};
;