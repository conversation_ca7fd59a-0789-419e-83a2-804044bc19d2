import { NextRequest, NextResponse } from 'next/server'

interface ScanHistory {
  id: string
  target: string
  scanType: string[]
  status: 'completed' | 'running' | 'failed' | 'queued'
  vulnerabilities: {
    critical: number
    high: number
    medium: number
    low: number
    info: number
  }
  startTime: string
  endTime?: string
  duration?: number
  progress: number
}

export async function GET(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Simple token validation for testing
    if (!token.startsWith('kxg_')) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    // Mock scan history data
    const scanHistory: ScanHistory[] = [
      {
        id: 'scan_001',
        target: 'https://example.com',
        scanType: ['sqli', 'xss', 'csrf'],
        status: 'completed',
        vulnerabilities: {
          critical: 2,
          high: 5,
          medium: 8,
          low: 12,
          info: 3
        },
        startTime: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
        endTime: new Date(Date.now() - 3300000).toISOString(), // 55 minutes ago
        duration: 300, // 5 minutes
        progress: 100
      },
      {
        id: 'scan_002',
        target: 'https://testsite.com',
        scanType: ['port_scan', 'ssl_check'],
        status: 'completed',
        vulnerabilities: {
          critical: 0,
          high: 1,
          medium: 3,
          low: 5,
          info: 2
        },
        startTime: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
        endTime: new Date(Date.now() - 6900000).toISOString(), // 1h 55m ago
        duration: 180, // 3 minutes
        progress: 100
      },
      {
        id: 'scan_003',
        target: 'https://webapp.local',
        scanType: ['sqli', 'xss', 'lfi', 'rfi'],
        status: 'running',
        vulnerabilities: {
          critical: 1,
          high: 2,
          medium: 4,
          low: 6,
          info: 1
        },
        startTime: new Date(Date.now() - 1800000).toISOString(), // 30 minutes ago
        duration: 1800, // 30 minutes so far
        progress: 65
      },
      {
        id: 'scan_004',
        target: 'https://api.example.com',
        scanType: ['api_security', 'auth_bypass'],
        status: 'failed',
        vulnerabilities: {
          critical: 0,
          high: 0,
          medium: 0,
          low: 0,
          info: 0
        },
        startTime: new Date(Date.now() - 10800000).toISOString(), // 3 hours ago
        endTime: new Date(Date.now() - 10740000).toISOString(), // 2h 59m ago
        duration: 60, // 1 minute before failure
        progress: 15
      },
      {
        id: 'scan_005',
        target: 'https://mobile-app.com',
        scanType: ['mobile_security', 'ssl_check'],
        status: 'queued',
        vulnerabilities: {
          critical: 0,
          high: 0,
          medium: 0,
          low: 0,
          info: 0
        },
        startTime: new Date().toISOString(),
        progress: 0
      }
    ]

    // Sort by start time (newest first)
    scanHistory.sort((a, b) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime())

    return NextResponse.json({
      success: true,
      data: scanHistory,
      meta: {
        total: scanHistory.length,
        completed: scanHistory.filter(s => s.status === 'completed').length,
        running: scanHistory.filter(s => s.status === 'running').length,
        failed: scanHistory.filter(s => s.status === 'failed').length,
        queued: scanHistory.filter(s => s.status === 'queued').length
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Scanner history API error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Simple token validation for testing
    if (!token.startsWith('kxg_')) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    const body = await request.json()
    const { scanId } = body

    if (!scanId) {
      return NextResponse.json({
        success: false,
        error: 'Scan ID is required'
      }, { status: 400 })
    }

    // In a real app, delete the scan from database
    // For demo, just return success
    
    return NextResponse.json({
      success: true,
      message: `Scan ${scanId} deleted successfully`,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Delete scan error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}
