'use client'

import { useState, useCallback } from 'react'
import Navbar from '@/components/Navbar'
import { Card, StatCard, AlertCard } from '@/components/Card'
import { 
  FileSearch, 
  Upload, 
  File, 
  AlertTriangle, 
  Shield,
  Download,
  Trash2,
  Eye,
  Clock,
  CheckCircle,
  XCircle,
  RefreshCw,
  Database,
  Zap,
  Key,
  Bug
} from 'lucide-react'

interface FileAnalysis {
  id: string
  filename: string
  fileSize: number
  mimeType: string
  hash: string
  threatLevel: 'safe' | 'suspicious' | 'malicious'
  analysisType: 'malware' | 'webshell' | 'secret' | 'general'
  results: {
    malwareDetection?: {
      detected: boolean
      signatures: string[]
      family?: string
    }
    webshellDetection?: {
      detected: boolean
      type?: string
      obfuscated: boolean
    }
    secretDetection?: {
      detected: boolean
      secrets: Array<{
        type: string
        value: string
        confidence: number
      }>
    }
    generalAnalysis?: {
      entropy: number
      suspiciousStrings: string[]
      fileStructure: any
    }
  }
  timestamp: string
  status: 'analyzing' | 'completed' | 'failed'
}

export default function FileAnalyzerPage() {
  const [user] = useState({
    username: 'admin',
    avatar: '',
    role: 'super_admin',
    plan: 'cybersecurity'
  })

  const [dragActive, setDragActive] = useState(false)
  const [files, setFiles] = useState<File[]>([])
  const [analyses, setAnalyses] = useState<FileAnalysis[]>([])
  const [analyzing, setAnalyzing] = useState(false)
  const [stats, setStats] = useState({
    totalFiles: 234,
    malwareDetected: 15,
    secretsFound: 42,
    webshellsFound: 8
  })

  const supportedTypes = [
    '.php', '.js', '.py', '.txt', '.zip', '.apk', '.exe', '.pdf', '.doc', '.docx'
  ]

  const threatLevelColors = {
    safe: 'text-green-400 bg-green-900/20',
    suspicious: 'text-yellow-400 bg-yellow-900/20',
    malicious: 'text-red-400 bg-red-900/20'
  }

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const newFiles = Array.from(e.dataTransfer.files)
      setFiles(prev => [...prev, ...newFiles])
    }
  }, [])

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files)
      setFiles(prev => [...prev, ...newFiles])
    }
  }

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index))
  }

  const analyzeFiles = async () => {
    if (files.length === 0) return

    setAnalyzing(true)

    for (const file of files) {
      const analysis: FileAnalysis = {
        id: Math.random().toString(36).substr(2, 9),
        filename: file.name,
        fileSize: file.size,
        mimeType: file.type || 'application/octet-stream',
        hash: 'sha256:' + Math.random().toString(36).substr(2, 64),
        threatLevel: 'safe',
        analysisType: 'general',
        results: {},
        timestamp: new Date().toISOString(),
        status: 'analyzing'
      }

      setAnalyses(prev => [...prev, analysis])

      // Simulate analysis
      setTimeout(async () => {
        const mockResults = await simulateAnalysis(file)
        setAnalyses(prev => prev.map(a => 
          a.id === analysis.id 
            ? { ...a, ...mockResults, status: 'completed' }
            : a
        ))
      }, Math.random() * 3000 + 2000)
    }

    setFiles([])
    setAnalyzing(false)
  }

  const simulateAnalysis = async (file: File): Promise<Partial<FileAnalysis>> => {
    const extension = file.name.split('.').pop()?.toLowerCase()
    
    if (extension === 'php') {
      return {
        threatLevel: Math.random() > 0.7 ? 'suspicious' : 'safe',
        analysisType: 'webshell',
        results: {
          webshellDetection: {
            detected: Math.random() > 0.6,
            type: Math.random() > 0.5 ? 'PHP Webshell' : 'Backdoor',
            obfuscated: Math.random() > 0.5
          },
          secretDetection: {
            detected: Math.random() > 0.4,
            secrets: [
              {
                type: 'API Key',
                value: 'sk-1234567890abcdef',
                confidence: 95
              },
              {
                type: 'Database Password',
                value: 'admin123',
                confidence: 87
              }
            ]
          }
        }
      }
    }

    if (extension === 'exe' || extension === 'apk') {
      return {
        threatLevel: Math.random() > 0.8 ? 'malicious' : 'safe',
        analysisType: 'malware',
        results: {
          malwareDetection: {
            detected: Math.random() > 0.7,
            signatures: ['Trojan.Generic', 'Backdoor.Agent'],
            family: 'Generic Trojan'
          }
        }
      }
    }

    return {
      threatLevel: 'safe',
      analysisType: 'general',
      results: {
        generalAnalysis: {
          entropy: Math.random() * 8,
          suspiciousStrings: ['eval', 'base64_decode', 'shell_exec'],
          fileStructure: { sections: 3, imports: 12 }
        }
      }
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getThreatIcon = (threatLevel: string) => {
    switch (threatLevel) {
      case 'malicious': return AlertTriangle
      case 'suspicious': return Eye
      default: return CheckCircle
    }
  }

  return (
    <div className="min-h-screen bg-gradient-cyber">
      <Navbar user={user} />
      
      <div className="pt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <FileSearch className="h-8 w-8 text-cyber-green" />
              <h1 className="text-3xl font-bold font-cyber text-white">
                File <span className="cyber-text">Analyzer</span>
              </h1>
            </div>
            <p className="text-gray-400 max-w-3xl">
              Deteksi webshell, ransomware, DDoS script, dan secret/token/API key dalam berbagai format file. 
              Analisis mendalam dengan threat level assessment.
            </p>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <StatCard
              title="Total Files Analyzed"
              value={stats.totalFiles}
              icon={FileSearch}
              color="green"
              trend={{ value: 23, isPositive: true }}
            />
            <StatCard
              title="Malware Detected"
              value={stats.malwareDetected}
              icon={Bug}
              color="red"
              trend={{ value: -5, isPositive: false }}
            />
            <StatCard
              title="Secrets Found"
              value={stats.secretsFound}
              icon={Key}
              color="purple"
              trend={{ value: 12, isPositive: true }}
            />
            <StatCard
              title="Webshells Found"
              value={stats.webshellsFound}
              icon={Shield}
              color="blue"
              trend={{ value: 3, isPositive: true }}
            />
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* File Upload */}
            <div className="lg:col-span-2">
              <Card border="green" glow>
                <div className="p-6">
                  <h2 className="text-xl font-bold text-white mb-6">Upload Files for Analysis</h2>
                  
                  {/* Drag & Drop Area */}
                  <div
                    className={`border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200 ${
                      dragActive 
                        ? 'border-cyber-green bg-cyber-green/10' 
                        : 'border-gray-600 hover:border-gray-500'
                    }`}
                    onDragEnter={handleDrag}
                    onDragLeave={handleDrag}
                    onDragOver={handleDrag}
                    onDrop={handleDrop}
                  >
                    <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-white mb-2">
                      Drop files here or click to upload
                    </h3>
                    <p className="text-gray-400 mb-4">
                      Supported formats: {supportedTypes.join(', ')}
                    </p>
                    <input
                      type="file"
                      multiple
                      onChange={handleFileSelect}
                      className="hidden"
                      id="file-upload"
                      accept={supportedTypes.join(',')}
                    />
                    <label
                      htmlFor="file-upload"
                      className="cyber-btn-primary cursor-pointer inline-block"
                    >
                      Select Files
                    </label>
                  </div>

                  {/* File List */}
                  {files.length > 0 && (
                    <div className="mt-6">
                      <h3 className="text-lg font-semibold text-white mb-4">
                        Files to Analyze ({files.length})
                      </h3>
                      <div className="space-y-2">
                        {files.map((file, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg"
                          >
                            <div className="flex items-center space-x-3">
                              <File className="h-5 w-5 text-gray-400" />
                              <div>
                                <div className="font-medium text-white">{file.name}</div>
                                <div className="text-sm text-gray-400">
                                  {formatFileSize(file.size)} • {file.type || 'Unknown type'}
                                </div>
                              </div>
                            </div>
                            <button
                              onClick={() => removeFile(index)}
                              className="text-red-400 hover:text-red-300 transition-colors"
                            >
                              <Trash2 className="h-5 w-5" />
                            </button>
                          </div>
                        ))}
                      </div>
                      <button
                        onClick={analyzeFiles}
                        disabled={analyzing}
                        className="w-full mt-4 cyber-btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {analyzing ? (
                          <>
                            <RefreshCw className="h-5 w-5 mr-2 animate-spin" />
                            Analyzing...
                          </>
                        ) : (
                          <>
                            <Zap className="h-5 w-5 mr-2" />
                            Start Analysis
                          </>
                        )}
                      </button>
                    </div>
                  )}
                </div>
              </Card>

              {/* Analysis Results */}
              {analyses.length > 0 && (
                <Card className="mt-6">
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-white mb-6">
                      Analysis Results ({analyses.length})
                    </h3>

                    <div className="space-y-4">
                      {analyses.map((analysis) => {
                        const ThreatIcon = getThreatIcon(analysis.threatLevel)
                        return (
                          <div
                            key={analysis.id}
                            className="bg-gray-800/50 rounded-lg p-4 border border-gray-700"
                          >
                            <div className="flex items-start justify-between mb-4">
                              <div className="flex items-center space-x-3">
                                <File className="h-6 w-6 text-gray-400" />
                                <div>
                                  <h4 className="font-semibold text-white">{analysis.filename}</h4>
                                  <p className="text-sm text-gray-400">
                                    {formatFileSize(analysis.fileSize)} • {analysis.mimeType}
                                  </p>
                                </div>
                              </div>
                              <div className="flex items-center space-x-3">
                                {analysis.status === 'analyzing' ? (
                                  <RefreshCw className="h-5 w-5 text-cyber-green animate-spin" />
                                ) : (
                                  <ThreatIcon className={`h-5 w-5 ${threatLevelColors[analysis.threatLevel].split(' ')[0]}`} />
                                )}
                                <span className={`px-2 py-1 rounded-full text-xs font-semibold ${threatLevelColors[analysis.threatLevel]}`}>
                                  {analysis.threatLevel.toUpperCase()}
                                </span>
                              </div>
                            </div>

                            {analysis.status === 'completed' && (
                              <div className="space-y-4">
                                {/* Hash */}
                                <div>
                                  <span className="text-sm text-gray-400">File Hash:</span>
                                  <code className="text-xs text-cyber-green ml-2 bg-gray-900 px-2 py-1 rounded">
                                    {analysis.hash}
                                  </code>
                                </div>

                                {/* Analysis Results */}
                                {analysis.results.malwareDetection && (
                                  <div className="bg-gray-900/50 rounded p-3">
                                    <h5 className="font-semibold text-white mb-2">Malware Detection</h5>
                                    {analysis.results.malwareDetection.detected ? (
                                      <div className="text-red-400">
                                        <p>⚠️ Malware detected!</p>
                                        <p className="text-sm">Family: {analysis.results.malwareDetection.family}</p>
                                        <p className="text-sm">Signatures: {analysis.results.malwareDetection.signatures.join(', ')}</p>
                                      </div>
                                    ) : (
                                      <p className="text-green-400">✓ No malware detected</p>
                                    )}
                                  </div>
                                )}

                                {analysis.results.webshellDetection && (
                                  <div className="bg-gray-900/50 rounded p-3">
                                    <h5 className="font-semibold text-white mb-2">Webshell Detection</h5>
                                    {analysis.results.webshellDetection.detected ? (
                                      <div className="text-yellow-400">
                                        <p>⚠️ Webshell detected!</p>
                                        <p className="text-sm">Type: {analysis.results.webshellDetection.type}</p>
                                        <p className="text-sm">
                                          Obfuscated: {analysis.results.webshellDetection.obfuscated ? 'Yes' : 'No'}
                                        </p>
                                      </div>
                                    ) : (
                                      <p className="text-green-400">✓ No webshell detected</p>
                                    )}
                                  </div>
                                )}

                                {analysis.results.secretDetection && (
                                  <div className="bg-gray-900/50 rounded p-3">
                                    <h5 className="font-semibold text-white mb-2">Secret Detection</h5>
                                    {analysis.results.secretDetection.detected ? (
                                      <div className="text-purple-400">
                                        <p>⚠️ Secrets found!</p>
                                        {analysis.results.secretDetection.secrets.map((secret, idx) => (
                                          <div key={idx} className="text-sm mt-1">
                                            <span className="font-medium">{secret.type}:</span>
                                            <code className="ml-2 bg-gray-800 px-1 rounded">{secret.value}</code>
                                            <span className="ml-2 text-xs">({secret.confidence}% confidence)</span>
                                          </div>
                                        ))}
                                      </div>
                                    ) : (
                                      <p className="text-green-400">✓ No secrets detected</p>
                                    )}
                                  </div>
                                )}

                                {analysis.results.generalAnalysis && (
                                  <div className="bg-gray-900/50 rounded p-3">
                                    <h5 className="font-semibold text-white mb-2">General Analysis</h5>
                                    <div className="text-sm text-gray-300 space-y-1">
                                      <p>Entropy: {analysis.results.generalAnalysis.entropy.toFixed(2)}</p>
                                      <p>Suspicious strings: {analysis.results.generalAnalysis.suspiciousStrings.join(', ')}</p>
                                    </div>
                                  </div>
                                )}
                              </div>
                            )}

                            {analysis.status === 'analyzing' && (
                              <div className="text-center py-4">
                                <RefreshCw className="h-6 w-6 text-cyber-green animate-spin mx-auto mb-2" />
                                <p className="text-gray-400">Analyzing file...</p>
                              </div>
                            )}
                          </div>
                        )
                      })}
                    </div>
                  </div>
                </Card>
              )}
            </div>

            {/* Analysis Info */}
            <div>
              <Card>
                <div className="p-6">
                  <h3 className="text-lg font-bold text-white mb-4">Analysis Types</h3>
                  <div className="space-y-3">
                    <div className="p-3 bg-gray-800/50 rounded-lg">
                      <div className="font-medium text-white">Malware Detection</div>
                      <div className="text-sm text-gray-400">Detect viruses, trojans, ransomware</div>
                    </div>
                    <div className="p-3 bg-gray-800/50 rounded-lg">
                      <div className="font-medium text-white">Webshell Detection</div>
                      <div className="text-sm text-gray-400">Identify PHP, ASP, JSP webshells</div>
                    </div>
                    <div className="p-3 bg-gray-800/50 rounded-lg">
                      <div className="font-medium text-white">Secret Detection</div>
                      <div className="text-sm text-gray-400">Find API keys, passwords, tokens</div>
                    </div>
                    <div className="p-3 bg-gray-800/50 rounded-lg">
                      <div className="font-medium text-white">General Analysis</div>
                      <div className="text-sm text-gray-400">Entropy, structure, suspicious patterns</div>
                    </div>
                  </div>
                </div>
              </Card>

              <Card className="mt-6" border="gold">
                <div className="p-6">
                  <h3 className="text-lg font-bold text-white mb-4">Plan Limits</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Daily Uploads</span>
                      <span className="text-white">Unlimited</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Max File Size</span>
                      <span className="text-white">1GB</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Concurrent Analysis</span>
                      <span className="text-white">10 files</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Deep Analysis</span>
                      <span className="text-cyber-green">✓ Enabled</span>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
