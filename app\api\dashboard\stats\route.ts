import { NextRequest, NextResponse } from 'next/server'

interface DashboardStats {
  overview: {
    totalScans: number
    activeScans: number
    vulnerabilitiesFound: number
    osintSearches: number
  }
  vulnerabilities: {
    critical: number
    high: number
    medium: number
    low: number
    info: number
  }
  scanActivity: {
    today: number
    thisWeek: number
    thisMonth: number
    lastMonth: number
  }
  osintActivity: {
    today: number
    thisWeek: number
    thisMonth: number
    lastMonth: number
  }
  topTargets: Array<{
    target: string
    scans: number
    lastScan: string
    status: 'secure' | 'vulnerable' | 'critical'
  }>
  recentActivity: Array<{
    id: string
    type: 'scan' | 'osint' | 'alert'
    title: string
    description: string
    timestamp: string
    status: 'success' | 'warning' | 'error' | 'info'
  }>
  systemHealth: {
    scannerStatus: 'online' | 'offline' | 'maintenance'
    osintStatus: 'online' | 'offline' | 'maintenance'
    databaseStatus: 'online' | 'offline' | 'maintenance'
    apiStatus: 'online' | 'offline' | 'maintenance'
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Simple token validation for testing
    if (!token.startsWith('kxg_')) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    // Mock dashboard statistics
    const stats: DashboardStats = {
      overview: {
        totalScans: 1247,
        activeScans: 3,
        vulnerabilitiesFound: 156,
        osintSearches: 892
      },
      vulnerabilities: {
        critical: 12,
        high: 34,
        medium: 67,
        low: 89,
        info: 23
      },
      scanActivity: {
        today: 15,
        thisWeek: 89,
        thisMonth: 234,
        lastMonth: 198
      },
      osintActivity: {
        today: 23,
        thisWeek: 156,
        thisMonth: 445,
        lastMonth: 378
      },
      topTargets: [
        {
          target: 'https://example.com',
          scans: 45,
          lastScan: new Date(Date.now() - 3600000).toISOString(),
          status: 'vulnerable'
        },
        {
          target: 'https://testsite.com',
          scans: 32,
          lastScan: new Date(Date.now() - 7200000).toISOString(),
          status: 'secure'
        },
        {
          target: 'https://webapp.local',
          scans: 28,
          lastScan: new Date(Date.now() - 1800000).toISOString(),
          status: 'critical'
        },
        {
          target: 'https://api.example.com',
          scans: 21,
          lastScan: new Date(Date.now() - 10800000).toISOString(),
          status: 'vulnerable'
        },
        {
          target: 'https://mobile-app.com',
          scans: 18,
          lastScan: new Date(Date.now() - 14400000).toISOString(),
          status: 'secure'
        }
      ],
      recentActivity: [
        {
          id: 'activity_001',
          type: 'scan',
          title: 'Vulnerability Scan Completed',
          description: 'Scan of https://example.com found 5 vulnerabilities',
          timestamp: new Date(Date.now() - 1800000).toISOString(),
          status: 'warning'
        },
        {
          id: 'activity_002',
          type: 'osint',
          title: 'OSINT Search Completed',
          description: 'Email <NAME_EMAIL> returned 3 results',
          timestamp: new Date(Date.now() - 3600000).toISOString(),
          status: 'success'
        },
        {
          id: 'activity_003',
          type: 'alert',
          title: 'Critical Vulnerability Detected',
          description: 'SQL injection vulnerability found in login form',
          timestamp: new Date(Date.now() - 5400000).toISOString(),
          status: 'error'
        },
        {
          id: 'activity_004',
          type: 'scan',
          title: 'Scan Started',
          description: 'Comprehensive security scan initiated for https://webapp.local',
          timestamp: new Date(Date.now() - 7200000).toISOString(),
          status: 'info'
        },
        {
          id: 'activity_005',
          type: 'osint',
          title: 'Phone Number Investigation',
          description: 'Phone lookup for +628123456789 completed successfully',
          timestamp: new Date(Date.now() - 9000000).toISOString(),
          status: 'success'
        },
        {
          id: 'activity_006',
          type: 'alert',
          title: 'Quota Warning',
          description: 'Daily OSINT quota 80% used (40/50 searches)',
          timestamp: new Date(Date.now() - 10800000).toISOString(),
          status: 'warning'
        }
      ],
      systemHealth: {
        scannerStatus: 'online',
        osintStatus: 'online',
        databaseStatus: 'online',
        apiStatus: 'online'
      }
    }

    return NextResponse.json({
      success: true,
      data: stats,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Dashboard stats API error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}
