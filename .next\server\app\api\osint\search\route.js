(()=>{var e={};e.id=8944,e.ids=[8944],e.modules={62849:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=62849,e.exports=t},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},96119:e=>{"use strict";e.exports=require("perf_hooks")},35816:e=>{"use strict";e.exports=require("process")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},95346:e=>{"use strict";e.exports=require("timers")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},98061:e=>{"use strict";e.exports=require("node:assert")},92761:e=>{"use strict";e.exports=require("node:async_hooks")},72254:e=>{"use strict";e.exports=require("node:buffer")},40027:e=>{"use strict";e.exports=require("node:console")},6005:e=>{"use strict";e.exports=require("node:crypto")},65714:e=>{"use strict";e.exports=require("node:diagnostics_channel")},30604:e=>{"use strict";e.exports=require("node:dns")},15673:e=>{"use strict";e.exports=require("node:events")},88849:e=>{"use strict";e.exports=require("node:http")},42725:e=>{"use strict";e.exports=require("node:http2")},22286:e=>{"use strict";e.exports=require("node:https")},87503:e=>{"use strict";e.exports=require("node:net")},70612:e=>{"use strict";e.exports=require("node:os")},38846:e=>{"use strict";e.exports=require("node:perf_hooks")},97742:e=>{"use strict";e.exports=require("node:process")},39630:e=>{"use strict";e.exports=require("node:querystring")},55467:e=>{"use strict";e.exports=require("node:sqlite")},84492:e=>{"use strict";e.exports=require("node:stream")},99397:e=>{"use strict";e.exports=require("node:timers/promises")},31764:e=>{"use strict";e.exports=require("node:tls")},41041:e=>{"use strict";e.exports=require("node:url")},47261:e=>{"use strict";e.exports=require("node:util")},93746:e=>{"use strict";e.exports=require("node:util/types")},24086:e=>{"use strict";e.exports=require("node:worker_threads")},65628:e=>{"use strict";e.exports=require("node:zlib")},67238:(e,t,r)=>{"use strict";r.r(t),r.d(t,{originalPathname:()=>f,patchFetch:()=>x,requestAsyncStorage:()=>y,routeModule:()=>m,serverHooks:()=>w,staticGenerationAsyncStorage:()=>S});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>h});var a=r(49303),n=r(88716),i=r(60670),o=r(87070),c=r(29712),u=r(24544);class l{constructor(){this.dataSources=new Map,this.initializeDataSources()}initializeDataSources(){this.dataSources.set("dukcapil",{name:"Dukcapil Database",type:"government",enabled:!0,confidence:95,searchFunction:this.searchDukcapil.bind(this)}),this.dataSources.set("kemkes",{name:"Kemkes Database",type:"government",enabled:!0,confidence:90,searchFunction:this.searchKemkes.bind(this)}),this.dataSources.set("github",{name:"GitHub Leaked Data",type:"public",enabled:!0,confidence:80,searchFunction:this.searchGitHub.bind(this)}),this.dataSources.set("social",{name:"Social Media OSINT",type:"social",enabled:!0,confidence:70,searchFunction:this.searchSocialMedia.bind(this)}),this.dataSources.set("domain",{name:"Domain Intelligence",type:"technical",enabled:!0,confidence:85,searchFunction:this.searchDomain.bind(this)}),this.dataSources.set("phone",{name:"Phone Intelligence",type:"telecom",enabled:!0,confidence:75,searchFunction:this.searchPhone.bind(this)})}async search(e){let t=Date.now();try{if(!e.query||!e.type)return{success:!1,results:[],sources:[],confidenceScore:0,searchTime:0,totalResults:0,error:"Invalid search parameters"};let r=this.getRelevantSources(e.type,e.sources).map(async t=>{try{let r=await t.searchFunction(e.query);return{source:t.name,confidence:t.confidence,data:r,success:!0}}catch(e){return console.error(`Search failed for ${t.name}:`,e),{source:t.name,confidence:0,data:null,success:!1,error:e instanceof Error?e.message:"Unknown error"}}}),s=await Promise.allSettled(r),a=[],n=[],i=0,o=0;s.forEach((t,r)=>{if("fulfilled"===t.status&&t.value.success&&t.value.data){let r=t.value;a.push({id:0,userId:e.userId,searchType:e.type,searchQuery:e.query,results:r.data,sources:[r.source],confidenceScore:r.confidence,createdAt:new Date}),n.push(r.source),i+=r.confidence,o++}});let c=o>0?i/o:0,u=Date.now()-t;return a.length>0&&await this.saveSearchResults(e,a),{success:!0,results:a,sources:n,confidenceScore:Math.round(c),searchTime:u,totalResults:a.length}}catch(e){return console.error("OSINT search error:",e),{success:!1,results:[],sources:[],confidenceScore:0,searchTime:Date.now()-t,totalResults:0,error:e instanceof Error?e.message:"Search failed"}}}getRelevantSources(e,t){let r=Array.from(this.dataSources.values()).filter(e=>e.enabled);if(t&&t.length>0)return r.filter(e=>t.some(t=>e.name.toLowerCase().includes(t.toLowerCase())));switch(e){case"nik":case"npwp":case"name":return r.filter(e=>["government","public"].includes(e.type));case"email":return r.filter(e=>["public","social"].includes(e.type));case"phone":case"imei":return r.filter(e=>["telecom","government"].includes(e.type));case"domain":return r.filter(e=>"technical"===e.type);default:return r}}async searchDukcapil(e){await new Promise(e=>setTimeout(e,1e3+2e3*Math.random()));let t={found:Math.random()>.3,data:{name:"John Doe",nik:"1234567890123456",birthDate:"1990-01-01",address:"Jakarta, Indonesia",verified:!0,lastUpdated:new Date().toISOString()}};return t.found?t.data:null}async searchKemkes(e){await new Promise(e=>setTimeout(e,800+1500*Math.random()));let t={found:Math.random()>.4,data:{name:"John Doe",healthId:"H123456789",vaccineStatus:"Complete",lastVisit:"2024-01-15",verified:!0}};return t.found?t.data:null}async searchGitHub(e){try{let t=await c.Z.get("https://api.github.com/search/code",{params:{q:`"${e}" filename:*.txt OR filename:*.csv OR filename:*.json`,sort:"indexed",order:"desc"},headers:{Accept:"application/vnd.github.v3+json","User-Agent":"KodeXGuard-OSINT"},timeout:1e4});return t.data.items?.slice(0,5)||null}catch(e){return console.error("GitHub search error:",e),null}}async searchSocialMedia(e){await new Promise(e=>setTimeout(e,1500+2e3*Math.random()));let t=["Facebook","Twitter","Instagram","LinkedIn"].map(e=>({platform:e,profiles:Math.floor(5*Math.random()),lastActivity:new Date(Date.now()-2592e6*Math.random()).toISOString()})).filter(e=>e.profiles>0);return t.length>0?t:null}async searchDomain(e){try{let t=e.replace(/^https?:\/\//,"").split("/")[0];return await new Promise(e=>setTimeout(e,1e3)),{domain:t,registrar:"Example Registrar",createdDate:"2020-01-01",expiryDate:"2025-01-01",nameservers:["ns1.example.com","ns2.example.com"],status:"Active"}}catch(e){return null}}async searchPhone(e){return await new Promise(e=>setTimeout(e,1200)),Math.random()>.2?{number:e,country:"Indonesia",carrier:"Telkomsel",type:"Mobile",location:"Jakarta",verified:!0}:null}async saveSearchResults(e,t){try{for(let r of t)await u.db.query(`INSERT INTO osint_results (user_id, search_type, search_query, results, sources, confidence_score, created_at)
           VALUES (?, ?, ?, ?, ?, ?, NOW())`,[e.userId,e.type,e.query,JSON.stringify(r.results),JSON.stringify(r.sources),r.confidenceScore])}catch(e){console.error("Failed to save OSINT results:",e)}}async getSearchHistory(e,t=20,r=0){try{return await u.db.query(`SELECT * FROM osint_results 
         WHERE user_id = ? 
         ORDER BY created_at DESC 
         LIMIT ? OFFSET ?`,[e,t,r])}catch(e){return console.error("Failed to get OSINT history:",e),[]}}async trackLocation(e,t){try{return await new Promise(e=>setTimeout(e,2e3)),{target:e,type:t,location:{latitude:-6.2088+(Math.random()-.5)*.1,longitude:106.8456+(Math.random()-.5)*.1,accuracy:Math.floor(1e3*Math.random())+100,address:"Jakarta, Indonesia",timestamp:new Date().toISOString()},confidence:Math.floor(40*Math.random())+60}}catch(e){return console.error("Location tracking error:",e),null}}}let d=new l;async function h(e){try{let t=e.headers.get("authorization"),r=t?.replace("Bearer ","");if(!r)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!r.startsWith("kxg_"))return o.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let{query:s,type:a,deepSearch:n=!1,sources:i=[]}=await e.json();if(!s||!a)return o.NextResponse.json({success:!1,error:"Query and type are required",code:"MISSING_PARAMETERS"},{status:400});if(!["email","phone","nik","npwp","name","domain","imei","address"].includes(a))return o.NextResponse.json({success:!1,error:"Invalid search type",code:"INVALID_TYPE"},{status:400});let c=await d.search({query:s,type:a,deepSearch:n,sources:i,userId:1});return o.NextResponse.json({success:!0,data:{searchId:`osint_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,query:s,type:a,results:c.results,sources:c.sources,confidenceScore:c.confidenceScore,searchTime:c.searchTime,totalResults:c.totalResults,quotaUsed:1,quotaLimit:100},message:`Found ${c.totalResults} results`,timestamp:new Date().toISOString()})}catch(e){return console.error("OSINT search error:",e),o.NextResponse.json({success:!1,error:"Internal server error",code:"INTERNAL_ERROR"},{status:500})}}async function p(e){try{let t=e.headers.get("authorization"),r=t?.replace("Bearer ","");if(!r)return o.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!r.startsWith("kxg_"))return o.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let{searchParams:s}=new URL(e.url),a=parseInt(s.get("page")||"1"),n=parseInt(s.get("limit")||"20"),i=[{id:1,searchType:"email",searchQuery:"<EMAIL>",results:{found:!0},sources:["Dukcapil","GitHub"],confidenceScore:85,createdAt:new Date().toISOString()},{id:2,searchType:"phone",searchQuery:"+62812345678",results:{found:!0},sources:["Telecom","Social Media"],confidenceScore:92,createdAt:new Date(Date.now()-36e5).toISOString()}];return o.NextResponse.json({success:!0,data:i,pagination:{page:a,limit:n,total:i.length,totalPages:Math.ceil(i.length/n)},timestamp:new Date().toISOString()})}catch(e){return console.error("OSINT history error:",e),o.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let m=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/osint/search/route",pathname:"/api/osint/search",filename:"route",bundlePath:"app/api/osint/search/route"},resolvedPagePath:"D:\\Users\\Downloads\\kodeXGuard\\app\\api\\osint\\search\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:y,staticGenerationAsyncStorage:S,serverHooks:w}=m,f="/api/osint/search/route";function x(){return(0,i.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:S})}},24544:(e,t,r)=>{"use strict";r.d(t,{db:()=>u,vo:()=>i});var s=r(73785),a=r(79984),n=r(31328);class i{constructor(){this.pool=s.createPool({host:process.env.DB_HOST||"localhost",port:parseInt(process.env.DB_PORT||"3306"),user:process.env.DB_USER||"root",password:process.env.DB_PASSWORD||"rootkan",database:process.env.DB_NAME||"db_kodexguard",waitForConnections:!0,connectionLimit:10,queueLimit:0,charset:"utf8mb4",timezone:"+00:00"})}static getInstance(){return i.instance||(i.instance=new i),i.instance}async query(e,t){try{let[r]=await this.pool.execute(e,t);return r}catch(e){throw console.error("Database query error:",e),e}}async transaction(e){let t=await this.pool.getConnection();try{await t.beginTransaction();let r=await e(t);return await t.commit(),r}catch(e){throw await t.rollback(),e}finally{t.release()}}async close(){await this.pool.end()}}class o{constructor(){this.client=(0,a.createClient)({url:process.env.REDIS_URL||"redis://localhost:6379",password:process.env.REDIS_PASSWORD||void 0,socket:{reconnectStrategy:e=>Math.min(50*e,500)}}),this.client.on("error",e=>{console.error("Redis Client Error:",e)}),this.client.on("connect",()=>{console.log("Redis Client Connected")})}static getInstance(){return o.instance||(o.instance=new o),o.instance}async connect(){this.client.isOpen||await this.client.connect()}async get(e){return await this.connect(),await this.client.get(e)}async set(e,t,r){await this.connect(),r?await this.client.setEx(e,r,t):await this.client.set(e,t)}async del(e){await this.connect(),await this.client.del(e)}async exists(e){return await this.connect(),await this.client.exists(e)===1}async incr(e){return await this.connect(),await this.client.incr(e)}async expire(e,t){await this.connect(),await this.client.expire(e,t)}async close(){this.client.isOpen&&await this.client.quit()}}class c{constructor(){this.client=new n.Client({node:process.env.ELASTICSEARCH_URL||"http://localhost:9200",auth:process.env.ELASTICSEARCH_USERNAME&&process.env.ELASTICSEARCH_PASSWORD?{username:process.env.ELASTICSEARCH_USERNAME,password:process.env.ELASTICSEARCH_PASSWORD}:void 0,requestTimeout:3e4,pingTimeout:3e3,sniffOnStart:!1})}static getInstance(){return c.instance||(c.instance=new c),c.instance}async index(e,t,r){try{await this.client.index({index:e,id:t,body:r})}catch(e){throw console.error("Elasticsearch index error:",e),e}}async search(e,t){try{return await this.client.search({index:e,body:t})}catch(e){throw console.error("Elasticsearch search error:",e),e}}async delete(e,t){try{await this.client.delete({index:e,id:t})}catch(e){throw console.error("Elasticsearch delete error:",e),e}}async createIndex(e,t){try{await this.client.indices.exists({index:e})||await this.client.indices.create({index:e,body:{mappings:t}})}catch(e){throw console.error("Elasticsearch create index error:",e),e}}async close(){await this.client.close()}}let u=i.getInstance();o.getInstance(),c.getInstance()}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9276,5972,9309,9712],()=>r(67238));module.exports=s})();