"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/scan/vulnerability/route";
exports.ids = ["app/api/scan/vulnerability/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fscan%2Fvulnerability%2Froute&page=%2Fapi%2Fscan%2Fvulnerability%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscan%2Fvulnerability%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fscan%2Fvulnerability%2Froute&page=%2Fapi%2Fscan%2Fvulnerability%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscan%2Fvulnerability%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Users_Downloads_kodeXGuard_app_api_scan_vulnerability_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/scan/vulnerability/route.ts */ \"(rsc)/./app/api/scan/vulnerability/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/scan/vulnerability/route\",\n        pathname: \"/api/scan/vulnerability\",\n        filename: \"route\",\n        bundlePath: \"app/api/scan/vulnerability/route\"\n    },\n    resolvedPagePath: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\api\\\\scan\\\\vulnerability\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Users_Downloads_kodeXGuard_app_api_scan_vulnerability_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/scan/vulnerability/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fscan%2Fvulnerability%2Froute&page=%2Fapi%2Fscan%2Fvulnerability%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscan%2Fvulnerability%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/scan/vulnerability/route.ts":
/*!*********************************************!*\
  !*** ./app/api/scan/vulnerability/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { target, scanTypes = [], maxDepth = 3, timeout = 30, followRedirects = true } = body;\n        // Validate input\n        if (!target) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Target URL is required\",\n                code: \"MISSING_TARGET\"\n            }, {\n                status: 400\n            });\n        }\n        if (!scanTypes || scanTypes.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"At least one scan type is required\",\n                code: \"MISSING_SCAN_TYPES\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate URL format\n        try {\n            new URL(target);\n        } catch  {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid URL format\",\n                code: \"INVALID_URL\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate scan types\n        const validScanTypes = [\n            \"sqli\",\n            \"xss\",\n            \"lfi\",\n            \"rfi\",\n            \"rce\",\n            \"csrf\",\n            \"ssrf\",\n            \"xxe\",\n            \"idor\",\n            \"path_traversal\"\n        ];\n        const invalidTypes = scanTypes.filter((type)=>!validScanTypes.includes(type));\n        if (invalidTypes.length > 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: `Invalid scan types: ${invalidTypes.join(\", \")}`,\n                code: \"INVALID_SCAN_TYPES\"\n            }, {\n                status: 400\n            });\n        }\n        // Generate scan ID\n        const scanId = `scan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        // Estimate scan time based on scan types and depth\n        const estimatedMinutes = Math.ceil(scanTypes.length * maxDepth / 2);\n        const estimatedTime = estimatedMinutes <= 1 ? \"1-2 minutes\" : estimatedMinutes <= 5 ? `${estimatedMinutes}-${estimatedMinutes + 2} minutes` : `${estimatedMinutes}-${estimatedMinutes + 5} minutes`;\n        // Store scan in memory (in real app, use database)\n        global.activeScan = {\n            scanId,\n            target,\n            scanTypes,\n            maxDepth,\n            timeout,\n            followRedirects,\n            status: \"started\",\n            startTime: new Date().toISOString(),\n            progress: 0\n        };\n        // Start background scan simulation\n        simulateScan(scanId, target, scanTypes);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                scanId,\n                status: \"started\",\n                target,\n                scanTypes,\n                estimatedTime,\n                maxDepth,\n                timeout\n            },\n            message: \"Vulnerability scan started successfully\",\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"Vulnerability scan error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\",\n            code: \"INTERNAL_ERROR\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function simulateScan(scanId, target, scanTypes) {\n    // Simulate scan progress\n    const totalSteps = scanTypes.length * 10;\n    let currentStep = 0;\n    const interval = setInterval(()=>{\n        currentStep += Math.floor(Math.random() * 3) + 1;\n        const progress = Math.min(currentStep / totalSteps * 100, 100);\n        if (global.activeScan && global.activeScan.scanId === scanId) {\n            global.activeScan.progress = progress;\n            if (progress >= 100) {\n                clearInterval(interval);\n                completeScan(scanId, target, scanTypes);\n            }\n        } else {\n            clearInterval(interval);\n        }\n    }, 1000);\n}\nfunction completeScan(scanId, target, scanTypes) {\n    // Generate mock vulnerabilities\n    const vulnerabilities = generateMockVulnerabilities(target, scanTypes);\n    if (global.activeScan && global.activeScan.scanId === scanId) {\n        global.activeScan.status = \"completed\";\n        global.activeScan.endTime = new Date().toISOString();\n        global.activeScan.vulnerabilities = vulnerabilities;\n        global.activeScan.summary = {\n            total: vulnerabilities.length,\n            critical: vulnerabilities.filter((v)=>v.severity === \"critical\").length,\n            high: vulnerabilities.filter((v)=>v.severity === \"high\").length,\n            medium: vulnerabilities.filter((v)=>v.severity === \"medium\").length,\n            low: vulnerabilities.filter((v)=>v.severity === \"low\").length\n        };\n    }\n}\nfunction generateMockVulnerabilities(target, scanTypes) {\n    const vulnerabilities = [];\n    const domain = new URL(target).hostname;\n    // Generate vulnerabilities based on scan types\n    scanTypes.forEach((scanType)=>{\n        if (Math.random() > 0.3) {\n            const vuln = {\n                type: scanType,\n                severity: [\n                    \"critical\",\n                    \"high\",\n                    \"medium\",\n                    \"low\"\n                ][Math.floor(Math.random() * 4)],\n                cvssScore: Math.round(Math.random() * 10 * 10) / 10,\n                url: `${target}${getRandomPath(scanType)}`,\n                parameter: getRandomParameter(scanType),\n                payload: getRandomPayload(scanType),\n                evidence: getRandomEvidence(scanType),\n                recommendation: getRecommendation(scanType),\n                cveId: Math.random() > 0.7 ? `CVE-2024-${Math.floor(Math.random() * 9999).toString().padStart(4, \"0\")}` : null\n            };\n            vulnerabilities.push(vuln);\n        }\n    });\n    return vulnerabilities;\n}\nfunction getRandomPath(scanType) {\n    const paths = {\n        sqli: [\n            \"/login.php\",\n            \"/search.php\",\n            \"/product.php?id=1\"\n        ],\n        xss: [\n            \"/comment.php\",\n            \"/search.php\",\n            \"/profile.php\"\n        ],\n        lfi: [\n            \"/include.php\",\n            \"/page.php\",\n            \"/file.php\"\n        ],\n        rfi: [\n            \"/include.php\",\n            \"/template.php\",\n            \"/load.php\"\n        ],\n        rce: [\n            \"/upload.php\",\n            \"/exec.php\",\n            \"/cmd.php\"\n        ]\n    };\n    const typePaths = paths[scanType] || [\n        \"/index.php\",\n        \"/admin.php\",\n        \"/api.php\"\n    ];\n    return typePaths[Math.floor(Math.random() * typePaths.length)];\n}\nfunction getRandomParameter(scanType) {\n    const params = {\n        sqli: [\n            \"id\",\n            \"username\",\n            \"search\",\n            \"category\"\n        ],\n        xss: [\n            \"comment\",\n            \"message\",\n            \"search\",\n            \"name\"\n        ],\n        lfi: [\n            \"file\",\n            \"page\",\n            \"include\",\n            \"template\"\n        ],\n        rfi: [\n            \"url\",\n            \"file\",\n            \"include\",\n            \"source\"\n        ]\n    };\n    const typeParams = params[scanType] || [\n        \"param\",\n        \"value\",\n        \"input\"\n    ];\n    return typeParams[Math.floor(Math.random() * typeParams.length)];\n}\nfunction getRandomPayload(scanType) {\n    const payloads = {\n        sqli: [\n            \"' OR '1'='1\",\n            \"' UNION SELECT 1,2,3--\",\n            \"'; DROP TABLE users;--\"\n        ],\n        xss: [\n            '<script>alert(\"XSS\")</script>',\n            \"<img src=x onerror=alert(1)>\",\n            \"<svg onload=alert(1)>\"\n        ],\n        lfi: [\n            \"../../../etc/passwd\",\n            \"....//....//....//etc/passwd\",\n            \"/etc/passwd%00\"\n        ],\n        rfi: [\n            \"http://evil.com/shell.txt\",\n            \"https://attacker.com/backdoor.php\",\n            \"ftp://malicious.com/payload\"\n        ]\n    };\n    const typePayloads = payloads[scanType] || [\n        \"malicious_input\",\n        \"exploit_payload\"\n    ];\n    return typePayloads[Math.floor(Math.random() * typePayloads.length)];\n}\nfunction getRandomEvidence(scanType) {\n    const evidence = {\n        sqli: [\n            \"MySQL error detected\",\n            \"Database error in response\",\n            \"SQL syntax error found\"\n        ],\n        xss: [\n            \"Script executed in response\",\n            \"JavaScript alert triggered\",\n            \"HTML injection successful\"\n        ],\n        lfi: [\n            \"File contents exposed\",\n            \"System file accessed\",\n            \"Directory traversal successful\"\n        ],\n        rfi: [\n            \"Remote file included\",\n            \"External resource loaded\",\n            \"Remote code execution detected\"\n        ]\n    };\n    const typeEvidence = evidence[scanType] || [\n        \"Vulnerability confirmed\",\n        \"Exploit successful\"\n    ];\n    return typeEvidence[Math.floor(Math.random() * typeEvidence.length)];\n}\nfunction getRecommendation(scanType) {\n    const recommendations = {\n        sqli: \"Use parameterized queries and input validation\",\n        xss: \"Implement proper output encoding and CSP headers\",\n        lfi: \"Validate and sanitize file path inputs\",\n        rfi: \"Disable remote file inclusion and validate URLs\",\n        rce: \"Avoid executing user input and use safe functions\",\n        csrf: \"Implement CSRF tokens and SameSite cookies\",\n        ssrf: \"Validate and whitelist allowed URLs\",\n        xxe: \"Disable external entity processing in XML parsers\",\n        idor: \"Implement proper access controls and authorization\",\n        path_traversal: \"Validate and sanitize file path inputs\"\n    };\n    return recommendations[scanType] || \"Follow security best practices\";\n}\nasync function OPTIONS() {\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 200,\n        headers: {\n            \"Access-Control-Allow-Origin\": \"*\",\n            \"Access-Control-Allow-Methods\": \"POST, OPTIONS\",\n            \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/scan/vulnerability/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@opentelemetry","vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fscan%2Fvulnerability%2Froute&page=%2Fapi%2Fscan%2Fvulnerability%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fscan%2Fvulnerability%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();