import { NextRequest, NextResponse } from 'next/server'

interface OSINTTemplate {
  id: string
  name: string
  description: string
  category: 'person' | 'business' | 'domain' | 'device' | 'custom'
  searchTypes: string[]
  sources: string[]
  settings: {
    deepSearch: boolean
    includeSocialMedia: boolean
    includeBreaches: boolean
    maxResults?: number
    confidenceThreshold?: number
  }
  isDefault: boolean
  createdAt: string
  updatedAt: string
  usageCount: number
}

export async function GET(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Simple token validation for testing
    if (!token.startsWith('kxg_')) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    // Mock OSINT templates data
    const templates: OSINTTemplate[] = [
      {
        id: 'osint_template_001',
        name: 'Person Investigation',
        description: 'Comprehensive person lookup using multiple identity sources',
        category: 'person',
        searchTypes: ['email', 'phone', 'name', 'nik', 'npwp'],
        sources: ['Dukcapil Database', 'Kemkes Database', 'Social Media', 'Email Breach DB', 'Phone Number DB'],
        settings: {
          deepSearch: true,
          includeSocialMedia: true,
          includeBreaches: true,
          maxResults: 50,
          confidenceThreshold: 70
        },
        isDefault: true,
        createdAt: new Date(Date.now() - 86400000 * 30).toISOString(),
        updatedAt: new Date(Date.now() - 86400000 * 5).toISOString(),
        usageCount: 234
      },
      {
        id: 'osint_template_002',
        name: 'Business Intelligence',
        description: 'Corporate and business entity investigation template',
        category: 'business',
        searchTypes: ['domain', 'email', 'name'],
        sources: ['Domain WHOIS', 'Social Media', 'GitHub', 'Email Breach DB'],
        settings: {
          deepSearch: true,
          includeSocialMedia: false,
          includeBreaches: true,
          maxResults: 100,
          confidenceThreshold: 60
        },
        isDefault: true,
        createdAt: new Date(Date.now() - 86400000 * 45).toISOString(),
        updatedAt: new Date(Date.now() - 86400000 * 3).toISOString(),
        usageCount: 156
      },
      {
        id: 'osint_template_003',
        name: 'Domain Analysis',
        description: 'Complete domain and infrastructure investigation',
        category: 'domain',
        searchTypes: ['domain', 'email'],
        sources: ['Domain WHOIS', 'GitHub', 'Social Media'],
        settings: {
          deepSearch: true,
          includeSocialMedia: false,
          includeBreaches: false,
          maxResults: 25,
          confidenceThreshold: 80
        },
        isDefault: true,
        createdAt: new Date(Date.now() - 86400000 * 20).toISOString(),
        updatedAt: new Date(Date.now() - 86400000 * 2).toISOString(),
        usageCount: 89
      },
      {
        id: 'osint_template_004',
        name: 'Device Tracking',
        description: 'Mobile device and IMEI investigation template',
        category: 'device',
        searchTypes: ['imei', 'phone'],
        sources: ['Phone Number DB', 'Location Services'],
        settings: {
          deepSearch: false,
          includeSocialMedia: false,
          includeBreaches: false,
          maxResults: 10,
          confidenceThreshold: 90
        },
        isDefault: true,
        createdAt: new Date(Date.now() - 86400000 * 15).toISOString(),
        updatedAt: new Date(Date.now() - 86400000 * 1).toISOString(),
        usageCount: 67
      },
      {
        id: 'osint_template_005',
        name: 'Quick Lookup',
        description: 'Fast basic search for quick information gathering',
        category: 'person',
        searchTypes: ['email', 'phone'],
        sources: ['Social Media', 'Email Breach DB'],
        settings: {
          deepSearch: false,
          includeSocialMedia: true,
          includeBreaches: true,
          maxResults: 15,
          confidenceThreshold: 50
        },
        isDefault: true,
        createdAt: new Date(Date.now() - 86400000 * 60).toISOString(),
        updatedAt: new Date(Date.now() - 86400000 * 7).toISOString(),
        usageCount: 345
      },
      {
        id: 'osint_template_006',
        name: 'Social Media Deep Dive',
        description: 'Comprehensive social media investigation template',
        category: 'custom',
        searchTypes: ['email', 'phone', 'name'],
        sources: ['Social Media', 'GitHub'],
        settings: {
          deepSearch: true,
          includeSocialMedia: true,
          includeBreaches: false,
          maxResults: 75,
          confidenceThreshold: 40
        },
        isDefault: false,
        createdAt: new Date(Date.now() - 86400000 * 10).toISOString(),
        updatedAt: new Date(Date.now() - 86400000 * 1).toISOString(),
        usageCount: 123
      }
    ]

    // Sort by usage count (most used first)
    templates.sort((a, b) => b.usageCount - a.usageCount)

    return NextResponse.json({
      success: true,
      data: templates,
      meta: {
        total: templates.length,
        default: templates.filter(t => t.isDefault).length,
        custom: templates.filter(t => !t.isDefault).length,
        categories: {
          person: templates.filter(t => t.category === 'person').length,
          business: templates.filter(t => t.category === 'business').length,
          domain: templates.filter(t => t.category === 'domain').length,
          device: templates.filter(t => t.category === 'device').length,
          custom: templates.filter(t => t.category === 'custom').length
        }
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('OSINT templates API error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Simple token validation for testing
    if (!token.startsWith('kxg_')) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    const body = await request.json()
    const { name, description, category, searchTypes, sources, settings } = body

    // Validate input
    if (!name || !description || !category || !searchTypes || !sources) {
      return NextResponse.json({
        success: false,
        error: 'Name, description, category, searchTypes, and sources are required'
      }, { status: 400 })
    }

    // Create new template
    const newTemplate: OSINTTemplate = {
      id: `osint_template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name,
      description,
      category,
      searchTypes,
      sources,
      settings: settings || {
        deepSearch: false,
        includeSocialMedia: true,
        includeBreaches: true,
        maxResults: 25,
        confidenceThreshold: 60
      },
      isDefault: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      usageCount: 0
    }

    // In a real app, save to database
    // For demo, just return the created template
    
    return NextResponse.json({
      success: true,
      data: newTemplate,
      message: 'OSINT template created successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Create OSINT template error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}
