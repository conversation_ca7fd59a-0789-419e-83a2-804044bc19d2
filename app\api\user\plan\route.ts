import { NextRequest, NextResponse } from 'next/server'

interface UserPlan {
  current: {
    id: string
    name: string
    type: 'free' | 'basic' | 'pro' | 'cybersecurity'
    price: number
    currency: string
    billing: 'monthly' | 'yearly'
    startDate: string
    endDate?: string
    isActive: boolean
    autoRenew: boolean
  }
  features: {
    scans: {
      limit: number | null // null = unlimited
      used: number
      resetDate: string
    }
    osintSearches: {
      limit: number | null
      used: number
      resetDate: string
    }
    apiCalls: {
      limit: number | null
      used: number
      resetDate: string
    }
    storage: {
      limit: number // in GB
      used: number
      resetDate: string
    }
    teamMembers: {
      limit: number
      used: number
    }
    features: string[]
  }
  usage: {
    thisMonth: {
      scans: number
      osintSearches: number
      apiCalls: number
      storageUsed: number
    }
    lastMonth: {
      scans: number
      osintSearches: number
      apiCalls: number
      storageUsed: number
    }
  }
  billing: {
    nextBillingDate?: string
    lastPayment?: {
      amount: number
      date: string
      method: string
      status: 'success' | 'failed' | 'pending'
    }
    paymentMethod?: {
      type: 'card' | 'bank' | 'ewallet'
      last4?: string
      brand?: string
    }
  }
  availableUpgrades: Array<{
    id: string
    name: string
    type: 'basic' | 'pro' | 'cybersecurity'
    price: number
    currency: string
    billing: 'monthly' | 'yearly'
    features: string[]
    popular?: boolean
  }>
}

export async function GET(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Simple token validation for testing
    if (!token.startsWith('kxg_')) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    // Mock user plan data
    const userPlan: UserPlan = {
      current: {
        id: 'plan_cybersecurity_001',
        name: 'Cybersecurity Pro',
        type: 'cybersecurity',
        price: 2500000,
        currency: 'IDR',
        billing: 'yearly',
        startDate: new Date(Date.now() - ******** * 30).toISOString(),
        endDate: new Date(Date.now() + ******** * 335).toISOString(),
        isActive: true,
        autoRenew: true
      },
      features: {
        scans: {
          limit: null, // unlimited
          used: 45,
          resetDate: new Date(Date.now() + ******** * 5).toISOString()
        },
        osintSearches: {
          limit: null, // unlimited
          used: 123,
          resetDate: new Date(Date.now() + ******** * 5).toISOString()
        },
        apiCalls: {
          limit: 100000,
          used: 28560,
          resetDate: new Date(Date.now() + ******** * 5).toISOString()
        },
        storage: {
          limit: 1000, // 1TB
          used: 156.7,
          resetDate: new Date(Date.now() + ******** * 335).toISOString()
        },
        teamMembers: {
          limit: 50,
          used: 8
        },
        features: [
          'Unlimited Vulnerability Scans',
          'Unlimited OSINT Searches',
          'Advanced Reporting & Analytics',
          'API Access (100k calls/month)',
          'Priority Support',
          'Custom Scan Templates',
          'Team Collaboration (50 members)',
          'White-label Reports',
          'Advanced Integrations',
          'Custom Webhooks',
          'Compliance Reports',
          'Advanced Security Features'
        ]
      },
      usage: {
        thisMonth: {
          scans: 45,
          osintSearches: 123,
          apiCalls: 28560,
          storageUsed: 12.3
        },
        lastMonth: {
          scans: 67,
          osintSearches: 189,
          apiCalls: 34120,
          storageUsed: 8.9
        }
      },
      billing: {
        nextBillingDate: new Date(Date.now() + ******** * 335).toISOString(),
        lastPayment: {
          amount: 2500000,
          date: new Date(Date.now() - ******** * 30).toISOString(),
          method: 'Bank Transfer',
          status: 'success'
        },
        paymentMethod: {
          type: 'bank',
          last4: '1234',
          brand: 'BCA'
        }
      },
      availableUpgrades: [
        {
          id: 'plan_enterprise_001',
          name: 'Enterprise',
          type: 'cybersecurity',
          price: 5000000,
          currency: 'IDR',
          billing: 'yearly',
          features: [
            'Everything in Cybersecurity Pro',
            'Unlimited Team Members',
            'Dedicated Support Manager',
            'Custom Integrations',
            'On-premise Deployment',
            'Advanced Compliance Reports',
            'Custom SLA'
          ],
          popular: true
        }
      ]
    }

    return NextResponse.json({
      success: true,
      data: userPlan,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('User plan API error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Simple token validation for testing
    if (!token.startsWith('kxg_')) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    const body = await request.json()
    const { planId, billing, paymentMethod } = body

    // Validate input
    if (!planId || !billing) {
      return NextResponse.json({
        success: false,
        error: 'Plan ID and billing type are required'
      }, { status: 400 })
    }

    // In a real app, process the plan upgrade/change
    // For demo, just return success
    
    return NextResponse.json({
      success: true,
      message: 'Plan upgrade initiated successfully',
      data: {
        planId,
        billing,
        paymentMethod,
        status: 'pending',
        estimatedActivation: new Date(Date.now() + 3600000).toISOString() // 1 hour
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Plan upgrade error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}
