(()=>{var e={};e.id=673,e.ids=[673],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4387:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>h,tree:()=>d}),t(9935),t(30829),t(35866);var a=t(23191),r=t(88716),i=t(37922),l=t.n(i),n=t(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["scanner",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,9935)),"D:\\Users\\Downloads\\kodeXGuard\\app\\scanner\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\Users\\Downloads\\kodeXGuard\\app\\scanner\\page.tsx"],m="/scanner/page",x={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/scanner/page",pathname:"/scanner",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3842:(e,s,t)=>{Promise.resolve().then(t.bind(t,64441))},64441:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var a=t(10326),r=t(17577),i=t(83061),l=t(2262),n=t(37202),c=t(94244),d=t(54659),o=t(58038),m=t(53468),x=t(48998),h=t(54014),p=t(94893);let u=(0,t(76557).Z)("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);var y=t(88378),g=t(31540);function v(){let[e]=(0,r.useState)({username:"admin",avatar:"",role:"super_admin",plan:"cybersecurity"}),[s,t]=(0,r.useState)({target:"",scanTypes:["sqli","xss","lfi"],maxDepth:3,timeout:30,userAgent:"KodeXGuard Scanner 1.0",followRedirects:!0,checkSSL:!0}),[v,b]=(0,r.useState)(!1),[j,N]=(0,r.useState)(0),[f,S]=(0,r.useState)([]),[w,k]=(0,r.useState)({totalScans:456,vulnerabilitiesFound:89,criticalIssues:12,lastScan:"1 hour ago"}),Z={info:"text-blue-400 bg-blue-900/20",low:"text-green-400 bg-green-900/20",medium:"text-yellow-400 bg-yellow-900/20",high:"text-orange-400 bg-orange-900/20",critical:"text-red-400 bg-red-900/20"},C=async()=>{if(!s.target.trim())return;b(!0),N(0),S([]);let e=setInterval(()=>{N(s=>s>=100?(clearInterval(e),100):s+10*Math.random())},500);try{await new Promise(e=>setTimeout(e,8e3));let e=[{id:"1",type:"SQL Injection",severity:"critical",title:"SQL Injection in login form",description:"The login form is vulnerable to SQL injection attacks through the username parameter.",url:`${s.target}/login.php`,method:"POST",parameter:"username",payload:"admin' OR '1'='1",evidence:"MySQL error: You have an error in your SQL syntax",cveId:"CVE-2023-1234",cvssScore:9.8,recommendation:"Use parameterized queries and input validation.",timestamp:new Date().toISOString()},{id:"2",type:"Cross-Site Scripting",severity:"high",title:"Reflected XSS in search parameter",description:"User input is reflected in the page without proper sanitization.",url:`${s.target}/search.php`,method:"GET",parameter:"q",payload:'<script>alert("XSS")</script>',evidence:"Script tag executed in browser",cvssScore:7.5,recommendation:"Implement proper input sanitization and output encoding.",timestamp:new Date().toISOString()},{id:"3",type:"Local File Inclusion",severity:"medium",title:"LFI vulnerability in file parameter",description:"The application allows reading arbitrary files from the server.",url:`${s.target}/view.php`,method:"GET",parameter:"file",payload:"../../../etc/passwd",evidence:"root:x:0:0:root:/root:/bin/bash",cvssScore:6.5,recommendation:"Validate and sanitize file paths, use whitelist approach.",timestamp:new Date().toISOString()}];S(e)}catch(e){console.error("Scan failed:",e)}finally{b(!1),clearInterval(e),N(100)}},M=e=>{switch(e){case"critical":case"high":return n.Z;case"medium":return c.Z;default:return d.Z}};return a.jsx(i.Z,{user:e,title:"Vulnerability Scanner",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[a.jsx(o.Z,{className:"h-8 w-8 text-cyber-green"}),(0,a.jsxs)("h1",{className:"text-3xl font-bold font-cyber text-white",children:["Vulnerability ",a.jsx("span",{className:"cyber-text",children:"Scanner"})]})]}),a.jsx("p",{className:"text-gray-400 max-w-3xl",children:"Deteksi SQLi, XSS, LFI, RCE, Path Traversal, CSRF dengan scoring CVSS dan mapping CVE otomatis. Simpan riwayat scan dengan auto versioning hasil."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[a.jsx(l.Rm,{title:"Total Scans",value:w.totalScans,icon:m.Z,color:"green",trend:{value:15,isPositive:!0}}),a.jsx(l.Rm,{title:"Vulnerabilities Found",value:w.vulnerabilitiesFound,icon:c.Z,color:"red",trend:{value:8,isPositive:!0}}),a.jsx(l.Rm,{title:"Critical Issues",value:w.criticalIssues,icon:n.Z,color:"purple",trend:{value:-12,isPositive:!1}}),a.jsx(l.Rm,{title:"Last Scan",value:w.lastScan,icon:x.Z,color:"blue"})]}),(0,a.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2",children:[a.jsx(l.Zb,{border:"green",glow:!0,children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h2",{className:"text-xl font-bold text-white mb-6",children:"Scan Configuration"}),(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Target URL"}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[a.jsx("input",{type:"url",value:s.target,onChange:e=>t(s=>({...s,target:e.target.value})),placeholder:"https://example.com",className:"cyber-input flex-1"}),a.jsx("button",{className:"cyber-btn",children:a.jsx(h.Z,{className:"h-5 w-5"})})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Vulnerability Types"}),a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-3",children:[{id:"sqli",name:"SQL Injection",description:"Database injection attacks"},{id:"xss",name:"Cross-Site Scripting",description:"Client-side code injection"},{id:"lfi",name:"Local File Inclusion",description:"File system access vulnerabilities"},{id:"rfi",name:"Remote File Inclusion",description:"Remote code execution via file inclusion"},{id:"rce",name:"Remote Code Execution",description:"Server-side code execution"},{id:"csrf",name:"Cross-Site Request Forgery",description:"Unauthorized request execution"},{id:"ssrf",name:"Server-Side Request Forgery",description:"Internal network access"},{id:"xxe",name:"XML External Entity",description:"XML parser vulnerabilities"},{id:"idor",name:"Insecure Direct Object Reference",description:"Access control bypass"},{id:"path_traversal",name:"Path Traversal",description:"Directory traversal attacks"}].map(e=>(0,a.jsxs)("label",{className:`cursor-pointer border rounded-lg p-3 transition-all duration-200 ${s.scanTypes.includes(e.id)?"border-cyber-green bg-cyber-green/10":"border-gray-600 hover:border-gray-500"}`,children:[a.jsx("input",{type:"checkbox",checked:s.scanTypes.includes(e.id),onChange:s=>{s.target.checked?t(s=>({...s,scanTypes:[...s.scanTypes,e.id]})):t(s=>({...s,scanTypes:s.scanTypes.filter(s=>s!==e.id)}))},className:"sr-only"}),a.jsx("div",{className:"text-sm font-medium text-white",children:e.name}),a.jsx("div",{className:"text-xs text-gray-400 mt-1",children:e.description})]},e.id))})]}),(0,a.jsxs)("div",{className:"border-t border-gray-700 pt-6",children:[(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Max Depth"}),a.jsx("input",{type:"number",value:s.maxDepth,onChange:e=>t(s=>({...s,maxDepth:parseInt(e.target.value)})),className:"cyber-input w-full",min:"1",max:"10"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Timeout (seconds)"}),a.jsx("input",{type:"number",value:s.timeout,onChange:e=>t(s=>({...s,timeout:parseInt(e.target.value)})),className:"cyber-input w-full",min:"5",max:"300"})]})]}),(0,a.jsxs)("div",{className:"mt-4 space-y-3",children:[(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[a.jsx("input",{type:"checkbox",checked:s.followRedirects,onChange:e=>t(s=>({...s,followRedirects:e.target.checked})),className:"rounded bg-gray-800 border-gray-600"}),a.jsx("span",{className:"text-sm text-gray-300",children:"Follow Redirects"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[a.jsx("input",{type:"checkbox",checked:s.checkSSL,onChange:e=>t(s=>({...s,checkSSL:e.target.checked})),className:"rounded bg-gray-800 border-gray-600"}),a.jsx("span",{className:"text-sm text-gray-300",children:"Verify SSL Certificate"})]})]})]}),(0,a.jsxs)("div",{className:"border-t border-gray-700 pt-6 mt-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex space-x-3",children:[v?(0,a.jsxs)("button",{onClick:()=>{b(!1),N(0)},className:"bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-semibold transition-colors",children:[a.jsx(u,{className:"h-5 w-5 mr-2"}),"Stop Scan"]}):(0,a.jsxs)("button",{onClick:C,disabled:!s.target.trim()||0===s.scanTypes.length,className:"cyber-btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:[a.jsx(p.Z,{className:"h-5 w-5 mr-2"}),"Start Scan"]}),(0,a.jsxs)("button",{className:"cyber-btn",children:[a.jsx(y.Z,{className:"h-5 w-5 mr-2"}),"Advanced"]})]}),f.length>0&&(0,a.jsxs)("button",{onClick:()=>{let e=new Blob([JSON.stringify(f,null,2)],{type:"application/json"}),s=URL.createObjectURL(e),t=document.createElement("a");t.href=s,t.download=`vulnerability-scan-${Date.now()}.json`,t.click()},className:"cyber-btn",children:[a.jsx(g.Z,{className:"h-5 w-5 mr-2"}),"Export"]})]}),v&&(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[a.jsx("span",{className:"text-sm text-gray-300",children:"Scanning Progress"}),(0,a.jsxs)("span",{className:"text-sm text-cyber-green",children:[Math.round(j),"%"]})]}),a.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2",children:a.jsx("div",{className:"bg-cyber-green h-2 rounded-full transition-all duration-300",style:{width:`${j}%`}})})]})]})]})}),f.length>0&&a.jsx(l.Zb,{className:"mt-6",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-white mb-6",children:["Scan Results (",f.length," vulnerabilities found)"]}),a.jsx("div",{className:"space-y-4",children:f.map(e=>{let s=M(e.severity);return(0,a.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-4 border border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(s,{className:`h-5 w-5 ${Z[e.severity].split(" ")[0]}`}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-semibold text-white",children:e.title}),a.jsx("p",{className:"text-sm text-gray-400",children:e.type})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[a.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-semibold ${Z[e.severity]}`,children:e.severity.toUpperCase()}),e.cvssScore&&(0,a.jsxs)("div",{className:"text-sm text-gray-400 mt-1",children:["CVSS: ",e.cvssScore]})]})]}),a.jsx("p",{className:"text-gray-300 mb-3",children:e.description}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-gray-400",children:"URL:"}),a.jsx("span",{className:"text-white ml-2",children:e.url})]}),e.parameter&&(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-gray-400",children:"Parameter:"}),a.jsx("span",{className:"text-white ml-2",children:e.parameter})]}),e.payload&&(0,a.jsxs)("div",{className:"md:col-span-2",children:[a.jsx("span",{className:"text-gray-400",children:"Payload:"}),a.jsx("code",{className:"text-cyber-green ml-2 bg-gray-900 px-2 py-1 rounded",children:e.payload})]})]}),(0,a.jsxs)("div",{className:"mt-4 p-3 bg-gray-900/50 rounded",children:[a.jsx("div",{className:"text-sm text-gray-400 mb-1",children:"Recommendation:"}),a.jsx("div",{className:"text-sm text-gray-300",children:e.recommendation})]})]},e.id)})})]})})]}),(0,a.jsxs)("div",{children:[a.jsx(l.Zb,{children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Recent Scans"}),a.jsx("div",{className:"space-y-3",children:[{target:"example.com",vulnerabilities:3,severity:"high",time:"2 hours ago"},{target:"test.local",vulnerabilities:1,severity:"medium",time:"1 day ago"},{target:"demo.site",vulnerabilities:0,severity:"info",time:"2 days ago"}].map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-800/50 rounded-lg",children:[(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium text-white text-sm",children:e.target}),a.jsx("div",{className:"text-xs text-gray-400",children:e.time})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-sm text-white",children:[e.vulnerabilities," issues"]}),a.jsx("div",{className:`text-xs ${"high"===e.severity?"text-red-400":"medium"===e.severity?"text-yellow-400":"text-green-400"}`,children:e.severity})]})]},s))})]})}),a.jsx(l.Zb,{className:"mt-6",border:"gold",children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Plan Limits"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Daily Scans"}),a.jsx("span",{className:"text-white",children:"Unlimited"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Concurrent Scans"}),a.jsx("span",{className:"text-white",children:"10"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Max Depth"}),a.jsx("span",{className:"text-white",children:"10 levels"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-400",children:"Export Results"}),a.jsx("span",{className:"text-cyber-green",children:"✓ Enabled"})]})]})]})})]})]})]})})}},37202:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},94244:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Bug",[["path",{d:"m8 2 1.88 1.88",key:"fmnt4t"}],["path",{d:"M14.12 3.88 16 2",key:"qol33r"}],["path",{d:"M9 7.13v-1a3.003 3.003 0 1 1 6 0v1",key:"d7y7pr"}],["path",{d:"M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6",key:"xs1cw7"}],["path",{d:"M12 20v-9",key:"1qisl0"}],["path",{d:"M6.53 9C4.6 8.8 3 7.1 3 5",key:"32zzws"}],["path",{d:"M6 13H2",key:"82j7cp"}],["path",{d:"M3 21c0-2.1 1.7-3.9 3.8-4",key:"4p0ekp"}],["path",{d:"M20.97 5c0 2.1-1.6 3.8-3.5 4",key:"18gb23"}],["path",{d:"M22 13h-4",key:"1jl80f"}],["path",{d:"M17.2 17c2.1.1 3.8 1.9 3.8 4",key:"k3fwyw"}]])},54659:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},48998:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},31540:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},54014:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},94893:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]])},88378:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},58038:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},53468:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},9935:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\scanner\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[9276,82,7608,3061],()=>t(4387));module.exports=a})();