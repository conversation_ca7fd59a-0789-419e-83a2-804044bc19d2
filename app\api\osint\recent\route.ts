import { NextRequest, NextResponse } from 'next/server'

interface RecentSearch {
  id: string
  query: string
  type: 'email' | 'phone' | 'nik' | 'npwp' | 'name' | 'domain' | 'imei' | 'address'
  results: number
  confidence: number
  timestamp: string
  status: 'completed' | 'failed' | 'partial'
  sources: string[]
}

export async function GET(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Simple token validation for testing
    if (!token.startsWith('kxg_')) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    // Mock recent searches data
    const recentSearches: RecentSearch[] = [
      {
        id: 'search_001',
        query: '<EMAIL>',
        type: 'email',
        results: 5,
        confidence: 92,
        timestamp: new Date(Date.now() - 1800000).toISOString(), // 30 minutes ago
        status: 'completed',
        sources: ['Dukcapil Database', 'Social Media', 'GitHub', 'Email Breach DB']
      },
      {
        id: 'search_002',
        query: '+628123456789',
        type: 'phone',
        results: 3,
        confidence: 87,
        timestamp: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
        status: 'completed',
        sources: ['Phone Number DB', 'Social Media', 'Location Services']
      },
      {
        id: 'search_003',
        query: '1234567890123456',
        type: 'nik',
        results: 2,
        confidence: 95,
        timestamp: new Date(Date.now() - 5400000).toISOString(), // 1.5 hours ago
        status: 'completed',
        sources: ['Dukcapil Database', 'Kemkes Database']
      },
      {
        id: 'search_004',
        query: 'example.com',
        type: 'domain',
        results: 8,
        confidence: 78,
        timestamp: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
        status: 'completed',
        sources: ['Domain WHOIS', 'GitHub', 'Social Media', 'Email Breach DB']
      },
      {
        id: 'search_005',
        query: 'Jane Smith',
        type: 'name',
        results: 12,
        confidence: 65,
        timestamp: new Date(Date.now() - 10800000).toISOString(), // 3 hours ago
        status: 'partial',
        sources: ['Social Media', 'GitHub', 'Location Services']
      },
      {
        id: 'search_006',
        query: '12.345.678.9-012.345',
        type: 'npwp',
        results: 1,
        confidence: 98,
        timestamp: new Date(Date.now() - 14400000).toISOString(), // 4 hours ago
        status: 'completed',
        sources: ['Dukcapil Database']
      },
      {
        id: 'search_007',
        query: '123456789012345',
        type: 'imei',
        results: 0,
        confidence: 0,
        timestamp: new Date(Date.now() - 18000000).toISOString(), // 5 hours ago
        status: 'failed',
        sources: []
      },
      {
        id: 'search_008',
        query: 'Jakarta Selatan',
        type: 'address',
        results: 25,
        confidence: 45,
        timestamp: new Date(Date.now() - 21600000).toISOString(), // 6 hours ago
        status: 'partial',
        sources: ['Location Services', 'Social Media', 'Dukcapil Database']
      },
      {
        id: 'search_009',
        query: '<EMAIL>',
        type: 'email',
        results: 7,
        confidence: 89,
        timestamp: new Date(Date.now() - 25200000).toISOString(), // 7 hours ago
        status: 'completed',
        sources: ['Email Breach DB', 'GitHub', 'Social Media']
      },
      {
        id: 'search_010',
        query: '+6281987654321',
        type: 'phone',
        results: 4,
        confidence: 82,
        timestamp: new Date(Date.now() - 28800000).toISOString(), // 8 hours ago
        status: 'completed',
        sources: ['Phone Number DB', 'Social Media', 'Location Services', 'Dukcapil Database']
      }
    ]

    // Sort by timestamp (newest first)
    recentSearches.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())

    // Get query parameters for filtering
    const url = new URL(request.url)
    const limit = parseInt(url.searchParams.get('limit') || '10')
    const type = url.searchParams.get('type')
    const status = url.searchParams.get('status')

    let filteredSearches = recentSearches

    // Apply filters
    if (type) {
      filteredSearches = filteredSearches.filter(search => search.type === type)
    }

    if (status) {
      filteredSearches = filteredSearches.filter(search => search.status === status)
    }

    // Apply limit
    filteredSearches = filteredSearches.slice(0, limit)

    return NextResponse.json({
      success: true,
      data: filteredSearches,
      meta: {
        total: recentSearches.length,
        filtered: filteredSearches.length,
        completed: recentSearches.filter(s => s.status === 'completed').length,
        failed: recentSearches.filter(s => s.status === 'failed').length,
        partial: recentSearches.filter(s => s.status === 'partial').length,
        averageConfidence: Math.round(
          recentSearches.filter(s => s.status === 'completed')
            .reduce((sum, s) => sum + s.confidence, 0) / 
          recentSearches.filter(s => s.status === 'completed').length
        ),
        searchTypes: {
          email: recentSearches.filter(s => s.type === 'email').length,
          phone: recentSearches.filter(s => s.type === 'phone').length,
          nik: recentSearches.filter(s => s.type === 'nik').length,
          npwp: recentSearches.filter(s => s.type === 'npwp').length,
          name: recentSearches.filter(s => s.type === 'name').length,
          domain: recentSearches.filter(s => s.type === 'domain').length,
          imei: recentSearches.filter(s => s.type === 'imei').length,
          address: recentSearches.filter(s => s.type === 'address').length
        }
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('OSINT recent searches API error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Simple token validation for testing
    if (!token.startsWith('kxg_')) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    const body = await request.json()
    const { searchId } = body

    if (!searchId) {
      return NextResponse.json({
        success: false,
        error: 'Search ID is required'
      }, { status: 400 })
    }

    // In a real app, delete the search from database
    // For demo, just return success
    
    return NextResponse.json({
      success: true,
      message: `Search ${searchId} deleted successfully`,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Delete search error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}
