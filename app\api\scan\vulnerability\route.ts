import { NextRequest, NextResponse } from 'next/server'
import { vulnerabilityScanner } from '@/lib/scanner'
import { AuthService } from '@/lib/auth'
import { Database } from '@/lib/database'

export async function POST(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Verify token and get user
    const user = await AuthService.getUserByToken(token)
    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    const body = await request.json()
    const {
      target,
      scanTypes = [],
      maxDepth = 3,
      timeout = 30,
      followRedirects = true
    } = body

    // Validate input
    if (!target) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Target URL is required',
          code: 'MISSING_TARGET'
        },
        { status: 400 }
      )
    }

    if (!scanTypes || scanTypes.length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'At least one scan type is required',
          code: 'MISSING_SCAN_TYPES'
        },
        { status: 400 }
      )
    }

    // Validate URL format
    try {
      new URL(target)
    } catch {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid URL format',
          code: 'INVALID_URL'
        },
        { status: 400 }
      )
    }

    // Validate scan types
    const validScanTypes = [
      'sqli', 'xss', 'lfi', 'rfi', 'rce', 'csrf', 'ssrf', 'xxe', 'idor', 'path_traversal'
    ]

    const invalidTypes = scanTypes.filter((type: string) => !validScanTypes.includes(type))
    if (invalidTypes.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: `Invalid scan types: ${invalidTypes.join(', ')}`,
          code: 'INVALID_SCAN_TYPES'
        },
        { status: 400 }
      )
    }

    // Check user quota
    const db = Database.getInstance()
    const today = new Date().toISOString().split('T')[0]

    const todayUsage = await db.query(
      `SELECT COUNT(*) as count FROM scan_history
       WHERE user_id = ? AND scan_type = 'vulnerability' AND DATE(created_at) = ?`,
      [user.id, today]
    )

    // Get user plan quotas
    const planInfo = await db.query(
      'SELECT scan_quota_daily FROM plans WHERE id = ?',
      [user.planId]
    )

    const dailyQuota = planInfo[0]?.scan_quota_daily || 5
    const usedToday = todayUsage[0]?.count || 0

    if (usedToday >= dailyQuota) {
      return NextResponse.json({
        success: false,
        error: `Daily scan quota exceeded. Used: ${usedToday}/${dailyQuota}`,
        code: 'QUOTA_EXCEEDED'
      }, { status: 429 })
    }

    // Start vulnerability scan
    const scanRequest = {
      target,
      scanTypes,
      maxDepth,
      timeout: timeout * 1000,
      followRedirects,
      userId: user.id
    }

    const result = await vulnerabilityScanner.startScan(scanRequest)

    return NextResponse.json({
      success: true,
      data: {
        scanId: result.scanId,
        status: result.status,
        target,
        scanTypes,
        quotaUsed: usedToday + 1,
        quotaLimit: dailyQuota,
        maxDepth,
        timeout
      },
      message: 'Vulnerability scan started successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Vulnerability scan error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Verify token and get user
    const user = await AuthService.getUserByToken(token)
    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    // Get scan history
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')

    const history = await vulnerabilityScanner.getScanHistory(user.id, limit)

    return NextResponse.json({
      success: true,
      data: history,
      pagination: {
        page,
        limit,
        total: history.length,
        totalPages: Math.ceil(history.length / limit)
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Scan history error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}
