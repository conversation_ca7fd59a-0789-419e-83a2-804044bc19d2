/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/login/page";
exports.ids = ["app/login/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/login/page.tsx */ \"(rsc)/./app/login/page.tsx\")), \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/login/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/login/page.tsx */ \"(ssr)/./app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNVc2VycyU1QyU1Q0Rvd25sb2FkcyU1QyU1Q2tvZGVYR3VhcmQlNUMlNUNhcHAlNUMlNUNsb2dpbiU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBMkYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb2RleGd1YXJkLz8zNTA4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcVXNlcnNcXFxcRG93bmxvYWRzXFxcXGtvZGVYR3VhcmRcXFxcYXBwXFxcXGxvZ2luXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CkodeXGuard%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/login/page.tsx":
/*!****************************!*\
  !*** ./app/login/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _components_Card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Card */ \"(ssr)/./components/Card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction LoginPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: \"\",\n        password: \"\",\n        rememberMe: false\n    });\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError(\"\");\n        try {\n            // TODO: Implement actual login API call\n            const response = await fetch(\"/api/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(formData)\n            });\n            if (response.ok) {\n                const data = await response.json();\n                // Store token in localStorage or cookie\n                localStorage.setItem(\"token\", data.token);\n                router.push(\"/dashboard\");\n            } else {\n                const errorData = await response.json();\n                setError(errorData.message || \"Login failed\");\n            }\n        } catch (err) {\n            setError(\"Network error. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleChange = (e)=>{\n        const { name, value, type, checked } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: type === \"checkbox\" ? checked : value\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center px-4 py-12\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-cyber opacity-50\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 nusantara-pattern opacity-5\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 w-full max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                className: \"inline-flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-12 w-12 text-cyber-green animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-3xl font-bold font-cyber\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"cyber-text\",\n                                                children: \"Kode\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"nusantara-gold\",\n                                                children: \"X\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white\",\n                                                children: \"Guard\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-gray-400\",\n                                children: \"Masuk ke akun Anda\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        border: \"green\",\n                        glow: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-8\",\n                            children: [\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Card__WEBPACK_IMPORTED_MODULE_4__.AlertCard, {\n                                        type: \"error\",\n                                        title: \"Login Gagal\",\n                                        message: error,\n                                        onClose: ()=>setError(\"\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"email\",\n                                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                    children: \"Email Address\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"h-5 w-5 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 100,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"email\",\n                                                            name: \"email\",\n                                                            type: \"email\",\n                                                            required: true,\n                                                            value: formData.email,\n                                                            onChange: handleChange,\n                                                            className: \"cyber-input pl-10 w-full\",\n                                                            placeholder: \"<EMAIL>\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 102,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"password\",\n                                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                    children: \"Password\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"h-5 w-5 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 122,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"password\",\n                                                            name: \"password\",\n                                                            type: showPassword ? \"text\" : \"password\",\n                                                            required: true,\n                                                            value: formData.password,\n                                                            onChange: handleChange,\n                                                            className: \"cyber-input pl-10 pr-10 w-full\",\n                                                            placeholder: \"••••••••\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>setShowPassword(!showPassword),\n                                                            className: \"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-cyber-green\",\n                                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 139,\n                                                                columnNumber: 37\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 139,\n                                                                columnNumber: 70\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"rememberMe\",\n                                                            name: \"rememberMe\",\n                                                            type: \"checkbox\",\n                                                            checked: formData.rememberMe,\n                                                            onChange: handleChange,\n                                                            className: \"h-4 w-4 text-cyber-green focus:ring-cyber-green border-gray-600 rounded bg-gray-800\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"rememberMe\",\n                                                            className: \"ml-2 block text-sm text-gray-300\",\n                                                            children: \"Remember me\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/forgot-password\",\n                                                    className: \"text-sm text-cyber-green hover:text-cyber-blue transition-colors\",\n                                                    children: \"Forgot password?\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: loading,\n                                            className: \"w-full cyber-btn-primary py-3 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-5 h-5 border-2 border-black border-t-transparent rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Signing in...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, this) : \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 p-4 bg-gray-800/50 rounded-lg border border-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-semibold text-cyber-green mb-2\",\n                                            children: \"Demo Credentials:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-400 space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Admin:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 20\n                                                        }, this),\n                                                        \" <EMAIL> / admin123\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"User:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 20\n                                                        }, this),\n                                                        \" <EMAIL> / admin123\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: [\n                                            \"Don't have an account?\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/register\",\n                                                className: \"text-cyber-green hover:text-cyber-blue transition-colors font-semibold\",\n                                                children: \"Sign up here\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 text-sm\",\n                            children: [\n                                \"By signing in, you agree to our\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/terms\",\n                                    className: \"text-cyber-green hover:underline\",\n                                    children: \"Terms of Service\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this),\n                                \" \",\n                                \"and\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/privacy\",\n                                    className: \"text-cyber-green hover:underline\",\n                                    children: \"Privacy Policy\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Card.tsx":
/*!*****************************!*\
  !*** ./components/Card.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlertCard: () => (/* binding */ AlertCard),\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   FeatureCard: () => (/* binding */ FeatureCard),\n/* harmony export */   LoadingCard: () => (/* binding */ LoadingCard),\n/* harmony export */   StatCard: () => (/* binding */ StatCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ Card,StatCard,FeatureCard,LoadingCard,AlertCard auto */ \nfunction Card({ children, className = \"\", hover = false, glow = false, border = \"default\" }) {\n    const borderColors = {\n        default: \"border-gray-700\",\n        green: \"border-cyber-green\",\n        blue: \"border-cyber-blue\",\n        red: \"border-cyber-red\",\n        gold: \"border-nusantara-gold\"\n    };\n    const glowColors = {\n        default: \"\",\n        green: \"shadow-lg shadow-cyber-green/20\",\n        blue: \"shadow-lg shadow-cyber-blue/20\",\n        red: \"shadow-lg shadow-cyber-red/20\",\n        gold: \"shadow-lg shadow-nusantara-gold/20\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `\n        bg-gray-900/50 backdrop-blur-sm rounded-lg border ${borderColors[border]}\n        ${hover ? \"hover:border-cyber-green hover:shadow-lg hover:shadow-cyber-green/20 transition-all duration-300 cursor-pointer\" : \"\"}\n        ${glow ? glowColors[border] : \"\"}\n        ${className}\n      `,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\nfunction StatCard({ title, value, icon: Icon, color = \"green\", trend, loading = false }) {\n    const colors = {\n        green: \"text-cyber-green\",\n        blue: \"text-cyber-blue\",\n        red: \"text-cyber-red\",\n        purple: \"text-cyber-purple\",\n        gold: \"text-nusantara-gold\"\n    };\n    const bgColors = {\n        green: \"bg-cyber-green/10\",\n        blue: \"bg-cyber-blue/10\",\n        red: \"bg-cyber-red/10\",\n        purple: \"bg-cyber-purple/10\",\n        gold: \"bg-nusantara-gold/10\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        border: color,\n        glow: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium text-gray-400\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-baseline space-x-2\",\n                                children: [\n                                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 w-20 bg-gray-700 animate-pulse rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: `text-2xl font-bold ${colors[color]}`,\n                                        children: typeof value === \"number\" ? value.toLocaleString() : value\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, this),\n                                    trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `text-sm font-medium ${trend.isPositive ? \"text-green-400\" : \"text-red-400\"}`,\n                                        children: [\n                                            trend.isPositive ? \"+\" : \"\",\n                                            trend.value,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `p-3 rounded-lg ${bgColors[color]}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            className: `h-6 w-6 ${colors[color]}`\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\nfunction FeatureCard({ title, description, icon: Icon, color = \"green\", onClick, disabled = false, badge }) {\n    const colors = {\n        green: \"text-cyber-green\",\n        blue: \"text-cyber-blue\",\n        red: \"text-cyber-red\",\n        purple: \"text-cyber-purple\",\n        gold: \"text-nusantara-gold\"\n    };\n    const bgColors = {\n        green: \"bg-cyber-green/10\",\n        blue: \"bg-cyber-blue/10\",\n        red: \"bg-cyber-red/10\",\n        purple: \"bg-cyber-purple/10\",\n        gold: \"bg-nusantara-gold/10\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        hover: !disabled && !!onClick,\n        border: color,\n        className: `relative ${disabled ? \"opacity-50 cursor-not-allowed\" : \"\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 h-full\",\n            onClick: disabled ? undefined : onClick,\n            children: [\n                badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-3 right-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `px-2 py-1 text-xs font-semibold rounded-full ${bgColors[color]} ${colors[color]}`,\n                        children: badge\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `inline-flex p-3 rounded-lg ${bgColors[color]} mb-4`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: `h-6 w-6 ${colors[color]}`\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-white mb-2\",\n                    children: title\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-400 text-sm leading-relaxed\",\n                    children: description\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this),\n                onClick && !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 pt-4 border-t border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-medium ${colors[color]} hover:underline`,\n                        children: \"Mulai Sekarang →\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\nfunction LoadingCard({ className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 animate-pulse\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 bg-gray-700 rounded-lg\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-gray-700 rounded w-3/4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-3 bg-gray-700 rounded w-1/2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                lineNumber: 184,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 183,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 182,\n        columnNumber: 5\n    }, this);\n}\nfunction AlertCard({ type = \"info\", title, message, onClose }) {\n    const styles = {\n        info: {\n            border: \"border-cyber-blue\",\n            bg: \"bg-cyber-blue/10\",\n            text: \"text-cyber-blue\",\n            icon: \"\\uD83D\\uDCA1\"\n        },\n        success: {\n            border: \"border-cyber-green\",\n            bg: \"bg-cyber-green/10\",\n            text: \"text-cyber-green\",\n            icon: \"✅\"\n        },\n        warning: {\n            border: \"border-nusantara-gold\",\n            bg: \"bg-nusantara-gold/10\",\n            text: \"text-nusantara-gold\",\n            icon: \"⚠️\"\n        },\n        error: {\n            border: \"border-cyber-red\",\n            bg: \"bg-cyber-red/10\",\n            text: \"text-cyber-red\",\n            icon: \"❌\"\n        }\n    };\n    const style = styles[type];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `border ${style.border} ${style.bg} rounded-lg p-4`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-lg\",\n                    children: style.icon\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: `font-semibold ${style.text}`,\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300 text-sm mt-1\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, this),\n                onClose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onClose,\n                    className: \"text-gray-400 hover:text-white transition-colors\",\n                    children: \"\\xd7\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n            lineNumber: 238,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\components\\\\Card.tsx\",\n        lineNumber: 237,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Card.tsx\n");

/***/ }),

/***/ "(rsc)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7cffc49d1683\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rb2RleGd1YXJkLy4vc3R5bGVzL2dsb2JhbHMuY3NzPzZkZDQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3Y2ZmYzQ5ZDE2ODNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(rsc)/./styles/globals.css\");\n\n\n\nconst metadata = {\n    title: \"KodeXGuard - Platform Cybersecurity & Bug Hunting\",\n    description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting dengan dukungan Bot WhatsApp/Telegram\",\n    keywords: \"cybersecurity, bug hunting, OSINT, vulnerability scanner, CVE, penetration testing, security tools\",\n    authors: [\n        {\n            name: \"KodeXGuard Team\"\n        }\n    ],\n    creator: \"KodeXGuard\",\n    publisher: \"KodeXGuard\",\n    robots: \"index, follow\",\n    openGraph: {\n        type: \"website\",\n        locale: \"id_ID\",\n        url: \"https://kodexguard.com\",\n        title: \"KodeXGuard - Platform Cybersecurity & Bug Hunting\",\n        description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting\",\n        siteName: \"KodeXGuard\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"KodeXGuard - Platform Cybersecurity & Bug Hunting\",\n        description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan Bug Hunting\"\n    },\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#00ff41\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"id\",\n        className: \"dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} min-h-screen bg-gradient-cyber text-white antialiased`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"matrix-bg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-b from-transparent via-cyber-dark/20 to-cyber-dark\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n              // Matrix Rain Effect\n              function createMatrixRain() {\n                const canvas = document.createElement('canvas');\n                const ctx = canvas.getContext('2d');\n                canvas.style.position = 'fixed';\n                canvas.style.top = '0';\n                canvas.style.left = '0';\n                canvas.style.width = '100%';\n                canvas.style.height = '100%';\n                canvas.style.pointerEvents = 'none';\n                canvas.style.zIndex = '-1';\n                canvas.style.opacity = '0.1';\n                document.body.appendChild(canvas);\n                \n                canvas.width = window.innerWidth;\n                canvas.height = window.innerHeight;\n                \n                const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';\n                const charArray = chars.split('');\n                const fontSize = 14;\n                const columns = canvas.width / fontSize;\n                const drops = [];\n                \n                for (let i = 0; i < columns; i++) {\n                  drops[i] = 1;\n                }\n                \n                function draw() {\n                  ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';\n                  ctx.fillRect(0, 0, canvas.width, canvas.height);\n                  \n                  ctx.fillStyle = '#00ff41';\n                  ctx.font = fontSize + 'px monospace';\n                  \n                  for (let i = 0; i < drops.length; i++) {\n                    const text = charArray[Math.floor(Math.random() * charArray.length)];\n                    ctx.fillText(text, i * fontSize, drops[i] * fontSize);\n                    \n                    if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {\n                      drops[i] = 0;\n                    }\n                    drops[i]++;\n                  }\n                }\n                \n                setInterval(draw, 50);\n                \n                window.addEventListener('resize', () => {\n                  canvas.width = window.innerWidth;\n                  canvas.height = window.innerHeight;\n                });\n              }\n              \n              // Initialize matrix effect after page load\n              if (document.readyState === 'loading') {\n                document.addEventListener('DOMContentLoaded', createMatrixRain);\n              } else {\n                createMatrixRain();\n              }\n            `\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\kodeXGuard\\\\app\\\\layout.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/login/page.tsx":
/*!****************************!*\
  !*** ./app/login/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\login\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CkodeXGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();