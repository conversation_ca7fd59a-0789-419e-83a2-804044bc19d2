"use strict";(()=>{var e={};e.id=504,e.ids=[504],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},92333:(e,t,a)=>{a.r(t),a.d(t,{originalPathname:()=>d,patchFetch:()=>g,requestAsyncStorage:()=>l,routeModule:()=>c,serverHooks:()=>h,staticGenerationAsyncStorage:()=>u});var r={};a.r(r),a.d(r,{GET:()=>p});var n=a(49303),o=a(88716),s=a(60670),i=a(87070);async function p(e){try{let t=e.headers.get("authorization"),a=t?.replace("Bearer ","");if(!a)return i.NextResponse.json({success:!1,error:"Authentication required"},{status:401});if(!a.startsWith("kxg_"))return i.NextResponse.json({success:!1,error:"Invalid token"},{status:401});let r=new URL(e.url),n=r.searchParams.get("period")||"current",o=r.searchParams.get("granularity")||"daily",s={current:{scans:{total:45,limit:null,percentage:0,resetDate:new Date(Date.now()+432e6).toISOString()},osintSearches:{total:123,limit:null,percentage:0,resetDate:new Date(Date.now()+432e6).toISOString()},apiCalls:{total:28560,limit:1e5,percentage:28.56,resetDate:new Date(Date.now()+432e6).toISOString()},storage:{used:156.7,limit:1e3,percentage:15.67}},history:{daily:Array.from({length:30},(e,t)=>({date:new Date(Date.now()-864e5*(29-t)).toISOString().split("T")[0],scans:Math.floor(5*Math.random())+1,osintSearches:Math.floor(10*Math.random())+2,apiCalls:Math.floor(2e3*Math.random())+500})),weekly:Array.from({length:12},(e,t)=>({week:`Week ${t+1}`,scans:Math.floor(20*Math.random())+5,osintSearches:Math.floor(50*Math.random())+10,apiCalls:Math.floor(1e4*Math.random())+2e3})),monthly:Array.from({length:6},(e,t)=>({month:new Date(Date.now()-2592e6*(5-t)).toISOString().slice(0,7),scans:Math.floor(80*Math.random())+20,osintSearches:Math.floor(200*Math.random())+50,apiCalls:Math.floor(4e4*Math.random())+1e4,storageUsed:Math.floor(50*Math.random())+10}))},breakdown:{scanTypes:[{type:"SQL Injection",count:18,percentage:40},{type:"XSS",count:12,percentage:26.7},{type:"CSRF",count:8,percentage:17.8},{type:"Directory Traversal",count:4,percentage:8.9},{type:"Others",count:3,percentage:6.7}],osintTypes:[{type:"Email",count:45,percentage:36.6},{type:"Phone",count:32,percentage:26},{type:"Name",count:28,percentage:22.8},{type:"Domain",count:12,percentage:9.8},{type:"Others",count:6,percentage:4.9}],apiEndpoints:[{endpoint:"/api/scan/vulnerability",calls:8520,percentage:29.8},{endpoint:"/api/osint/search",calls:7340,percentage:25.7},{endpoint:"/api/notifications",calls:4680,percentage:16.4},{endpoint:"/api/dashboard/stats",calls:3920,percentage:13.7},{endpoint:"Others",calls:4100,percentage:14.4}]},predictions:{monthlyProjection:{scans:67,osintSearches:184,apiCalls:42840},quotaExhaustion:{apiCalls:new Date(Date.now()+3888e6).toISOString()}}};return i.NextResponse.json({success:!0,data:s,meta:{period:n,granularity:o,generatedAt:new Date().toISOString()},timestamp:new Date().toISOString()})}catch(e){return console.error("User usage API error:",e),i.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/user/usage/route",pathname:"/api/user/usage",filename:"route",bundlePath:"app/api/user/usage/route"},resolvedPagePath:"D:\\Users\\Downloads\\kodeXGuard\\app\\api\\user\\usage\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:l,staticGenerationAsyncStorage:u,serverHooks:h}=c,d="/api/user/usage/route";function g(){return(0,s.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:u})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[216,592],()=>a(92333));module.exports=r})();