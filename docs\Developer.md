# 🛠️ KodeXGuard Developer Guide

## 📋 Table of Contents
- [Project Structure](#project-structure)
- [Development Setup](#development-setup)
- [Database Management](#database-management)
- [API Development](#api-development)
- [Frontend Development](#frontend-development)
- [Bot Development](#bot-development)
- [Testing](#testing)
- [Deployment](#deployment)

## 🏗️ Project Structure

```
kodexguard/
├── app/                          # Next.js 14 App Router
│   ├── layout.tsx               # Global layout
│   ├── page.tsx                 # Landing page
│   ├── login/page.tsx           # Login page
│   ├── register/page.tsx        # Register page
│   ├── dashboard/page.tsx       # Dashboard
│   ├── osint/page.tsx           # OSINT tools
│   ├── scanner/page.tsx         # Vulnerability scanner
│   ├── file-analyzer/page.tsx   # File analyzer
│   ├── cve/page.tsx             # CVE intelligence
│   ├── bot/page.tsx             # Bot management
│   ├── playground/page.tsx      # API playground
│   ├── profile/page.tsx         # User profile
│   ├── admin/page.tsx           # Admin panel
│   └── api/                     # API routes
│       ├── auth/                # Authentication endpoints
│       ├── scan/                # Scanning endpoints
│       ├── osint/               # OSINT endpoints
│       ├── file/                # File analysis endpoints
│       ├── cve/                 # CVE endpoints
│       └── bot/                 # Bot endpoints
├── components/                   # Reusable UI components
│   ├── Navbar.tsx               # Navigation component
│   ├── Sidebar.tsx              # Sidebar component
│   ├── Card.tsx                 # Card components
│   ├── Table.tsx                # Table component
│   ├── Modal.tsx                # Modal component
│   └── forms/                   # Form components
├── lib/                         # Core business logic
│   ├── database.ts              # Database connections
│   ├── auth.ts                  # Authentication logic
│   ├── scanner.ts               # Vulnerability scanner
│   ├── osint.ts                 # OSINT functions
│   ├── file-analyzer.ts         # File analysis
│   ├── cve.ts                   # CVE management
│   └── bot.ts                   # Bot integration
├── utils/                       # Helper utilities
│   ├── formatter.ts             # Data formatting
│   ├── validator.ts             # Input validation
│   ├── crypto.ts                # Cryptography utils
│   └── api.ts                   # API utilities
├── types/                       # TypeScript definitions
│   ├── user.ts                  # User types
│   ├── scan.ts                  # Scan types
│   ├── plan.ts                  # Plan types
│   └── api.ts                   # API types
├── constants/                   # Global constants
│   ├── roles.ts                 # User roles
│   ├── plans.ts                 # Plan definitions
│   └── endpoints.ts             # API endpoints
├── hooks/                       # Custom React hooks
│   ├── useAuth.ts               # Authentication hook
│   ├── useAPI.ts                # API hook
│   └── useWebSocket.ts          # WebSocket hook
├── middlewares/                 # Middleware functions
│   ├── auth.ts                  # Auth middleware
│   ├── rateLimit.ts             # Rate limiting
│   └── validation.ts            # Input validation
├── styles/                      # Global styles
│   └── globals.css              # Global CSS
└── docs/                        # Documentation
    ├── API.md                   # API documentation
    ├── Developer.md             # This file
    ├── BotSetup.md              # Bot setup guide
    └── database-schema.sql      # Database schema
```

## 🚀 Development Setup

### 1. Environment Setup

Create `.env.local` file:
```env
# Database
DATABASE_URL="mysql://root:password@localhost:3306/kodexguard"
DB_HOST="localhost"
DB_PORT="3306"
DB_USER="root"
DB_PASSWORD="password"
DB_NAME="kodexguard"

# Redis
REDIS_URL="redis://localhost:6379"
REDIS_HOST="localhost"
REDIS_PORT="6379"

# Elasticsearch (Optional)
ELASTICSEARCH_URL="http://localhost:9200"

# JWT
JWT_SECRET="your-super-secret-jwt-key"
JWT_EXPIRES_IN="7d"

# Bot Configuration
WHATSAPP_SESSION_NAME="kodexguard-wa"
TELEGRAM_BOT_TOKEN="your-telegram-bot-token"

# Development
NODE_ENV="development"
DEBUG="true"
```

### 2. Database Setup

```bash
# Start MySQL
sudo systemctl start mysql

# Create database
mysql -u root -p
CREATE DATABASE kodexguard CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
exit

# Initialize schema
bun run db:init
```

### 3. Redis Setup

```bash
# Ubuntu/Debian
sudo apt install redis-server
sudo systemctl start redis-server

# Or using Docker
docker run -d -p 6379:6379 redis:alpine
```

### 4. Start Development

```bash
# Install dependencies
bun install

# Start development server
bun run dev

# Open browser
open http://localhost:3000
```

## 🗄️ Database Management

### Schema Design

The database uses MySQL with the following main tables:
- `users` - User accounts and profiles
- `api_keys` - API key management
- `plans` - Subscription plans
- `subscriptions` - User subscriptions
- `scans` - Scan records
- `scan_results` - Scan findings
- `cve_database` - CVE information
- `file_analysis` - File analysis results
- `audit_logs` - System audit logs

### Database Operations

```bash
# Initialize database
bun run db:init

# Reset database (WARNING: Deletes all data)
bun run db:reset

# Seed sample data
bun run db:seed
```

### Database Queries

```typescript
import { db } from '@/lib/database'

// Simple query
const users = await db.query('SELECT * FROM users WHERE role = ?', ['admin'])

// Transaction
await db.transaction(async (connection) => {
  await connection.execute('INSERT INTO users (...) VALUES (...)', [...])
  await connection.execute('INSERT INTO user_stats (...) VALUES (...)', [...])
})
```

## 🔌 API Development

### Creating API Routes

Create API routes in `app/api/` directory:

```typescript
// app/api/example/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'
import { verifyToken } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const user = await verifyToken(request)
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Your logic here
    const data = await db.query('SELECT * FROM table WHERE user_id = ?', [user.id])
    
    return NextResponse.json({ data })
  } catch (error) {
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    if (!body.required_field) {
      return NextResponse.json({ error: 'Missing required field' }, { status: 400 })
    }

    // Your logic here
    
    return NextResponse.json({ success: true })
  } catch (error) {
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
```

### Authentication Middleware

```typescript
// lib/auth.ts
import jwt from 'jsonwebtoken'
import { NextRequest } from 'next/server'

export async function verifyToken(request: NextRequest) {
  const token = request.headers.get('authorization')?.replace('Bearer ', '')
  
  if (!token) {
    return null
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any
    const user = await db.query('SELECT * FROM users WHERE id = ?', [decoded.userId])
    return user[0]
  } catch (error) {
    return null
  }
}
```

## 🎨 Frontend Development

### Component Structure

```typescript
// components/ExampleComponent.tsx
'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/Card'

interface ExampleComponentProps {
  title: string
  data?: any[]
  loading?: boolean
}

export default function ExampleComponent({ title, data = [], loading = false }: ExampleComponentProps) {
  const [state, setState] = useState(null)

  useEffect(() => {
    // Component logic
  }, [])

  if (loading) {
    return <div className="animate-pulse">Loading...</div>
  }

  return (
    <Card>
      <div className="p-6">
        <h2 className="text-xl font-bold text-white mb-4">{title}</h2>
        {/* Component content */}
      </div>
    </Card>
  )
}
```

### Custom Hooks

```typescript
// hooks/useAPI.ts
import { useState, useEffect } from 'react'

export function useAPI<T>(url: string) {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetch(url)
      .then(res => res.json())
      .then(data => {
        setData(data)
        setLoading(false)
      })
      .catch(err => {
        setError(err.message)
        setLoading(false)
      })
  }, [url])

  return { data, loading, error }
}
```

### Styling Guidelines

Use TailwindCSS with cyber-themed classes:

```css
/* Cyber theme colors */
.cyber-text { color: #00ff41; }
.cyber-bg { background: #0a0a0a; }
.cyber-border { border-color: #00ff41; }
.cyber-glow { box-shadow: 0 0 10px #00ff41; }

/* Nusantara theme colors */
.nusantara-gold { color: #d4af37; }
.nusantara-emerald { color: #50c878; }
```

## 🤖 Bot Development

### WhatsApp Bot Setup

```typescript
// lib/bot/whatsapp.ts
import venom from 'venom-bot'

export class WhatsAppBot {
  private client: any

  async initialize() {
    this.client = await venom.create({
      session: process.env.WHATSAPP_SESSION_NAME,
      multidevice: true
    })

    this.client.onMessage(this.handleMessage.bind(this))
  }

  private async handleMessage(message: any) {
    if (message.body.startsWith('/scan')) {
      const url = message.body.split(' ')[1]
      await this.handleScanCommand(message.from, url)
    }
  }

  private async handleScanCommand(from: string, url: string) {
    // Implement scan logic
    await this.client.sendText(from, `Starting scan for ${url}...`)
  }
}
```

### Telegram Bot Setup

```typescript
// lib/bot/telegram.ts
import TelegramBot from 'node-telegram-bot-api'

export class TelegramBotHandler {
  private bot: TelegramBot

  constructor() {
    this.bot = new TelegramBot(process.env.TELEGRAM_BOT_TOKEN!, { polling: true })
    this.setupCommands()
  }

  private setupCommands() {
    this.bot.onText(/\/scan (.+)/, async (msg, match) => {
      const chatId = msg.chat.id
      const url = match![1]
      
      await this.bot.sendMessage(chatId, `Starting scan for ${url}...`)
      // Implement scan logic
    })
  }
}
```

## 🧪 Testing

### Unit Tests

```typescript
// tests/auth.test.ts
import { describe, it, expect } from 'bun:test'
import { verifyToken } from '@/lib/auth'

describe('Authentication', () => {
  it('should verify valid token', async () => {
    const token = 'valid-jwt-token'
    const user = await verifyToken(token)
    expect(user).toBeTruthy()
  })

  it('should reject invalid token', async () => {
    const token = 'invalid-token'
    const user = await verifyToken(token)
    expect(user).toBeNull()
  })
})
```

### Integration Tests

```typescript
// tests/api.test.ts
import { describe, it, expect } from 'bun:test'

describe('API Endpoints', () => {
  it('should authenticate user', async () => {
    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password'
      })
    })

    expect(response.status).toBe(200)
    const data = await response.json()
    expect(data.token).toBeTruthy()
  })
})
```

## 🚀 Deployment

### Production Build

```bash
# Build application
bun run build

# Start production server
bun run start
```

### Environment Variables

```env
# Production environment
NODE_ENV="production"
DATABASE_URL="mysql://user:pass@prod-db:3306/kodexguard"
REDIS_URL="redis://prod-redis:6379"
JWT_SECRET="production-secret-key"
```

### Docker Deployment

```dockerfile
# Dockerfile
FROM oven/bun:1.2-alpine

WORKDIR /app

COPY package.json bun.lockb ./
RUN bun install --frozen-lockfile

COPY . .
RUN bun run build

EXPOSE 3000

CMD ["bun", "run", "start"]
```

### Performance Optimization

1. **Database Optimization**
   - Use connection pooling
   - Add proper indexes
   - Optimize queries

2. **Caching Strategy**
   - Redis for session storage
   - API response caching
   - Static asset caching

3. **Security Hardening**
   - Rate limiting
   - Input validation
   - SQL injection prevention
   - XSS protection

## 📚 Additional Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [TailwindCSS Documentation](https://tailwindcss.com/docs)
- [Bun Documentation](https://bun.sh/docs)
- [MySQL Documentation](https://dev.mysql.com/doc/)
- [Redis Documentation](https://redis.io/documentation)

## 🤝 Contributing Guidelines

1. Follow TypeScript best practices
2. Use consistent naming conventions
3. Write comprehensive tests
4. Document your code
5. Follow the existing code style
6. Create meaningful commit messages

---

For more information, contact the development team or check the main documentation.
