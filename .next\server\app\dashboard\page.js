(()=>{var e={};e.id=7702,e.ids=[7702],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},88123:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>h,originalPathname:()=>u,pages:()=>o,routeModule:()=>x,tree:()=>d}),s(69521),s(30829),s(35866);var a=s(23191),r=s(88716),i=s(37922),n=s.n(i),l=s(95231),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(t,c);let d=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,69521)),"D:\\Users\\Downloads\\kodeXGuard\\app\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,30829)),"D:\\Users\\Downloads\\kodeXGuard\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\Users\\Downloads\\kodeXGuard\\app\\dashboard\\page.tsx"],u="/dashboard/page",h={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79014:(e,t,s)=>{Promise.resolve().then(s.bind(s,91436))},91436:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var a=s(10326),r=s(17577),i=s(83061),n=s(2262),l=s(88307),c=s(94244),d=s(6530),o=s(58038),u=s(50732),h=s(92498),x=s(37202),m=s(48998),p=s(54659),g=s(66697),y=s(17069),b=s(67382),f=s(27564),j=s(35047);function v({children:e,requiredRole:t=[],requiredPlan:s=[],fallbackPath:r="/login"}){let{user:i,isAuthenticated:n,isLoading:l}=(0,b.a)(),c=(0,j.useRouter)();if(l)return a.jsx("div",{className:"min-h-screen bg-gradient-cyber flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-cyber-green mx-auto"}),a.jsx("p",{className:"mt-4 text-gray-400",children:"Authenticating..."})]})});if(!n)return a.jsx("div",{className:"min-h-screen bg-gradient-cyber flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-red-500 text-6xl mb-4",children:"\uD83D\uDD12"}),a.jsx("h1",{className:"text-2xl font-bold text-white mb-2",children:"Access Denied"}),a.jsx("p",{className:"text-gray-400 mb-4",children:"Please log in to access this page"}),a.jsx("button",{onClick:()=>c.push("/login"),className:"bg-cyber-green text-black px-6 py-2 rounded-lg font-semibold hover:bg-green-400 transition-colors",children:"Go to Login"})]})});if(i){if(t.length>0&&!t.includes(i.role))return a.jsx("div",{className:"min-h-screen bg-gradient-cyber flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-yellow-500 text-6xl mb-4",children:"⚠️"}),a.jsx("h1",{className:"text-2xl font-bold text-white mb-2",children:"Insufficient Permissions"}),(0,a.jsxs)("p",{className:"text-gray-400 mb-4",children:["This feature requires ",t.join(" or ")," role"]}),a.jsx("button",{onClick:()=>c.push("/dashboard"),className:"bg-cyber-green text-black px-6 py-2 rounded-lg font-semibold hover:bg-green-400 transition-colors",children:"Back to Dashboard"})]})});if(s.length>0&&!s.includes(i.plan))return a.jsx("div",{className:"min-h-screen bg-gradient-cyber flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-purple-500 text-6xl mb-4",children:"\uD83D\uDC8E"}),a.jsx("h1",{className:"text-2xl font-bold text-white mb-2",children:"Upgrade Required"}),(0,a.jsxs)("p",{className:"text-gray-400 mb-4",children:["This feature requires ",s.join(" or ")," plan"]}),(0,a.jsxs)("div",{className:"space-x-4",children:[a.jsx("button",{onClick:()=>c.push("/upgrade"),className:"bg-cyber-green text-black px-6 py-2 rounded-lg font-semibold hover:bg-green-400 transition-colors",children:"Upgrade Plan"}),a.jsx("button",{onClick:()=>c.push("/dashboard"),className:"bg-gray-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-gray-500 transition-colors",children:"Back to Dashboard"})]})]})})}return a.jsx(a.Fragment,{children:e})}function N(){let{user:e,isAuthenticated:t}=(0,b.a)(),{data:s,loading:j,error:N}=(0,f.f_)(),{data:k,loading:w}=(0,f.Gr)();if(!t)return a.jsx(v,{children:a.jsx("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-cyber-green mx-auto"}),a.jsx("p",{className:"mt-4 text-gray-400",children:"Loading..."})]})})});let[Z,P]=(0,r.useState)([{id:1,type:"scan",title:"Vulnerability scan completed",target:"example.com",severity:"high",time:"2 minutes ago"},{id:2,type:"osint",title:"OSINT search completed",target:"<EMAIL>",severity:"medium",time:"15 minutes ago"},{id:3,type:"file",title:"File analysis completed",target:"suspicious.php",severity:"critical",time:"1 hour ago"}]),A=[{title:"OSINT Investigator",description:"Investigasi mendalam dengan pencarian NIK, NPWP, nomor HP, email, dan domain",icon:l.Z,color:"green",href:"/osint"},{title:"Vulnerability Scanner",description:"Deteksi SQLi, XSS, LFI, RCE dengan scoring CVSS dan mapping CVE",icon:c.Z,color:"red",href:"/scanner"},{title:"File Analyzer",description:"Analisis webshell, malware, dan deteksi secret dalam berbagai format file",icon:d.Z,color:"blue",href:"/file-analyzer"},{title:"CVE Intelligence",description:"Database CVE terupdate dengan Google dorking dan payload generator",icon:o.Z,color:"purple",href:"/cve"},{title:"Bot Integration",description:"WhatsApp & Telegram bot untuk monitoring dan notifikasi real-time",icon:u.Z,color:"gold",href:"/bot"},{title:"API Playground",description:"Testing environment dengan Swagger docs dan request builder",icon:h.Z,color:"green",href:"/playground"}],M=e=>{switch(e){case"critical":return"text-red-400";case"high":return"text-orange-400";case"medium":return"text-yellow-400";case"low":return"text-blue-400";default:return"text-gray-400"}},C=e=>{switch(e){case"critical":case"high":return x.Z;case"medium":return m.Z;default:return p.Z}};return a.jsx(v,{children:a.jsx(i.Z,{user:e,title:"Dashboard",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold font-cyber text-white mb-2",children:["Welcome back, ",a.jsx("span",{className:"cyber-text",children:e.username})]}),a.jsx("p",{className:"text-gray-400",children:"Monitor your cybersecurity activities and manage your tools from here."})]}),a.jsx("div",{className:"mb-8",children:a.jsx(n.XO,{type:"success",title:"System Status: Online",message:"All systems are operational. Last updated: 2 minutes ago"})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[a.jsx(n.Rm,{title:"Total Scans",value:stats.totalScans,icon:g.Z,color:"green",trend:{value:12,isPositive:!0},loading:stats.loading}),a.jsx(n.Rm,{title:"Vulnerabilities Found",value:stats.vulnerabilitiesFound,icon:c.Z,color:"red",trend:{value:8,isPositive:!0},loading:stats.loading}),a.jsx(n.Rm,{title:"Files Analyzed",value:stats.filesAnalyzed,icon:d.Z,color:"blue",trend:{value:15,isPositive:!0},loading:stats.loading}),a.jsx(n.Rm,{title:"API Calls",value:stats.apiCalls,icon:y.Z,color:"purple",trend:{value:23,isPositive:!0},loading:stats.loading})]}),(0,a.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2",children:[a.jsx("h2",{className:"text-xl font-bold text-white mb-6",children:"Quick Access"}),a.jsx("div",{className:"grid md:grid-cols-2 gap-6",children:A.map((e,t)=>a.jsx(n.TT,{title:e.title,description:e.description,icon:e.icon,color:e.color,onClick:()=>window.location.href=e.href},t))})]}),(0,a.jsxs)("div",{children:[a.jsx("h2",{className:"text-xl font-bold text-white mb-6",children:"Recent Activity"}),a.jsx(n.Zb,{children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("div",{className:"space-y-4",children:Z.map(e=>{let t=C(e.severity);return(0,a.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg bg-gray-800/50",children:[a.jsx(t,{className:`h-5 w-5 mt-0.5 ${M(e.severity)}`}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[a.jsx("p",{className:"text-sm font-medium text-white",children:e.title}),a.jsx("p",{className:"text-sm text-gray-400 truncate",children:e.target}),a.jsx("p",{className:"text-xs text-gray-500",children:e.time})]}),a.jsx("span",{className:`px-2 py-1 text-xs font-semibold rounded-full ${"critical"===e.severity?"bg-red-900/50 text-red-400":"high"===e.severity?"bg-orange-900/50 text-orange-400":"medium"===e.severity?"bg-yellow-900/50 text-yellow-400":"bg-blue-900/50 text-blue-400"}`,children:e.severity})]},e.id)})}),a.jsx("div",{className:"mt-4 pt-4 border-t border-gray-700",children:a.jsx("button",{className:"w-full text-sm text-cyber-green hover:text-cyber-blue transition-colors",children:"View All Activity →"})})]})}),a.jsx(n.Zb,{className:"mt-6",border:"gold",glow:!0,children:(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"Current Plan"}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[a.jsx("span",{className:"text-2xl font-bold nusantara-gold",children:e.plan.charAt(0).toUpperCase()+e.plan.slice(1)}),a.jsx("span",{className:"px-3 py-1 bg-nusantara-gold/20 text-nusantara-gold rounded-full text-sm font-semibold",children:"Active"})]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm text-gray-400",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{children:"Daily Scans"}),a.jsx("span",{className:"text-white",children:"Unlimited"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{children:"API Calls"}),a.jsx("span",{className:"text-white",children:"100,000/day"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{children:"File Upload"}),a.jsx("span",{className:"text-white",children:"1GB"})]})]}),a.jsx("button",{className:"w-full mt-4 cyber-btn text-sm",children:"Manage Plan"})]})})]})]})]})})})}},27564:(e,t,s)=>{"use strict";s.d(t,{EZ:()=>d,Gr:()=>l,Vb:()=>c,f_:()=>n});var a=s(17577),r=s(67382);function i(e,t={immediate:!0}){let[s,i]=(0,a.useState)(null),[n,l]=(0,a.useState)(!1),[c,d]=(0,a.useState)(null),{token:o,isAuthenticated:u}=(0,r.a)();return{data:s,loading:n,error:c,refetch:async()=>{if(!u||!o){d("Authentication required");return}try{l(!0),d(null);let t=await fetch(e,{headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json"}}),s=await t.json();s.success&&s.data?i(s.data):d(s.error||"Failed to fetch data")}catch(e){d(e instanceof Error?e.message:"Network error")}finally{l(!1)}},mutate:async(t="POST",s)=>{if(!u||!o)throw Error("Authentication required");try{l(!0),d(null);let a=await fetch(e,{method:t,headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json"},body:s?JSON.stringify(s):void 0}),r=await a.json();if(r.success)return r.data&&i(r.data),r;throw Error(r.error||"Request failed")}catch(t){let e=t instanceof Error?t.message:"Network error";throw d(e),Error(e)}finally{l(!1)}}}}function n(){return i("/api/dashboard/stats")}function l(){return i("/api/user/profile")}function c(){return i("/api/osint/recent")}function d(){return i("/api/osint/templates")}},66697:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},37202:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},50732:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},94244:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Bug",[["path",{d:"m8 2 1.88 1.88",key:"fmnt4t"}],["path",{d:"M14.12 3.88 16 2",key:"qol33r"}],["path",{d:"M9 7.13v-1a3.003 3.003 0 1 1 6 0v1",key:"d7y7pr"}],["path",{d:"M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6",key:"xs1cw7"}],["path",{d:"M12 20v-9",key:"1qisl0"}],["path",{d:"M6.53 9C4.6 8.8 3 7.1 3 5",key:"32zzws"}],["path",{d:"M6 13H2",key:"82j7cp"}],["path",{d:"M3 21c0-2.1 1.7-3.9 3.8-4",key:"4p0ekp"}],["path",{d:"M20.97 5c0 2.1-1.6 3.8-3.5 4",key:"18gb23"}],["path",{d:"M22 13h-4",key:"1jl80f"}],["path",{d:"M17.2 17c2.1.1 3.8 1.9 3.8 4",key:"k3fwyw"}]])},54659:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},48998:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},92498:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},6530:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("FileSearch",[["path",{d:"M4 22h14a2 2 0 0 0 2-2V7.5L14.5 2H6a2 2 0 0 0-2 2v3",key:"am10z3"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["path",{d:"M5 17a3 3 0 1 0 0-6 3 3 0 0 0 0 6z",key:"ychnub"}],["path",{d:"m9 18-1.5-1.5",key:"1j6qii"}]])},88307:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},58038:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},17069:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},35047:(e,t,s)=>{"use strict";var a=s(77389);s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}})},69521:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`D:\Users\Downloads\kodeXGuard\app\dashboard\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[9276,82,7608,3061],()=>s(88123));module.exports=a})();