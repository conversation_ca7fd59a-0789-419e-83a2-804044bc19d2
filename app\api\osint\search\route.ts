import { NextRequest, NextResponse } from 'next/server'
import { osintEngine } from '@/lib/osint'

export async function POST(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Simple token validation for testing
    if (!token.startsWith('kxg_')) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    // Mock user for testing
    const user = {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      role: 'super_admin',
      planId: 5
    }

    const body = await request.json()
    const { query, type, deepSearch = false, sources = [] } = body

    // Validate input
    if (!query || !type) {
      return NextResponse.json(
        {
          success: false,
          error: 'Query and type are required',
          code: 'MISSING_PARAMETERS'
        },
        { status: 400 }
      )
    }

    // Validate search type
    const validTypes = ['email', 'phone', 'nik', 'npwp', 'name', 'domain', 'imei', 'address']
    if (!validTypes.includes(type)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid search type',
          code: 'INVALID_TYPE'
        },
        { status: 400 }
      )
    }

    // Perform OSINT search
    const searchRequest = {
      query,
      type,
      deepSearch,
      sources,
      userId: user.id
    }

    const result = await osintEngine.search(searchRequest)

    return NextResponse.json({
      success: true,
      data: {
        searchId: `osint_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        query,
        type,
        results: result.results,
        sources: result.sources,
        confidenceScore: result.confidenceScore,
        searchTime: result.searchTime,
        totalResults: result.totalResults,
        quotaUsed: 1,
        quotaLimit: 100
      },
      message: `Found ${result.totalResults} results`,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('OSINT search error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get auth token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 })
    }

    // Simple token validation for testing
    if (!token.startsWith('kxg_')) {
      return NextResponse.json({
        success: false,
        error: 'Invalid token'
      }, { status: 401 })
    }

    // Mock user for testing
    const user = {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      role: 'super_admin',
      planId: 5
    }

    // Get search history
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')

    // Mock history data
    const mockHistory = [
      {
        id: 1,
        searchType: 'email',
        searchQuery: '<EMAIL>',
        results: { found: true },
        sources: ['Dukcapil', 'GitHub'],
        confidenceScore: 85,
        createdAt: new Date().toISOString()
      },
      {
        id: 2,
        searchType: 'phone',
        searchQuery: '+62812345678',
        results: { found: true },
        sources: ['Telecom', 'Social Media'],
        confidenceScore: 92,
        createdAt: new Date(Date.now() - 3600000).toISOString()
      }
    ]

    return NextResponse.json({
      success: true,
      data: mockHistory,
      pagination: {
        page,
        limit,
        total: mockHistory.length,
        totalPages: Math.ceil(mockHistory.length / limit)
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('OSINT history error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}
