'use client'

import { useState, useEffect } from 'react'
import DashboardLayout from '@/components/DashboardLayout'
import { StatCard, FeatureCard, Card, AlertCard } from '@/components/Card'
import { 
  Shield, 
  Search, 
  FileSearch, 
  Bug, 
  Bot, 
  Code, 
  Users, 
  Activity,
  TrendingUp,
  Clock,
  AlertTriangle,
  CheckCircle
} from 'lucide-react'
import { useAuth } from '@/app/contexts/AuthContext'
import { useDashboardStats, useUserProfile } from '@/app/hooks/useApi'
import ProtectedRoute from '@/app/components/ProtectedRoute'

export default function DashboardPage() {
  const { user, isAuthenticated } = useAuth()
  const { data: dashboardStats, loading: statsLoading, error: statsError } = useDashboardStats()
  const { data: userProfile, loading: profileLoading } = useUserProfile()

  // Show loading state if not authenticated
  if (!isAuthenticated) {
    return (
      <ProtectedRoute>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-cyber-green mx-auto"></div>
            <p className="mt-4 text-gray-400">Loading...</p>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  const [recentActivity, setRecentActivity] = useState([
    {
      id: 1,
      type: 'scan',
      title: 'Vulnerability scan completed',
      target: 'example.com',
      severity: 'high',
      time: '2 minutes ago'
    },
    {
      id: 2,
      type: 'osint',
      title: 'OSINT search completed',
      target: '<EMAIL>',
      severity: 'medium',
      time: '15 minutes ago'
    },
    {
      id: 3,
      type: 'file',
      title: 'File analysis completed',
      target: 'suspicious.php',
      severity: 'critical',
      time: '1 hour ago'
    }
  ])

  useEffect(() => {
    // Simulate loading stats
    setTimeout(() => {
      setStats({
        totalScans: 1247,
        vulnerabilitiesFound: 89,
        filesAnalyzed: 456,
        apiCalls: 12890,
        loading: false
      })
    }, 1000)
  }, [])

  const features = [
    {
      title: 'OSINT Investigator',
      description: 'Investigasi mendalam dengan pencarian NIK, NPWP, nomor HP, email, dan domain',
      icon: Search,
      color: 'green' as const,
      href: '/osint'
    },
    {
      title: 'Vulnerability Scanner',
      description: 'Deteksi SQLi, XSS, LFI, RCE dengan scoring CVSS dan mapping CVE',
      icon: Bug,
      color: 'red' as const,
      href: '/scanner'
    },
    {
      title: 'File Analyzer',
      description: 'Analisis webshell, malware, dan deteksi secret dalam berbagai format file',
      icon: FileSearch,
      color: 'blue' as const,
      href: '/file-analyzer'
    },
    {
      title: 'CVE Intelligence',
      description: 'Database CVE terupdate dengan Google dorking dan payload generator',
      icon: Shield,
      color: 'purple' as const,
      href: '/cve'
    },
    {
      title: 'Bot Integration',
      description: 'WhatsApp & Telegram bot untuk monitoring dan notifikasi real-time',
      icon: Bot,
      color: 'gold' as const,
      href: '/bot'
    },
    {
      title: 'API Playground',
      description: 'Testing environment dengan Swagger docs dan request builder',
      icon: Code,
      color: 'green' as const,
      href: '/playground'
    }
  ]

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-400'
      case 'high': return 'text-orange-400'
      case 'medium': return 'text-yellow-400'
      case 'low': return 'text-blue-400'
      default: return 'text-gray-400'
    }
  }

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return AlertTriangle
      case 'high': return AlertTriangle
      case 'medium': return Clock
      default: return CheckCircle
    }
  }

  return (
    <ProtectedRoute>
      <DashboardLayout user={user} title="Dashboard">
      <div>
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold font-cyber text-white mb-2">
              Welcome back, <span className="cyber-text">{user.username}</span>
            </h1>
            <p className="text-gray-400">
              Monitor your cybersecurity activities and manage your tools from here.
            </p>
          </div>

          {/* System Status Alert */}
          <div className="mb-8">
            <AlertCard
              type="success"
              title="System Status: Online"
              message="All systems are operational. Last updated: 2 minutes ago"
            />
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <StatCard
              title="Total Scans"
              value={stats.totalScans}
              icon={Activity}
              color="green"
              trend={{ value: 12, isPositive: true }}
              loading={stats.loading}
            />
            <StatCard
              title="Vulnerabilities Found"
              value={stats.vulnerabilitiesFound}
              icon={Bug}
              color="red"
              trend={{ value: 8, isPositive: true }}
              loading={stats.loading}
            />
            <StatCard
              title="Files Analyzed"
              value={stats.filesAnalyzed}
              icon={FileSearch}
              color="blue"
              trend={{ value: 15, isPositive: true }}
              loading={stats.loading}
            />
            <StatCard
              title="API Calls"
              value={stats.apiCalls}
              icon={TrendingUp}
              color="purple"
              trend={{ value: 23, isPositive: true }}
              loading={stats.loading}
            />
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Features Grid */}
            <div className="lg:col-span-2">
              <h2 className="text-xl font-bold text-white mb-6">Quick Access</h2>
              <div className="grid md:grid-cols-2 gap-6">
                {features.map((feature, index) => (
                  <FeatureCard
                    key={index}
                    title={feature.title}
                    description={feature.description}
                    icon={feature.icon}
                    color={feature.color}
                    onClick={() => window.location.href = feature.href}
                  />
                ))}
              </div>
            </div>

            {/* Recent Activity */}
            <div>
              <h2 className="text-xl font-bold text-white mb-6">Recent Activity</h2>
              <Card>
                <div className="p-6">
                  <div className="space-y-4">
                    {recentActivity.map((activity) => {
                      const SeverityIcon = getSeverityIcon(activity.severity)
                      return (
                        <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-lg bg-gray-800/50">
                          <SeverityIcon className={`h-5 w-5 mt-0.5 ${getSeverityColor(activity.severity)}`} />
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-white">{activity.title}</p>
                            <p className="text-sm text-gray-400 truncate">{activity.target}</p>
                            <p className="text-xs text-gray-500">{activity.time}</p>
                          </div>
                          <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                            activity.severity === 'critical' ? 'bg-red-900/50 text-red-400' :
                            activity.severity === 'high' ? 'bg-orange-900/50 text-orange-400' :
                            activity.severity === 'medium' ? 'bg-yellow-900/50 text-yellow-400' :
                            'bg-blue-900/50 text-blue-400'
                          }`}>
                            {activity.severity}
                          </span>
                        </div>
                      )
                    })}
                  </div>
                  
                  <div className="mt-4 pt-4 border-t border-gray-700">
                    <button className="w-full text-sm text-cyber-green hover:text-cyber-blue transition-colors">
                      View All Activity →
                    </button>
                  </div>
                </div>
              </Card>

              {/* Plan Info */}
              <Card className="mt-6" border="gold" glow>
                <div className="p-6">
                  <h3 className="text-lg font-semibold text-white mb-2">Current Plan</h3>
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-2xl font-bold nusantara-gold">
                      {user.plan.charAt(0).toUpperCase() + user.plan.slice(1)}
                    </span>
                    <span className="px-3 py-1 bg-nusantara-gold/20 text-nusantara-gold rounded-full text-sm font-semibold">
                      Active
                    </span>
                  </div>
                  <div className="space-y-2 text-sm text-gray-400">
                    <div className="flex justify-between">
                      <span>Daily Scans</span>
                      <span className="text-white">Unlimited</span>
                    </div>
                    <div className="flex justify-between">
                      <span>API Calls</span>
                      <span className="text-white">100,000/day</span>
                    </div>
                    <div className="flex justify-between">
                      <span>File Upload</span>
                      <span className="text-white">1GB</span>
                    </div>
                  </div>
                  <button className="w-full mt-4 cyber-btn text-sm">
                    Manage Plan
                  </button>
                </div>
              </Card>
            </div>
          </div>
      </div>
    </DashboardLayout>
    </ProtectedRoute>
  )
}
